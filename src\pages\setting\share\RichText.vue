<template>
  <view id="_root" :class="(selectable ? '_select ' : '') + '_root'" :style="containerStyle">
    <slot v-if="!nodes[0]" />
    <node v-else :childs="nodes" :opts="[lazyLoad, loadingImg, errorImg, showImgMenu, selectable]" name="span" />
  </view>
</template>

<script>
import mpHtml from '@/components/mp-html-lite/mp-html.vue';
import node from '@/components/mp-html-lite/node/node.vue';

export default {
  name: 'RichText',
  components: { node },
  mixins: [mpHtml],
  props: {
    copyLink: {
      type: [Boolean, String],
      default: true,
    },
    domain: String,
    pauseVideo: {
      type: [Boolean, String],
      default: true,
    },
    previewImg: {
      type: [Boolean, String],
      default: true,
    },
    scrollTable: [Boolean, String],
    setTitle: {
      type: [<PERSON>olean, String],
      default: true,
    },
    tagStyle: Object,
    useAnchor: [<PERSON><PERSON><PERSON>, Number],
  },
  mounted() {
    console.log('RichText mounted, content:', this.content);
    console.log('RichText nodes:', this.nodes);
  },
};
</script>

<style>
/* #ifndef APP-PLUS-NVUE */
/* 根节点样式 */
._root {
  padding: 1px 0;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
}

/* 长按复制 */
._select {
  user-select: text;
}
/* #endif */
</style>
