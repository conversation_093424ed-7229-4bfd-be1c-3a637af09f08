<!--
@name: 地址列表
@description: 描述
@time: 2024/7/30
-->
<template>
  <view class="address-list">
    <view class="address-list-content">
      <view v-if="addressList.length !== 0" class="list">
        <view class="address-card" v-for="item in addressList" :key="item.custAddrId" @click="chooseAddress(item)">
          <view class="info">
            <view class="content">
              <view class="person">
                <text class="name">{{ item.contactName }}</text>
                <text class="phone">{{ item.contactMobile }}</text>
              </view>
              <text class="address">{{ item.addrInte }} {{ item.addr }}</text>
            </view>
            <img
              class="edit-icon"
              :src="`${IMG_PATH}edit.png`"
              :data-item="item"
              @click.stop="editAddress($event, item, 'edit')"
            />
          </view>
          <view class="line"></view>
          <view class="operate">
            <view class="check">
              <img
                @click.stop="editAddress($event, item, 'check')"
                class="check-icon"
                :data-item="item"
                :src="`${IMG_PATH}${item.isDefault === '1' ? 'check' : 'circle'}.png`"
              />
              <text class="check-des">默认地址</text>
            </view>
            <text class="delete" :data-item="item" @click.stop="editAddress($event, item, 'delete')">删除</text>
          </view>
        </view>
      </view>
      <view class="empty" v-else>
        <AEmpty></AEmpty>
        <text class="add-propmt">先新增收货地址吧</text>
      </view>
    </view>
    <!--        底部按钮-->
    <view class="footer">
      <BottomPannel>
        <view class="footer-button">
          <AppButton class="button-item" type="primary" @click="editAddress('', '', 'edit')">添加收货地址</AppButton>
        </view>
      </BottomPannel>
    </view>
  </view>
</template>

<script>
import AEmpty from '@/components/AEmpty/index';
import { getAddressList, editAddress } from '@/services/index.js';
import BottomPannel from '@/components/BottomPannel/index';
import AppButton from '@/components/AppButton/index';

export default {
  name: 'index',
  components: {
    AEmpty,
    BottomPannel,
    AppButton,
  },
  data() {
    return {
      addressList: [],
    };
  },
  onLoad() {
    this.getAddressList();
  },
  onShow() {
    console.log('执行onshow');
    uni.$on('refreshData', this.getAddressList);
    this.getAddressList();
  },
  onHide() {
    uni.$off('refreshData', this.getAddressList);
  },
  methods: {
    // 编辑地址
    editAddress(e, data, type) {
      console.log('输入e', e, data, type);
      let item = {};
      // #ifdef MP
      if (e) {
        item = e.currentTarget.dataset.item;
      }
      // #endif
      // #ifdef H5
      if (data) {
        item = data;
      }
      // #endif
      switch (type) {
        case 'edit':
          const custAddrId = item.custAddrId || '';
          uni.navigateTo({
            url: `/pages/addressManage/addressEdit/index?custAddrId=${custAddrId}`,
          });
          break;
        case 'check':
          if (item.isDefault == '0') {
            this.chooseAddress(item);
          }
        case 'delete':
          this.saveAddress(item, type);
          break;
        default:
          break;
      }
    },
    // 查询地址列表
    async getAddressList() {
      this.addressList = [];
      const [err, res] = await getAddressList({});
      if (res && res.data.queryList) {
        this.addressList = res.data.queryList;
      }
    },
    // 编辑地址
    async saveAddress(item, type) {
      const params = {
        custAddrId: item.custAddrId,
        contactName: item.contacts,
        contactMobile: item.contactMobile,
        addr: item.addr,
        postCode: item.postCode,
        procode: item.procode,
        citycode: item.citycode,
        countycode: item.countycode,
      };
      if (type === 'check') {
        params.isDefault = item.isDefault === '0' ? '1' : '0';
        params.operType = '2';
      } else if (type === 'delete') {
        params.operType = '3';
      }
      const [err, res] = await editAddress(params);
      if (res) {
        uni.showToast({
          title: type === 'delete' ? '删除成功！' : '设置成功！',
          icon: 'none',
        });
        this.getAddressList();
      }
    },
    // 选择地址
    async chooseAddress(item) {
      await this.saveAddress(item, 'check');
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2]; //上一个页面
      console.log(prevPage.route, 'prevPage', pages);
      if (prevPage.route.match('pages/intergral/confirmOrder/index')) {
        // 给上一页面实例绑定getAddressInfo()方法和参数（注意是$vm）
        prevPage.$vm.getAddressInfo(item);
        // 返回上一页面
        uni.navigateBack({
          delta: 1, // 返回的页面数
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.address-list {
  &-content {
    padding: 40rpx 24rpx 0 24rpx;
    .list {
      .address-card {
        background: white;
        padding: 23rpx 30rpx 23rpx 23rpx;
        margin-bottom: 16rpx;
        border-radius: 16rpx;
        .info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .content {
            width: 558rpx;
            margin-right: 50rpx;
            .person {
              .name,
              .phone {
                font-size: 28rpx;
                line-height: 42rpx;
                color: #333333;
              }
              .phone {
                margin-left: 24rpx;
              }
            }
            .address {
              padding-top: 12rpx;
              font-size: 24rpx;
              line-height: 32rpx;
              color: #999999;
            }
          }
          .edit-icon {
            height: 40rpx;
            width: 40rpx;
          }
        }
        .line {
          margin: 24rpx 0;
          height: 1rpx;
          background: #eeeeee;
        }
        .operate {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .check {
            display: flex;
            align-items: center;
            .check-icon {
              width: 32rpx;
              height: 32rpx;
              margin-right: 12rpx;
            }
            .check-des {
              font-size: 24rpx;
              line-height: 32rpx;
              color: #333333;
            }
          }
          .delete {
            font-size: 24rpx;
            line-height: 32rpx;
            color: #333333;
          }
        }
      }
    }
    .empty {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 48rpx;
      .add-propmt {
        font-size: 24rpx;
        line-height: 32rpx;
        color: #999999;
        margin-top: 4rpx;
      }
    }
  }
  .footer {
    z-index: 1;
    .footer-button {
      display: flex;
      gap: 24rpx;
      grid-gap: 24rpx;
      .button-item {
        flex: 1;
      }
    }
  }
}
</style>
