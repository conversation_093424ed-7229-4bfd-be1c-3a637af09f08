// 桥接方法

// 原生端 获取token
function NativeGetToken() {
  return new Promise(async (resolve, reject) => {
    const tokenBridge = await window.bridge.promisifyDsAsync('bangdao.user.getToken')({ isNeedLogin: true });
    if (tokenBridge) {
      uni.setStorageSync('app', tokenBridge.app);
      resolve(tokenBridge);
    } else {
      resolve(null);
    }
  });
}
// 鸿蒙端 获取token
function HarmonyOsGetToken() {
  return new Promise(async (resolve, reject) => {
    const tokenBridge = await window.bridge.promisifyDsAsync('HarmonyOsGetToken')({ isNeedLogin: true });
    console.log('鸿蒙端获取的token', tokenBridge);
    if (tokenBridge) {
      uni.setStorageSync('app', tokenBridge.app);
      resolve(tokenBridge);
    } else {
      resolve(null);
    }
  });
}
// 鸿蒙端 获取当前位置
function HarmonyOsGetLocal() {
  return new Promise(async (resolve, reject) => {
    const local = await window.bridge.promisifyDsAsync('HarmonyOsGetLocal')();
    console.log('鸿蒙端获取的位置信息', local);
    if (local) {
      resolve(local);
    } else {
      resolve(null);
    }
  });
}
// 获取token 和app标识
// app:电动宁德  原生端
// app:鸿蒙电动宁德  鸿蒙端
// i宁德暂定
function GetToken() {
  return new Promise(async (resolve, reject) => {
    let tokenBridge = null;
    tokenBridge = await NativeGetToken();
    if (!tokenBridge) {
      tokenBridge = await HarmonyOsGetToken();
    }
    if (tokenBridge) {
      uni.setStorageSync('token', tokenBridge.data);
      resolve(tokenBridge);
    } else {
      reject();
    }
  });
}

// 去支付
function ToPay(params) {
  return new Promise(async (resolve, reject) => {
    const appName = uni.getStorageSync('app') || '';
    if (appName == '电动宁德') {
      const payBridge = await window.bridge.promisifyDsAsync('bangdao.pay.bd_appPay')(params);
      if (payBridge) {
        resolve(payBridge);
      } else {
        resolve(null);
      }
    } else if (appName === '鸿蒙电动宁德') {
      const payBridge = await window.bridge.promisifyDsAsync('HarmonyOsToPay')(params);
      if (payBridge) {
        resolve(payBridge);
      } else {
        resolve(null);
      }
    }
  });
}
// 页面跳转 -目前只用于原生端
function toCustomer() {
  window.bridge.promisifyDsAsync('bangdao.user.toCustomer')();
}

// 开启导航 -- 目前用于鸿蒙
function openNavigation(params) {
  window.bridge.promisifyDsAsync('HarmonyOsOpenNavigation')(params);
}
// 开启导航 -- 目前用于鸿蒙
function HarmonyOsShareWxCard(params) {
  window.bridge.promisifyDsAsync('HarmonyOsShareWxCard')(params);
}
// 拨打电话 -- 目前用于鸿蒙
function HarmonyOsMakePhoneCall(params) {
  window.bridge.promisifyDsAsync('HarmonyOsMakePhoneCall')(params);
}

// 调用原生自动续费签约
function callNativeAutoRenew(signData) {
  return new Promise(async (resolve, reject) => {
    try {
      // 参考 NativeGetToken 的实现方式
      const autoRenewBridge = await window.bridge.promisifyDsAsync('bangdao.autorenew.sign')(signData);
      if (autoRenewBridge) {
        resolve(autoRenewBridge);
      } else {
        resolve(null);
      }
    } catch (error) {
      console.error('调用原生自动续费桥接失败:', error);
      reject(error);
    }
  });
}

export {
  GetToken,
  ToPay,
  toCustomer,
  openNavigation,
  HarmonyOsShareWxCard,
  HarmonyOsMakePhoneCall,
  HarmonyOsGetLocal,
  callNativeAutoRenew,
};
