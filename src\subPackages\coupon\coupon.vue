<template>
  <!-- pages/yhj.wxml -->
  <view class="jusa">
    <view @tap="jump" class="goCupe">去领券</view>
    <!-- <view class='list'>
    <view class='listL'>
    <text class='net1'>充电30元送10元优惠券</text>
    <text class='net2'>有效期：2017-05-12～2017-05-31</text>
    <text class='net3'>按金额，消费满10元可用</text>
    </view>
    <view class='listR'>
    <image src='../../image/bg_discount_red_2.png'></image>
     <view>10 < text>元</text></view>
    </view>
  </view> -->

    <view @tap="forEach" :data-msg="item" class="list" v-for="(item, index) in queryList" :key="index">
      <view class="listL">
        <text class="net1">{{ item.cpnName }}</text>
        <text class="net2">有效期：{{ item.effectTime }}</text>
        <text class="net3">{{ item.cpnMarks }}</text>
      </view>

      <view class="listR">
        <image :src="`${IMG_PATH}bg_discount_red_2.png`"></image>
        <view>{{ item.cpnAmt }}</view>
      </view>
    </view>
    <view @tap="nots" class="goCupe pullNos">不使用优惠券</view>
  </view>
</template>

<script>
import { getAccountsCoupons } from '@/services/index.js';
export default {
  data() {
    return {
      city: '',
      stationId: '',
      changeType: 0,
      queryList: [],
    };
  },
  async onShow() {
    this.setData({
      changeType: 0,
    });
    var city = uni.getStorageSync('cityPont');
    var stationId = uni.getStorageSync('stationIdPint');
    var that = this;
    that.setData({
      city: city,
      stationId: stationId,
    });
    var parmes = {
      // getChannel: '02',
      useStatus: '01',
      pageNum: '1',
      totalNum: '100',
      stationId: stationId,
      city: city,
      amount: '99999',
    };
    const [, res] = await getAccountsCoupons(parmes);
    if (res && res.ret == '200') {
      that.setData({
        queryList: res.queryList,
      });
    }
  },
  methods: {
    nots() {
      uni.setStorageSync('youhuiID', '');
      uni.setStorageSync('cpnAmt', '不使用优惠券');
      uni.setStorageSync('cpnName', '不使用优惠券');
      uni.navigateBack();
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    forEach(e) {
      console.log(e);
      var youhuiID = e.currentTarget.dataset.msg.cpnId;
      var cpnAmt = e.currentTarget.dataset.msg.cpnAmt;
      var cpnName = e.currentTarget.dataset.msg.cpnName;
      uni.setStorageSync('youhuiID', youhuiID);
      uni.setStorageSync('cpnName', cpnName);
      uni.setStorageSync('cpnAmt', cpnAmt);
      uni.navigateBack();
    },

    change(e) {
      console.log(e.currentTarget.dataset.type);
      this.setData({
        changeType: e.currentTarget.dataset.type,
      });
    },

    jump() {
      uni.navigateTo({
        url: '/subPackages/gocup/gocup?city=' + this.city + '&stationId=' + this.stationId,
      });
    },
  },
};
</script>
<style>
@import './coupon.css';
</style>
