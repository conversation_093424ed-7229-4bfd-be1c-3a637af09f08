<template>
  <div class="receive-card" v-show="showCard">
    您还有优惠券待领取
    <div class="btn" @click.stop="jumpCup">去领取</div>
    <div class="close" @click.stop="() => (showCard = false)">X</div>
  </div>
</template>

<script>
import { getMsgUnused } from '@/services/index';
export default {
  name: 'receive',
  props: {},
  components: {},
  data() {
    return {
      showCard: false,
    };
  },
  computed: {},
  watch: {},
  created() {},
  onShow() {
    this.getCoupons();
  },
  methods: {
    jumpCup: function () {
      uni.navigateTo({
        url: '/subPackages/gocup/gocup',
      });
    },
    // 获取是否有可以领取的优惠券
    async getCoupons() {
      var that = this;
      var parmeType = {
        prodBusiType: '02',
      };
      const [, res] = await getMsgUnused(parmeType);
      if (res) {
        if (res.cpnList.length > 0) this.showCard = true;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.receive-card {
  width: 100%;
  background-color: #fdecd8;
  color: #f79f4d;
  display: flex;
  align-items: center;
  height: 25px;
  line-height: 25rpx;
  padding: 0 30rpx;
  position: fixed;
  bottom: 170rpx;
  left: 0;
  right: 0;
  .btn {
    margin-left: auto;
    color: #fff;
    background: #c8723c;
    font-size: 24rpx;
    padding: 5rpx 10rpx;
    box-sizing: border-box;
    border-radius: 5rpx;
  }
  .close {
    margin-left: 20rpx;
  }
}
</style>
