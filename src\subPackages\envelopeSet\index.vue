<template>
  <view>
    <image class="noticeSet-img" mode="widthFix" :src="`${IMG_PATH}envelopeSet1.png`" />
    <image class="noticeSet-img" mode="widthFix" :src="`${IMG_PATH}envelopeSet2.png`" />
    <image class="noticeSet-img" mode="widthFix" :src="`${IMG_PATH}envelopeSet3.png`" />
    <image class="mine-bg" :src="`${IMG_PATH_UI}bg.png`"></image>
  </view>
</template>

<script>
export default {
  components: {},
  data() {
    return {};
  },
  onLoad: function (options) {},
  onReady: function (e) {},
  onShow: function () {},
  methods: {},
};
</script>
<style>
@import './index.css';
</style>
