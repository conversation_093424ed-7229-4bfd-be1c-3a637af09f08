<template>
  <view class="scan-container" v-if="value">
    <!-- 顶部导航栏 -->
    <view class="scan-header">
      <view class="close-btn" @click="handleClose">
        <text class="close-icon">×</text>
      </view>
      <view class="scan-title">扫一扫</view>
    </view>

    <!-- 扫码区域 -->
    <view class="scan-content">
      <view id="qr-reader"></view>

      <!-- 扫描框 -->
      <view class="scan-frame">
        <view class="corner top-left"></view>
        <view class="corner top-right"></view>
        <view class="corner bottom-left"></view>
        <view class="corner bottom-right"></view>
        <view class="scan-line"></view>
      </view>

      <!-- 提示文字 -->
      <view class="scan-tips">
        <text>将二维码放入框内，即可自动扫描</text>
      </view>
    </view>
  </view>
</template>

<script>
// html5-qrcode的扫码，只支持https，
// <scanCode v-model="showScan" @success="getScan" @err="err"></scanCode>
// showScan显示 @success成功回调 @err失败回调
import { Html5Qrcode } from 'html5-qrcode';
export default {
  name: 'Scan',
  model: {
    props: 'value',
    event: 'close',
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    value(val) {
      if (val) {
        this.$nextTick(() => {
          this.getCameras();
        });
      }
    },
  },
  data() {
    return {
      cameraId: '',
      html5QrCode: '',
    };
  },
  beforeDestroy() {
    this.stop();
  },
  methods: {
    getCameras() {
      uni.showLoading({
        title: '相机启动中...',
        icon: 'none',
      });
      Html5Qrcode.getCameras()
        .then((devices) => {
          /**
           * devices 是对象数组
           * 例如：[ { id: "id", label: "label" }]
           */
          if (devices && devices.length) {
            if (devices.length > 1) {
              this.cameraId = devices[1].id;
            } else {
              this.cameraId = devices[0].id;
            }
            console.log(this.cameraId, 'cameraId');
            this.start();
          }
        })
        .catch((err) => {
          this.close();
          console.log(err);
          uni.showToast({
            title: '启用相机失败' + err,
            icon: 'none',
          });
        });
    },
    start() {
      this.html5QrCode = new Html5Qrcode('qr-reader');
      setTimeout(() => {
        uni.hideLoading();
      }, 1500);

      // 获取屏幕宽度
      const screenWidth = uni.getSystemInfoSync().windowWidth;
      // 计算扫描框大小（与CSS中的.scan-frame保持一致）
      const scanBoxSize = Math.min(screenWidth * 0.7, 500); // 500rpx约等于屏幕宽度的70%

      this.html5QrCode
        .start(
          this.cameraId, // 传入cameraId参数，这个参数在之前的步骤中已经获取到
          {
            fps: 10, // 设置摄像头的帧率为10帧每秒
            qrbox: {
              width: scanBoxSize,
              height: scanBoxSize,
            }, // 设置需要扫描的QR码区域
            aspectRatio: 1.777778, // 设置扫描结果的宽高比
          },
          (qrCodeMessage) => {
            // 当成功读取到QR码时，执行此回调函数
            if (qrCodeMessage) {
              // 播放扫码成功的提示音
              const innerAudioContext = uni.createInnerAudioContext();
              innerAudioContext.autoplay = true;
              innerAudioContext.src = '/static/audio/scan-success.mp3';

              // 震动反馈
              uni.vibrateShort({
                success: function () {
                  console.log('震动成功');
                },
              });

              // 发送扫码结果
              this.qrCodeMessage = qrCodeMessage;
              this.$emit('success', qrCodeMessage);
              this.close();
              this.stop();
            }
          },
          (errorMessage) => {
            // 错误处理
            console.log('扫码错误:', errorMessage);
          }
        )
        .catch((err) => {
          // 如果扫描启动失败，执行此catch块中的代码
          console.error('扫描启动失败:', err);
          uni.showToast({
            title: `扫码功能启动失败，请检查相机权限`,
            icon: 'none',
            duration: 3000,
          });

          // 自动关闭
          setTimeout(() => {
            this.handleClose();
          }, 3000);
        });
    },
    stop() {
      this.html5QrCode &&
        this.html5QrCode.stop().finally(() => {
          this.html5QrCode.clear();
          this.html5QrCode = null;
        });
    },
    close() {
      this.$emit('close', false);
    },

    // 处理关闭按钮点击
    handleClose() {
      this.stop();
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
.scan-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999999999; /* 极高的z-index值 */
  height: 100vh;
  width: 100vw;
  background-color: #000;
  display: flex;
  flex-direction: column;
  /* 确保扫码组件在最顶层 */
  pointer-events: auto !important;
  /* 防止其他元素的transform影响层级 */
  transform: translateZ(0);
  /* 确保iOS设备上也能正确显示 */
  -webkit-transform: translateZ(0);
}

// 顶部导航栏
.scan-header {
  position: relative;
  height: 88rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
  color: #fff;
  padding-top: var(--status-bar-height, 20px);
  /* 确保导航栏在最顶层 */
  z-index: 999999999;

  .scan-title {
    font-size: 36rpx;
    font-weight: 500;
    position: relative;
    z-index: 999999999;
  }

  .close-btn {
    position: absolute;
    left: 30rpx;
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    /* 确保关闭按钮在最顶层 */
    z-index: 999999999;

    .close-icon {
      font-size: 48rpx;
      color: #fff;
    }
  }
}

// 扫码内容区域
.scan-content {
  flex: 1;
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  /* 确保内容区域也有高z-index */
  z-index: 999999999;

  #qr-reader {
    width: 100%;
    height: 100%;
    /* 确保扫码区域在最顶层 */
    position: relative;
    z-index: 999999999;

    // 隐藏html5-qrcode库自带的UI
    :deep(#qr-reader__dashboard) {
      display: none !important;
    }

    :deep(video) {
      width: 100% !important;
      height: 100% !important;
      object-fit: cover !important;
      /* 确保视频元素在最顶层 */
      position: relative;
      z-index: 999999999;
    }
  }
}

// 扫描框
.scan-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 500rpx;
  height: 500rpx;
  /* 确保扫描框在最顶层 */
  z-index: 999999999;

  // 四个角
  .corner {
    position: absolute;
    width: 60rpx;
    height: 60rpx;
    border-color: #4598fe;
    border-style: solid;
    border-width: 0;
    /* 确保角在最顶层 */
    z-index: 999999999;
  }

  .top-left {
    top: 0;
    left: 0;
    border-top-width: 6rpx;
    border-left-width: 6rpx;
  }

  .top-right {
    top: 0;
    right: 0;
    border-top-width: 6rpx;
    border-right-width: 6rpx;
  }

  .bottom-left {
    bottom: 0;
    left: 0;
    border-bottom-width: 6rpx;
    border-left-width: 6rpx;
  }

  .bottom-right {
    bottom: 0;
    right: 0;
    border-bottom-width: 6rpx;
    border-right-width: 6rpx;
  }

  // 扫描线
  .scan-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 6rpx;
    background-color: #4598fe;
    animation: scanAnimation 2s linear infinite;
    /* 确保扫描线在最顶层 */
    z-index: 999999999;
  }
}

// 提示文字
.scan-tips {
  position: absolute;
  bottom: 160rpx;
  left: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-size: 28rpx;
  padding: 0 30rpx;
  /* 确保提示文字在最顶层 */
  z-index: 999999999;
}

// 扫描线动画
@keyframes scanAnimation {
  0% {
    top: 0;
  }
  50% {
    top: calc(100% - 6rpx);
  }
  100% {
    top: 0;
  }
}
</style>
