page {
  background: #0a1214;
}

.charge_tap {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  height: 70rpx;
  background: rgba(255, 20, 20, 0.7);
  font-size: 26rpx;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 1;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.charge_tap button {
  width: 159rpx;
  height: 51rpx;
  background: #fff;
  color: #fc7272;
  line-height: 51rpx;
  padding: 0;
  margin: 0;
  font-size: 26rpx;
}

.charge-animation {
  width: 750rpx;
  height: 578rpx;
  box-sizing: border-box;
  position: relative;
  display: flex;
  background: #f4f6f5;
  justify-content: center;
  align-items: center;
}

.charge-animation image {
  width: 520rpx;
  height: 520rpx;
}

.charge-animation .charge-text {
  position: absolute;
  display: flex;
  flex-wrap: wrap;
  width: 200rpx;
  left: calc(50% - 100rpx);
  top: calc(50% - 120rpx);
}

.charge-animation .charge-text text {
  width: 100%;
  text-align: center;
  box-sizing: border-box;
}

.charge-animation .charge-text .charge-symbol {
  text-align: right;
  font-size: 45rpx;
  color: #00abeb;
  font-weight: bold;
  /* padding-right: 50rpx */
}

.charge-animation .charge-text .charge-number {
  text-align: center;
  font-size: 95rpx;
  color: #00abeb;
  font-weight: bold;
}

.charge-animation .charge-text .charge-text-status {
  text-align: center;
  font-size: 30rpx;
  color: #666;
}

.charge_midd {
  width: 100%;
}

.licenseNo-btn {
  display: flex;
  align-items: center;
}

.charge_midd .charge_midd_btn {
  width: 435rpx;
  height: 51rpx;
  background: #e7e7e7;
  color: #2096f3;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 26rpx;
  margin: 0 auto;
}

.charge_midd .charge_midd_btn icon {
  height: 100%;
  margin-left: 23rpx;
}

.charge_midd .charge_midd_msg {
  width: 100%;
  text-align: center;
  line-height: 50rpx;
  color: #333333;
  font-size: 28rpx;
}

.whrite-disc {
  width: 320rpx;
  height: 320rpx;
  background: #fff;
  border-radius: 50%;
  border: 1rpx solid #fff;
  position: absolute;
  left: calc(50% - 160rpx);
  top: calc(50% - 160rpx);
  box-shadow: -2rpx 2rpx 6rpx 6rpx #42a6bf;
}

.charge-msg {
  position: absolute;
  /* top: calc(50% - 40rpx); */
  top: calc(50% - 30rpx);
  font-size: 52rpx;
  color: #00abeb;
}

.charge-msgdd {
  top: calc(50% - 130rpx);
}

.charge-msg .infsa {
  font-size: 30rpx;
  text-align: center;
  line-height: 100rpx;
}

.charge-start {
  position: absolute;
  left: calc(50% - 260rpx);
  top: calc(50% - 260rpx);
}

.charge-Dynamic {
  width: 100%;
  box-sizing: border-box;
  padding: 30rpx 30rpx 30rpx 30rpx;
  background: #fff;
  position: absolute;
  bottom: 120rpx;
}

.charge-flex {
  display: flex;
  justify-content: space-between;
  /* padding-top: 60rpx; */
}

.charge-flex .charge-flex-list {
  width: 33%;
  text-align: center;
}

.charge-flex .charge-flex-list image {
  width: 30rpx;
  height: 30rpx;
  display: inline-block;
}

.charge-flex-list .charge-flex-size text {
  font-size: 32rpx;
}

.charge-flex-list .charge-flex-size {
  font-size: 32rpx;
  color: #00abeb;
}

.charge-flex-list .charge-flex-status {
  font-size: 20rpx;
  color: #d6dce0;
  line-height: 35rpx;
}

.step {
  width: 100%;
  padding: 20rpx 74rpx;
  box-sizing: border-box;
  position: absolute;
  bottom: 0;
  background: #fff;
}

.step button {
  width: 100%;
  height: 89rpx;
  background: #1eb5ee;
  color: #fff;
  font-size: 38rpx;
  line-height: 89rpx;
}

.charge-animation .cent-disc {
  position: absolute;
  -webkit-transition: -webkit-transform 1s ease-out;
  -moz-transition: -moz-transform 1s ease-out;
  -o-transition: -o-transform 1s ease-out;
  -ms-transition: -ms-transform 1s ease-out;
  -webkit-transition-property: -webkit-transform;
  -webkit-transition-duration: 3s;
  -moz-transition-property: -moz-transform;
  -moz-transition-duration: 2s;
  -webkit-animation: rotate 2s linear infinite;
  -moz-animation: rotate 2s linear infinite;
  -o-animation: rotate 2s linear infinite;
  animation: rotate 2s linear infinite;
  left: calc(50% - 260rpx);
  top: calc(50% - 260rpx);
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@-webkit-keyframes rotate {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
  }
}

@-webkit-keyframes rotate {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
  }
}

.aletMsg {
  width: 100%;
  height: 100%;
  background: #000;
  z-index: 99;
  position: absolute;
  opacity: 0.5;
  left: 0;
  top: 0;
}

.count {
  width: 400rpx;
  height: 300rpx;
  background: #fff;
  position: absolute;
  left: calc(50% - 200rpx);
  border-radius: 8rpx;
  top: 30%;
  z-index: 100;
  box-sizing: border-box;
  padding-top: 20rpx;
}

.count .count_head {
  width: 100%;
  font-size: 32rpx;
  color: #000;
  text-align: center;
  line-height: 40rpx;
}

.count .count_msg {
  width: 100%;
  font-size: 26rpx;
  color: #000;
  text-align: center;
  line-height: 40rpx;
}

.count .count_net {
  width: 100%;
  font-size: 26rpx;
  color: #000;
  text-align: center;
  line-height: 40rpx;
}

.count .pop {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #999;
  background: #fff;
  margin-top: 10rpx;
}

.count .pop view {
  width: 110rpx;
  height: 110rpx;
  background: #1eb5ee;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mask {
  position: absolute;
  width: 100%;
  height: 100%;
  background: #000;
  opacity: 0.5;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.mask-alers {
  position: absolute;
  width: 100%;
  height: 622rpx;
  background: #fff;
  z-index: 1000;
  bottom: 0;
  left: 0;
  right: 0;
}

.mask-alers .mask-alers-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  box-sizing: border-box;
  padding: 0 30rpx;
  height: 88rpx;
  background: #2196f3;
  color: #fff;
}

.mask-alers .mask-list {
  width: 100%;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mask-alers .mask-list {
  width: 100%;
  height: 88rpx;
}

.mask-alers .mask-list .mask-list-li .mask-list-lf {
  width: 420rpx;
}

.mask-alers .mask-list .mask-list-li {
  width: 690rpx;
  height: 100%;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #383838;
  border-bottom: 1rpx solid #979797;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mask-alers .mask-list .mask-list-li .x {
  width: 60rpx;
  font-size: 50rpx;
  text-align: right;
  color: red;
}

.mask-alers .mask-list .mask-list-li .mask-list-rt .changes {
  font-size: 50rpx !important;
}

.mask-alers .mask-list .mask-list-li .mask-list-rt .icon-checked {
  color: #2196f3 !important;
}

.charge-bg {
  position: absolute;
  left: 0rpx;
  top: -170rpx;
  width: 100vw;
  height: 1325rpx;
  z-index: 0;
}

.charge-content {
  position: relative;
  z-index: 4;
  margin-top: 40rpx;
  padding-bottom: 120rpx;
}

.charge-type {
  text-align: center;
  font-family: PingFang SC;
  font-size: 40rpx;
  font-weight: 500;
  line-height: 70rpx;
  height: 70rpx;
  text-align: center;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.type-blue {
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 45rpx;
  font-size: 32rpx;
  color: #2da1da;
  margin-right: 20rpx;
}

.type-number {
  font-size: 60rpx;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 84rpx;
}

.charge-electri-bg {
  width: 248rpx;
  height: 22rpx;
  position: absolute;
  left: 270rpx;
  top: 370rpx;
  z-index: 2;
}

.charge-battery-bg {
  width: 250rpx;
  height: 30rpx;
  position: absolute;
  left: 270rpx;
  top: 370rpx;
  z-index: 1;
}

.charge-licenseNo {
  margin-top: 360rpx;
  display: flex;
  justify-content: center;
  font-family: PingFang SC;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 36rpx;
  color: #ffffff;
  align-items: center;
  position: relative;
}

.charge-station {
  margin-top: 80rpx;
  font-family: PingFang SC;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 28rpx;
  color: #ffffff;
  text-align: center;
  letter-spacing: 1rpx;
  padding: 0 30rpx;
}

.charge-licenseNo image {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}

.charge-orderNo {
  margin-top: 24rpx;
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: normal;
  line-height: 24rpx;
  color: #5a6b90;
  text-align: center;
}

.charge-ul {
  display: flex;
  flex-wrap: wrap;
  padding: 0 20rpx;
}

.charge-li {
  width: 33%;
  margin-top: 56rpx;
  position: relative;
}

.li-recharge-bg {
  position: absolute;
  width: 150rpx;
  height: 30rpx;
  left: calc(50% - 75rpx);
  top: -30rpx;
}

.li-value {
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: normal;
  line-height: 24rpx;
  text-align: center;
  color: #5a6b90;
  text-align: center;
  font-weight: 400;
}

.li-value text {
  font-family: PingFang SC;
  font-size: 36rpx;
  font-weight: 500;
  line-height: 32rpx;
  text-align: center;
  letter-spacing: 1rpx;
  color: #ffffff;
  margin-right: 4rpx;
}

.li-key {
  margin-top: 16rpx;
  font-family: PingFang SC;
  font-size: 24rpx;
  line-height: 32rpx;
  text-align: center;
  color: #5a6b90;
  font-weight: 400;
}

.charge-service {
  position: fixed;
  bottom: calc(200rpx + env(safe-area-inset-bottom));
  width: 140rpx;
  height: 140rpx;
  right: -5rpx;
  z-index: 1;
}

.bottom-wrap {
  position: fixed;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  width: 100%;
  /* background-color: linear-gradient(180deg, #040404 0%, #0a1214 52%, #0a1214 100%); */
}

.charge-button {
  bottom: calc(34rpx + env(safe-area-inset-bottom));
  width: 620rpx;
  height: 92rpx;
  padding: 0 20rpx;
  background: linear-gradient(180deg, #002a4a 0%, #034c6a 52%, #002a4a 100%);
  box-sizing: border-box;
  border: 2rpx solid #0084aa;
  box-shadow: 0rpx 0rpx 1rpx 0rpx #01346f, inset 0rpx 1rpx 1rpx 1rpx #021646;
  border-radius: 16rpx;
  text-align: center;
  font-family: PingFang SC;
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
  line-height: 92rpx;
}

.charge-button text {
  font-size: 24rpx;
  color: #2da1da;
}