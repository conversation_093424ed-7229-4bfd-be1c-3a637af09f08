<template>
  <div class="">
    <view class="list-picker">
      <picker mode="date" :value="startTime" @change="bindstartTimeChange" class="picker-li">
        <view class="picker-li">
          <image class="li-img" :src="`${IMG_PATH}calendar.png`"></image>
          <view class="picker">
            {{ startTime ? startTime : '选择开始日期' }}
          </view>
        </view>
      </picker>
      <text class="picker-mid">至</text>
      <picker mode="date" :value="endTime" @change="bindendTimeChange" class="picker-li">
        <view class="picker-li">
          <image class="li-img" :src="`${IMG_PATH}calendar.png`"></image>
          <view class="picker">
            {{ endTime ? endTime : '选择结束日期' }}
          </view>
        </view>
      </picker>
    </view>
    <div class="card">
      <div class="card-item first">
        <div class="title">充电次数 ( 次 )</div>
        <div class="num">{{ info.orderNum }}</div>
      </div>
      <div class="card-item">
        <div class="title">合计消费 ( 元 )</div>
        <div class="num">{{ info.elecAmts }}</div>
      </div>
    </div>
    <div class="list-item" :class="{ 'list-title': item.type == 'title' }" v-for="(item, index) in list" :key="index">
      <div class="text">{{ item.title }}</div>
      <div class="value">{{ item.value || 0 }} {{ item.unit || '' }}</div>
    </div>
  </div>
</template>

<script>
import moment from 'moment';
import { orderCount } from '@/services/index.js';
export default {
  props: {},
  components: {},
  data() {
    return {
      startTime: '',
      endTime: '',
      info: {
        orderNum: '-',
        elecAmts: '-',
        chargeAmts: '-',
        serviceAmts: '-',
        discountBals: '-',
        chargePqs: '-',
        chargeTimes: '-',
      },
    };
  },
  computed: {
    list() {
      return [
        {
          type: 'title',
          title: '支出明细',
        },
        {
          title: '充电费',
          value: this.info.chargeAmts,
          unit: '¥',
        },
        {
          title: '服务费',
          value: this.info.serviceAmts,
          unit: '¥',
        },
        {
          title: '我的优惠',
          type: 'title',
        },
        {
          title: '优惠金额',
          value: this.info.discountBals,
          unit: '¥',
        },
        {
          title: '我的充电',
          type: 'title',
        },
        {
          title: '充电量',
          value: this.info.chargePqs,
          unit: 'kWh',
        },
        {
          title: '充电时长',
          value: this.info.chargeTimes,
          unit: 'h',
        },
      ];
    },
  },
  watch: {},
  created() {},
  mounted() {
    this.startTime = moment().startOf('month').format('YYYY-MM-DD');
    this.endTime = moment().format('yyyy-MM-DD');
    this.refesh();
  },
  methods: {
    bindstartTimeChange(event) {
      const { value } = event.detail;
      this.setData({
        startTime: value,
      });
      this.refesh();
    },
    bindendTimeChange(event) {
      const { value } = event.detail;
      this.setData({
        endTime: value,
      });
      this.refesh();
    },
    async refesh() {
      const that = this;
      this.$store.getters['login/getUserInfo'];
      const [, res] = await orderCount({
        settleTime1: that.startTime,
        settleTime2: that.endTime,
      });
      if (res) {
        that.info = res.totalChargeOrder;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.mt20 {
  margin-top: 20rpx;
}
.list-picker {
  position: fixed;
  width: 100vw;
  top: 0rpx;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  z-index: 10;
}
.list-picker .btn-yellow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background: #f1e243ce;
  border-radius: 50%;
  padding: 0 5rpx;
}
.picker-li {
  opacity: 0.7;
  background: #f5f5f5;
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}
.li-img {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}
.picker-mid {
  opacity: 0.7;
  padding: 0 40rpx;
  line-height: 70rpx;
}
.card {
  margin: 125rpx auto 15rpx auto;
  width: 90%;
  display: flex;
  padding: 60rpx;
  background: #249aef;
  border-radius: 8rpx;
  box-sizing: border-box;
  .first {
    position: relative;
    &::before {
      position: absolute;
      content: '';
      width: 5rpx;
      height: 70%;
      top: 15%;
      right: 0rpx;
      background: #fff;
    }
  }
  .card-item {
    flex: 1;
    color: #fff;
    padding: 0 25rpx;
    .title {
      font-size: 24rpx;
      margin-bottom: 15rpx;
    }
    .num {
      font-weight: 600;
      font-size: 36rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.list-item {
  display: flex;
  width: 100%;
  padding: 25rpx 15rpx;
  font-size: 24rpx;
  background: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  color: #999999;
  .value {
    margin-left: auto;
    max-width: 70%;
    overflow: hidden;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
.list-title {
  background: transparent;
  color: #000000;
}
</style>
