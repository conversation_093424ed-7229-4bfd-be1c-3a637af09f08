<!--
@name: 购买记录
@description: 组件
@time: 2024/8/12
-->
<template>
  <view class="record-list">
    <!--        标题-->
    <view class="record-list-title">
      <text class="title-item">购买记录</text>
      <view class="title-item" @click="jumpToRecord" v-if="recordList.length !== 0">
        <text>查看更多</text>
        <img class="more-icon" :src="`${IMG_PATH}member/more-arrow.png`" />
      </view>
    </view>
    <!--        列表-->
    <view class="record-list-content" v-if="recordList.length !== 0">
      <view class="list-item" v-for="item in recordList" :key="item.recordId">
        <view class="list-item-left">
          <text class="active-content">{{ item.vipTypeName }}</text>
          <text class="active-time">{{ item.payTime }}</text>
        </view>
        <view class="list-item-right">
          <view class="circle"> </view>
          <text class="price"> ￥{{ item.amount }} </text>
        </view>
      </view>
    </view>
    <AEmpty v-else />
  </view>
</template>

<script>
import AEmpty from '@/components/AEmpty/index';
import { getRecord } from '@/services/index.js';

export default {
  name: 'Record',
  components: {
    AEmpty,
  },
  data() {
    return {
      recordList: [],
    };
  },
  mounted() {
    this.getRecord();
  },
  methods: {
    // 跳转到记录列表页面
    jumpToRecord() {
      uni.navigateTo({
        url: '/pages/member/orderRecord/index',
      });
    },
    // 查询购买记录
    async getRecord() {
      const params = {
        pageNum: 1,
        totalNum: 3,
      };
      const [err, res] = await getRecord(params);
      if (res) {
        this.recordList = res.vipPayRecords;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.record-list {
  padding: 24rpx;
  border-radius: 32rpx 32rpx 0 0;
  &-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title-item:nth-child(1) {
      font-size: 32rpx;
      font-weight: bold;
      line-height: 38rpx;
      color: #333333;
    }
    .title-item:nth-child(2) {
      font-size: 28rpx;
      line-height: 42rpx;
      color: #333333;
      display: flex;
      align-items: center;
      .more-icon {
        margin-left: 4rpx;
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
  &-content {
    margin-top: 24rpx;
    .list-item {
      padding: 24rpx 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1rpx solid #eeeeee;
      &-left {
        display: flex;
        flex-direction: column;
        .active-content {
          font-size: 28rpx;
          font-weight: bold;
          color: #333333;
          line-height: 42rpx;
        }
        .active-time {
          margin-top: 8rpx;
          font-size: 24rpx;
          line-height: 32rpx;
          color: #999999;
        }
      }
      &-right {
        display: flex;
        align-items: center;
        .circle {
          width: 10rpx;
          height: 10rpx;
          border-radius: 10rpx;
          background: #ff8301;
          margin-right: 6rpx;
        }
        .price {
          font-size: 36rpx;
          font-weight: bold;
          line-height: 42rpx;
          letter-spacing: 0.58rpx;
          color: #333333;
        }
      }
    }
  }
}
</style>
