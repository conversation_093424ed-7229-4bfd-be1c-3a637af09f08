<template>
  <div class="rate-wrap">
    <div class="rate-item" v-for="(item, index) in list" :key="index" @click="change(item, index)">
      <img :src="item" alt="" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Number | String,
      default: 0,
    },
  },
  components: {},
  data() {
    return {
      startNum: 5,
    };
  },
  computed: {
    activeImg() {
      return this.IMG_PATH + 'starSelected.png';
      // return 'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250103153958699107055033703035_w47_h45.png';
    },
    activeBanImg() {
      return this.IMG_PATH + '<EMAIL>';
    },
    normalImg() {
      return this.IMG_PATH + 'starNoSelected.png';
      // return 'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250103154003754107055033704809_w47_h45.png';
    },
    list() {
      let arr = [];
      for (let i = 1; i <= parseInt(+this.value); i++) {
        arr.push(this.activeImg);
      }
      if (+this.value % 1 > 0) {
        arr.push(this.activeBanImg);
      }
      if (arr.length > 5) {
        arr.slice(0, 5);
      } else {
        arr = [...arr, ...Array(5 - arr.length).fill(this.normalImg)];
      }
      return arr;
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 点击评分
    change(item, index) {
      console.log(item, index);
      this.$emit('rateOnChange', index);
    },
  },
};
</script>

<style scoped lang="scss">
.rate-wrap {
  display: flex;
  flex-direction: row;
  height: 50rpx;
  align-items: center;
  .rate-item {
    margin-left: 20rpx;
    img {
      width: 50rpx;
      height: 50rpx;
    }
  }
}
</style>
