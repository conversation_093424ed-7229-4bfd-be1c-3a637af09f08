import { Ajax } from '../utils/Ajax/index'
import { PUB_URL } from '../config/global'
/** 3.1.8	亨通小程序  评价
 *
 */
export const mapCeshiApi = async (params) => {
  return Ajax({
    url: '/base/api/v0.4/login',
    method: 'POST',
    timeout: 20000,
    data: params,
  })
}

/** 3.1.8	亨通小程序  评价
 *
 */
export const myOpencommentApi = async (params) => {
  return Ajax({
    url: '/ord/api/v0.2/order-eva',
    method: 'POST',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序  充值
 *
 */
export const myWallerRechargeApi = async (params) => {
  return Ajax({
    url: '/base/api/v0.1/account/balance',
    method: 'POST',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序  站点详情 评论列表
 *
 */
export const mapStationEvaluateApi = async (params) => {
  return Ajax({
    url: '/ord/api/v0.1/eva-dtl',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序  开票历史
 *
 */
export const myInvoiceHistorysApi = async (params) => {
  return Ajax({
    url: '/base/api/v0.1/accounts/invoice-log',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序  开票
 *
 */
export const myInvoiceSubmitApis = async (params) => {
  return Ajax({
    url: '/base/api/v0.1/account/invoice',
    method: 'POST',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序  开票列表
 *
 */
export const myInvoiceListAps = async (params) => {
  return Ajax({
    url: '/base/api/v0.1/invoices',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序  提现历史
 *
 */
export const myRecordtxDeatilApi = async (params) => {
  return Ajax({
    url: '/base/api/v0.1/accounts/cashlog',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序  提现
 *
 */
export const myWithdrawalDeatilApi = async (params) => {
  return Ajax({
    url: '/base/api/v0.1/account/form',
    method: 'POST',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序  交易记录
 *
 */
export const transactionRecordsApi = async (params) => {
  return Ajax({
    url: '/base/api/v0.1/accounts/tradelog',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序  系统参数
 *
 */
export const mapParasListApi = async (params) => {
  return Ajax({
    url: '/base/api/v0.1/paras',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序  可领优惠劵
 *
 */
export const myReceiveConcupApi = async (params) => {
  return Ajax({
    url: '/busi/api/v0.1/coupons',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序   领优惠劵
 *
 */
export const myPostConcupApi = async (params) => {
  return Ajax({
    url:
      '/bil/api/v0.1/coupons?cpnId=' +
      params.cpnId +
      '&getChannel=' +
      params.getChannel,
    method: 'POST',
    timeout: 20000,
    data: {},
  })
}
/** 3.1.8	亨通小程序  取消订单-充电
 *
 */
export const myOrderClosesApi = async (params) => {
  return Ajax({
    url: '/busi/api/v0.1/order/' + params.orderNo,
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序  启动充电
 *
 */
export const myStartUpChargeApi = async (params) => {
  return Ajax({
    url: '/ord/api/v0.1/order-charge-controls',
    method: 'POST',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序  轮训
 *
 */
export const myChargeOrderDetailsApi = async (params) => {
  return Ajax({
    url: '/ord/api/v0.1/order-charge-setbacks',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序  下单
 *
 */
export const openOrderAgainApi = async (params) => {
  return Ajax({
    url: '/ord/api/v0.3/order',
    method: 'POST',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序 余额
 *
 */
export const accountsrQrcodeApi = async (params) => {
  return Ajax({
    url: '/base/api/v0.1/accounts',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}

/** 3.1.8	亨通小程序 下单  优惠券statinId
 *
 */
export const couponQrcodeApi = async (params) => {
  return Ajax({
    url: '/base/api/v0.1/accounts/coupons',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}

/** 3.1.8	亨通小程序 下单 订单详情qrocde
 *
 */
export const chargeOrderQrcodeApi = async (params) => {
  return Ajax({
    url: '/base/api/v0.2/guns/qrcode',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}

/** 3.1.8	亨通小程序 订单详情--取消订单
 *
 */
export const myOrderCloseApi = async (params) => {
  return Ajax({
    url: '/ord/api/v0.1/order/' + params.orderNo,
    method: 'GET',
    timeout: 20000,
    data: {},
  })
}
/** 3.1.8	亨通小程序 订单详情--评价
 *
 */
export const myOrderEvaluateApi = async (params) => {
  return Ajax({
    url: '/ord/api/v0.1/eva-dtl',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序 订单详情
 *
 */
export const myOrderDetailsApi = async (params) => {
  return Ajax({
    url: '/ord/api/v0.1/charge_order_dtl',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序 订单列表
 *
 */
export const myOrderListApi = async (params) => {
  return Ajax({
    url: '/ord/api/v0.1/order-charge-list',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序 登录
 *
 */
export const loginSumbitApi = async (params) => {
  return Ajax({
    url: '/base/api/v0.4/login',
    method: 'POST',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序 登录发送验证码
 *
 */
export const longinSendApi = async (params) => {
  return Ajax({
    url: '/base/api/v1/sms',
    method: 'POST',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	亨通小程序 登录
 *
 */
// export const longinSubmitApi = async (params) => {
//   return Ajax({
//     url: '/base/api/v0.4/login',
//     method: 'POST',
//     timeout: 20000,
//     data: params,
//   })
// }
/** 3.1.8	亨通小程序站点列表
 *
 */
export const mapStationApis = async (params) => {
  return Ajax({
    url: '/base/api/open/v0.3/charging-stations',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}

/** 3.1.8	亨通小程序地图点
 *
 */
export const mapStationListApi = async (params) => {
  return Ajax({
    url: '/base/api/v0.2/charging-stations',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}

/** 3.1.8	亨通小程序站点列表- 距离最近
 *
 */
export const mapDistanceListApi = async (params) => {
  return Ajax({
    url: '/base/api/open/v0.3/charging-stations',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}

/** 3.1.8	亨通小程序站点列表- 价格最低
 *
 */
export const mapPriceListApi = async (params) => {
  return Ajax({
    url: '/base/api/open/v0.3/charging-stations',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}

/** 3.1.8	亨通小程序 所有站点 搜索作用
 *
 */
export const mapStationApias = async (params) => {
  return Ajax({
    url: '/base/api/open/v0.3/charging-stations',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}

//亨通小程序  站点详情
/** 3.1.
 *  stationId
 */
export const mapStationDetailsApi = async (params) => {
  return Ajax({
    url: '/base/api/v0.1/charging-station/guns',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}
//亨通小程序  我的资料
/** 3.1.8
 */
export const myCustinfoDeatilApi = async (params) => {
  return Ajax({
    url: '/base/api/v0.2/custinfo',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}

//亨通小程序  我的余额
/** 3.1.8
 */
export const myAccountsDeatilApi = async (params) => {
  return Ajax({
    url: '/base/api/v0.1/accounts',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}

//亨通小程序  我的优惠券
/** 3.1.8
 */
export const myCouponsDeatilApi = async (params) => {
  return Ajax({
    url: '/base/api/v0.1/accounts/coupons',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}



 /** 支付域	宁德H5  退款中的充值记录
 *
 */
export const myBalanceRecordAps = async (params) => {
  return Ajax({
    url: '/def/api/v0.1/balance-can-return-record',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}
/** 支付域	宁德H5  退款记录
 *
 */
export const myRefundListAps = async (params) => {
  return Ajax({
    url: '/def/api/v0.1/online-refund-log',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}
/** 支付域	宁德H5  撤销退款
 *
 */
export const myCancellationAps = async (params) => {
  return Ajax({
    url: '/def/api/v0.1/online-refund-cancel',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}
/** 3.1.8	宁德H5  退款 提交
 *
 */
export const myOnlineRefundAps = async (params) => {
  return Ajax({
    url: '/def/api/v0.1/online-refund',
    method: 'POST',
    timeout: 20000,
    data: params,
  })
}

/** 支付域	宁德H5  退款中的 退款说明
 *
 */
 export const myBalanceInfosAps = async (params) => {
  return Ajax({
    url: '/pub/api/v0.1/infos/0411',
    method: 'GET',
    timeout: 20000,
    data: params,
  })
}

