<template>
  <view>
    <view class="listTypes">
      <view @click="jump(0)" data-type="0" :class="'lity ' + (infoType == 0 ? 'active' : '')">
        <img class="type-img" v-if="infoType == 0" :src="`${IMG_PATH}icon-menu-detail.png`" />
        <img class="type-img" v-else :src="`${IMG_PATH}icon-menu-detail-normal.png`" />
        详情
      </view>
      <view @click="jump(1)" data-type="1" :class="infoType == 1 ? 'active' : ''">
        <img class="type-img" v-if="infoType == 1" :src="`${IMG_PATH}icon-station-active.png`" />
        <img class="type-img" v-else :src="`${IMG_PATH}icon-station-normal.png`" />
        充电桩
      </view>
      <view @click="jump(2)" data-type="2" :class="infoType == 2 ? 'active' : ''">
        <img class="type-img" v-if="infoType == 2" :src="`${IMG_PATH}icon-message.png`" />
        <img class="type-img" v-else :src="`${IMG_PATH}icon-message-normal.png`" />
        评价
      </view>
    </view>
    <view v-if="infoType == 0" class="oh">
      <view class="stationImg">
        <image :src="img"></image>
        <view class="station-opac">
          <view class="station-opac-msg">
            <img
              v-if="info.defAmt.vvipFlag && info.defAmt.vipFlag"
              class="vvip-flag"
              :src="`${IMG_PATH}member/vvipFlag.png`"
            />
            <img v-else-if="info.defAmt.vipFlag" class="vip-flag" :src="`${IMG_PATH}member/vipFlag.png`" />
            <text>{{ info.stationName }}</text>
          </view>
          <view v-if="aligStar == 0" class="station-opac-img">
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
          </view>
          <view v-if="aligStar == 1 || aligStar == 2 || aligStar == 3" class="station-opac-img">
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
          </view>
          <view v-if="aligStar == 4 || aligStar == 5" class="station-opac-img">
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
          </view>
          <view v-if="aligStar == 6 || aligStar == 7" class="station-opac-img">
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
          </view>
          <view v-if="aligStar == 8 || aligStar == 9" class="station-opac-img">
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
          </view>
          <view v-if="aligStar == 10 || aligStar > 10" class="station-opac-img">
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
          </view>
        </view>
      </view>
      <view class="stationAdd">
        <view class="stationAdd-left" @click="handleCollection">
          <div class="text">
            {{ addreDom }}
            <image v-if="isCollection" class="station-collection" :src="`${IMG_PATH}<EMAIL>`"></image>
            <image v-else class="station-collection" :src="`${IMG_PATH}<EMAIL>`"></image>
          </div>
          <scroll-view
            v-if="tagList && Array.isArray(tagList) && tagList.length > 0"
            class="tag-wrap"
            scroll-x="true"
            :scroll-with-animation="true"
            @scroll="handleScroll"
            show-scrollbar="false"
          >
            <view class="tag-container">
              <view
                class="tag-item"
                v-for="(item, index) in tagList"
                :key="index"
                :id="`tag-${index}`"
                @tap="handleTagTap(item, index)"
              >
                <!-- :class="{ active: activeTagIndex === index }" -->
                {{ item.tagName || '-' }}
              </view>
            </view>
          </scroll-view>
        </view>
        <view @click="distanceMap" class="stationAdd-right">
          <view style="line-height: 30rpx">
            <icon class="icon-right color color-red" type="icon iconfont icon-guide1" size="20" />
          </view>
          <template v-if="distance > 1000">
            <view>{{ distance | toFix }}km</view></template
          >
          <template v-if="distance > 0 && distance <= 1000">
            <view>{{ distance | fixed }}m</view></template
          >
        </view>
      </view>

      <!-- 检修状态横幅 -->
      <view v-if="isUnderMaintenance" class="maintenance-banner">
        <view class="maintenance-content">
          <img class="maintenance-icon" :src="`${IMG_PATH}member/icon-maintenance.png`" />
          <text class="maintenance-text">当前站点检修中</text>
        </view>
      </view>

      <!-- 检修状态遮罩容器 -->
      <!-- :class="{ 'under-maintenance': isUnderMaintenance }" -->
      <view class="maintenance-container">
        <view class="content-area">
          <view @click="look" class="stationDetailed">
            <view class="stationDetailed-one pageCollect">
              <text class="pagePost">
                7天内成功充电
                <text class="ci">{{ suc }}</text>
                次
              </text>
              <view v-if="types">
                <text class="befores">最近充电{{ min }}</text>
                <text class="icon iconfont icon-arrow_right" size="20"></text>
              </view>
              <view v-else>
                <text class="befores">暂无数据</text>
              </view>
            </view>
          </view>
          <!-- 立即开通 -->
          <view class="activate-now" v-if="memberInfo.vipFlag === '0'">
            <ActivateNow />
          </view>

          <view class="charging">
            <view class="charging_head">
              <view class="head">
                <image :src="`${IMG_PATH}p9.png`"></image>
              </view>
              <view class="middles">
                <view class="middle_type">计费信息</view>
                <view class="middle_number-box" :class="{ line: info.defAmt.vipFlag }">
                  <template v-if="memberInfo.vipFlag == '1'">
                    <view :class="{ line: info.defAmt.vipFlag }">
                      <text class="price-text"></text>
                      <text class="price-num" :class="{ gray: info.defAmt.vipFlag }">{{
                        newItems.priceRemarkNum
                      }}</text>
                      <text class="price-text">度/元</text>
                    </view>
                    <view v-if="info.defAmt.vipFlag" class="price-vip-box">
                      <div class="price-label">
                        会员价 :
                        <div class="price-card">
                          <div class="price-card-wrap"></div>
                          <div class="price-box"></div>
                        </div>
                      </div>
                      <div class="price-money">¥ {{ info.defAmt.vipPrice || 0 }}</div>
                    </view>
                  </template>
                  <template v-else>
                    <view class="price-vip-box">
                      <div class="price-label">
                        会员价 :
                        <div class="price-card">
                          <div class="price-card-wrap"></div>
                          <div class="price-box"></div>
                        </div>
                      </div>
                      <div class="price-money">¥ {{ info.defAmt.vipPrice || '' }}</div>
                    </view>
                    <view>
                      <text class="price-text">当前价 :</text>
                      <text class="price-num">{{ newItems.priceRemarkNum }}</text>
                      <text class="price-text">度/元</text>
                    </view>
                  </template>
                </view>
              </view>
            </view>
            <view class="charging_middle">
              <view class="middle_list">
                <text>计费时段</text>
                <view @click="Jums" class="inner">
                  <text>{{ startValue }}</text>
                  <text class="icon iconfont icon-arrow_right" size="20"></text>
                </view>
              </view>
              <view class="middle_list">
                <text>电费</text>
                <text
                  class="overflows"
                  style="text-align: right"
                  @click="handClickPop"
                  :data-msg="newItems.chargeAmt"
                  >{{ newItems.chargeAmt }}</text
                >
              </view>
              <view class="middle_list">
                <text>服务费</text>
                <text
                  class="overflows"
                  style="text-align: right"
                  @click="handClickPop"
                  :data-msg="newItems.serviceAmt"
                  >{{ newItems.serviceAmt }}</text
                >
              </view>
            </view>
          </view>

          <view class="charging">
            <view class="charging_head">
              <view class="head">
                <image :src="`${IMG_PATH}p7.png`"></image>
              </view>
              <view class="middles">
                <view class="middle_type">停车信息</view>
              </view>
            </view>
            <view class="charging_middles">
              <view class="middle_list">
                <text>{{ pricingSectName }}</text>
              </view>
            </view>
          </view>

          <view class="charging">
            <view class="charging_head">
              <view class="head">
                <image :src="`${IMG_PATH}p4.png`"></image>
              </view>
              <view class="middles">
                <view class="middle_type">场地信息</view>
              </view>
            </view>
            <view class="charging_middle">
              <view class="middle_list">
                <text>营业时间</text>
                <text>{{ info.busiTime }}</text>
              </view>
              <view class="middle_list">
                <text>运营商</text>
                <text>{{ info.operName }}</text>
              </view>
              <view class="middle_list">
                <text>服务电话</text>
                <view @click="calling" class="inner">
                  <text>{{ info.serviceTel }}</text>
                  <image :src="`${IMG_PATH}p3.png`"></image>
                </view>
              </view>
            </view>
          </view>
          <view class="charging">
            <view class="charging_head">
              <view class="head">
                <image :src="`${IMG_PATH}p1.png`"></image>
              </view>
              <view class="middles">
                <view class="middle_type">服务信息</view>
              </view>
            </view>
            <view class="charging_middles">
              <view class="middle_list">
                <view class="list" v-for="(item, index) in Point" :key="index">
                  <image class="image1" :src="item.src"></image>

                  <view>{{ item.serviceName }}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view v-if="isUnderMaintenance" class="maintenance-mask"></view>
      </view>
    </view>

    <view v-if="infoType == 1" class="oh">
      <view class="swiper-tab swiper-tab-other">
        <view class="swiper-tab-list">
          <view data-type="0" @click="tabChange" :class="'swiper-tab-btn ' + (start == true ? 'styleColorC' : '')">
            <icon class="icon-right color color-red" type="icon iconfont icon-fast" size="24" />
            共{{ info.dcNums || 0 }}个 空闲{{ info.dcFreeNums || 0 }}个
          </view>
        </view>
        <view class="swiper-tab-list">
          <view data-type="1" @click="tabChange" :class="'swiper-tab-btn ' + (start == true ? '' : 'styleColorC')">
            <icon class="icon-right color color-blue" type="icon iconfont icon-slow" size="24" />
            共{{ info.acNums || 0 }}个 空闲{{ info.acFreeNums || 0 }}个
          </view>
        </view>
        <view class="swiper-lines"></view>
      </view>
      <view class="stationPoint" v-for="(item, index) in gunList" :key="index">
        <view v-if="item.gunBusiStatus === '02'" class="stationPoint-img">
          <image :src="`${IMG_PATH}maxPage.png`" />
        </view>

        <view v-else-if="item.gunBusiStatus === '03' && item.soc != ''" class="stationPoint-img">
          <canvas style="width: 100%; height: 100%" :canvas-id="'canvasid' + index"></canvas>
          <view class="stationNumber">
            {{ item.soc || '使用中' }}
            <text v-if="item.soc != ''">%</text>
          </view>
          <!-- <view class='stationSoc'>SOC</view> -->
          <view v-if="item.ldTime != ''" class="stationTime">{{ item.ldTime || 0 }}分钟</view>
        </view>

        <view v-else-if="item.gunBusiStatus === '03' && item.soc === ''" class="stationPoint-img">
          <image :src="`${IMG_PATH}nullPageott.png`" />
          <view style="color: #ff9802" class="gunType">使用中</view>
        </view>

        <view v-else class="stationPoint-img">
          <image :src="`${IMG_PATH}nullPage.png`" />
          <view class="gunType">{{ item.gunBusiStatusName || '' }}</view>
        </view>

        <view class="stationPoint-code">
          <view class="code-fault">
            <text class="code">{{ item.displayGunName || '' }}</text>
            {{ item.faultConditions || '' }}
            <text class="fault" v-if="item.faultConditions === '01'">疑似故障</text>
            <text class="fault-icon" v-if="item.faultConditions === '01'" @click="openFaultDialog">?</text>
          </view>
          <view>
            编号:
            <text>{{ item.pileNo || '' }}</text>
          </view>
          <view v-if="item.power">
            功率:
            <text>{{ item.power || '' }}kw</text>
          </view>
        </view>

        <view
          @click="clijc"
          :data-name="' ' + item.displayGunName"
          :data-qrcode="item.qrCode"
          :data-id="item.qrCode"
          v-if="item.gunBusiStatus == '02'"
          class="stationPoint-jiantou"
        >
          充电
        </view>

        <!-- <view wx:if='{{item.gunBusiStatus=="03"}}' class='stationPoint-jiantou'></view> -->
      </view>
    </view>
    <view v-if="infoType == 2" class="comment">
      <!-- 评价统计信息 -->
      <!-- <view class="comment-summary">
        <view class="summary-header">
          <view class="summary-title">用户评价</view>
          <view class="summary-score">
            <view class="score-number">{{ aligStar || 0 }}</view>
            <view class="score-stars">
              <image v-for="n in Math.floor(aligStar / 2)" :src="STARTA" :key="'summary-star-' + n"></image>
              <image v-for="n in 5 - Math.floor(aligStar / 2)" :src="STARTB" :key="'summary-empty-' + n"></image>
            </view>
          </view>
        </view>
        <view class="summary-count">共{{ evaluateList.length }}条评价</view>
      </view> -->

      <!-- 暂无评价 -->

      <view v-if="evaluateList.length == 0" class="nothing">
        <image :src="NoIMGINFOS"></image>
        <view>暂无评价数据</view>
      </view>

      <!-- 评价列表 -->
      <template v-else>
        <view v-for="(item, index) in evaluateList" class="comment-card" :key="index">
          <view class="comment-header">
            <view class="user-info">
              <image :src="item.avatar || `${IMG_PATH}defaultAvatar.png`" class="user-avatar" />
              <view class="user-details">
                <view class="user-name">{{ item.displayMobile || item.evaUserName || '匿名用户' }}</view>
                <view class="comment-time">{{ item.displayTime || item.evaTime }}</view>
              </view>
            </view>
            <view class="user-rating">
              <image
                v-for="n in item.evaScore"
                :src="`${IMG_PATH}<EMAIL>`"
                :key="'star-' + n"
                class="star-icon"
              ></image>
              <image
                v-for="n in item.noEvaScore"
                :src="`${IMG_PATH}<EMAIL>`"
                :key="'empty-' + n"
                class="star-icon"
              ></image>
            </view>
          </view>
          <view class="comment-content">
            <view class="comment-text">{{ item.evaRemark || '' }}</view>

            <view v-if="item.oderEvaPicList && item.oderEvaPicList.length > 0" class="comment-images">
              <image
                v-for="(imgUrl, imgIndex) in item.oderEvaPicList"
                @click="previewEvaluateImage"
                :data-src="imgUrl.evaPic"
                :data-urls="item.oderEvaPicList"
                :src="imgUrl.evaPic"
                :key="'img-' + imgIndex"
                class="comment-image"
              />
            </view>
          </view>
          <view v-if="item.replyRemark" class="official-reply">
            <view class="reply-header">
              <icon type="icon iconfont icon-service" size="16" style="color: #2196f3; margin-right: 8rpx" />
              <text class="reply-label">官方回复</text>
            </view>
            <view class="reply-content">{{ item.replyRemark }}</view>
          </view>
        </view>
      </template>
    </view>
    <!-- <loading hidden="{{hiddenLoading}}">正在加载</loading> -->
  </view>
</template>

<script>
import { mapState } from 'vuex';
import { openNavigation } from '@/utils/bridge/index.js';
import ActivateNow from '@/components/ActivateNow/index';
import { openLocation, makePhoneCall } from '@/utils/index.js';
import {
  getMemberInfo,
  getStationDetail,
  recentChargingInfo,
  getCollectChargingStation,
  collectStation,
  delCollectStation,
  getEvaDtl,
} from '@/services/index.js';

export default {
  components: {
    ActivateNow,
  },
  data() {
    return {
      evaluateList: [],
      stationInfo: {},
      memberInfo: {},
      pricingSectName: '',
      oppp: true,
      city: '',
      curred: 0,
      infoType: 0, // 0: 详情, 1: 充电桩, 2: 评价
      hiddenLoading: false,
      aligStar: 0,
      longitude: '',
      img: '',
      latitude: '',
      lon: '',
      lat: '',
      distance: '',
      winWidth: 0,
      winHeight: 2000,
      winHeightItem: 0,
      currentTab: 1,
      msg: '',
      addreDom: '',
      token: '',
      gunList: [],
      tagList: [],
      info: {
        defAmt: {
          serviceAmt: '',
        },

        busiTime: '',
        operName: '',
        serviceTel: '',
      },

      start: true,
      strat_num: 0,

      // end_num: 20,
      sAngle: 1.5 * Math.PI,

      eAngle: 0,
      opens: true,
      suc: 0,
      types: true,
      startValue: '',
      Point: [],
      phoneType: true,

      //新增
      newItems: [],

      stationId: '',
      min: '',
      gunListA: [],
      gunListB: [],
      isCollection: false,
      isCollected: false, // 展示收藏icon是否高亮

      // 评价相关数据
      NoIMGINFOS: this.IMG_PATH + 'no-data.png',
      NOTIMAGS: this.IMG_PATH + 'default-avatar.png',

      // 检修状态
      isUnderMaintenance: false, // 是否检修中
    };
  },
  computed: {
    ...mapState({
      userInfo: (state, getters) => getters['login/getUserInfo'],
    }),
  },
  onShow() {
    var that = this;
    that.setData({
      start: true,
    });

    // // 判断是否登录
    // if (this.userInfo?.mobile) {
    //   that.setData({
    //     phoneType: true,
    //   });
    // } else {
    //   that.setData({
    //     phoneType: false,
    //   });
    // }
    // uni.setStorageSync('startd', 0);
    // if (uni.getStorageSync('stationIdMap') && uni.getStorageSync('distanceMap')) {
    //   console.log('存在 所以返回会刷新');
    //   that.sloop();
    // } else {
    //   console.log('不存在  所以目前是从上个页面直接跳转来的 正规流程');
    // }
  },
  onLoad(options) {
    this.getMemberInfo();
    uni.setStorageSync('stationIdMap', options.stationId); //"可启动充电余额值"
    uni.setStorageSync('distanceMap', options.distance); //"可启动充电余额值"

    // 处理tab参数，如果传入了tab参数，则切换到对应的tab
    if (options.tab) {
      const tabIndex = parseInt(options.tab);
      this.setData({
        infoType: tabIndex,
        curred: tabIndex,
      });

      // 如果是评价tab，需要在数据加载完成后获取评价数据
      if (tabIndex === 2) {
        // 延迟获取评价数据，确保stationId已经设置
        setTimeout(() => {
          this.getEvaluateList();
        }, 500);
      }
    }

    this.sloop();
    this.getLastChargingTime();
    this.getCollection();
  },
  methods: {
    // 获取会员信息
    async getMemberInfo() {
      const [err, res] = await getMemberInfo();
      if (res) {
        this.memberInfo = res;
      }
    },
    jump(type) {
      var that = this;

      console.log('切换tab:', type);
      that.setData({
        infoType: type,
        curred: type,
      });

      // 如果切换到评价tab，获取评价数据
      if (type === 2) {
        if (!this.userInfo?.mobile) {
          uni.navigateTo({
            url: '/pages/login/login',
          });
        } else {
          this.getEvaluateList();
        }
      }
    },

    address(lat1, lng1, lat2, lng2) {
      function toRad(d) {
        return (d * Math.PI) / 180;
      }
      // function getDisance(lat1, lng1, lat2, lng2) {
      var dis = 0;
      var radLat1 = toRad(lat1);
      var radLat2 = toRad(lat2);
      var deltaLat = radLat1 - radLat2;
      var deltaLng = toRad(lng1) - toRad(lng2);
      var dis =
        2 *
        Math.asin(
          Math.sqrt(
            Math.pow(Math.sin(deltaLat / 2), 2) +
              Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(deltaLng / 2), 2)
          )
        );
      // return dis * 6378137;
      var distance = ((dis * 6378137) / 1000).toFixed(2);
      // this.setData({
      //   distance: distance
      // });
    },

    clijc(event) {
      console.log(event, 'event');
      var gunId = event.target.dataset.id;
      var data = {
        gunId: event.target.dataset.id,
        name: event.target.dataset.name,
        qrcode: event.target.dataset.qrcode,
        info: 666,
        city: this.city,
        stationId: this.stationId,
        danjia: this.info.defAmt.priceRemark,
      };
      var msg = JSON.stringify(data);
      var city = this.city;
      if (this.userInfo?.mobile) {
        uni.navigateTo({
          url: `/subPackages/placeorder/placeorder?msg=${gunId}&titles=10`,
        });
      } else {
        uni.navigateTo({
          url: '/pages/login/login',
        });
      }
    },

    distanceMap() {
      openLocation({
        address: this.addreDom,
        latitude: this.lat,
        longitude: this.lon,
      });
    },
    look() {
      uni.navigateTo({
        url: '/subPackages/searchCar/searchCar?stationId=' + this.stationId,
      });
    },

    handClickPop(item) {
      if (item && item.currentTarget && item.currentTarget.dataset) {
        var msg = item.currentTarget.dataset.msg.replace(/;/g, '\r\n');
        uni.showModal({
          title: '提示',
          showCancel: false,
          content: msg || '---',
          success(res) {
            if (res.confirm) {
              console.log('用户点击确定');
            } else if (res.cancel) {
              console.log('用户点击取消');
            }
          },
        });
      }
    },

    async sloop() {
      var that = this;
      var stationId = uni.getStorageSync('stationIdMap');
      var distanced = uni.getStorageSync('distanceMap');
      var msg = uni.getStorageSync('msg');
      that.setData({
        distance: distanced,
        longitude: that.longitude,
        latitude: that.latitude,
        stationId: stationId,
        msg: msg,
        min: '',
        oppp: true,
        curred: 0,
      });
      var params = {
        stationId: stationId,
      };
      const [, res] = await getStationDetail(params);
      if (res) {
        if (!res?.stationNo) {
          return false;
        }

        var serviceInfosList = res.serviceInfosList || [];
        if (serviceInfosList.length > 0) {
          for (var i = 0; i < serviceInfosList.length; i++) {
            if (serviceInfosList[i].serviceCode == '01') {
              serviceInfosList[i].src = that.IMG_PATH + 'p2.png';
            } else if (serviceInfosList[i].serviceCode == '02') {
              serviceInfosList[i].src = that.IMG_PATH + 'p10.png';
            } else if (serviceInfosList[i].serviceCode == '03') {
              serviceInfosList[i].src = that.IMG_PATH + 'p1.png';
            } else if (serviceInfosList[i].serviceCode == '04') {
              serviceInfosList[i].src = that.IMG_PATH + 'p6.png';
            } else if (serviceInfosList[i].serviceCode == '05') {
              serviceInfosList[i].src = that.IMG_PATH + 'p5.png';
            } else if (serviceInfosList[i].serviceCode == '06') {
              serviceInfosList[i].src = that.IMG_PATH + 'p1.png';
            } else if (serviceInfosList[i].serviceCode == '07') {
              serviceInfosList[i].src = that.IMG_PATH + 'p8.png';
            }
          }
        }
        var TC = [];
        for (var i = 0; i < res.prodList.length; i++) {
          if (res.prodList[i].prodName.indexOf('停车费') > -1) {
            TC.push(res.prodList[i]);
          }
        }
        if (TC.length > 0) {
          var jack = TC[0].pricingCombine[0].pricingSectName;
        } else {
          var jack = '';
        }
        that.setData({
          hiddenLoading: true,
          city: res.county,
          pricingSectName: jack,
          info: res,
          // winHeight: 700 + res.gunList.length * 190,
          winHeightItem: 700 + res.gunList.length * 190,
          lat: res.lat,
          lon: res.lon,
          addreDom: res.stationAddr,
          tagList: res.tagList || [],
          aligStar: res.evaScore || 0,
          newItems: res.defAmt,
          Point: serviceInfosList,
          isCollected: res.isCollected || false,
          // 检修状态判断，根据实际接口字段调整
          // isUnderMaintenance: res.maintenanceStatus === '1' || res.stationStatus === 'maintenance' || false,
          isUnderMaintenance: res?.busiStatus == '04',
        });
        console.log(res, 'info');
        that.stationInfo = res;
        function strToMs(str) {
          let arr = str.split(':');
          return Number(arr[0]) * 3600000 + Number(arr[1]) * 60000;
        }
        let date = new Date();
        let nowMs = strToMs(date.getHours() + ':' + date.getMinutes());
        let arr =
          res.defAmt?.chargeItemList?.filter(
            (item) => nowMs > strToMs(item.startValue) && nowMs < strToMs(item.endValue)
          ) || [];
        if (arr && arr[0]) {
          that.setData({
            startValue: arr[0].startValue + '~' + arr[0].endValue,
          });
        }
        var list = res.gunList;
        var reg = new RegExp('02');
        var regTo = new RegExp('01');
        var a = list.filter(function (item) {
          return reg.test(item.gunSubtype);
        });
        var b = list.filter(function (item) {
          return regTo.test(item.gunSubtype);
        });
        // a[0].soc = '100'
        that.setData({
          // gunList: a,
          gunListA: a,
          gunListB: b,
          gunList: a,
        });
        that.setData({
          // gunList: a,
          gunListA: a,
          gunListB: b,
          gunList: a,
        });
        that.animant();
        if (res.imgList.length > 0) {
          that.setData({
            img: res.imgList[0].stationImgUrl,
          });
        } else {
          that.setData({
            img: that.IMG_PATH + 'bgk_img_17_1.png',
          });
        }
        that.address(that.latitude, that.longitude, res.lat, res.lon);
      } else {
      }
    },

    calling() {
      var phone = this.info.serviceTel || '';
      makePhoneCall(phone);
    },

    Jums() {
      uni.navigateTo({
        url: '/pages/basic/standard/standard?stationId=' + this.stationId,
      });
    },

    stopTouchMove() {
      return false;
    },

    animant() {
      if (this.gunListA && Array.isArray(this.gunListA) && this.gunListA.length > 0) {
        this.gunListA
          .map((item, index) => ({
            context: uni.createCanvasContext('canvasid' + index),
            dataSource: item,
          }))
          .forEach(({ context, dataSource }) => this.canvas(context, dataSource));
      }
    },

    canvas(context, ds) {
      const dataSource = JSON.parse(JSON.stringify(ds));
      var context = context;
      var strat_num = this.strat_num;
      var end_num = this.end_num;
      var sAngle = this.sAngle;
      var eAngle = this.eAngle;
      if (ds.soc == '') {
        var numb = '1';
      } else {
        var numb = ds.soc;
      }
      if (strat_num <= 100) {
        eAngle = (numb * (numb / 100) * Math.PI * 2) / numb + 1.5 * Math.PI;
        var eAngle2 = (100 * Math.PI * 2) / 100 + 1.5 * Math.PI;
        context.beginPath();
        context.setStrokeStyle('#ccc');
        context.setLineWidth(5);
        context.arc(38, 31, 26, 0, 2 * Math.PI, false);
        context.stroke();
        context.draw();
        context.closePath();

        // setTimeout(() => {
        context.setStrokeStyle('#FF9802');
        context.setLineWidth(5);
        // context.fillText(strat_num <= 100 ? strat_num : 100, 22, 35)
        context.arc(38, 31, 26, sAngle, eAngle, false);
        context.stroke();
        context.draw(true);
        // this.canvas(context, dataSource)
        // strat_num++
        // this.setData({
        //   strat_num: this.data.strat_num + 1
        // });
        // }, 16)
      } else {
        console.log('strat_num_end:', strat_num);
      }
    },

    tabChange(e) {
      // wx.startPullDownRefresh()
      var that = this;
      that.setData({
        strat_num: 0,
      });
      if (e.currentTarget.dataset.type == 0 || e.currentTarget.dataset.type == '0') {
        that.setData({
          start: true,
          gunList: that.gunListA || [],
          opens: true,
        });
        if (this.gunListA && Array.isArray(this.gunListA) && this.gunListA.length > 0) {
          this.gunListA
            .map((item, index) => ({
              context: uni.createCanvasContext('canvasid' + index),
              dataSource: item,
            }))
            .forEach(({ context, dataSource }) => this.canvas(context, dataSource));
        }
      } else {
        that.setData({
          start: false,
          gunList: that.gunListB || [],
          opens: false,
        });
        if (this.gunListB && Array.isArray(this.gunListB) && this.gunListB.length > 0) {
          this.gunListB
            .map((item, index) => ({
              context: uni.createCanvasContext('canvasid' + index),
              dataSource: item,
            }))
            .forEach(({ context, dataSource }) => this.canvas(context, dataSource));
        }
      }
    },

    /**
     * 滑动切换tab
     */
    bindChange(e) {
      var that = this;
      that.setData({
        curred: e.detail.current,
      });
      if (e.detail.current === 0) {
        that.setData({
          winHeight: 2000,
        });
      } else {
        that.setData({
          winHeight: that.winHeightItem,
        });
      }
    },

    /**
     * 点击tab切换
     */
    swichNav(e) {
      var that = this;
      if (this.currentTab === e.target.dataset.current) {
        return false;
      } else {
        that.setData({
          currentTab: e.target.dataset.current,
        });
      }
    },
    async getLastChargingTime() {
      const that = this;
      var params = {
        stationId: this.stationId,
      };
      const [, res] = await recentChargingInfo(params);
      if (res) {
        var start = new Date(res.lastChargingTime);
        var end = new Date();
        var times = end - start;
        var ti = Math.ceil(end - start) / 1000 / 60;
        var c = Math.ceil(times / 100 / 86400 / 10) * 24 * 60;
        if (ti < 1 && ti > 0) {
          var T = '不到1分钟'; //最近充电不到一分钟
        } else if (ti > 1 && ti < 60) {
          var T = Math.ceil(ti) + '分钟前'; //最近充电xx分钟前
        } else if (ti >= 60 && ti <= 1440) {
          var T = parseInt(ti / 60) + '小时前'; //最近充电xx分钟前
        } else if (ti > 1440) {
          var T = parseInt(ti / 1440) + '天前'; //最近充电xx分钟前
        }
        if (res.chargingNum == 0) {
          that.setData({
            types: false,
          });
        }
        var timesfo = T;
        that.setData({
          suc: res.data?.chargingNum,
          min: timesfo,
        });
      }
    },
    // 获取收藏
    async getCollection(e) {
      const that = this;
      const [, res] = await getCollectChargingStation({
        prefType: '01',
      });
      if (res && res.ret == '200') {
        const isCollection = res.prefList.findIndex((item) => item.relaId == that.stationId);
        this.isCollection = isCollection >= 0;
      } else {
      }
    },

    // 获取评价列表
    async getEvaluateList() {
      const that = this;
      console.log('获取评价列表，stationId:', this.stationId);
      if (!this.stationId) {
        console.log('stationId为空，无法获取评价');
        return;
      }
      const params = {
        stationId: this.stationId,
        pageNum: 1,
        totalNum: 99,
        // #ifdef MP-WEIXIN
        evaUserType: '02',
        evaObjectId: this.stationId,
        // #endif
        // #ifdef H5
        // reqType: '01',
        // #endif
      };

      const [err, res] = await getEvaDtl(params);
      console.log(res, 'res');
      if (res && res.orderEvaList) {
        //   // 处理评价数据
        this.setData({
          evaluateList: res.orderEvaList.map((q) => {
            // 计算评分值
            const score = Math.min(5, Math.max(0, q.evaScore ? Math.floor(Number(q.evaScore)) : 0));

            // 计算未评分的数量（确保不超过5）
            const noScore = Math.max(0, Math.min(5, 5 - score));
            console.log(score, noScore);
            return {
              ...q,
              // 创建评分数组（长度=score）
              evaScore: Array.from({ length: Math.max(0, score) }, (_, i) => i + 1),

              // 创建未评分数组（长度=5-score）
              noEvaScore: Array.from({ length: Math.max(0, noScore) }, (_, i) => i + 1),
            };
          }),
        });
        //   // this.evaluateList = res.orderEvaList;
        //   this.evaluateList = res.orderEvaList;
        //   console.log('评价列表获取成功:', res.orderEvaList || [], this.evaluateList);
      } else {
        this.setData({
          evaluateList: [],
        });
        console.log('暂无评价数据');
      }
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '';

      const date = new Date(timeStr);
      const now = new Date();
      const diff = now - date;

      // 小于1分钟
      if (diff < 60000) {
        return '刚刚';
      }

      // 小于1小时
      if (diff < 3600000) {
        return Math.floor(diff / 60000) + '分钟前';
      }

      // 小于1天
      if (diff < 86400000) {
        return Math.floor(diff / 3600000) + '小时前';
      }

      // 小于7天
      if (diff < 604800000) {
        return Math.floor(diff / 86400000) + '天前';
      }

      // 超过7天显示具体日期
      return (
        date.getFullYear() +
        '-' +
        String(date.getMonth() + 1).padStart(2, '0') +
        '-' +
        String(date.getDate()).padStart(2, '0')
      );
    },

    // 预览评价图片
    previewEvaluateImage(event) {
      const current = event.currentTarget.dataset.src;
      const urls = event.currentTarget.dataset.urls;

      uni.previewImage({
        current: current,
        urls: urls,
      });
    },
    async handleCollection(e) {
      const that = this;
      let result = null;
      if (this.isCollection) {
        const [, res] = await delCollectStation({
          prefType: '01',
          relaId: that.stationId,
        });
        result = res;
      } else {
        const [, res] = await collectStation({
          prefType: '01',
          relaId: that.stationId,
        });
        result = res;
      }

      const text = that.isCollection ? '取消收藏' : '收藏';
      if (result && result.ret === 200) {
        uni.showToast({
          title: text + '成功',
          icon: 'success',
        });
      } else {
      }
      // 刷新一下页面
      this.getCollection();
    },
    // 打开疑似故障弹框
    openFaultDialog() {
      uni.showModal({
        title: '提示',
        content: '该充电桩疑似故障，若无法启动充电，请更换其他充电桩充电！',
        showCancel: false,
      });
    },
  },
  filters: {
    toFix: (value, count = 2) => {
      var num = (value * 1) / 1000;
      return +num.toFixed(count);
    },
    fixed: (value, count = 2) => {
      return parseFloat(value).toFixed(count);
    },
  },
};
</script>
<style lang="scss" scoped>
$text: #2095ff;
.comment {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 0;

  // 评价统计信息
  .comment-summary {
    background: #fff;
    padding: 30rpx;
    margin-bottom: 20rpx;
    border-bottom: 1rpx solid #e1e1e1;

    .summary-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .summary-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
      }

      .summary-score {
        display: flex;
        align-items: center;

        .score-number {
          font-size: 36rpx;
          font-weight: bold;
          color: #ff6b35;
          margin-right: 16rpx;
        }

        .score-stars {
          display: flex;
          align-items: center;

          image {
            width: 24rpx;
            height: 24rpx;
            margin-left: 4rpx;
          }
        }
      }
    }

    .summary-count {
      font-size: 28rpx;
      color: #666;
    }
  }

  // 暂无数据
  .nothing {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
    background: #fff;
    margin: 20rpx;
    border-radius: 12rpx;

    image {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 20rpx;
    }

    view {
      color: #999;
      font-size: 28rpx;
    }
  }

  // 评价卡片
  .comment-card {
    background: #fff;
    margin: 0 20rpx 20rpx 20rpx;
    border-radius: 12rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

    // 用户信息头部
    .comment-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 24rpx;

      .user-info {
        display: flex;
        align-items: center;
        flex: 1;

        .user-avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 20rpx;
          flex-shrink: 0;
        }

        .user-details {
          flex: 1;

          .user-name {
            font-size: 30rpx;
            color: #333;
            font-weight: 500;
            line-height: 42rpx;
          }

          .comment-time {
            font-size: 24rpx;
            color: #999;
            line-height: 34rpx;
            margin-top: 4rpx;
          }
        }
      }

      .user-rating {
        display: flex;
        align-items: center;

        .star-icon {
          width: 24rpx;
          height: 24rpx;
          margin-left: 4rpx;
        }
      }
    }

    // 评价内容区域
    .comment-content {
      .comment-text {
        font-size: 28rpx;
        color: #333;
        line-height: 40rpx;
        margin-bottom: 20rpx;
        word-break: break-all;
      }

      // 评价图片
      .comment-images {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;
        margin-bottom: 20rpx;

        .comment-image {
          width: 120rpx;
          height: 120rpx;
          border-radius: 8rpx;
          object-fit: cover;
          border: 1rpx solid #f0f0f0;
        }
      }
    }

    // 官方回复
    .official-reply {
      background: #f8f9fa;
      border-radius: 12rpx;
      padding: 24rpx;
      margin-top: 20rpx;
      border-left: 4rpx solid #2196f3;

      .reply-header {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;

        .reply-label {
          font-size: 26rpx;
          color: #2196f3;
          font-weight: 500;
        }
      }

      .reply-content {
        font-size: 26rpx;
        color: #666;
        line-height: 38rpx;
      }
    }
  }
}
.listTypes {
  width: 100%;
  overflow: hidden;
  height: 87rpx;
  background: #fff;
  font-size: 30rpx;
  color: #7a7a7a;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 10rpx;
}
.oh {
  overflow: hidden;
}
.listTypes view {
  width: 33.33%;
  height: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.type-img {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}

.listTypes view:not(:last-child) {
  border-right: 2rpx solid #ccc;
}

.listTypes .active {
  color: #2196f3;
}

.swiper-tab {
  width: 100%;
  text-align: center;
  height: 88rpx;
  box-sizing: border-box;
  position: relative;
  display: flex;
  align-items: center;
}

.swiper-tab .swiper-lines {
  position: absolute;
  width: 1rpx;
  height: 30rpx;
  background: #ccc;
  display: inline-block;
  z-index: 999;
  top: 30rpx;
  left: 375rpx;
}

.swiper-tab-list {
  font-size: 30rpx;
  width: 50%;
  box-sizing: border-box;
  color: #666;
  height: 100%;
  /* padding:20rpx 0; */
  display: flex;
  justify-content: center;
  align-items: center;
}

.on {
  color: #2196f3;
  border-bottom: 2rpx solid #2196f3;
}

.swiper-box {
  display: block;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.swiper-box view {
  /* text-align: center; */
}

.stationImg {
  width: 100%;
  height: 260rpx;
  position: relative;
}

.stationImg image {
  width: 100%;
  height: 100%;
}

.stationImg .station-opac {
  width: 100%;
  position: absolute;
  bottom: 10rpx;
  height: 50rpx;
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
  box-sizing: border-box;
  align-items: center;
  font-size: 30rpx;
  color: #fff;
  /* background: #020202 */
}

.stationImg .station-opac image {
  width: 32rpx;
  height: 29rpx;
  margin-right: 10rpx;
  vertical-align: middle;
}

.stationImg .station-opac-img image {
  width: 32rpx;
  height: 30rpx;
}
.stationImg .station-opac .station-opac-msg .vip-flag {
  width: 54rpx;
  height: 30rpx;
  margin-right: 8rpx;
}
.stationImg .station-opac .station-opac-msg .vvip-flag {
  width: 126rpx;
  height: 30rpx;
  margin-right: 8rpx;
}
.stationAdd {
  width: 100%;
  min-height: 143rpx;
  box-sizing: border-box;
  padding: 26rpx 30rpx;
  background: #fff;
  font-size: 28rpx;
  color: #666;
  line-height: 50rpx;
  display: flex;
  padding-right: 0;
  .tag-wrap {
    white-space: nowrap;
    width: 100%;
    .tag-item {
      display: inline-block;
      color: #498fc7;
      border: 1px solid #498fc7;
      border-radius: 20rpx;
      padding: 0rpx 15rpx;
      margin-right: 15rpx;
      max-width: 150rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 20rpx;
    }
  }
}

.stationAdd .stationAdds {
  height: 90rpx;
  display: flex;
  justify-content: space-between;
  align-content: center;
}

.stationAdd .stationAdd-left {
  position: relative;
  width: 500rpx;
  border-right: 1rpx solid #d8d8d8;
  box-sizing: border-box;
  padding-right: 40rpx;
  overflow: hidden;
  /* white-space: nowrap;
  text-overflow: ellipsis; */
}
.stationAdd .stationAdd-left .station-collection {
  position: absolute;
  width: 42rpx;
  height: 42rpx;
  right: 20rpx;
  bottom: 0;
}

.stationAdd .stationAdd-right {
  text-align: center;
  width: 220rpx;
  color: #2196f3;
  font-size: 24rpx;
}

.stationDetailed {
  height: 80rpx;
  width: 100%;
  background: #fff;
  box-sizing: border-box;
  margin: 18rpx 0 0 0;
  padding: 20rpx 30rpx;
}

.stationDetailed view {
  height: 40rpx;
}

.stationDetailed .stationDetailed-one {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.stationDetailed .stationDetailed-one .overflows2 {
  width: 58%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.stationDetailed .pageCollect view {
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.stationDetailed .pageCollect view .icon-arrow_right {
  padding-bottom: 5rpx;
  margin-left: 10rpx;
  color: #666;
}

.stationDetailed .pageCollect .pagePost {
  font-size: 30rpx;
  color: #333;
}

.stationDetailed .pageCollect .pagePost .ci {
  color: #2196f3;
}

.stationDetailed .pageCollect .befores {
  font-size: 28rpx;
  color: #999;
}

.stationDetailed-two {
  display: flex;
  align-items: center;
}

.stationDetailed-two text {
  display: inline-block;
  width: 40%;
}

.stationDetailed view text {
  font-size: 24rpx;
  color: #666;
}

.swiper-tab-other {
  height: 86rpx;
  background: #fff;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 18rpx;
}

.swiper-tab-other .swiper-tab-list icon {
  vertical-align: middle;
  margin-right: 10rpx;
}

.swiper-tab-other .swiper-tab-list .color-red {
  color: #f44336;
}

.swiper-tab-other .swiper-tab-list .color-blue {
  color: #2196f3;
}

.stationPoint {
  width: 100%;
  height: 180rpx;
  box-sizing: border-box;
  padding: 22rpx 28rpx;
  background: #fff;
  display: flex;
  align-items: center;
  padding-right: 0;
  padding-left: 17rpx;
  margin-bottom: 10rpx;
}

/* .stationPoint .viewCpi{
  width: 100%;
    height: 120rpx;
  box-sizing: border-box;
  padding: 22rpx 28rpx;
  background: #fff;
  display: flex;
  padding-right: 0;
} */

.stationPoint .stationPoint-img {
  width: 20%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  left: 0;
  top: 0;
}

.stationPoint .stationPoint-img image {
  width: 76%;
  height: 81%;
}

.stationPoint .stationPoint-img .gunType {
  position: absolute;
  left: 0;
  top: 38%;
  width: 100%;
  text-align: center;
  font-size: 24rpx;
  color: #ccc;
}

.stationPoint .stationPoint-img .stationNumber {
  position: absolute;
  top: 30%;
  left: 4rpx;
  font-size: 26rpx;
  color: #666;
  width: 100%;
  text-align: center;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
}

.stationPoint .stationPoint-img .stationNumber text {
  font-size: 18rpx;
  padding-top: 6rpx;
}

.stationPoint .stationPoint-img .stationSoc {
  position: absolute;
  top: 41%;
  font-size: 25rpx;
  color: #ff9802;
  width: 100%;
  text-align: center;
}

.stationPoint .stationPoint-img .stationTime {
  position: absolute;
  left: 0;
  bottom: -12rpx;
  color: #333;
  font-size: 22rpx;
  width: 100%;
  text-align: center;
}

.stationPoint .stationPoint-img .color {
  color: #f44336;
}

.stationPoint .stationPoint-code {
  color: #999;
  font-size: 26rpx;
  line-height: 40rpx;
  width: 60%;
  height: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-content: space-around;
  flex-wrap: wrap;
  border-left: 1rpx solid #cbcbcb;
  padding-left: 30rpx;
  box-sizing: border-box;
  margin-left: 10rpx;
}

.stationPoint .stationPoint-code view {
  width: 100%;
}

.stationPoint .stationPoint-code text {
  color: #454545;
  font-size: 26rpx;
  width: 40%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stationPoint .stationPoint-code .codeMsg {
  font-size: 28rpx;
}
.stationPoint .stationPoint-code .code-fault {
  display: flex;
  align-items: center;
}
.stationPoint .stationPoint-code .code-fault .fault {
  width: 102.26rpx;
  color: #fff;
  font-size: 20.4rpx;
  // border: 1rpx solid #ff0000;
  border-radius: 4rpx;
  background: #ffe5e5;
  background: #ff8301;
  margin-left: 12rpx;
  line-height: 34rpx;
  text-align: center;
}
.stationPoint .stationPoint-code .code-fault .fault-icon {
  color: white;
  background: #ff8301;
  width: 30rpx;
  height: 30rpx;
  border-radius: 30rpx;
  font-size: 20.4rpx;
  margin-left: 12rpx;
  text-align: center;
  line-height: 30rpx;
}
.stationPoint .stationPoint-jiantou {
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  background: #2196f3;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.stationPoint .stationPoint-jiantou icon {
  vertical-align: middle;
  margin-left: 10rpx;
}

.swiper-tab-btn {
  width: 80%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.charging {
  width: 100%;
  background: #fff;
  margin-top: 18rpx;
}

.charging .charging_head {
  display: flex;
  /* justify-content: space-around; */
  box-sizing: border-box;
  font-size: 26rpx;
  color: #333;
  height: 88rpx;
  width: 100%;
}

.charging .charging_head .head {
  width: 93rpx;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.charging .charging_head .head image {
  width: 37rpx;
  height: 37rpx;
}
.price-vip-box {
  font-family: D-DIN;
  height: 40rpx;
  background: #feebe0;
  margin: 0 4rpx 0 24rpx;
  font-size: 22rpx;
  color: #fff;
  line-height: 40rpx;
  border-radius: 10rpx;
  width: auto;
  display: inline-flex;
  // overflow: hidden;

  .price-label {
    padding: 0 12rpx;
    height: 100%;
    background-color: #333333;
    color: #ffe7da;
    position: relative;
    width: auto;
    display: inline-block;
    border-radius: 12rpx 0 12rpx 12rpx;
    .price-card {
      width: 24rpx;
      height: 24rpx;
      position: absolute;
      right: -24rpx;
      top: 0;
      border-color: transparent;
      background: transparent;
      overflow: hidden;
      padding: 0;
      .price-card-wrap {
        width: 100%;
        height: 100%;
        background: #333333;
        z-index: 0;
      }
      .price-box {
        position: absolute;
        width: 48rpx;
        height: 48rpx;
        background-color: #feebe0;
        border-radius: 48rpx;
        bottom: -24rpx;
        right: -24rpx;
        z-index: 1;
      }
    }
  }
  .price-money {
    padding: 0 12rpx;
    font-size: 24rpx;
    color: #333333;
    z-index: 2;
  }
}
.charging .charging_head .middles {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 30rpx;
  box-sizing: border-box;
  border-bottom: 1rpx dotted #e1e1e1;
  .middle_type {
    min-width: 150rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .middle_number-box {
    display: flex;
    align-items: center;
    position: relative;
    width: calc(100% - 150rpx);
    box-sizing: border-box;
    height: 100%;
    gap: 8rpx;
    grid-gap: 8rpx;
    justify-content: flex-end;
    // view {
    //   flex: 1;
    // }
    .line {
      text-decoration: line-through;
    }
    .price-text {
      color: #999999;
    }
    .price-num {
      font-size: 24rpx;
      font-weight: bold;
      color: #ff9802;
    }
    .price-num.gray {
      color: #999999;
    }
  }
}

.charging .charging_middle {
  width: 100%;
  height: 234rpx;
  box-sizing: border-box;
  margin-left: 93rpx;
  display: flex;
  flex-wrap: wrap;
  align-content: space-around;
}

.charging .charging_middle .middle_list {
  width: calc(100% - 93rpx);
  font-size: 26rpx;
  color: #666;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 30rpx;
  box-sizing: border-box;
}

.charging .charging_middle .middle_list view {
  color: #2196f3;
}

.charging .charging_middle .middle_list view image {
  height: 20rpx;
  width: 24rpx;
  margin-left: 13rpx;
}

.charging .charging_middle .middle_list .inner {
  display: flex;
  align-items: center;
}

.charging .charging_middle .middle_list .inner .icon {
  line-height: 65rpx;
}
.charging .charging_middle .middle_list .overflows {
  width: 80%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  height: 100%;
  line-height: 80rpx;
}

.charging .charging_middles {
  /* width: calc(100% - 93rpx);  */
  width: 100%;
  box-sizing: border-box;
  /* margin-left: 93rpx; */
  display: flex;
  flex-wrap: wrap;
  align-content: space-around;
  font-size: 26rpx;
  color: #666;
  padding: 23rpx 0;
  padding-left: 86rpx;
}

.charging .charging_middles .middle_list {
  display: flex;
  width: 100%;
  align-content: center;
}

.charging .charging_middles .middle_list .list {
  height: 100%;
  padding: 23rpx 0;
  display: flex;
  font-size: 26rpx;
  color: #333;
  flex-wrap: wrap;
  box-sizing: border-box;
  justify-content: center;
  align-items: center;
  width: 20%;
}

.charging .charging_middles .middle_list .list image {
  display: block;
}

.charging .charging_middles .middle_list .list view {
  width: 100%;
  text-align: center;
  margin-top: 20rpx;
}

.charging .charging_middles .middle_list .list .image1 {
  width: 47rpx;
  height: 55rpx;
}

.charging .charging_middles .middle_list .list .image2 {
  width: 55rpx;
  height: 39rpx;
}

.charging .charging_middles .middle_list .list .image3 {
  width: 36rpx;
  height: 60rpx;
}

.charging .charging_middles .middle_list .list .image4 {
  width: 59rpx;
  height: 48rpx;
}

.charging .charging_middles .middle_list .list .image5 {
  width: 57rpx;
  height: 43rpx;
}

/* .charging  .charging_middles .middle_list .list  image{
  margin: 0 auto
} */
.activate-now {
  padding: 24rpx 24rpx 0 24rpx;
}

/* 检修状态横幅样式 */
.maintenance-banner {
  width: 100%;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  padding: 24rpx 30rpx;
  margin-top: 18rpx;
  box-sizing: border-box;
}

.maintenance-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.maintenance-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.maintenance-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 500;
  letter-spacing: 1rpx;
}

/* 检修状态遮罩容器 */
.maintenance-container {
  position: relative;
}

.maintenance-container.under-maintenance .content-area {
  position: relative;
  pointer-events: none;
  opacity: 0.6;
}

/* 检修状态遮罩层 */
.maintenance-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 999;
}
</style>
