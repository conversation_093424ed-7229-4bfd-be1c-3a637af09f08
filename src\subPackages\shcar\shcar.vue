<template>
  <view>
    <view>
      <view class="search-head">
        <text @tap="nav" class="icon iconfont icon-arrow_left search-lefy" size="30"></text>
        <input @input="inputs" placeholder="查找车型" />
        <text @tap="founds" class="icon iconfont icon-search" size="30"></text>
        <!-- <input placeholder='查找车型'></input>
    <icon class="icon iconfont icon-search" size="30"></icon> -->
      </view>
    </view>
    <view class="shCar">
      <view
        @tap="active"
        :data-msg="item.licenseNo"
        :data-index="index"
        :data-num="item.types"
        class="shCar_list"
        v-for="(item, index) in list"
        :key="index"
      >
        <text>{{ item.licenseNo }}</text>

        <!-- <icon class="icon iconfont changes icon-checked:{{item.types==true?'icon-checked':'icon-unchecked'}}"  size="10"></icon> -->

        <text
          :class="'icon iconfont changes ' + (item.types == true ? 'icon-checked' : 'icon-unchecked')"
          size="17"
        ></text>
      </view>
    </view>
    <loading v-if="!hiddenLoading">请稍后...</loading>
  </view>
</template>

<script>
import { getPref, selectLicenseNo } from '@/services/index.js';
export default {
  data() {
    return {
      list: [],
      licenseNo: '',
      orderNo: '',
      hiddenLoading: true,
      LT: '',
      a: false,
      z: false,
      mask: false,
      msg: '',
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */ onLoad(options) {
    var that = this;
    if (options.orderNo) {
      that.setData({
        orderNo: options.orderNo,
      });
      console.log('充电界面来的');
    } else {
      console.log('下单界面来的');
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},
  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    var paramd = {};
    var that = this;
    that.setData({
      hiddenLoading: false,
    });
    const [, res] = await getPref(paramd);
    if (res) {
      this.list = res.carList;
      this.LT = res.carList;
      this.hiddenLoading = true;
    } else {
      this.hiddenLoading = true;
    }
  },
  methods: {
    nav() {
      uni.navigateBack();
    },

    async active(e) {
      console.log(e.currentTarget.dataset.index, '索引');
      console.log(e.currentTarget.dataset.num, '点击状态');
      var that = this;
      that.setData({
        hiddenLoading: false,
      });
      var licenseNo = e.currentTarget.dataset.msg;
      var index = e.currentTarget.dataset.index;
      for (var i = 0; i < that.list.length; i++) {
        var a = 'list[' + i + '].types';
        that.setData({
          [a]: false,
        });
      }
      var z = 'list[' + index + '].types';
      that.setData({
        [z]: true,
        licenseNo: licenseNo,
        mask: false,
      });
      console.log(licenseNo, '当前选中车牌');
      uni.setStorageSync('licenseNoSH', licenseNo);
      if (that.orderNo != '') {
        console.log('需要进行车辆接口才返回');
        var paramd = {
          licenseNo: licenseNo,
          orderNo: that.orderNo,
        };
        const [, res] = await selectLicenseNo(paramd);
        if (res) {
          if (res.ret == 200) {
            that.setData({
              hiddenLoading: false,
            });
            setTimeout(function () {
              uni.navigateBack();
            }, 3000);
          } else {
            console.log(res, '炸了');
          }
        }
      } else {
        uni.setStorageSync('licenseNoOint', licenseNo);
        uni.navigateBack();
      }
    },

    founds() {
      var Msg = this.msg;
      console.log(Msg, '用户输入的值');
      var list = [];
      var that = this;
      that.setData({
        hiddenLoading: false,
      });
      if (!Msg) {
        console.log('空值');
        that.setData({
          list: that.LT,
          hiddenLoading: true,
        });
        console.log(that.LT, '_self.data.LT');
        return false;
      }
      for (var i = 0; i < that.LT.length; i++) {
        if (that.LT[i].licenseNo.indexOf(Msg) > -1) {
          list.push(that.LT[i]);
        }
      }
      that.setData({
        list: list,
        hiddenLoading: true,
      });
    },

    inputs(e) {
      console.log(e.detail.value);
      this.setData({
        msg: e.detail.value,
      });
      // var Msg = e.detail.value
      // var list = []
      // var _self = this
      // _self.setData({
      //   Msg: e.detail.value
      // });
      // if (!Msg) {
      //   console.log('空值')
      //   _self.setData({
      //     list: _self.data.LT
      //   });
      //   console.log(_self.data.LT, '_self.data.LT')
      //   return false
      // }

      // for (var i = 0; i < _self.data.LT.length; i++) {
      //   console.log(Msg, '这是搜索之')
      //   if (_self.data.LT[i].licenseNo.indexOf(Msg) > -1) {
      //     list.push(_self.data.LT[i])
      //   }
      //   console.log(list, '当前搜索到的')
      // }
      // _self.setData({
      //   list: list
      // });
    },
  },
};
</script>
<style>
@import './shcar.css';
</style>
