import { getHomeOrderApi } from "../../services/homeService"
import { chargeQrcodeApi, orderChargeControlsApi, cancelCrderChargeApi, getUnpaidOrderApi } from "../../services/orderService"
const state = {
    orderInfo: null,        //订单信息
    stationInfo: null,      //站点信息
    chargeWaitTimeInfo: {},        //订单操作计时  key订单号操作类型
    chargeErrorTimeInfo: {},       //订单错误次数  key订单号操作类型
    priceList: ['10', '50', '100'],  //默认价格表
    carInfo: {          //当前车牌信息
        licenseNo: null,     //车牌号
        prefId: null         //车辆id
    },
    unpaidOrderNo: '',    //待支付订单号
    unpaidPrice: '',      //待支付订单金额
    thirdCallBackOrderNo: ''  //第三方订单号
}
const getters = {
    /** 
     * 获取订单信息
    */
    getOrderInfo: state => {
        return state.orderInfo
    },
    /** 
     * 获取站点信息
    */
    getStationInfo: state => {
        return state.stationInfo
    },
    /** 
     * 获取订单操作等待时间
    */
    getChargeWaitTimeInfo: state => {
        return state.chargeWaitTimeInfo
    },
    /** 
     * 获取订单操作失败次数
    */
    getChargeErrorTimeInfo: state => {
        return state.chargeErrorTimeInfo
    },
    /** 
     * 获取价格列表
    */
    getPriceList: state => {
        return state.priceList
    },
    /** 
     * 获取当前车辆信息
    */
    getCarInfo: state => {
        return state.carInfo
    },
    /** 
     * 获取待支付订单信息
    */
    getUnpaidOrderInfo: state => {
        return {
            unpaidOrderNo: state.unpaidOrderNo,
            unpaidPrice: state.unpaidPrice
        }
    },

    getThirdCallBackOrderNo: state => {
        return state.thirdCallBackOrderNo
    }
}
const actions = {
    setThirdCallBackOrderNo ({ commit }, orderNo) {
        commit('SET_THIRD_CALLBACK_ORDERNO', orderNo)
    },
    /** 
     * 重置订单操作等待时间
    */
    resetChargeWaitTimeInfo ({ commit }, orderNo) {
        commit('REST_CHARGE_WAITTIME', orderNo)
    },
    /** 
     * 添加订单错误次数
    */
    addChargeErrorTimeInfo ({ commit }, orderNo) {
        commit('ADD_CHARGE_ERROR_TIME', orderNo)
    },
    /** 
     * 重置订单错误次数
    */
    resetChargeErrorTimeInfo ({ commit }, orderNo) {
        commit('REST_CHARGE_ERROR_TIME', orderNo)
    },
    /** 
     * 重置所有订单错误次数
    */
    resetAllChargeErrorTimeInfo ({ commit }, orderNo) {
        commit('REST_ALL_CHARGE_ERROR_TIME')
    },
    /** 
     * 获取首页订单信息
    */
    async initOrder ({ commit }, mobile) {
        try {
            const info = await getHomeOrderApi(mobile);
            if (info.orderInfo) {
                commit('SET_ORDERINFO', info.orderInfo);
                return info.orderInfo;
            }
            return
        } catch (error) {
            return Promise.reject(error)
        }
    },
    /** 
     * 启动充电
    */
    async startCharge ({ commit, dispatch, state }, orderNo) {
        try {
            // await dispatch('order/startWait', {}, { root: true });

            const info = await orderChargeControlsApi({ orderNo: orderNo, controlType: '01' });
            commit('ADD_CHARGE_WAITTIME', { orderNo: orderNo, type: 'start' })

            return info
        } catch (error) {
            commit('ADD_CHARGE_ERROR_TIME', { orderNo: orderNo, type: 'start' })

            return Promise.reject(error)
        }
    },
    /** 
     * 取消充电
    */
    async cancelCharge ({ commit, dispatch }, orderNo) {
        try {
            // await dispatch('order/startWait', {}, { root: true });

            const info = await orderChargeControlsApi({ orderNo: orderNo, controlType: '02' });
            commit('ADD_CHARGE_WAITTIME', { orderNo: orderNo, type: 'cancel' })

            return info
        } catch (error) {
            commit('ADD_CHARGE_ERROR_TIME', { orderNo: orderNo, type: 'cancel' })
            return Promise.reject(error)
        }
    },
    /** 
     * 扫码充电 获取站点信息
    */
    async chargeQrcode ({ commit, dispatch }, options) {
        try {
            console.log('这里是下单的入参options：', options)
            const localInfo = await dispatch('common/getLocation', false, { root: true });
            const params = {
                lon: localInfo.longitude,
                lat: localInfo.latitude,
                qrCode: options.qrCode,
                // qrCode: this.$store.state.login.qrcodes,
                // qrCode: options.qrCode.replace(/\D/g, ''),
            }

            if (options.tenantId) {
                params.tenantId = options.tenantId;
            }
            console.log(888888, params)
            const info = await chargeQrcodeApi(params);
            commit('SET_STATIONINFO', info);
            return info
        } catch (error) {
            return Promise.reject(error)
        }
    },
    /** 
     * 设置重置金额列表
    */
    setPriceList ({ commit }, option) {
        commit('SET_PRICE_LIST', option);
    },
    /** 
     * 设置当前车辆信息
    */
    setCarInfo ({ commit }, carInfo) {
        commit('SET_CARINFO', carInfo)
    },
    /** 
     * 设置待支付订单信息
    */
    setUnpaidOrderInfo ({ commit }, option) {
        commit('SET_UNIPAD_ORDER', {
            unpaidOrderNo: option.unpaidOrderNo,
            unpaidPrice: option.unpaidPrice
        });
    },
    async checkHasUnpaidOrder ({ commit }) {
        try {
            const status = await getUnpaidOrderApi();
            const { data: { orderInfo } } = status;
            if (orderInfo) {

                if (orderInfo.orderStatus == '05') {
                    if (orderInfo.applyMode == '01') {
                        uni.showModal({
                            title: '订单待支付',
                            showCancel: false,
                            content: '您有一个App订单待支付，请先完成支付。',
                            confirmText: "我知道了",
                            success: function (res) {
                                if (res.confirm) {
                                    console.log('用户点击我知道了');
                                } else if (res.cancel) {
                                    console.log('用户点击我知道了2');

                                }
                            }
                        });
                    }
                    // #ifdef  MP-ALIPAY
                    else if (orderInfo.applyMode == '07') {
                        uni.showModal({
                            title: '订单待支付',
                            showCancel: false,
                            content: '您有一个微信小程序订单待支付，请先完成支付。',
                            confirmText: "我知道了",
                            success: function (res) {
                                if (res.confirm) {
                                    console.log('用户点击我知道了');
                                } else if (res.cancel) {
                                    console.log('用户点击我知道了2');

                                }
                            }
                        });
                    }
                    // #endif

                    // #ifdef  MP-WEIXIN
                    else if (orderInfo.applyMode == '10') {
                        uni.showModal({
                            title: '订单待支付',
                            showCancel: false,
                            content: '您有一个支付宝小程序订单待支付，请先完成支付。',
                            confirmText: "我知道了",
                            success: function (res) {
                                if (res.confirm) {
                                    console.log('用户点击我知道了');
                                } else if (res.cancel) {
                                    console.log('用户点击我知道了2');

                                }
                            }
                        });
                    }
                    // #endif
                    else {
                        commit('SET_UNIPAD_ORDER', {
                            unpaidOrderNo: orderInfo.orderNo,
                            unpaidPrice: orderInfo.payAmt
                        });
                        return true
                    }

                }
            }
            return false
        } catch (error) {
            console.log('errorGet', error)
            return Promise.reject(error)
        }
    }
}


const mutations = {
    ['SET_THIRD_CALLBACK_ORDERNO'] (state, orderNo) {
        state.thirdCallBackOrderNo = orderNo;
    },
    ['SET_ORDERINFO'] (state, time) {
        state.orderInfo = time;
    },
    ['SET_PRICE_LIST'] (state, list) {
        state.priceList = list;
    },
    ['SET_CARINFO'] (state, carInfo) {
        state.carInfo = carInfo;
    },
    ['SET_UNIPAD_ORDER'] (state, option) {
        state.unpaidOrderNo = option.unpaidOrderNo;
        state.unpaidPrice = option.unpaidPrice;
    },
    ['SET_STATIONINFO'] (state, station) {
        state.stationInfo = station;
    },
    ['ADD_CHARGE_WAITTIME'] (state, orderInfo) {
        const date = new Date();
        let time = date.getTime();
        for (const key in state.chargeWaitTimeInfo) {
            if (state.chargeWaitTimeInfo.hasOwnProperty(key)) {
                const element = state.chargeWaitTimeInfo[key];
                if (element < time) {
                    state.chargeWaitTimeInfo[key] = null;
                    delete state.chargeWaitTimeInfo[key];
                }
            }
        }
        let waitTime = 90;
        switch (orderInfo.type) {
            case 'start':
                waitTime = 90;
                break;
            case 'cancel':
                waitTime = 60;
                break;
            default:
                break;
        }
        time += (Number(waitTime) * 1000);
        console.log(5555, `${orderInfo.type}_${orderInfo.orderNo}`)
        state.chargeWaitTimeInfo[`${orderInfo.type}_${orderInfo.orderNo}`] = time;
    },
    ['DEL_CHARGE_WAITTIME'] (state, orderInfo) {
        delete state.chargeWaitTimeInfo[`${orderInfo.type}_${orderInfo.orderNo}`];
    },
    ['REST_CHARGE_WAITTIME'] (state, orderNo) {
        delete state.chargeWaitTimeInfo[`start_${orderNo}`];
        delete state.chargeWaitTimeInfo[`cancel_${orderNo}`];
    },
    ['ADD_CHARGE_ERROR_TIME'] (state, orderInfo) {
        let orderKey = `${orderInfo.type}_${orderInfo.orderNo}`;
        if (!state.chargeErrorTimeInfo[orderKey]) {
            state.chargeErrorTimeInfo[orderKey] = 0;
        }
        state.chargeErrorTimeInfo[orderKey] = state.chargeErrorTimeInfo[orderKey] + 1;
    },
    ['REST_CHARGE_ERROR_TIME'] (state, orderNo) {
        delete state.chargeErrorTimeInfo[`start_${orderNo}`];
        delete state.chargeErrorTimeInfo[`cancel_${orderNo}`];
    },
    ['REST_ALL_CHARGE_ERROR_TIME'] (state) {
        state.chargeErrorTimeInfo = {}
    },
}
export const order = {
    namespaced: true,
    state,
    getters,
    actions,
    mutations
}