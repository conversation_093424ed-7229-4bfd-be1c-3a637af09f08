<template>
  <view>
    <!-- pages/yhj.wxml -->
    <view>
      <!-- <view class='list'>
    <view class='listL'>
    <text class='net1'>充电30元送10元优惠券</text>
    <text class='net2'>有效期：2017-05-12～2017-05-31</text>
    <text class='net3'>按金额，消费满10元可用</text>
    </view>
    <view class='listR'>
    <image src='../../image/bg_discount_red_2.png'></image>
     <view>10 < text>元</text></view>
     <view class='notte'>立即领取</view>
    </view>
  </view> -->

      <view @tap="push" :data-cpnId="item.cpnId" class="list" v-for="(item, index) in cpnList" :key="index">
        <view class="listL">
          <text class="net1">{{ item.cpnName }}</text>
          <text class="net2">有效期：{{ item.timeLimit }}</text>
          <text class="net3">{{ item.cpnMarks }}</text>
        </view>

        <view class="listR">
          <image :src="`${IMG_PATH}bg_discount_red_2.png`"></image>
          <view>{{ item.cpnAmt }}</view>
          <view class="notte">立即领取</view>
        </view>
      </view>
    </view>
    <view v-if="cpnList.length == 0" class="nothImg" @tap="push">
      <image :src="`${IMG_PATH}icon-empty.png`"></image>
      <text>暂无可领优惠券</text>
    </view>
  </view>
</template>

<script>
import { getCouponReceive, getMsgUnused } from '@/services/index.js';
export default {
  data() {
    return {
      cpnList: [],
    };
  },
  onShow() {
    this.tab0();
  },
  methods: {
    jump() {
      uni.navigateTo({
        url: '/subPackages/gocup/gocup',
      });
    },
    /**
     * 生命周期函数--监听页面显示
     */
    async push(e) {
      var that = this;
      console.log(e.currentTarget.dataset.cpnid);
      var parmes1 = {
        mobile: '',
        getChannel: '02',
        cpnId: e.currentTarget.dataset.cpnid,
      };
      const [, res] = await getCouponReceive(parmes1);
      if (res) {
        uni.showToast({
          title: '领取成功',
          icon: 'success',
          duration: 1200,
        });
        that.tab0();
      }
    },

    async tab0() {
      var that = this;
      var parmes1 = {
        prodBusiType: '02',
      };
      const [, res] = await getMsgUnused(parmes1);
      if (res) {
        that.setData({
          cpnList: res.cpnList,
        });
      }
    },
  },
};
</script>
<style>
@import './gocup.css';
</style>
