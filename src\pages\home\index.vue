<template>
  <view>
    <!-- index.wxml -->
    <!-- #ifdef MP-WEIXIN -->
    <view class="banner" :style="'padding-top:' + s_top + 'rpx;line-height:' + s_height + 'rpx'">
      <view class="top-title">电动宁德</view>
    </view>
    <!-- #endif -->
    <!-- <div class="top-banner-bg" :style="{ backgroundImage: `url(${IMG_PATH_UI}bg.png)` }"></div> -->
    <view class="index-header">
      <view>宁德</view>
      <view class="header-inp" @tap="goSearch">
        <image class="inp-icon" :src="`${IMG_PATH}index-search.png`"></image>
        <view>搜索站点</view>
      </view>
      <image class="inp-service" @tap="goCustomer" :src="`${IMG_PATH}index-service.png`"></image>
      <div class="msg-wrap">
        <!-- <image class="inp-service" @tap="goEnvelope" :src="`${IMG_PATH}envelope.png`"></image> -->
        <!-- <view class="text-num" v-if="msgTotal > 0"> {{ msgTotal > 99 ? '99+' : msgTotal }} </view> -->
      </div>
    </view>

    <view class="model-box" :style="'height:' + (s_top + s_height + 102) + 'rpx;'">
      <image class="model-box-img" mode="widthFix" :src="`${IMG_PATH_UI}bg.png`"></image>
    </view>
    <!-- <view class="station-tab fixed-tab" v-if="ifFixed" :style="{ top: ifFixed ? `${s_top + s_height + 102}rpx` : '0' }">
      <view class="tab-ul">
        <view
          :class="'tab-li ' + (currentIndex === item.currentItem ? 'tab-li-checked' : '') + ' '"
          @tap="currentChange"
          :data-current="item.currentItem"
          v-for="(item, index) in tabList"
          :key="index"
        >
          {{ item.text }}
          <view v-if="currentIndex === item.currentItem" class="li-line-checked"></view>
        </view>
      </view>
      <view class="tab-more" @tap="goMap">
        <view>更多场站</view>
        <image class="more-right" :src="`${IMG_PATH}more-station.png`"></image>
      </view>
    </view> -->
    <view class="index-content">
      <view class="index-banner" v-if="bannerList.length > 0">
        <swiper
          style="height: 280rpx"
          :indicator-dots="bannerList.length > 1"
          indicator-color="rgba(0, 0, 0, .3)"
          indicator-active-color="#fff"
          :autoplay="autoplay"
          :interval="interval"
          :duration="duration"
        >
          <block v-for="(item, index) in bannerList" :key="index">
            <swiper-item>
              <image class="banner-img" :src="item.imgUrl" @click="jumpToLink(item.contentUrl)"></image>
            </swiper-item>
          </block>
        </swiper>
      </view>

      <view class="index-func">
        <view class="func-li" v-for="(item, index) in funcList" :key="index" @click="toCallback(item)">
          <image class="li-icon" :src="item.icon"></image>

          <view class="li-text">{{ item.text }}</view>
          <view class="li-text-num" v-if="msgTotal > 0 && item.text == '我的消息'">
            {{ msgTotal > 99 ? '99+' : msgTotal }}
          </view>
        </view>
      </view>
      <view class="index-station">
        <view class="station-tab">
          <view class="tab-ul">
            <view
              :class="'tab-li ' + (currentIndex === item.currentItem ? 'tab-li-checked' : '') + ' '"
              @tap="currentChange"
              :data-current="item.currentItem"
              v-for="(item, index) in tabList"
              :key="index"
            >
              {{ item.text }}

              <view v-if="currentIndex === item.currentItem" class="li-line-checked"></view>
            </view>
          </view>
          <view class="tab-more" @tap="goMap">
            <view>更多场站</view>
            <image class="more-right" :src="`${IMG_PATH}more-station.png`"></image>
          </view>
        </view>
        <view class="station-ul">
          <station-li
            :memberInfo="memberInfo"
            :currentItem="item"
            v-for="(item, index) in distanceCup"
            :key="index"
          ></station-li>
          <view v-if="distanceCup.length === 0" class="myorder-image">
            <image :src="`${IMG_PATH}main-data-null.png`" mode="heightFix" />
            <text>暂无数据</text>
          </view>
        </view>
      </view>
      <custom-nav-bar :show="showCard" currentNavbar="indexPage"></custom-nav-bar>
    </view>
    <!-- <image class="mine-bg" :src="`${IMG_PATH}bg.png`"></image> -->
    <view class="mine-charging" v-if="orderNo" @tap="goChargeIng">
      <view class="charging-content">
        <image class="charging-ball" :src="`${IMG_PATH}index-charging.png`"></image>
        <view class="charging-text">充电中</view>
      </view>
    </view>
    <!-- <loading v-if="!hiddenLoading">正在加载</loading> -->

    <swiper
      v-if="imgBannerType"
      class="modal-alert"
      :indicator-dots="indicatorDots"
      :autoplay="autoplay"
      :interval="interval"
      :duration="duration"
    >
      <block v-for="(item, index) in imgBannerList" :key="index">
        <swiper-item style="text-align: center">
          <image
            class="modal-alert-images"
            style="border-radius: 36rpx"
            @tap="imgUrlrouter"
            :data-url="item.contentUrl"
            :src="item.imgUrl"
          ></image>
        </swiper-item>
      </block>
    </swiper>
    <image v-if="imgBannerType" class="slide-cover-close" @tap="moveBannerType" :src="`${IMG_PATH}delect.png`"></image>
    <guideStep
      v-if="step"
      :step="step"
      ref="guideStep"
      class="home-guid"
      :showArrow="false"
      tipStyle="background: transparent;"
    >
      <template slot-scope="{ guideInfo }">
        <div class="tips-box">
          <div class="text">{{ guideInfo.tips }}</div>
          <div class="img-wrap"></div>
          <image v-if="guideInfo.icon" :src="guideInfo.icon" mode="heightFix" />
        </div>
      </template>
      <template slot="btn" slot-scope="{ guideInfo }">
        <div class="btn-wrap">
          <text @click="triggerGuide('skip')" v-if="!guideInfo.noJump" class="btn-item">跳过</text>
          <view class="btn-item primary" style="" @click="triggerGuide('next')">{{ guideInfo.next }}</view>
        </div>
      </template>
    </guideStep>
    <!-- <div class="black" :style="{ height: distanceCup.length > 4 ? '0' : '400rpx' }"></div> -->
  </view>
</template>

<script>
import { mapState } from 'vuex';
import customNavBar from '@/components/custom-navbar/index';
import stationLi from '@/components/station-li/index';
import guideStep from '@/components/xky-guideStep/xky-guideStep.vue';
import { startsWithHttp } from '../../utils/util';
import { getLocation } from '@/utils/index.js';
//index.js
//获取应用实例
import {
  getMsgUnusedTotal,
  getMsgUnused,
  getHomeBanner,
  getChargingOrder,
  getV06ChargingStation,
  getCollectChargingStation,
  getMemberInfo,
} from '@/services/index.js';
export default {
  components: {
    customNavBar,
    stationLi,
    guideStep,
  },
  data() {
    return {
      showCard: false,
      s_top: '',
      s_height: '',
      vertical: false,
      autoplay: false,
      interval: 2000,
      duration: 500,
      tabList: [
        {
          text: '推荐',
          currentItem: 0,
        },
        { text: '收藏', currentItem: 1 },
        { text: '会员', currentItem: 2 },
      ],
      longitude: '',
      latitude: '',
      hiddenLoading: false,
      distanceCup: [],
      orderNo: '',
      imgBannerType: false,
      currentIndex: 0,
      imgBannerList: [],
      indicatorDots: true,
      vertical: false,
      autoplay: true,
      interval: 2000,
      duration: 500,
      NosCant: false,
      step: undefined,
      bannerList: [], // banner列表
      ifFixed: false,
      msgTotal: 0,
      memberInfo: null,
    };
  },
  async mounted() {
    const that = this;
    const res = await getLocation();
    if (res) {
      console.log(res, 'res');
      that.longitude = res.longitude;
      that.latitude = res.latitude;
      that.loadDate();
    } else {
      console.log('获取定位失败');
      that.hiddenLoading = true;
    }
  },
  onShow() {
    // this.getCustinfo();
    // this.getChargingOrder();
    this.getMemberInfo();
    // #ifdef MP
    this.initTopImg();
    // #endif
    this.getHomeBanner();
    this.banneShow();
    this.getCoupons();
    this.getNotUseCoupons();
    // if (this.userInfo?.mobile) {
    this.getChargingOrder();
    // }
    this.$nextTick(() => {
      this.step = {
        name: 'banner-title',
        repeat: false,
        guideList: [
          {
            el: '.header-inp',
            tips: '搜索站点信息,查找充电站',
            style: 'border-radius: 8rpx;margin: 0',
            next: '下一步',
            icon: this.IMG_PATH + 'icon-magnifier.png',
          },
          {
            el: '.index-func',
            tips: '快捷功能入口，方便进行多项操作',
            style: 'border-radius: 8rpx;margin: 0',
            next: '下一步',
            icon: this.IMG_PATH + 'icon-rocket.png',
          },
          {
            el: '.tab-ul',
            tips: '支持查看推荐站点和收藏站点',
            style: 'border-radius: 8rpx;margin: 0',
            next: '完成',
            noJump: true,
            icon: this.IMG_PATH + 'icon-car.png',
          },
        ],
      };
    });
  },
  computed: {
    ...mapState({
      userInfo: (state, getters) => getters['login/getUserInfo'],
    }),
    funcList() {
      return [
        {
          icon: this.IMG_PATH_UI + 'index-icon1.png',
          text: '账户充值',
          methods: this.goRecharge,
        },
        {
          icon: this.IMG_PATH_UI + 'index-icon2.png',
          text: '电子发票',
          methods: this.goInvoiceopening,
        },
        {
          // icon: this.IMG_PATH_UI + 'icon-order.png',
          icon: this.IMG_PATH_UI + 'index-icon-order.png',
          text: '我的订单',
          methods: this.toMyOrder,
        },
        // {
        //   icon: this.IMG_PATH_UI + 'index-icon3.png',
        //   text: '优惠活动',
        //   methods: this.waitOpen,
        // },
        {
          icon: this.IMG_PATH_UI + 'index-icon4.png',
          text: '意见反馈',
          methods: this.goSevice,
        },
        {
          icon: this.IMG_PATH_UI + 'index-icon6.png',
          text: '我的消息',
          methods: this.waitOpen,
        },
        // {
        //   icon: this.IMG_PATH_UI + 'index-icon5.png',
        //   text: '更多',
        //   methods: this.waitOpen,
        // },
      ];
    },
    // bannerList() {
    //   return [this.IMG_PATH + 'index-banner1.png'];
    // },
  },
  methods: {
    getScan(val) {
      console.log('扫码成功', val);
      alert(val);
    },
    err(err) {
      console.log(err);
      alert(err);
    },
    // 获取会员信息
    async getMemberInfo() {
      const [err, res] = await getMemberInfo();
      if (res) {
        this.memberInfo = res;
      } else {
        this.memberInfo = {
          vipFlag: '0',
        };
      }
    },
    // 获取未使用的优惠券数量
    async getNotUseCoupons() {
      const [, res] = await getMsgUnusedTotal({
        msgType: '0103,0201,0301',
      });
      console.log(res, 'res');
      if (res) {
        this.msgTotal = res?.unReadCount || 0;
      }
    },
    // 获取是否有可以领取的优惠券
    async getCoupons() {
      var that = this;
      var parmeType = {
        prodBusiType: '02',
      };
      const [, res] = await getMsgUnused(parmeType);
      if (res) {
        if (res.cpnList.length > 0) this.showCard = true;
      }
    },
    /**
     * 获取首页 banner
     *
     */
    async getHomeBanner() {
      let that = this;
      const [, res] = await getHomeBanner();
      if (res && res) {
        const {
          homeMsgObj: { bannerList },
        } = res;
        that.bannerList = bannerList;
      }
    },
    jumpToLink(url) {
      if (!url) return;
      const jumpUrl = startsWithHttp(url);
      const encodedUrl = encodeURIComponent(jumpUrl);
      uni.navigateTo({
        url: '/pages/setting/out/index' + '?url=' + encodedUrl + '&title=' + '',
      });
    },
    toCallback(item) {
      console.log(item.methods, item);
      item.methods && item.methods(item);
    },
    initTopImg() {
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      this.setData({
        s_top: menuButtonInfo.top * 2,
        s_height: menuButtonInfo.height * 2,
      });
    },
    imgUrlrouter(e) {
      let url = e.target.dataset.url;
      if (url) {
        uni.navigateTo({
          url: '/pages/setting/out/index?url=' + encodeURIComponent(JSON.stringify(url)),
        });
      }
    },
    async banneShow() {
      var _self = this;
      if (uni.getStorageSync('banneStatus') == '1') {
        const [, res] = await getHomeBanner();
        if (res) {
          if (res.homeMsgObj && res.homeMsgObj.popList.length > 0) {
            let imgList = res.homeMsgObj.popList || [];
            _self.imgBannerType = true;
            _self.imgBannerList = imgList || [];
            uni.setStorageSync('banneStatus', '0');
          }
        }
      }
    },

    moveBannerType() {
      this.imgBannerType = false;
    },
    // 获取充电中的订单
    async getChargingOrder() {
      const [, res] = await getChargingOrder();
      if (res?.ret === 200) {
        const { ret, orderNo } = res;
        this.orderNo = orderNo ? orderNo.split(',')[0] : '';
      }
    },

    goChargeIng() {
      uni.navigateTo({
        url: `/pages/basic/charge/charge?orderNo=${this.orderNo}`,
      });
    },

    // getCustinfo() {
    //   const _self = this;
    //   if (_self.userInfo.mobile) {
    //     _self.getChargingOrder();
    //   }
    // },
    // 获取站点
    async loadDate() {
      const _self = this;
      const { longitude, latitude, currentIndex = 0 } = this;
      const params = {
        orderType: '02',
        positionLon: longitude,
        positionLat: latitude,
        pageNum: 1,
        totalNum: 10,
        ...(currentIndex === 2
          ? {
              memberStationFlag: '1',
            }
          : {}),
      };
      const [, res] = await getV06ChargingStation(params);
      if (res) {
        this.distanceCup = res.chcList;
        this.hiddenLoading = true;
        this.$nextTick(() => {
          if (this.ifFixed) {
            uni.pageScrollTo({
              scrollTop: this.s_top + this.s_height + 80,
            });
          }
        });
      } else {
        this.distanceCup = [];
      }
    },
    async loadCollect() {
      const { longitude, latitude, currentIndex = 0 } = this;
      const [, res] = await getCollectChargingStation({
        prefType: '01',
        positionLon: longitude,
        positionLat: latitude,
      });
      if (res) {
        if (res.ret === 200) {
          this.distanceCup = res.prefList.map((item) => item.stationInfo);
        } else {
          this.distanceCup = [];
        }
        this.$nextTick(() => {
          if (this.ifFixed) {
            uni.pageScrollTo({
              scrollTop: this.s_top + this.s_height + 80,
            });
          }
        });
      }
    },

    currentChange(event) {
      const { current } = event.currentTarget.dataset;
      const { currentIndex } = this;
      if (currentIndex !== current) {
        this.currentIndex = current;
        if (current === 0) {
          this.loadDate();
        }
        if (current === 1) {
          this.loadCollect();
        }
        if (current === 2) {
          this.loadDate();
        }
      }
    },

    goRecharge() {
      console.log(this.userInfo, 'this.userInfo');
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '/pages/login/login',
        });
      } else {
        uni.navigateTo({
          url: '/subPackages/recharge/recharge',
        });
      }
    },

    goFeedback() {
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '/pages/login/login',
        });
      } else {
        uni.navigateTo({
          url: '/subPackages/feedback/index',
        });
      }
    },
    goSevice() {
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '/pages/login/login',
        });
      } else {
        uni.navigateTo({
          url: '/subPackages/feedback/index',
        });
      }
    },
    // 客服
    goCustomer() {
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '/pages/login/login',
        });
      } else {
        uni.navigateTo({
          url: '/subPackages/customerService/index',
        });
      }
    },
    // 站内消息
    goEnvelope() {
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '/pages/login/login',
        });
      } else {
        uni.navigateTo({
          url: '/subPackages/envelope/index',
        });
      }
    },
    goInvoiceopening() {
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '/pages/login/login',
        });
      } else {
        uni.navigateTo({
          url: '/subPackages/invoiceopening/invoiceopening',
        });
      }
    },

    waitOpen() {
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '/pages/login/login',
        });
      } else {
        uni.navigateTo({
          url: '/subPackages/envelope/index',
        });
      }
    },
    toMyOrder() {
      console.log(this.userInfo?.mobile, 'tomy');
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '/pages/login/login',
        });
      } else {
        uni.navigateTo({
          url: '/pages/basic/myorder/myorder',
        });
      }
      // if (!this.userInfo?.mobile) {
      //   uni.navigateTo({
      //     url: '/pages/login/login',
      //   });
      // } else {
      //   uni.navigateTo({
      //     url: '/subPackages/myorder/myorder',
      //   });
      // }
    },
    goMap() {
      uni.redirectTo({
        url: '/pages/basic/mapStation/map?showList=true',
      });
    },

    goSearch() {
      uni.navigateTo({
        url: '/pages/basic/stationcup/stationcup',
      });
    },
    cons(next, skip, guideInfo) {},
    triggerGuide(type) {
      this.$refs.guideStep && this.$refs.guideStep[type]();
    },
  },
  onPageScroll(e) {
    const scrollTop = e.scrollTop;
    const top = this.s_top + this.s_height + 70;
    this.ifFixed = scrollTop > top;
  },
};
</script>
<style lang="scss" scoped>
page {
  background: #f6f7f9;
}

.top-banner-bg {
  position: fixed;
  top: 0;
  z-index: 12;
  background-size: 100% auto;
  height: 280rpx;
  width: 100%;
}
.black {
  width: 100%;
}
.model-box {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  background: #fff;
  .model-box-img {
    width: 100%;
  }
}
.fixed-tab {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 15;
  padding: 20rpx 20rpx;
  box-sizing: border-box;
  height: 100rpx !important;
}
.tips-box {
  background: linear-gradient(168deg, #1e9cff 16%, #0d87e8 70%);
  padding: 16rpx 32rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0px;
  color: #ffffff;
  position: relative;
  display: flex;
  flex-direction: row;
  box-sizing: content-box;
  .text {
    width: auto;
    max-width: 500rpx;
    white-space: nowrap;
  }
  .img-wrap {
    padding: 0 40rpx;
  }
  image {
    position: absolute;
    height: 100rpx;
    right: 20rpx;
    bottom: 0;
  }
}
.btn-wrap {
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
  margin: 15rpx 0;
  .btn-item {
    margin-right: 15rpx;
    height: 59rpx;
    border-radius: 100rpx;
    box-sizing: border-box;
    font-size: 28rpx;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0px;
    color: #ffffff;
    border: 1px solid #ffffff;
    padding: 10rpx 24rpx;
  }
  .primary {
    background: #1e9cff;
    border-color: #1e9cff;
  }
}
::v-deep .guide.guide-step-tips {
  background: red !important;
}
.banner {
  padding: 20rpx 20rpx 20rpx;
  display: flex;
  align-items: center;
  width: 100vw;
  box-sizing: border-box;
  position: relative;
  z-index: 1;
}

.top-title {
  font-family: 'PingFang SC';
  font-size: 36rpx;
  font-style: normal;
  font-weight: 500;
  flex: 1;
  text-indent: 4rpx;
  color: #000000;
  text-align: left;
  font-weight: 700;
}

.banner-title {
  font-family: 'PingFang SC';
  font-size: 36rpx;
  font-style: normal;
  font-weight: 500;
  flex: 1;
  text-indent: 4rpx;
  color: #000000;
}
.mine-bg {
  width: 100vw;
  height: 600rpx;
  position: fixed;
  left: 0;
  top: 0;
  z-index: -1;
}
.index-content {
  position: relative;
  z-index: 3;
  padding-top: 40rpx;
  padding-bottom: 180rpx;
}
.index-header {
  display: flex;
  align-items: center;
  font-family: PingFang SC;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  margin: 0 24rpx;
  line-height: 42rpx;
  text-align: center;
  color: #333333;
  z-index: 2;
  position: relative;
  /* #ifdef H5 */
  margin-top: 15rpx;
  /* #endif */
}
.header-inp {
  flex: 1;
  height: 64rpx;
  border-radius: 30rpx;
  padding: 0 24rpx;
  background: #ffffff;
  box-sizing: border-box;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  font-family: PingFang SC;
  font-size: 30rpx;
  font-weight: normal;
  color: #999999;
  display: flex;
  align-items: center;
  margin-left: 28rpx;
  margin-right: 18rpx;
}
.inp-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}
.inp-service {
  width: 44rpx;
  height: 44rpx;
  margin: 0 10rpx;
}
.msg-wrap {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  .text-num {
    position: absolute !important;
    top: -5rpx;
    right: -5rpx;
    height: 30rpx;
    width: 30rpx;
    border-radius: 50%;
    background: red;
    font-size: 20.34rpx;
    font-weight: 500;
    color: #fff;
    text-align: center;
    width: auto !important;
    padding: 0 10rpx;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    justify-content: center;
  }
}
.index-banner {
  margin: 32rpx 24rpx;
  border-radius: 4rpx;
  overflow: hidden;
}
.banner-img {
  height: 280rpx;
  width: 100%;
}
.index-func {
  margin: 0 24rpx 32rpx;
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
}
.func-li {
  flex: 1;
  min-width: 115rpx;

  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin: 0 8rpx;
  position: relative;
}
.li-text-num {
  position: absolute;
  right: 0;
  top: 0;
  border-radius: 50%;
  background: red;
  font-size: 20.34rpx;
  font-weight: 500;
  color: #fff;
  text-align: center;
  width: auto !important;
  padding: 10rpx;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  justify-content: center;
}
.li-icon {
  width: 72rpx;
  height: 72rpx;
}
.li-text {
  margin-top: 16rpx;
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: normal;
  line-height: 32rpx;
  text-align: center;
  color: #333333;
  width: 100%;
}
.func-li:first-child {
  margin-left: 0;
}
.func-li:last-child {
  margin-right: 0;
}
.index-station {
  margin: 0 24rpx;
}
.station-tab {
  display: flex;
  justify-content: space-between;
  height: 42rpx;
  align-items: center;
}
.tab-more {
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: normal;
  color: #2196f3;
  display: flex;
  align-items: center;
  line-height: 32rpx;
  text-align: right;
}
.tab-ul {
  display: flex;
  align-items: center;
}
.tab-li {
  font-family: PingFang SC;
  font-size: 28rpx;
  font-weight: normal;
  line-height: 42rpx;
  text-align: center;
  color: #666666;
  margin-right: 32rpx;
  position: relative;
}
.tab-li-checked {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}
.li-line-checked {
  position: absolute;
  left: 0rpx;
  bottom: 0rpx;
  height: 14rpx;
  border-radius: 2rpx;
  opacity: 0.5;
  background: #1390f394;
  // filter: blur(4rpx);
  width: 66rpx;
}
.more-right {
  width: 24rpx;
  height: 24rpx;
  margin-left: 4rpx;
}
.station-ul {
  margin-top: 5rpx;
}
.mine-charging {
  right: 24rpx;
  position: fixed;
  bottom: calc(240rpx + env(safe-area-inset-bottom));
  z-index: 11;
  width: 100rpx;
  height: 100rpx;
}
.charging-ball {
  width: 100rpx;
  height: 100rpx;
  position: absolute;
  left: 0;
  top: 0;
}
.charging-content {
  position: relative;
  width: 100rpx;
  height: 100rpx;
}
.charging-text {
  padding-top: 76rpx;
  text-align: center;
  font-family: PingFang SC;
  font-size: 16rpx;
  font-weight: normal;
  line-height: 20rpx;
  color: #ffffff;
  position: relative;
  z-index: 1;
}
.myorder-image {
  text-align: center;
  color: #b2b2b2;
  font-size: 32rpx;
  margin-top: 120rpx;
}
.myorder-image image {
  display: block;
  height: 250rpx;
  margin: 0 auto 20rpx auto;
}
.modal-alert {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0rpx;
  padding-top: 22%;
  z-index: 6667;
  background-color: rgba(0, 0, 0, 0.75);
}
.modal-alert .modal-alert-images {
  width: 630rpx;
  height: 844rpx;
  margin-top: 100rpx;
}
.cover-view .slide-view {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  z-index: 88;
}
.slide-cover-close {
  width: 80rpx;
  height: 80rpx;
  display: inline-block;
  margin: 68rpx auto 0 auto;
  border-radius: 50%;
  z-index: 88;
  cursor: pointer;
  position: fixed;
  bottom: 14vh;
  z-index: 6668;
  left: 45%;
}
</style>
