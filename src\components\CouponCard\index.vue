<!--
@name: 优惠券
@description: 优惠券组件
@time: 2024/9/2
-->
<template>
  <view class="list">
    <view class="listL">
      <text class="net1">{{ cardInfo.cpnName }}</text>
      <text class="net2">有效期：{{ cardInfo.timeLimit }}</text>
      <text class="net3">{{ cardInfo.cpnMarks }}</text>
    </view>
    <view class="listR">
      <!-- <image :src="`${IMG_PATH}bg_discount_red_2.png`"></image> -->
      <view
        >{{ cardInfo.cpnAmt }}
        <span class="unit">张</span>
      </view>
      <!-- <img :src="`${IMG_PATH}/bg_discount_red_2.png`" v-if="unUseFlag" />
      <img :src="`${IMG_PATH}/bg_unUseFlag.png`" v-else />
      <view>{{ cardInfo.cpnAmt }}</view>
      <view class="notte" v-if="redeemButtonFlag">立即领取</view> -->
    </view>
  </view>
</template>

<script>
export default {
  props: {
    cardInfo: {
      type: Object,
      default: () => {},
    },
    unUseFlag: {
      type: Boolean,
      default: true,
    },
    redeemButtonFlag: {
      type: Boolean,
      default: true,
    },
    pageName: {
      type: String,
      default: '',
    },
  },
  components: {},
  data() {
    return {};
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.list {
  width: 690rpx;
  height: 214rpx;
  background: #fff;
  margin: 0 auto;
  margin-top: 30rpx;
  box-sizing: border-box;
  display: flex;
}

.list .listL {
  width: 462rpx;
  height: 100%;
  display: flex;
  box-sizing: border-box;
  padding: 40rpx 28rpx;
  align-content: space-between;
  flex-wrap: wrap;
}

.list .listL text {
  color: #666;
  font-size: 24rpx;
  display: block;
  width: 100%;
}

.list .listL .net1 {
  font-size: 28rpx;
  color: #666;
}

.list .listL .net2 {
  color: #666;
  font-size: 24rpx;
}

.list .listL .net3 {
  color: #999;
  font-size: 24rpx;
}

.list .listR {
  width: 228rpx;
  height: 100%;
  background: #3297f3;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  position: relative;
}

.list .listR view {
  font-size: 54rpx;
  color: #fff;
  z-index: 2;
  text-align: center;
  display: flex;
  align-items: center;
  .unit {
    font-size: 40rpx;
  }
}

.list .listR view text {
  font-size: 24rpx;
  padding-left: 10rpx;
}

.list .listR image {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
  left: 0;
  top: 0;
}
</style>
