<template>
  <view class="mine-page">
    <!-- <custom-head title="诉求反馈" isback /> -->
    <view class="form-item align-center">
      <view class="label">
        <text>*</text>
        <text>问题分类</text>
      </view>
      <view class="value">
        <picker :range="feedbackTypeList" range-key="typeName" @change="pickerChange" :disabled="!!faqId">
          <view class="align-center justify-end">
            <text>{{ form.feedbackType || '请选择问题类型' }}</text>
            <image :src="`${IMG_PATH}right_arrow.png`" class="right_arrow" mode="aspectFill" />
          </view>
        </picker>
      </view>
    </view>

    <view class="form-item align-center">
      <view class="label">
        <text>*</text>
        <text>问题标题</text>
      </view>
      <view class="value">
        <input
          :value="form.title"
          @input="inputChange"
          data-name="title"
          placeholder="请输入问题标题"
          placeholder-class="placeholder"
          :disabled="!!faqId"
        />
      </view>
    </view>

    <view class="form-item align-center" v-show="!faqId">
      <view class="label">
        <text style="opacity: 0">*</text>
        <text>相关场站</text>
      </view>
      <view class="value" @click="tabs">
        <view class="align-center justify-end">
          <text>{{ form.station.stationName || '请选择相关场站' }}</text>
          <image :src="`${IMG_PATH}right_arrow.png`" class="right_arrow" mode="aspectFill" />
        </view>
      </view>
    </view>

    <view class="form-item align-center" style="margin-bottom: 0; border-radius: 16rpx 16rpx 0 0">
      <view class="label">
        <text>*</text>
        <text>问题详情</text>
      </view>
      <view class="value">
        <view class="placeholder">{{ form.content ? form.content.length : 0 }}/500</view>
      </view>
    </view>
    <view class="form-item align-center noheight" style="border-radius: 0; margin-bottom: 0; padding-top: 0">
      <textarea
        :value="form.content"
        @input="inputChange"
        data-name="content"
        placeholder="请详细描述您遇到的问题，我们会尽快处理（限500）"
        placeholder-class="placeholder"
        maxlength="500"
      />
    </view>

    <view class="form-item noheight" style="border-radius: 0 0 16rpx 16rpx; padding-top: 0">
      <view class="label grey">相关问题的截图或照片（可选）</view>

      <view class="align-center imgUpload flex-wrap">
        <l-upload
          style="width: 100%"
          @complete="imgChange"
          :limit="3"
          uploadType="img"
          width="160"
          height="160"
          :formData="imgFormData"
          :header="imgHeader"
          :sourceType="['album']"
        ></l-upload>
        <!-- <view v-for="(item, index) in form.faqImageList" :key="index">
          <image :src="item.image" mode="aspectFill" />

          <text @tap="delImg" :data-key="index">X</text>
        </view>
        <view @tap="chooseImg">
          <image :src="`${IMG_PATH}upload.png`" />
        </view> -->
      </view>
    </view>

    <main-btn @click="submit" class="submit">提交</main-btn>

    <view v-if="pages" class="page-hed">
      <view class="search-head">
        <input @input="debounceSearch" placeholder="查找充电站" v-model="searchVal" />
        <text class="icon iconfont icon-search" size="30"></text>
      </view>
      <view class="page-hed-ul">
        <view @tap="tabChang('02')" :class="'page-hed-li ' + (start === '02' ? 'hed-li-checked' : '')">距离最近</view>
        <view @tap="tabChang('03')" :class="'page-hed-li ' + (start === '03' ? 'hed-li-checked' : '')">价格最低</view>
        <view @tap="tabChang('01')" :class="'page-hed-li ' + (start === '01' ? 'hed-li-checked' : '')">评价最高</view>
      </view>
    </view>
    <scroll-view v-if="pages" :scroll-top="topCup" @scrolltolower="toLowFunNet" scroll-y class="list-found">
      <view class="station-ul">
        <station-li
          :memberInfo="memberInfo"
          :currentItem="item"
          v-for="(item, index) in distanceCup"
          :key="index"
          pageType="rtnStation"
          @rtnStationInfo="handleChooseStation"
        ></station-li>
      </view>
    </scroll-view>

    <!-- <scroll-view v-if="pages && danC" :scroll-top="topCupNet" @scrolltolower="toLowFun" scroll-y class="list-found">
      <view class="station-ul">
        <station-li
          :currentItem="item"
          v-for="(item, index) in pricCup"
          :key="index"
          pageType="rtnStation"
          @rtnStationInfo="handleChooseStation"
        ></station-li>
      </view>
    </scroll-view> -->
  </view>
</template>

<script>
// import customHead from '@/components/custom-head';
import mainBtn from '@/components/main-btn';
const { chooseImg, uploadFile, getImageToBase64_url } = require('../../utils/upload');
import lUpload from '@/components/l-upload/l-upload';
import { getStationListV06, getFaqType, submitPubFaq, replyPubFaq, getMemberInfo } from '@/services/index';
import stationLi from '@/components/station-li/index';
import { getLocation } from '@/utils/index.js';
import { baseDateNo } from '@/utils/base.js';
const fieldNameMap = {
  feedbackType: '问题分类',
  title: '问题标题',
  content: '问题详情',
};
export default {
  components: {
    // customHead,
    lUpload,
    mainBtn,
    stationLi,
  },
  computed: {
    topFixed() {
      return 50 + this.s_top + 'rpx';
    },
  },
  data() {
    return {
      feedbackTypeList: [],
      form: {
        feedbackType: null,
        typeId: null,
        title: null,
        content: null,
        faqImageList: [],
        station: {}, // 选择的场站
      },
      faqId: null,
      imgFormData: {
        contentType: '02',
        // relaId: this.integralId,
        relaTable: 'orderEva',
      },
      imgHeader: {
        minitProgramToken: uni.getStorageSync('token') || '',
        authorization: uni.getStorageSync('token') || '',
        // #ifdef H5
        // 'content-type': 'application/x-www-form-urlencoded',
        // #endif
      },
      s_top: '',
      pages: false,
      danC: true,
      priC: false,
      topCup: 0,
      topCupNet: 0,
      pageDance_pageNum: 1,
      pageDance_totalNum: 5,
      pagePric_pageNum: 1,
      pagePric_totalNum: 5,
      distanceCup: [],
      pricCup: [],
      pageListAdd: false,
      mapCop: true,
      distance: '',
      distanceL: '10',
      lon: '',
      lat: '',
      start: '02',
      searchVal: '',
      actionUrl: '',
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // #ifdef MP
    this.actionUrl = baseDateNo() + 'scan/wx/v0.1/file-upload';
    // #endif
    // #ifdef H5
    this.actionUrl = baseDateNo() + 'cst/api/v0.1/pub-faq';
    // #endif
    this.setData({
      faqId: options.faqId,
      'form.title': options.title || '',
      'form.typeId': options.typeId || '',
      'form.feedbackType': options.typeName || '',
    });
    console.log(this.form, 'form');
    this.loadFeedbackType();
    // #ifdef MP
    this.initTopImg();
    // #endif
    this.getLocationCallbcak();
    this.getMemberInfo();
  },
  created() {
    this.debounceSearch = this.debounce((val) => {
      this.found(val);
    }, 500);
  },
  methods: {
    // 获取会员信息
    async getMemberInfo() {
      const [err, res] = await getMemberInfo();
      if (res) {
        this.memberInfo = res;
      } else {
        this.memberInfo = null;
      }
    },
    initTopImg() {
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      this.s_top = +(menuButtonInfo?.top * 2) + 20;
    },

    tabs() {
      var that = this;
      that.setData(
        {
          // pages: !_self.data.pages,
          condition: false,
          topCup: 0,
          topCupNet: 0,
          oio: '82.5vh',
          // cityPage: false,
          pageDance_pageNum: 1,
          pageDance_totalNum: 5,
          pagePric_pageNum: 1,
          pagePric_totalNum: 5,
        },
        () => {
          if (that.pages == true) {
            that.setData({
              pages: false,
              cityPage: false,
              pageListAdd: false,
              mapCop: true,
              searchVal: '',
            });
          } else {
            that.setData({
              pages: true,
              cityPage: false,
              pageListAdd: true,
            });
          }
        }
      );
      that.pageDanceInfo();
      //   that.pagePricInfo();
      // _self.julis()
    },
    tabChang(index) {
      this.setData({
        start: index,
        topCup: 0,
        topCupNet: 0,
        distanceCup: [],
        pageDance_pageNum: 1,
        pageDance_totalNum: 5,
      });
      this.pageDanceInfo();
    },

    toLowFun() {
      var _self = this;
      _self.setData({
        pagePric_pageNum: _self.pagePric_pageNum + 1,
        pagePric_totalNum: _self.pagePric_totalNum,
      });
      _self.pagePricInfo();
    },

    toLowFunNet() {
      var _self = this;
      _self.setData({
        pageDance_pageNum: _self.pageDance_pageNum + 1,
        pageDance_totalNum: _self.pageDance_totalNum,
      });
      _self.pageDanceInfo();
    },

    // 获取距离
    async getLocationCallbcak() {
      const that = this;
      const res = await getLocation();
      if (res) {
        console.log(res, '位置信息');
        that.setData({
          longitude: res.longitude,
          longitudeMap: res.longitude,
          latitude: res.latitude,
          latitudeMap: res.latitude,
        });
      }
    },
    //获取当前位置
    async getLocation() {
      const that = this;
      const res = await getLocation();
      if (res) {
        console.log(res, '位置信息');
        that.setData({
          longitude: res.longitude,
          longitudeMap: res.longitude,
          latitude: res.latitude,
          latitudeMap: res.latitude,
        });
        that.pageDanceInfo();
      }
    },
    async pagePricInfo() {
      var _self = this;
      var params = {
        positionLon: _self.longitude,
        positionLat: _self.latitude,
        orderType: '03',
        pageNum: _self.pagePric_pageNum,
        totalNum: _self.pagePric_totalNum,
        stationName: this.searchVal,
      };
      const [, res] = await getStationListV06(params);
      if (res) {
        if (!res || !res.chcList) {
          return;
        }
        if (res.chcList.length > 0) {
        } else {
          uni.showToast({
            icon: 'none',
            title: '到底啦',
          });
          return false;
        }
        let pricCup = _self.pricCup.concat(res.chcList);
        _self.setData({
          pricCup: _self.beath(pricCup),
        });
      } else {
        uni.showToast({
          icon: 'loading',
          title: '加载数据失败',
        });
      }
    },

    async pageDanceInfo() {
      // 01按照评分排序  02按照距离排序  03 按金额排序
      var _self = this;
      var params = {
        positionLon: _self.longitude,
        positionLat: _self.latitude,
        orderType: _self.start,
        pageNum: _self.pageDance_pageNum,
        totalNum: _self.pageDance_totalNum,
        stationName: this.searchVal,
      };
      const [, res] = await getStationListV06(params);
      if (res) {
        if (!res || !res.chcList) {
          return;
        }
        if (res && res.chcList && res.chcList.length > 0) {
        } else if (res && res.chcList) {
          uni.showToast({
            icon: 'none',
            title: '到底啦',
          });
          return false;
        }
        let distanceCup = _self.distanceCup.concat(res.chcList);
        _self.setData({
          distanceCup: _self.beath(distanceCup),
        });
      }
    },

    beath(arr) {
      var result = [];
      var hash = {};
      var cur = null;
      for (var i = 0; i < arr.length; i++) {
        cur = arr[i];
        if (cur.stationNo && !hash[cur.stationNo]) {
          result.push(cur);
          hash[cur.stationNo] = true;
        }
      }
      return result;
    },
    async loadFeedbackType() {
      const [, res] = await getFaqType();
      if (res) {
        this.feedbackTypeList = res.faqTypeList || [];
        console.log(this.feedbackTypeList, 'this.feedbackTypeList');
      }
    },

    pickerChange(e) {
      const range = this.feedbackTypeList;
      const key = e.detail.value;
      console.log(range[key], 'range[key]');
      this.setData({
        'form.feedbackType': range[key].typeName || '',
        'form.typeId': range[key].typeId || '',
      });
    },

    inputChange(e) {
      const name = e.currentTarget.dataset.name;
      const value = e.detail.value;
      this.setData({
        [`form.${name}`]: value,
      });
    },

    async chooseImg() {
      const [err, data] = await chooseImg();
      const imgBase64 = await getImageToBase64_url(data[0]);
      this.setData({
        'form.faqImageList': [
          ...this.form.faqImageList,
          {
            image: imgBase64,
          },
        ],
      });
    },

    delImg(e) {
      const index = e.currentTarget.dataset.key;
      this.form.faqImageList.splice(index, 1);
      this.setData({
        'form.faqImageList': [...this.form.faqImageList],
      });
    },

    async imgChange(e) {
      this.form.faqImageList = e.imageArr;
      console.log('this.form.faqImageList===', this.form.faqImageList);
    },
    async submit() {
      let errField = null;
      for (let i in this.form) {
        if (i !== 'faqImageList' && i !== 'station' && !this.form[i]) {
          errField = i;
          break;
        }
      }
      console.log(errField, 'errField');
      if (errField) {
        return uni.showToast({
          title: `请填写${fieldNameMap[errField]}`,
          icon: 'error',
        });
      }
      console.log(this.faqId, 'this.faqId');

      await this.uploadImage(this.faqId);
      // 如果是新增反馈，先提交反馈获取faqId，然后上传图片
      // if (!this.faqId) {
      //   const { typeId, title, content, station = {} } = this.form;
      //   const [, res] = await submitPubFaq({
      //     typeId,
      //     title,
      //     content,
      //     stationId: station.stationId || '',
      //   });

      //   if (res && res.faqId) {
      //     // 提交成功后上传图片
      //   } else {
      //     uni.showToast({
      //       icon: 'error',
      //       title: '提交失败',
      //     });
      //   }
      // } else {
      //   // 如果是回复反馈，直接上传图片
      //   await this.uploadImage(this.faqId);
      // }
    },

    async addFeedback(fieldString = '') {
      const { typeId, title, content, station = {} } = this.form;
      const [, res] = await submitPubFaq(
        {
          typeId,
          title,
          content,
          fileId: fieldString, // 传入图片上传后的field字符串
          stationId: station.stationId || '',
        }
        // {
        //   'content-type': 'application/x-www-form-urlencoded',
        // }
      );

      if (res) {
        uni.showToast({
          title: '提交成功',
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 500);
      } else {
        uni.showToast({
          icon: 'error',
          title: '提交失败',
        });
      }
    },

    async replyFeedback(fieldString = '') {
      const { content, station = {} } = this.form;

      const [, res] = await replyPubFaq(
        {
          faqId: this.faqId,
          content,
          fileId: fieldString, // 传入图片上传后的field字符串
          stationId: station.stationId || '',
        }
        // {
        //   'content-type': 'application/x-www-form-urlencoded',
        // }
      );
      if (res) {
        uni.showToast({
          title: '提交成功',
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 500);
      } else {
        uni.showToast({
          icon: 'error',
          title: '提交失败',
        });
      }
    },
    async uploadImage(faqId) {
      console.log(this.form.faqImageList, 'this.form.faqImageList');

      if (this.form.faqImageList.length > 0) {
        try {
          // 使用Promise.all等待所有图片上传完成
          const uploadPromises = this.form.faqImageList.map((item) => {
            return new Promise((resolve, reject) => {
              uni.uploadFile({
                url: this.actionUrl,
                filePath: item,
                header: this.imgHeader,
                name: 'food_image',
                formData: {
                  contentType: '02',
                  relaId: 123,
                  relaTable: 'c_faq',
                },
                success: function (res) {
                  console.log('图片上传成功:', res);
                  try {
                    // 解析返回的数据，获取field字段
                    const responseData = JSON.parse(res.data);
                    console.log(responseData, 'responseData');
                    if (responseData && responseData.fileId) {
                      resolve(responseData.fileId);
                    } else {
                      console.warn('上传成功但未返回field字段:', res);
                      resolve(''); // 如果没有field字段，返回空字符串
                    }
                  } catch (error) {
                    console.error('解析上传响应数据失败:', error);
                    resolve(''); // 解析失败时返回空字符串
                  }
                },
                fail: function (res) {
                  console.error('图片上传失败:', res);
                  reject(new Error('图片上传失败'));
                },
              });
            });
          });

          // 等待所有图片上传完成
          const fieldList = await Promise.all(uploadPromises);

          // 过滤掉空字符串，然后用逗号拼接
          const fieldString = fieldList.filter((field) => field && field.trim()).join(',');

          console.log('所有图片上传完成，field字符串:', fieldString);

          // 调用反馈接口，传入field字符串
          if (!this.faqId) {
            await this.addFeedback(fieldString);
          } else {
            await this.replyFeedback(fieldString);
          }
        } catch (error) {
          console.error('图片上传过程中出现错误:', error);
          uni.showToast({
            title: '图片上传失败，请重试',
            icon: 'error',
          });
        }
      } else {
        // 没有图片需要上传，直接调用反馈接口
        if (!this.faqId) {
          await this.addFeedback('');
        } else {
          await this.replyFeedback('');
        }
      }
    },

    found(value) {
      console.log(value);
      this.setData({
        pageDance_pageNum: 1,
        pageDance_totalNum: 5,
        pagePric_pageNum: 1,
        pagePric_totalNum: 5,
        pricCup: [],
        distanceCup: [],
      });
      this.pageDanceInfo();
    },
    debounce(fn, time = 100) {
      let timer = null;
      return function (...args) {
        if (timer) clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(this, args);
        }, time);
      };
    },
    handleChooseStation(station) {
      this.tabs();
      this.setData({
        'form.station': { ...station },
      });
      console.log('form.station', this.form);
    },
  },
};
</script>
<style scoped lang="scss">
/* @import '../../common/css/global.css'; */
@import './index.css';
.imgs-box {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  grid-gap: 24rpx;
  margin-top: 24rpx;
  padding: 20rpx;
}
</style>
