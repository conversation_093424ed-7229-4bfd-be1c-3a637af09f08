<!--
@name: 用户信息
@description: 用户信息
@time: 2024/8/18
-->
<template>
  <view>
    <view class="mine-header">
      <img class="header-logo" :src="userInfo.imgUrl || `${IMG_PATH}defaultAvatar.png`" @click="jumpToUser" />
      <view class="header-content">
        <!--          昵称、电话、续费、有效期-->
        <view class="name-phone">
          <view v-if="!userInfo.mobile" class="right-login" @click="toLogin">请登录</view>
          <view v-if="userInfo.mobile" class="user-info">
            <!--            名称、手机号-->
            <view class="right-top">
              <text class="user-name" @click="jumpToUser">{{ userInfo.custNickname || '未设置昵称' }}</text>
              <img
                class="user-vip-rank"
                @click="jumpToCenter"
                :src="`${IMG_PATH}member/rank-${rankBackGround}.png`"
                v-if="memberInfo.vipFlag === '0' && rankBackGround"
              />
              <img
                class="user-vip-rank"
                @click="jumpToCenter"
                :src="`${IMG_PATH}member/rank-plus.png`"
                v-if="memberInfo.vipFlag === '1'"
              />
            </view>
            <text class="user-phone">({{ desensitization(userInfo.mobile) }})</text>
            <!--            有效期-->
            <text class="validity" v-if="memberInfo.vipFlag === '1'">
              会员有效期至：{{ memberInfo.vipExpireTime }}
            </text>
          </view>
          <AppButton
            v-if="memberInfo.vipFlag === '1'"
            :custom-style="{
              'line-height': '40rpx',
              color: '#1E9CFF',
              'font-size': '26rpx',
              border: '1px solid #1E9CFF',
              height: '48rpx',
              width: '126rpx',
              padding: 'unset',
            }"
            shape="circle"
            color="#E6F4FF"
            @click="toBuyPlus"
          >
            会员续费
          </AppButton>
        </view>
        <!--            等级进度条-->
        <view class="progress" v-if="memberInfo.vipFlag === '0'">
          <view class="progress-bg"></view>
          <view class="progress-charge" :style="{ width: getProgress }"></view>
          <view class="progress-text" v-if="memberInfo.rankMax && memberInfo.rankMax !== 0">
            <view class="progress-line">
              <text class="text-item">距离升级</text>
              <text class="text-item">{{
                memberInfo.levelFlag == 'quantity'
                  ? memberInfo.chargePq
                  : memberInfo.levelFlag == 'amount'
                  ? memberInfo.chargeAmt
                  : ''
              }}</text>
              <text class="text-item">{{
                memberInfo.levelFlag == 'quantity' ? 'kWh' : memberInfo.levelFlag == 'amount' ? '元' : ''
              }}</text>
            </view>
            <view class="progress-duration" v-if="memberInfo.durationDays">
              <text class="duration-text">保持{{ memberInfo.durationDays }}天</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import AppButton from '@/components/AppButton/index';
import { mapState } from 'vuex';
import { desensitization } from '@/utils/util.js';

export default {
  name: 'UserDetail',
  components: {
    AppButton,
  },
  props: {
    memberInfo: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    ...mapState({
      userInfoObj: (state, getters) => getters['login/getUserInfo'],
    }),
    userInfo() {
      return {
        mobile: '',
        vipExpireTime: '',
        ...this.userInfoObj,
      };
    },
    rankBackGround() {
      return this.memberInfo && this.memberInfo.rankNo ? this.map[this.memberInfo.rankNo] : '';
    },
    getProgress() {
      let length = 400;
      if (this.memberInfo?.rankMax !== 0) {
        length =
          ((this.memberInfo.levelFlag == 'quantity'
            ? this.memberInfo.chargePq
            : this.memberInfo.levelFlag == 'amount'
            ? this.memberInfo.chargeAmt
            : 0) /
            this.memberInfo?.rankMax) *
          400;
        // length = this.memberInfo.chargePq;
      }
      return `${length}rpx`;
    },
  },
  data() {
    return {
      map: {
        1: 'qt',
        2: 'by',
        3: 'hj',
        4: 'bj',
      },
    };
  },
  mounted() {},
  methods: {
    desensitization,
    // 跳转到个人中心页面
    jumpToCenter() {
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '/pages/login/login',
        });
      } else {
        uni.navigateTo({
          url: '/pages/member/memberCenter/index',
        });
      }
    },
    // 跳转到个人中心页面
    jumpToUser() {
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '/pages/login/login',
        });
      } else {
        uni.navigateTo({
          url: '/pages/information/index',
        });
      }
    },
    // 跳转购买会员
    toBuyPlus() {
      uni.navigateTo({
        url: '/pages/member/buyPlus/index',
      });
    },
    // 去开通
    jumpToBuy() {
      uni.navigateTo({
        url: '/pages/member/buyPlus/index',
      });
    },
    toLogin() {
      uni.navigateTo({
        url: '/pages/login/login',
      });
    },
  },
};
</script>

<style scoped lang="scss">
.mine-header {
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  margin-bottom: 24rpx;
  .header-logo {
    width: 100rpx;
    height: 100rpx;
    margin: 0 16rpx;
  }
  .header-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 554rpx;
    .name-phone {
      display: flex;
      justify-content: space-between;
      flex: 1;
      margin-right: 16rpx;
      align-items: center;
      .user-info {
        display: flex;
        flex-direction: column;
        .right-top {
          display: flex;
          align-items: center;
          .user-name {
            font-size: 36rpx;
            font-weight: bold;
            line-height: 42rpx;
            letter-spacing: 0.58rpx;
            color: #333333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 250rpx;
          }
          .user-vip-rank {
            margin-left: 8rpx;
            background-size: cover;
            width: 99rpx;
            height: 42rpx;
            display: flex;
            align-items: center;
          }
        }
        .user-phone {
          font-size: 26rpx;
          line-height: 40rpx;
          color: #666666;
          margin-left: 8rpx;
        }
        .validity {
          font-size: 22rpx;
          line-height: 26rpx;
          color: #666666;
          margin-top: 8rpx;
        }
      }
      .right-login {
        height: 100rpx;
        font-family: PingFang SC;
        font-size: 36rpx;
        font-weight: 600;
        line-height: 100rpx;
        color: #333333;
      }
    }
    .progress {
      margin-top: 8rpx;
      height: 26rpx;
      position: relative;
      display: flex;
      align-items: center;
      &-bg {
        background: rgba(0, 0, 0, 0.2);
        width: 400rpx;
      }
      &-charge {
        position: absolute;
        background: white;
      }
      &-bg,
      &-charge {
        height: 8rpx;
        border-radius: 6px;
      }
      &-text {
        color: #3d3d3d;
        margin-left: 12rpx;
        display: flex;
        flex-direction: column;

        .progress-line {
          display: flex;
          align-items: center;
          margin-bottom: 4rpx;

          .text-item:nth-child(1),
          .text-item:nth-child(3) {
            font-size: 18rpx;
            line-height: 26rpx;
          }
          .text-item:nth-child(2) {
            font-size: 24rpx;
            font-weight: bold;
            font-variation-settings: 'opsz' auto;
            margin-left: 4rpx;
          }
        }

        .progress-duration {
          display: flex;
          align-items: center;

          .duration-text {
            font-size: 16rpx;
            line-height: 22rpx;
            color: #666666;
          }
        }
      }
    }
  }
}
</style>
