<template>
  <div class="detail-content">
    <rich-text :nodes="content"></rich-text>
  </div>
</template>

<script>
import { readMsg, getMsgCenterList } from '@/services/index.js';
export default {
  props: {},
  components: {},
  data() {
    return {
      content: '',
      msgType: '',
      noticeId: '',
    };
  },
  computed: {},
  onLoad(options) {
    console.log(options, 'options');
    const { msgType, noticeId } = options;
    this.noticeId = noticeId;
    this.msgType = msgType;
    this.getContent();
    this.read();
  },
  methods: {
    async read() {
      // pub/api/v0.1/msg-read
      const [, res] = await readMsg({
        noticeId: this.noticeId, // 0201 活动  0301 服务
      });
      if (res) {
        console.log(res, '已读');
      }
    },
    async getContent() {
      const params = {
        msgType: this.msgType, // 0201 活动  0301 服务
        pageNum: 1,
        totalNum: 1000000,
        noticeId: this.noticeId,
      };
      console.log(params, 'params');
      const [, res] = await getMsgCenterList(params);
      if (res && res.ret == '200') {
        this.content = res.msgList.find((q) => q.noticeId == this.noticeId)?.remark;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.detail-content {
  padding: 40rpx 24rpx;
}
</style>
