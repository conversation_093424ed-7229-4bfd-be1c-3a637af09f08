page {
  background: #fff;
}
.billinghistory-lists {
  display: flex;
  height: 100rpx;
  margin: 0 30rpx;
  align-items: center;
  border-bottom: 2rpx solid #e7e9ec;
}

.list-main {
  display: flex;
  flex: 1;
  align-items: center;
  height: 100%;
}

.delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 48rpx;
  background-color: #ff4757;
  border-radius: 6rpx;
  margin-left: 16rpx;
}

.delete-text {
  color: #fff;
  font-size: 22rpx;
  font-weight: 400;
}

.list-content {
  color: #333;
  font-family: 'PingFang SC';
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
  max-width: 420rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.list-right {
  color: #2196f3;
  font-size: 24rpx;
  margin-left: 12rpx;
  border-radius: 4rpx;
  background: #ebefde;
  padding: 0px 12rpx;
  color: #8bb70c;
  font-family: 'PingFang SC';
  font-size: 20rpx;
  font-style: normal;
  font-weight: 400;
}
.list-full {
  flex: 1;
}
.list-icon {
  margin-left: 32rpx;
  width: 40rpx;
  height: 40rpx;
}
.myorder-image {
  text-align: center;
  color: #b2b2b2;
  font-size: 32rpx;
  margin-top: 50%;
}
.myorder-image image {
  display: block;
  width: 70rpx;
  height: 86rpx;
  margin: 0 auto 20rpx auto;
}
.icon-right {
  float: right;
  color: #999;
  height: 30rpx;
  padding: 7rpx 0;
}
.btn-fiexd {
  bottom: 50rpx;
}
.bottom-bg {
  background: #fff;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  padding: 20rpx;
  box-sizing: border-box;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}
.blue-bg {
  color: #fff;
  font-family: 'PingFang SC';
  font-size: 32rpx;
  font-weight: 400;
}
.default-height {
  height: 120rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}
