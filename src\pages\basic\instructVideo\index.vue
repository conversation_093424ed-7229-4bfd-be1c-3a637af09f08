<template>
  <div class="">
    <!-- #ifdef MP -->
    <view class="banner" v-if="type !== 'app'" :style="'padding-top:' + s_top + 'rpx;line-height:' + s_height + 'rpx'">
      <icon type="icon iconfont icon-arrow_left" @click="back" size="20" />
      <view class="banner-title" @click="back">指导视频</view>
    </view>
    <!-- #endif -->
    <template v-if="videoListData.length > 0">
      <scroll-view :style="{ paddingTop: cardTop + 'rpx' }" scroll-y="true" style="" class="vide-wrap">
        <view class="video-item-wrap" v-for="(item, index) in videoListData" :key="index">
          <div class="video-item" v-for="(int, i) in item" :key="i">
            <div
              class="video-wrap"
              @click="
                startPlay({
                  int,
                  id: `videoId${index}-${i}`,
                })
              "
            >
              <div class="loading-video" v-if="!int.ifLoad">
                <div class="loading"></div>
                加载中
              </div>
              <video
                :id="`videoId${index}-${i}`"
                :ref="`videoId${index}-${i}`"
                :src="int.videoSrc"
                object-fit="contain"
                @loadedmetadata="loadedmetadata(int)"
                @play="play($event, int)"
                @fullscreenchange="fullscreenchange"
                :danmu-btn="false"
                controls
              ></video>
            </div>
            <div class="video-title">{{ int.title || '-' }}</div>
          </div>
        </view>
      </scroll-view>
    </template>
    <div class="empty-wrap" v-else>
      <view class="nodata" v-if="!videoListData.length">
        <image :src="`${IMG_PATH}nodata.png`" mode="widthFix" />
        <text>暂无数据</text>
      </view>
    </div>
    <image class="mine-bg" :src="`${IMG_PATH}bg.png`"></image>
    <loading v-if="!hiddenLoading">正在加载</loading>
  </div>
</template>

<script>
import { anthodsDate } from '@/utils/base.js';
import { getUserAgreement } from '@/services/index';
import { startsWithHttp } from '@/utils/util';
export default {
  props: {},
  components: {},
  data() {
    return {
      s_top: '',
      s_height: '',
      videoList: [],
      videoContext: null,
      active: '',
      hiddenLoading: true,
      type: '',
    };
  },
  computed: {
    videoListData() {
      return this.chunk(this.videoList, 2);
    },
    cardTop() {
      return +this.s_top + 100;
    },
  },
  watch: {},
  onLoad(options) {
    if (!options.type || options.type != 'app') {
      // #ifdef MP
      this.initTopImg();
      // #endif
    }
    this.type = options.type;
  },
  onShow() {
    this.initVideoData();
  },
  methods: {
    loadedmetadata(event) {
      event.ifLoad = true;
    },
    initTopImg() {
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      this.setData({
        s_top: menuButtonInfo?.top * 2,
        s_height: menuButtonInfo?.height * 2,
      });
    },
    play(event, int) {
      console.log(event, 'event', int);
      const videoContext = uni.createVideoContext(event.target.id, this);
      console.log(videoContext, 'videoContext');
      if (!int.ifLoad) {
        videoContext.pause();
        return;
      }
      if (this.active) return;
      this.active = event.target.id;
      videoContext.requestFullScreen({
        direction: 90,
      });
    },
    startPlay(event) {
      if (!event.int.ifLoad) {
        return;
      }
      const videoContext = uni.createVideoContext(event.id, this);
      // if (!event.int.ifLoad) {
      //   videoContext.pause();
      //   return;
      // }
      if (this.active) return;
      this.active = event.id;
      videoContext.requestFullScreen({
        direction: 90,
      });
    },
    fullscreenchange(event) {
      const fullScreen = event.detail.fullScreen;
      const videoContext = uni.createVideoContext(event.target.id, this);
      if (fullScreen) {
        videoContext.play();
      } else {
        videoContext.pause();
        this.active = '';
      }
    },
    back() {
      uni.navigateBack({ delta: -1 });
    },
    chunk(array, size) {
      if (!Array.isArray(array) || size <= 0) {
        return []; // 确保输入有效
      }
      const result = [];
      for (let i = 0; i < array.length; i += size) {
        const chunk = array.slice(i, i + size); // 切片数组
        result.push(chunk);
      }
      return result;
    },
    async initVideoData() {
      const _this = this;
      const [, res] = await getUserAgreement({ code: '0208' });
      if (res) {
        _this.videoList = res.infoList.reduce((cur, nex) => {
          if (nex.imgList && Array.isArray(nex.imgList)) {
            cur = [
              ...cur,
              ...nex.imgList.map((q, i) => {
                return {
                  videoSrc: startsWithHttp(q.imgUlr),
                  title: (nex.title || '-') + '-' + (+i + 1),
                  ifLoad: false,
                };
              }),
            ];
          }
          return cur;
        }, []);
      }
      // #ifdef MP-WEIXIN
      _this.videoList.forEach(async (q) => {
        const url = await this.downLoadUrl(q.videoSrc);
        q.videoSrc = url;
        console.log(url, 'url');
      });
      // #endif
    },
    async downLoadUrl(url) {
      return new Promise((resolve, reject) => {
        // #ifdef MP-WEIXIN
        uni.downloadFile({
          url: url,
          success: (res) => {
            if (res.tempFilePath) {
              resolve(res.tempFilePath);
            } else {
              resolve(url);
            }
          },
          fail: () => {
            resolve(url);
          },
        });
        return;
        // #endif
        resolve(url);
      });
    },
  },
};
</script>

<style scoped lang="scss">
page {
  background: #f6f7f9;
}
.nodata image {
  width: 586rpx;
  display: block;
  margin: 330rpx auto 0;
}

.nodata text {
  display: block;
  margin: -80rpx auto 0;
  text-align: center;
}

.empty-wrap {
  width: 100%;
  height: 500rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.banner {
  padding: 55rpx 20rpx 20rpx;
  display: flex;
  align-items: baseline;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  z-index: 2;
  box-sizing: border-box;
}

.banner-title {
  font-family: 'PingFang SC';
  font-size: 36rpx;
  font-style: normal;
  font-weight: 500;
  flex: 1;
  text-indent: 4rpx;
  color: #000000;
  font-weight: 700;
  padding-left: 30%;
}
.mine-bg {
  width: 100vw;
  height: 600rpx;
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
}
.vide-wrap {
  width: 100%;
  height: 100vh;
  padding-bottom: 40rpx;
  padding-top: 140rpx;
  .video-item-wrap {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15rpx;
    height: 300rpx;
    padding: 0 15rpx;
    .video-item {
      width: calc(50% - 10rpx);
      height: 100%;
      .video-wrap {
        width: 100%;
        height: calc(100% - 50rpx);
        overflow: hidden;
        border-radius: 20rpx;
        position: relative;
        .loading-video {
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          top: 0;
          z-index: 1;
          background-color: rgba(0, 0, 0, 0.5);
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        video {
          width: 100%;
          height: 100%;
          z-index: 0;
        }
      }
      .video-title {
        font-size: 29rpx;
        line-height: 50rpx;
        font-weight: 600;
        letter-spacing: 0px;
        color: #333333;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
