<template>
  <view>
    <view class="myorder">
      <!-- 固定在顶部的导航区域 -->
      <view class="fixed-header">
        <!-- Tab栏 -->
        <view class="myorder-tab">
          <text :class="' ' + (currentTab == 0 ? 'active' : '')" data-id="0" data-current="0" @tap="swichNav"
            >全部</text
          >
          <text :class="' ' + (currentTab == 1 ? 'active' : '') + ' infos'" data-id="1" data-current="1" @tap="swichNav"
            >未完成</text
          >
          <text :class="' ' + (currentTab == 2 ? 'active' : '')" data-id="2" data-current="2" @tap="swichNav"
            >已完成</text
          >
          <text :class="currentTab == 3 ? 'active' : ''" data-id="3" data-current="3" @tap="swichNav">已取消</text>
          <text class="btn" @click="toCount">统计</text>
        </view>

        <!-- 支付方式筛选下拉框 -->
        <view class="filter-container">
          <view class="payment-filter">
            <view class="filter-label">支付方式:</view>
            <picker :range="paymentTypeList" range-key="name" @change="paymentTypeChange">
              <view class="picker-content input-wrap">
                <text class="picker-text">{{ currentPaymentType.name || '全部' }}</text>
                <image
                  :src="`${IMG_PATH}icon-close-row.png`"
                  class="arrow-icon"
                  mode="aspectFit"
                  style="width: 16rpx; height: 16rpx"
                />
              </view>
            </picker>
          </view>
        </view>
      </view>

      <!-- 占位元素，与固定头部高度相同 -->
      <view class="header-placeholder"></view>

      <!-- <view class="myorder-search-box">
        <text class="myorder-search-text" v-if="debitCardNumber">卡号:xxxxx</text>
        <view class="myorder-search-input" v-else>
          <view class="form-item align-center">
            <view class="label">
              <text>支付方式:</text>
            </view>
            <view class="value">
              <picker :range="payOptionsList" range-key="typeName" @change="pickerChange">
                <view class="input-wrap">
                  <text>{{ payName || '请选择' }}</text>
                  <image :src="`${IMG_PATH}right_arrow.png`" class="right_arrow" mode="aspectFill" />
                </view>
              </picker>
            </view>
          </view>
        </view>
      </view> -->
      <view class="myorder-main">
        <swiper
          :current="currentTab"
          class="swiper-box"
          duration="300"
          style="height: calc(100vh - 160rpx)"
          @change="bindChange"
        >
          <swiper-item>
            <view v-if="alls" class="myorder-image">
              <image :src="`${IMG_PATH}main-data-null.png`" mode="heightFix" />
              <text>暂无订单</text>
            </view>
            <scroll-view v-else scroll-y :style="'height: calc(100vh - 160rpx)'" @scrolltolower="loadMoreAll">
              <view
                @tap="add"
                :data-msg="item.orderNo"
                class="myorder-list"
                v-for="(item, index) in orderChargeListAll"
                :key="index"
              >
                <view class="list-header">
                  <text v-if="item.chargeStatus == '01'" class="order-state unpaid-bg fr blue">{{
                    item.orderStatusName
                  }}</text>
                  <text v-if="item.chargeStatus == '02'" class="order-state unpaid-bg fr red">{{
                    item.orderStatusName
                  }}</text>
                  <text v-if="item.chargeStatus == '04'" class="order-state unpaid-bg fr gray">{{
                    item.orderStatusName
                  }}</text>
                  <text v-if="item.chargeStatus == '03'" class="order-state unpaid-bg fr orange">{{
                    item.orderStatusName
                  }}</text>
                  <text class="order-no">{{ item.equipName }}</text>
                </view>
                <view class="list-main">
                  <view class="order-address">
                    <icon type="icon iconfont icon-location" size="16" />
                    {{ item.stationName }}
                  </view>
                  <view class="order-time">
                    <icon type="icon iconfont icon-time" size="16" />
                    {{ item.applyTime }}
                  </view>
                  <text class="order-money">¥{{ item.orderTBal }}</text>
                </view>
              </view>
            </scroll-view>
          </swiper-item>

          <!-- 未完成 -->
          <swiper-item>
            <view v-if="Nos" class="myorder-image">
              <image :src="`${IMG_PATH}main-data-null.png`" mode="heightFix" />
              <text>暂无订单</text>
            </view>
            <scroll-view v-else scroll-y :style="'height: calc(100vh - 160rpx)'" @scrolltolower="loadMoreNo">
              <view
                @tap="add"
                :data-msg="item.orderNo"
                class="myorder-list"
                v-for="(item, index) in orderChargeListNo"
                :key="index"
              >
                <view class="list-header">
                  <text v-if="item.chargeStatus == '01'" class="order-state unpaid-bg fr blue">{{
                    item.orderStatusName
                  }}</text>
                  <text v-if="item.chargeStatus == '02'" class="order-state unpaid-bg fr red">{{
                    item.orderStatusName
                  }}</text>
                  <text v-if="item.chargeStatus == '04'" class="order-state unpaid-bg fr gray">{{
                    item.orderStatusName
                  }}</text>
                  <text v-if="item.chargeStatus == '03'" class="order-state unpaid-bg fr orange">{{
                    item.orderStatusName
                  }}</text>
                  <text class="order-no">{{ item.equipName }}</text>
                </view>
                <view class="list-main">
                  <view class="order-address">
                    <icon type="icon iconfont icon-location" size="16" />
                    {{ item.stationName }}
                  </view>
                  <view class="order-time">
                    <icon type="icon iconfont icon-time" size="16" />
                    {{ item.applyTime }}
                  </view>
                  <text class="order-money">¥{{ item.orderTBal }}</text>
                </view>
              </view>
            </scroll-view>
          </swiper-item>
          <!-- 已完成 -->
          <swiper-item>
            <view v-if="Sucss" class="myorder-image">
              <image :src="`${IMG_PATH}main-data-null.png`" />
              <text>暂无订单</text>
            </view>
            <scroll-view v-else scroll-y :style="'height: calc(100vh - 160rpx)'" @scrolltolower="loadMoreSuccess">
              <view
                @tap="add"
                :data-msg="item.orderNo"
                class="myorder-list"
                v-for="(item, index) in orderChargeListSuccess"
                :key="index"
              >
                <view class="list-header">
                  <text v-if="item.chargeStatus == '01'" class="order-state unpaid-bg fr blue">{{
                    item.chargeStatusName
                  }}</text>
                  <text v-if="item.chargeStatus == '02'" class="order-state unpaid-bg fr red">{{
                    item.chargeStatusName
                  }}</text>
                  <text v-if="item.chargeStatus == '04'" class="order-state unpaid-bg fr gray">{{
                    item.chargeStatusName
                  }}</text>
                  <text v-if="item.chargeStatus == '03'" class="order-state unpaid-bg fr orange">{{
                    item.chargeStatusName
                  }}</text>
                  <text class="order-no">{{ item.equipName }}</text>
                </view>
                <view class="list-main">
                  <view class="order-address">
                    <icon type="icon iconfont icon-location" size="16" />
                    {{ item.stationName }}
                  </view>
                  <view class="order-time">
                    <icon type="icon iconfont icon-time" size="16" />
                    {{ item.applyTime }}
                  </view>
                  <text class="order-money">¥{{ item.orderTBal }}</text>
                </view>
              </view>
            </scroll-view>
          </swiper-item>

          <!-- 已取消 -->
          <swiper-item>
            <view v-if="Overs" class="myorder-image">
              <image :src="`${IMG_PATH}main-data-null.png`" mode="heightFix" />
              <text>暂无订单</text>
            </view>
            <scroll-view v-else scroll-y :style="'height: calc(100vh - 160rpx)'" @scrolltolower="loadMoreOver">
              <view
                :data-msg="item.orderNo"
                @tap="add"
                class="myorder-list"
                v-for="(item, index) in orderChargeListOver"
                :key="index"
              >
                <view class="list-header">
                  <text v-if="item.chargeStatus == '01'" class="order-state unpaid-bg fr blue">{{
                    item.chargeStatusName
                  }}</text>
                  <text v-if="item.chargeStatus == '02'" class="order-state unpaid-bg fr red">{{
                    item.chargeStatusName
                  }}</text>
                  <text v-if="item.chargeStatus == '04'" class="order-state unpaid-bg fr gray">{{
                    item.chargeStatusName
                  }}</text>
                  <text v-if="item.chargeStatus == '03'" class="order-state unpaid-bg fr orange">{{
                    item.chargeStatusName
                  }}</text>
                  <text class="order-no">{{ item.equipName }}</text>
                </view>
                <view class="list-main">
                  <view class="order-address">
                    <icon type="icon iconfont icon-location" size="16" />
                    {{ item.stationName }}
                  </view>
                  <view class="order-time">
                    <icon type="icon iconfont icon-time" size="16" />
                    {{ item.applyTime }}
                  </view>
                  <text class="order-money">¥{{ item.orderTBal }}</text>
                </view>
              </view>
            </scroll-view>
          </swiper-item>
        </swiper>
      </view>
    </view>
    <!-- #ifdef MP -->
    <loading v-if="!hiddenLoading">正在加载</loading>
    <!-- #endif -->
  </view>
</template>

<script>
import customNavBar from '@/components/custom-navbar/index';
import { getOrderList } from '@/services/index';
export default {
  components: {
    customNavBar,
  },
  data() {
    return {
      hiddenLoading: true,
      hidden: true,
      currentTab: 0,
      orderChargeListAll: [],
      orderChargeListNo: [],
      orderChargeListSuccess: [],
      orderChargeListOver: [],
      alls: false,
      Nos: false,
      Sucss: false,
      Overs: false,
      // 支付方式下拉列表
      paymentTypeList: [
        { id: 'all', name: '全部' },
        { id: '0201', name: '钱包支付' },
        { id: '0209', name: '充值卡支付' },
        { id: '0206', name: '馈赠金支付' },
      ],
      // 当前选中的支付方式
      currentPaymentType: { id: 'all', name: '全部' },
      // 存储外部传入的卡ID
      cardId: '',
      cardDetailId: '',
    };
  },
  onLoad(options) {
    // 检查是否有cardId参数
    if (options && options.cardId) {
      this.cardId = options.cardId;
    }
    if (options && options.cardDetailId) {
      this.cardDetailId = options.cardDetailId;
    }

    this.init();
  },
  computed: {
    tokenInfo() {
      return uni.getStorageSync('token') || '=1=';
    },
  },
  onReady() {},
  onShow() {
    this.init();
  },
  methods: {
    // 支付方式选择变化处理
    paymentTypeChange(e) {
      const index = e.detail.value;
      const selectedPaymentType = this.paymentTypeList[index];

      // 更新当前选中的支付方式
      this.currentPaymentType = selectedPaymentType;

      // 根据选择的支付方式重新获取订单列表
      this.refreshOrderList(selectedPaymentType.id);
    },

    // 根据支付方式刷新订单列表
    async refreshOrderList(paymentTypeId) {
      let orderStatus = '';

      // 根据当前选项卡确定订单状态
      switch (this.currentTab) {
        case 0: // 全部
          orderStatus = '';
          break;
        case 1: // 未完成
          orderStatus = '01,02,03,04,05,08';
          break;
        case 2: // 已完成
          orderStatus = '07,06';
          break;
        case 3: // 已取消
          orderStatus = '09';
          break;
      }

      // 显示加载中
      this.hiddenLoading = false;

      // 构建请求参数
      const params = {
        orderStatus,
        pageNum: 1,
        totalNum: 50,
        payType: paymentTypeId, // 添加支付方式筛选
      };

      // 如果有卡ID，添加到请求参数中
      if (this.cardId) {
        params.cardId = this.cardId;
      }
      if (this.cardDetailId) {
        params.cardDetailId = this.cardDetailId;
      }

      // 获取订单列表
      const [, res] = await getOrderList(params);

      if (res && res.ret === 200) {
        const orderChargeList = res.orderChargeList;

        // 根据当前选项卡更新对应的订单列表
        switch (this.currentTab) {
          case 0: // 全部
            this.orderChargeListAll = orderChargeList;
            this.alls = orderChargeList.length === 0;
            break;
          case 1: // 未完成
            this.orderChargeListNo = orderChargeList;
            this.Nos = orderChargeList.length === 0;
            break;
          case 2: // 已完成
            this.orderChargeListSuccess = orderChargeList;
            this.Sucss = orderChargeList.length === 0;
            break;
          case 3: // 已取消
            this.orderChargeListOver = orderChargeList;
            this.Overs = orderChargeList.length === 0;
            break;
        }
      } else {
        // 处理错误情况
        switch (this.currentTab) {
          case 0:
            this.alls = true;
            this.orderChargeListAll = [];
            break;
          case 1:
            this.Nos = true;
            this.orderChargeListNo = [];
            break;
          case 2:
            this.Sucss = true;
            this.orderChargeListSuccess = [];
            break;
          case 3:
            this.Overs = true;
            this.orderChargeListOver = [];
            break;
        }
      }

      this.hiddenLoading = true;
    },

    toCount() {
      uni.navigateTo({
        url: '/subPackages/orderCount/index',
      });
    },
    jump(e) {
      uni.redirectTo({
        url: '/pages/basic/list/list?status=' + e.currentTarget.dataset.name,
      });
    },

    add(orderChargeListOver) {
      const orderNo = orderChargeListOver.currentTarget.dataset.msg;
      uni.navigateTo({
        url: '/subPackages/ordercharge/ordercharge?orderNo=' + orderNo,
      });
    },

    cancel() {
      this.hidden = true;
    },

    async init() {
      this.hiddenLoading = false;

      // 构建请求参数
      const params = {
        orderStatus: '',
        pageNum: 1,
        totalNum: 50,
      };

      // 如果有卡ID，添加到请求参数中
      if (this.cardId) {
        params.cardId = this.cardId;
      }
      if (this.cardDetailId) {
        params.cardDetailId = this.cardDetailId;
      }

      const [, res] = await getOrderList(params);
      if (res && res.ret === 200) {
        this.orderChargeListAll = res.orderChargeList;
        this.alls = res.orderChargeList.length === 0;
      } else {
        this.alls = true;
        this.orderChargeListAll = [];
      }
      this.hiddenLoading = true;
    },

    /**
     * 滑动切换tab
     */
    bindChange(e) {
      this.currentTab = e.detail.current;
      this.swichNav(e);
    },

    /**
     * 点击tab切换
     */
    async swichNav(e) {
      const newTab = parseInt(e.target.dataset.current || e.detail.current);
      if (this.currentTab === newTab) {
        return;
      }
      this.currentTab = newTab;

      // 使用统一的refreshOrderList方法
      await this.refreshOrderList(this.currentPaymentType.id === 'all' ? '' : this.currentPaymentType.id);
    },

    loadMoreAll() {
      // 触底加载更多全部订单
      // 分页参数自增，调用接口追加数据
    },
    loadMoreNo() {
      // 触底加载更多未完成订单
    },
    loadMoreSuccess() {
      // 触底加载更多已完成订单
    },
    loadMoreOver() {
      // 触底加载更多已取消订单
    },
  },
};
</script>
<style>
@import './myorder.css';
</style>
<style lang="scss" scoped>
/* 固定头部样式 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 占位元素，防止内容被固定头部遮挡 */
.header-placeholder {
  height: 160rpx; /* 根据固定头部的实际高度调整 */
}

/* 支付方式筛选样式 */
.filter-container {
  display: flex;
  justify-content: flex-end;
  padding: 0 30rpx;
  margin-top: 100rpx;
  margin-bottom: 10rpx;
  background-color: #fff;

  .payment-filter {
    width: 280rpx;
    display: flex;
    align-items: center;

    .filter-label {
      font-size: 28rpx;
      color: #333;
      margin-right: 10rpx;
      white-space: nowrap;
    }

    .picker-content {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      flex-wrap: nowrap;
      padding: 10rpx 0;
      font-size: 28rpx;
      color: #333;

      text {
        margin-right: 10rpx;
      }

      .picker-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 150rpx;
        display: inline-block;
      }

      .arrow-icon {
        width: 30rpx;
        height: 30rpx;
        transform: rotate(90deg);
      }
    }
  }
}

.myorder-search-box {
  margin-top: 98rpx;
}

.myorder-search-input {
  margin-left: auto;
  margin-right: 30rpx;
  width: 100%;
  padding-right: 30rpx;
}
.myorder-search-text {
  width: 100%;
  padding: 10rpx 30rpx;
}
.form-item {
  background: #fff;
  margin-left: auto;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  .noheight {
    height: auto;
  }
  .label {
    font-size: 24rpx;
    color: #333;
    margin-right: 10rpx;
  }
  .label.grey {
    color: #999;
    font-size: 18rpx;
  }
  .value {
    width: 180rpx;
    text-align: left;
    color: #999999;
    font-size: 24rpx;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 10rpx;
    padding: 5rpx 10rpx;
  }
  .input-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .right_arrow {
    width: 35rpx;
    height: 35rpx;
  }
  .placeholder {
    color: #999999;
    font-size: 24rpx;
  }
  textarea {
    background: rgba(0, 0, 0, 0.04);
    width: 100%;
    padding: 24rpx;
  }
}
</style>
