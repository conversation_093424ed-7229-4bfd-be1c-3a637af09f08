<!--
@name: 积分商城
@description: 积分商城页面
@time: 2024/7/25
-->
<template>
  <view class="intergral-shop">
    <view class="intergral-shop-content">
      <view
        class="my-point-card"
        :style="{
          backgroundImage: `url(${IMG_PATH}myPointsCardBg.png)`,
        }"
      >
        <view class="my-point-card-span">可兑换积分：</view>
        <view class="my-point-card-body">
          <view class="my-point-card-points">{{ custIntegralNumber }}</view>
          <view class="my-point-card-btns">
            <view class="my-point-card-span my-point-card-btn" @click="goMyPoints"> 积分收支明细 </view>
            <view class="my-point-card-span my-point-card-btn" @click="jumpToExchange"> 兑换说明 </view>
          </view>
        </view>
        <view class="my-point-card-tip">您的积分将于12月31日到期 ，请尽快使用</view>
      </view>
      <!--        搜索框-->
      <view class="search-input">
        <img class="search-icon" :src="`${IMG_PATH}search.png`" />
        <input class="input-box" v-model="goodsName" placeholder="搜索商品" />
        <text class="search-text" @click="searchGoods">搜索</text>
      </view>
      <!--        积分选择器-->
      <shopFilter :optionNames="filters" @selectChaneg="selectChaneg"></shopFilter>
      <tab-select class="tag-list" :options="tabList" @change="changeTab"></tab-select>
      <view class="points-cards" v-if="cardList.length !== 0">
        <pointsCard v-for="cardItem in cardList" :key="cardItem.goodsId" :card-item="cardItem" />
      </view>
      <!--       暂无数据-->
      <AEmpty v-else />
    </view>
    <!--        底部按钮-->
    <view class="intergral-shop-footer">
      <BottomPannel>
        <view class="footer-button">
          <AppButton class="button-item" :custom-style="{ 'border-color': '#E5E5E5' }" plain @click="jumpToRecord">
            兑换记录
          </AppButton>
          <AppButton class="button-item" type="primary" @click="jumpToAddress">管理地址</AppButton>
        </view>
      </BottomPannel>
    </view>
  </view>
</template>

<script>
import pointsCard from './components/pointsCard';
import { getGoods, getStandardCode } from '@/services/index.js';
import BottomPannel from '@/components/BottomPannel/index';
import AppButton from '@/components/AppButton/index';
import AEmpty from '@/components/AEmpty/index';
import tabSelect from './components/tabSelect';
import shopFilter from './components/shopFilter';

export default {
  name: 'index',
  components: {
    pointsCard,
    BottomPannel,
    AppButton,
    AEmpty,
    tabSelect,
    shopFilter,
  },
  data() {
    return {
      tab: '',
      goodsName: '',
      tabList: [
        {
          id: '',
          name: '全部',
        },
        {
          id: '1',
          name: '<100积分',
        },
        {
          id: '2',
          name: '100-500积分',
        },
        {
          id: '3',
          name: '500-1000积分',
        },
      ],
      cardList: [],
      status: 'more',
      pageNum: 1,
      totalNum: 10,
      goodsMode: '',
      goodsType: '',
      custIntegralNumber: '',
      goodsTypeOptions: [],
    };
  },
  computed: {
    filters() {
      return [
        {
          name: '兑换模式',
          value: '',
          Option: [
            {
              value: '',
              text: '全部',
              type: 'goodsMode',
            },
            {
              value: '01',
              text: '积分',
              type: 'goodsMode',
            },
            {
              value: '02',
              text: '积分+金额',
              type: 'goodsMode',
            },
          ],
        },
        {
          name: '货物类型',
          value: '',
          Option: this.goodsTypeOptions,
        },
      ];
    },
  },
  onLoad() {
    this.getGodsTypeOptions();
    this.getGoods();
  },
  onShow() {
    this.$store.dispatch('login/getUserInfoCallback').then((res) => {
      const { custIntegralNumber } = res;
      this.custIntegralNumber = custIntegralNumber;
    });
  },
  // 触底分页查询
  onReachBottom() {
    if (this.status === 'noMore') return;
    this.getGoods();
  },
  methods: {
    // 获取优惠券说明
    async getGodsTypeOptions() {
      const [err, res] = await getStandardCode('goodsType');
      if (res && res.list) {
        this.goodsTypeOptions = res.list.map((q) => {
          return {
            ...q,
            text: q.name,
            type: 'goodsType',
          };
        });
      }
    },
    // 搜索
    searchGoods() {
      this.cardList = [];
      this.pageNum = 1;
      this.getGoods();
    },
    // tab切换
    changeTab(data) {
      this.tab = data.id;
      this.cardList = [];
      this.pageNum = 1;
      this.getGoods();
    },
    // 查询商品列表
    async getGoods() {
      const params = {
        interval: this.tab,
        goodsName: this.goodsName,
        totalNum: this.totalNum,
        pageNum: this.pageNum,
        goodsMode: this.goodsMode,
        goodsType: this.goodsType,
      };
      const [err, res] = await getGoods(params);
      // this.cardList = [];
      if (res) {
        this.cardList.push(...res.goodsList);
        this.status = res.goodsList.length >= this.totalNum ? 'more' : 'noMore';
        if (this.status === 'more') {
          this.pageNum++;
        }
      }
    },
    // 跳转到兑换记录页面
    jumpToRecord() {
      uni.navigateTo({
        url: '/pages/intergral/redeemRecord/index',
      });
    },
    // 跳转到地址管理页面
    jumpToAddress() {
      uni.navigateTo({
        url: '/pages/addressManage/addressList/index',
      });
    },
    // 筛选
    selectChaneg(val) {
      console.log('调用接口', val);
      if (val.type == 'goodsMode') {
        this.goodsMode = val.value;
      } else if (val.type == 'goodsType') {
        this.goodsType = val.value;
      }
      this.$nextTick(() => {
        this.pageNum = 1;
        this.cardList = [];
        this.getGoods();
      });
    },
    // 积分收支明细
    goMyPoints() {
      uni.navigateTo({
        url: '/pages/intergral/myPoints/index',
      });
    },
    // 跳转积分说明
    jumpToExchange() {
      uni.navigateTo({
        url: '/pages/intergral/exchangeExplain/index',
      });
    },
  },
};
</script>

<style scoped lang="scss">
.intergral-shop {
  &-content {
    padding: 24rpx;
    .search-input {
      padding: 8rpx 24rpx;
      background: white;
      border-radius: 397rpx;
      display: flex;
      align-items: center;
      .search-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 8rpx;
      }
      .input-box {
        height: 52rpx;
        width: 534rpx;
        border-right: 1rpx solid #eeeeee;
        font-size: 30rpx;
        line-height: 28rpx;
        color: #999999;
      }
      .search-text {
        margin-left: 20rpx;
        font-size: 30rpx;
        line-height: 36rpx;
        color: #1e9cff;
      }
    }
    .tag-list {
      margin: 41rpx 0 12rpx 0;
      display: block;
    }
    .my-point-card {
      margin: 28rpx 0 24rpx 0;
      padding: 40rpx 32rpx 20rpx 32rpx;
      border-radius: 16px;
      opacity: 1;
      // background: linear-gradient(114deg, #3fb9f9 16%, #109dfd 81%);
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      background-repeat: no-repeat;
      background-size: cover;
      .my-point-card-span {
        font-size: 28rpx;
        font-weight: 500;
        line-height: 42rpx;
        text-align: center;
        letter-spacing: 0rpx;
        color: #ffffff;
      }
      .my-point-card-tip {
        margin-top: 20rpx;
        font-size: 28rpx;
        font-weight: 500;
        line-height: 42rpx;
        text-align: center;
        letter-spacing: 0rpx;
        color: #ffffff;
      }
      .my-point-card-body {
        display: flex;
        justify-content: space-between;
        width: 100%;
        align-items: flex-end;
        .my-point-card-points {
          font-size: 56rpx;
          font-weight: bold;
          line-height: 42rpx;
          text-align: center;
          letter-spacing: 0rpx;
          color: #ffffff;
        }
      }
      .my-point-card-btns {
        display: flex;
        gap: 12rpx;
        grid-gap: 12rpx;
      }
      .my-point-card-btn {
        height: 58rpx;
        padding: 8rpx 24rpx;
        border-radius: 322rpx;
        opacity: 1;
        box-sizing: border-box;
        border: 2rpx solid rgba(255, 255, 255, 0.5);
      }
    }
    .points-cards {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      margin-bottom: 280rpx;
    }
  }
  &-footer {
    width: 100%;
    .footer-button {
      display: flex;
      gap: 24rpx;
      grid-gap: 24rpx;
      .button-item {
        flex: 1;
      }
    }
  }
}
</style>
