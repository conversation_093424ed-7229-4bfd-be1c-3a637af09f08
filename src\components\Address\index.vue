<!--
@name: 选择地址组件
@description: 描述
@time: 2024/7/30
-->
<template>
  <view class="address">
    <view class="header">收货地址</view>
    <view class="content" @click="chooseAddress">
      <!--            选择地址-->
      <view class="choose" v-if="!addressDetail.custAddrId">
        <img class="nav" :src="`${IMG_PATH}nav.png`" />
        <text class="choose-text">请选择您的收货地址</text>
        <img class="arrow" :src="`${IMG_PATH}arrow.png`" />
      </view>
      <!--         已选择地址-->
      <view class="chosen" v-else>
        <view class="user-info">
          <text class="name">{{ addressDetail.contactName }}</text>
          <text class="phone">{{ addressDetail.contactMobile }}</text>
        </view>
        <view class="address-info">
          <text class="chosen-text"> {{ addressDetail.addrInte }} {{ addressDetail.addr }} </text>
          <img class="arrow" :src="`${IMG_PATH}arrow.png`" />
        </view>
      </view>
    </view>
    <img class="address-line" :src="`${IMG_PATH}/address-line.png`" />
  </view>
</template>

<script>
export default {
  name: 'index',
  props: {
    addressDetail: {
      type: Object,
      default: () => {},
    },
  },
  methods: {
    // 选择地址
    chooseAddress() {
      uni.navigateTo({
        url: '/pages/addressManage/addressList/index',
      });
    },
  },
};
</script>

<style scoped lang="scss">
.address {
  position: relative;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx 23rpx;
  .header {
    font-size: 34rpx;
    font-weight: bold;
    line-height: 41rpx;
    color: #333333;
    margin-bottom: 31rpx;
  }
  .content {
    border-radius: 8rpx;
    border: 1rpx solid #eeeeee;
    .arrow {
      width: 24rpx;
      height: 24rpx;
    }
    .choose {
      display: flex;
      align-items: center;
      padding: 29rpx 24rpx 35rpx 24rpx;
      .nav {
        width: 36rpx;
        height: 36rpx;
      }
      .choose-text {
        font-size: 30rpx;
        line-height: 36rpx;
        color: #333333;
        margin-left: 16rpx;
        margin-right: 260rpx;
      }
    }
    .chosen {
      padding: 29rpx 24rpx 35rpx 24rpx;
      .user-info {
        .name {
          margin-right: 24rpx;
        }
        .name,
        .phone {
          font-size: 28rpx;
          line-height: 42rpx;
          color: #333333;
        }
      }
      .address-info {
        margin-top: 14rpx;
        display: flex;
        .chosen-text {
          width: 558rpx;
          margin-right: 24rpx;
          font-size: 24rpx;
          line-height: 32rpx;
          color: #999999;
        }
      }
    }
  }
  .address-line {
    position: absolute;
    width: 684rpx;
    height: 4rpx;
    bottom: 0rpx;
    right: 12rpx;
  }
}
</style>
