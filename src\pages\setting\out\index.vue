<template>
  <view>
    <view class="empty-box" v-if="loadError">加载失败</view>
    <web-view v-if="showWebView && !loadError" :src="url" @error="error" @load="load" />
  </view>
</template>

<script>
export default {
  props: {},
  components: {},
  data() {
    return {
      url: '',
      showWebView: false,
      loadError: false,
    };
  },
  computed: {},
  watch: {},
  onLoad(options) {
    const { url = '', title } = options;
    // 解码URL并去掉首尾的引号
    let decodedUrl = decodeURIComponent(url);
    // 去掉首尾的单引号或双引号
    this.url = decodedUrl.replace(/^["']|["']$/g, '');
    console.log(this.url, 'this.url');
    if (title) {
      uni.setNavigationBarTitle({
        title: title,
      });
    }
  },
  onShow() {
    this.showWebView = true;
  },
  onUnload() {
    this.showWebView = false;
  },
  mounted() {},
  methods: {
    error() {
      this.loadError = true;
      console.log(this.loadError, 'this.loadError');
      uni.setNavigationBarTitle({
        title: '链接无法访问',
      });
    },
    load() {
      console.log('网站加载成功');
    },
  },
};
</script>

<style scoped lang="scss">
.empty-box {
  width: 100%;
  height: 300rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #7e7979;
  font-weight: 700;
}
</style>
