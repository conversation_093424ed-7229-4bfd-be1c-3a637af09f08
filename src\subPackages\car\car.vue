<template>
  <view>
    <view class="car-msg">
      <view
        @tap="jump"
        :data-img="item.modelImg"
        :data-id="item.modelId"
        :data-name="item.modelName"
        class="car-msg-list"
        v-for="(item, index) in list"
        :key="index"
      >
        <view class="car-img">
          <image :src="item.modelImg || IMG_PATH + 'default-car.png'" mode="aspectFit" />
          <!-- <image wx:else src='../../image/<EMAIL>' /> -->
        </view>

        <view class="car-name">{{ item.modelName }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import { getVehicleInfo } from '@/services';
export default {
  data() {
    return {
      list: [],
      brandId: '',
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    console.log(options, 'optionsoptions');
    this.init(options);
  },
  methods: {
    async init(options) {
      let brandId = options.brandid;
      var params = {
        getLevel: '02',
        brandId: brandId,
        fuelTyle: '01,03',
      };
      let that = this;
      that.setData({
        brandId: brandId,
      });

      const [err, res] = await getVehicleInfo(params);
      if (err) {
      }
      if (res) {
        console.log(res, '获取车型列表');
        that.setData({
          list: res.modelList,
        });
        console.log(that.list);
      }
    },
    jump: function (e) {
      // 存储选择的车辆信息
      const selectedCar = {
        carName: e.currentTarget.dataset.name,
        modelId: e.currentTarget.dataset.id,
        img: e.currentTarget.dataset.img,
        brandId: this.brandId,
      };

      // 将信息存储到本地
      uni.setStorageSync('selectedCar', selectedCar);

      // 获取页面栈
      // const pages = getCurrentPages();

      // 如果是从 add 页面打开的，则返回并跳转到 add 页面
      // if (pages.length > 1) {
      //   const prevPage = pages[pages.length - 2];
      //   if (prevPage.route.includes('add')) {
      //     // 直接返回上一页
      uni.navigateBack({
        delta: 2,
      });
      //   } else {
      //     // 如果上一页不是 add 页面，则关闭当前页面并跳转到 add 页面
      //     uni.redirectTo({
      //       url:
      //         '/subPackages/add/add?carName=' +
      //         selectedCar.carName +
      //         '&modelId=' +
      //         selectedCar.modelId +
      //         '&img=' +
      //         selectedCar.img +
      //         '&brandId=' +
      //         selectedCar.brandId,
      //     });
      //   }
      // } else {
      //   // 如果没有上一页（比如通过分享直接进入），则跳转到 add 页面
      //   uni.redirectTo({
      //     url:
      //       '/subPackages/add/add?carName=' +
      //       selectedCar.carName +
      //       '&modelId=' +
      //       selectedCar.modelId +
      //       '&img=' +
      //       selectedCar.img +
      //       '&brandId=' +
      //       selectedCar.brandId,
      //   });
      // }
      // console.log(e, '666');
      // console.log(e.currentTarget.dataset.name, '车名');
      // console.log(e.currentTarget.dataset.id, '车ID');
      // console.log(e.currentTarget.dataset.img, '图片');
      // uni.navigateTo({
      //   url:
      //     '/subPackages/add/add?carName=' +
      //     e.currentTarget.dataset.name +
      //     '&modelId=' +
      //     e.currentTarget.dataset.id +
      //     '&img=' +
      //     e.currentTarget.dataset.img +
      //     '&brandId=' +
      //     this.brandId,
      // });
    },
  },
};
</script>
<style>
@import './car.css';
</style>
