<!--
@name:商品评价
@description: 描述
@time: 2025/1/3
-->
<template>
  <view class="item-box">
    <view class="evaluate-box">
      <view class="evaluate-box-left">
        <view class="evaluate-box-left-name">
          <view class="name-img">
            <img :src="`${IMG_PATH}defaultAvatar.png`" alt="" />
          </view>
          <view class="name-span">{{ evaluateItem.mobile.slice(7) }}: </view>
        </view>
        <view class="evaluate-box-left-span">{{ evaluateItem.evaRemark }} </view>
      </view>
      <view class="evaluate-box-right" v-if="evaluateItem.imgList[0]">
        <img :src="evaluateItem.imgList[0]" alt="" />
      </view>
    </view>
    <view class="reply-box" v-if="evaluateItem.replyRemark">
      <view class="reply-box-name">
        <view class="name-img">
          <img :src="`${IMG_PATH}shopAvatar.png`" alt="" />
        </view>
        <view class="name-span">商家回复: </view>
      </view>
      <view class="reply-box-span">{{ evaluateItem.replyRemark }} </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'evaluate',
  props: {
    evaluateItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
};
</script>

<style scoped lang="scss">
.item-box {
  padding: 24rpx 0;
  border-bottom: 2rpx solid #eeeeee;
  .evaluate-box {
    display: flex;
    .evaluate-box-left {
      flex: 1;
      .evaluate-box-left-name {
        display: flex;
        align-items: center;
        .name-img {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          img {
            width: 40rpx;
            height: 40rpx;
            border-radius: 50%;
          }
        }
        .name-span {
          font-size: 28rpx;
          font-weight: bold;
          line-height: 42rpx;
          letter-spacing: 0rpx;
          color: #333333;
          margin-left: 8rpx;
        }
      }
      .evaluate-box-left-span {
        margin-top: 4rpx;
        font-size: 28rpx;
        font-weight: normal;
        line-height: 42rpx;
        letter-spacing: 0rpx;
        color: #666666;
        overflow: hidden; /* 隐藏超出容器的部分 */
        text-overflow: ellipsis; /* 显示省略号 */
        display: -webkit-box; /* 设置为弹性盒子 */
        -webkit-line-clamp: 3; /* 限制显示三行文本 */
        -webkit-box-orient: vertical; /* 设置盒子的布局方向为纵向 */
      }
    }
    .evaluate-box-right {
      width: 184rpx;
      height: 184rpx;
      border-radius: 12rpx;
      margin-left: 24rpx;
      img {
        width: 184rpx;
        height: 184rpx;
        border-radius: 12rpx;
      }
    }
  }
  .reply-box {
    margin: 12rpx 31rpx 0 40rpx;
    .reply-box-name {
      display: flex;
      align-items: center;
      .name-img {
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        img {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
        }
      }
      .name-span {
        font-size: 28rpx;
        font-weight: bold;
        line-height: 42rpx;
        letter-spacing: 0rpx;
        color: #333333;
        margin-left: 8rpx;
      }
    }
    .reply-box-span {
      margin-top: 4rpx;
      font-size: 28rpx;
      font-weight: normal;
      line-height: 42rpx;
      letter-spacing: 0rpx;
      color: #666666;
      overflow: hidden; /* 隐藏超出容器的部分 */
      text-overflow: ellipsis; /* 显示省略号 */
      display: -webkit-box; /* 设置为弹性盒子 */
      -webkit-line-clamp: 2; /* 限制显示三行文本 */
      -webkit-box-orient: vertical; /* 设置盒子的布局方向为纵向 */
    }
  }
}
</style>
