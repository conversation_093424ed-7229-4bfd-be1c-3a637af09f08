@import '../../common/css/iconfont';
@import '../../common/css/styleColor'; /* pages/codecharge/codecharge.wxss */
.codecharge {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background-color: #fff;
}
.code {
    height: 588rpx;
    text-align: center;
    border-bottom: 30rpx solid #f5f5f5;
}
.code text {
    display: block;
    padding: 150rpx 0 60rpx 0;
    text-align: center;
    font-size: 38rpx;
}
.code icon {
    display: inline-block;
    padding: 50rpx;
    text-align: center;
    border-radius: 100%;
    color: #fff;
    background-color: #e00a22;
    -moz-box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.3);
    -webkit-box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.3);
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.3);
}
.codecharge .code .red-colors {
    margin-top: 170rpx;
}
.charge {
    background-color: #fff;
}
.charge text {
    display: block;
    padding-left: 70rpx;
    padding-top: 20rpx;
    font-size: 24rpx;
    color: #999;
}
.charge .blue-color {
    display: block;
    padding-top: 170rpx;
    padding-bottom: 40rpx;
    padding-left: 0;
    text-align: center;
    font-size: 38rpx;
    color: #2196f3;
}
.charge .txt-btn {
    display: block;
    width: 620rpx;
    height: 100rpx;
    border: 2rpx solid #b2b2b2;
    border-radius: 4rpx;
    margin: 0 auto;
}
.charge .txt-btn input {
    float: left;
    width: 430rpx;
    padding-left: 20rpx;
    height: 100rpx;
    font-size: 32rpx;
    text-align: left;
}
.charge .txt-btn button {
    float: right;
    width: 140rpx;
    height: 72rpx;
    line-height: 72rpx;
    text-align: center;
    overflow: hidden;
    font-size: 28rpx;
    background: #f4f6f5;
    color: #999;
    margin: 13rpx;
    padding: 0;
}
button {
    border-radius: 3rpx;
    background: #f4f6f5;
}
.charge .txt-btn .color-btn {
    color: #fff;
    background: #e00a22;
}
