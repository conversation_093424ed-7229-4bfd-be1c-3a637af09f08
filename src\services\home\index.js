import { request as __request } from '../request/request';
import {
  ENVR,
  scanBaseurl,
  pubBaseurl,
  astBaseurl,
  defBaseurl,
  busiBaseurl,
  bilBaseurl,
  cstBaseurl,
  ordBaseurl,
  baseBaseurl,
} from '../../config/global';
const baseurl = ENVR === 'wx' ? scanBaseurl : baseBaseurl;

// 获取未使用的优惠券数量
export const getMsgUnusedTotal = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: ENVR === 'wx' ? 'POST' : 'GET',
      url: `${baseurl}/v0.1/msg-unread-total`,
      data: params,
    }
  );
};
// 获取是否有可以领取的优惠券
export const getMsgUnused = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl + '/v0.1/coupons/get' : bilBaseurl + '/v0.1/coupons'}`,
      data: params,
    }
  );
};
// 获取首页 banner
export const getHomeBanner = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${pubBaseurl}/open/v0.1/home-msg-loading`,
      query: params,
    }
  );
};
// 获取充电中的订单
// H5接口：orderStatus=01,02,03,04,05
export const getChargingOrder = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ENVR == 'wx' ? scanBaseurl : defBaseurl}/v0.1/orderChargeInfo`,
      query: { ...params },
    }
  );
};
// 获取推荐站点
export const getV06ChargingStation = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${astBaseurl}/open/v0.6/charging-stations`,
      query: params,
    }
  );
};
// 获取收藏站点
// !H5:接口报错 H5对应接口未提供
export const getCollectChargingStation = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: ENVR == 'wx' ? 'POST' : 'GET',
      url: `${ENVR == 'wx' ? scanBaseurl + '/v0.1/pref-search' : baseBaseurl + '/v0.1/pref'}`,
      data: params,
    }
  );
};
// 交易记录
export const getAccountsTradelog = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: ENVR == 'wx' ? 'POST' : 'GET',
      url: `${ENVR == 'wx' ? scanBaseurl : defBaseurl}/v0.1/accounts/tradelog`,
      data: params,
    }
  );
};
// 获取节能勋章数据
export const getCutInfo = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ENVR === 'wx' ? scanBaseurl : ordBaseurl}/v0.1/order-cust-operstat`,
      data: params,
    }
  );
};
