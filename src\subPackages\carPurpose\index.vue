<template>
  <view>
    <view class="type-box" v-for="(item, index) in resCarList" :key="index">
      <view class="type-box-title">{{ item.codeName }}</view>
      <view class="type-box-cars">
        <view
          :class="it.selected ? 'type-box-car-item type-box-car-item-select' : 'type-box-car-item'"
          v-for="(it, i) in item.subList"
          :key="i"
          @click="selectPurpose(it)"
        >
          <image :src="`${IMG_PATH}selectCar.png`" class="type-box-car-item-select-icon" v-if="it.selected" />
          <image :src="`${IMG_PATH}${it.img}`" class="type-box-car-item-image" />
          <view class="type-box-car-item-name">{{ it.codeName }}</view>
        </view>
      </view>
    </view>
    <!--        底部按钮-->
    <view class="footer">
      <BottomPannel>
        <view class="footer-button">
          <AppButton class="button-item" type="primary" @click="submitClick">完成</AppButton>
        </view>
      </BottomPannel>
    </view>
  </view>
</template>

<script>
import BottomPannel from '@/components/BottomPannel/index';
import AppButton from '@/components/AppButton/index';
import { getMyCarUsetype } from '@/services';

export default {
  name: 'carPurpose',
  components: {
    BottomPannel,
    AppButton,
  },
  data() {
    return {
      carList: [
        {
          title: '营运车',
          list: [
            {
              name: '网约车',
              img: 'car1.png',
              selected: false,
            },
            {
              name: '出租车',
              img: 'car2.png',
              selected: false,
            },
            {
              name: '货运车',
              img: 'car3.png',
              selected: false,
            },
            {
              name: '大巴车',
              img: 'car4.png',
              selected: false,
            },
          ],
        },
        {
          title: '非营运车',
          list: [
            {
              name: '私家车',
              img: 'car1-1.png',
              selected: false,
            },
            {
              name: '分时租赁',
              img: 'car1-2.png',
              selected: false,
            },
            {
              name: '企事业单位',
              img: 'car1-3.png',
              selected: false,
            },
            {
              name: '机关单位',
              img: 'car1-4.png',
              selected: false,
            },
            {
              name: '公交车',
              img: 'car1-5.png',
              selected: false,
            },
            {
              name: '其他',
              img: 'car1-6.png',
              selected: false,
            },
          ],
        },
      ],
      userPurpose: {},
      resCarList: [],
      imgList: [
        {
          codeValue: '0101',
          img: 'car1.png',
        },
        {
          codeValue: '0102',
          img: 'car2.png',
        },
        {
          codeValue: '0103',
          img: 'car3.png',
        },
        {
          codeValue: '0104',
          img: 'car4.png',
        },
        {
          codeValue: '0201',
          img: 'car1-1.png',
        },
        {
          codeValue: '0202',
          img: 'car1-2.png',
        },
        {
          codeValue: '0203',
          img: 'car1-3.png',
        },
        {
          codeValue: '0204',
          img: 'car1-4.png',
        },
        {
          codeValue: '0205',
          img: 'car1-5.png',
        },
        {
          codeValue: '0206',
          img: 'car1-6.png',
        },
      ],
    };
  },
  onLoad(options) {},
  methods: {
    // 获取车辆用途
    async getData() {
      const [err, res] = await getMyCarUsetype();
      if (res) {
        if (res && res.ret == 200) {
          this.resCarList = res.list;
          this.resCarList.forEach((element) => {
            element.subList.forEach((item) => {
              this.imgList.forEach((it) => {
                if (it.codeValue == item.codeValue) {
                  item.img = it.img;
                }
              });
              item.selected = false;
            });
          });
          console.log('this.resCarList', this.resCarList);
        }
      }

      // uni.request({
      //   url: baseUrl + 'base/open/api/v0.1/codes/myCarUsetype',
      //   data: {},
      //   method: 'GET',
      //   header: {
      //     'content-type': 'application/json',
      //   },
      //   success: (res) => {
      //     // console.log('获取车辆用途', res);
      //     if (res.data && res.data.ret == 200) {
      //       this.resCarList = res.data.list;
      //       this.resCarList.forEach((element) => {
      //         element.subList.forEach((item) => {
      //           this.imgList.forEach((it) => {
      //             if (it.codeValue == item.codeValue) {
      //               item.img = it.img;
      //             }
      //           });
      //           item.selected = false;
      //         });
      //       });
      //       console.log('this.resCarList', this.resCarList);
      //     }
      //   },
      //   fail: (err) => {
      //     console.log(err, '请求出问题。');
      //   },
      // });
    },
    // 点击选择用途
    selectPurpose(val) {
      if (!val) {
        console.warn('选择的车辆用途无效');
        return;
      }

      // 如果点击已选中的项目，则取消选择
      const isCurrentlySelected = val.selected;

      // 先清除所有选中状态
      this.resCarList.forEach((category) => {
        category.subList.forEach((item) => {
          item.selected = false;
        });
      });

      // 如果不是取消选择的操作，则设置新的选中状态
      if (!isCurrentlySelected) {
        // 找到对应的项目并设置选中状态
        const targetCategory = this.resCarList.find((category) =>
          category.subList.some((item) => item.codeValue === val.codeValue)
        );

        if (targetCategory) {
          const targetItem = targetCategory.subList.find((item) => item.codeValue === val.codeValue);

          if (targetItem) {
            targetItem.selected = true;
            this.userPurpose = targetItem;
          }
        }
      } else {
        // 如果是取消选择，清空用户选择
        this.userPurpose = {};
      }

      // 强制更新视图
      this.$forceUpdate();
    },
    // 完成
    submitClick() {
      console.log(this.userPurpose);
      if (!this.userPurpose.codeName) {
        uni.showToast({
          title: '请选择您的车辆用途',
          icon: 'none',
          duration: 1200,
        });
        return;
      }
      this.$store.dispatch('common/setUserPurpose', this.userPurpose.codeName);
      this.$store.dispatch('common/setUserPurposeValue', this.userPurpose.codeValue);
      uni.redirectTo({
        url: `/subPackages/add/add`,
      });
    },
  },
  mounted() {
    this.getData();
  },
};
</script>

<style>
@import './index.css';
</style>
