<template>
  <view class="billingpersonal">
    <view class="billingpersonal-lists">
      <view class="list-main">
        <view class="list-main-hd">
          <text>*</text>
          发票主体
        </view>
        <radio-group class="list-main-bd" @change="radioChange">
          <view class="radio" v-for="(item, index) in radioItems" :key="index">
            <radio :id="item.name" :value="item.name" :checked="item.checked"></radio>

            <text>{{ item.value }}</text>
          </view>
        </radio-group>
      </view>

      <view class="list-main-hd"></view>

      <view class="list-main">
        <view class="list-main-hd">
          <text>*</text>
          发票抬头
        </view>
        <label class="list-main-bd">
          <input
            :value="taitou"
            type="text"
            @input="taiTou"
            placeholder="请输入发票抬头"
            placeholder-style="color:#999999"
          />
        </label>
      </view>
      <view v-if="personal" class="list-main">
        <view class="list-main-hd">
          <text>*</text>
          税号
        </view>
        <label class="list-main-bd">
          <input
            :value="shuihao"
            type="text"
            @input="shuiHao"
            placeholder="请输入纳税公司税号"
            placeholder-style="color:#999999"
          />
        </label>
      </view>
      <view v-if="personal" class="list-main">
        <view class="list-main-hd">开户银行</view>
        <label class="list-main-bd">
          <input
            type="text"
            :value="bankName"
            @input="bankNameInp"
            placeholder="请输入开户银行"
            placeholder-style="color:#999999"
          />
        </label>
      </view>
      <view v-if="personal" class="list-main">
        <view class="list-main-hd">银行账号</view>
        <label class="list-main-bd">
          <input
            type="text"
            :value="bankAccount"
            @input="bankAccountInp"
            placeholder="请输入银行账号"
            placeholder-style="color:#999999"
          />
        </label>
      </view>
      <view class="list-main main-switch">
        <view class="list-main-hd">设为默认抬头</view>
        <switch :checked="defaultFlag === '1'" color="#2196F3" @change="switchChange" />
      </view>
    </view>
    <view class="bottom-bg">
      <button v-if="panduan" @tap="open" class="blue-bg" type="">确认</button>
      <button v-else class="blue-bg color-tj" type="">确认</button>
    </view>
    <!-- <loading v-if="!hiddenLoading">请稍后...</loading> -->
  </view>
</template>

<script>
import { getInvoiceTitleInfo, addInvoiceTitle, updataInvoiceTitleInfo } from '@/services';
export default {
  data() {
    return {
      hiddenLoading: true,
      panduan: false,
      typed: '01',
      taitou: '',
      shuihao: '',
      token: '',
      radioItems: [
        {
          name: 'personal',
          value: '个人',
          checked: 'true',
        },
        {
          name: 'enterprise',
          value: '企业',
        },
      ],
      personal: false,
      bankName: '',
      bankAccount: '',
      defaultFlag: '0',
      invoiceTitleId: '',
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */ onLoad: function (options) {
    const that = this;
    const { invoiceTitleId } = options;
    that.setData({
      token: uni.getStorageSync('token') || '',
    });
    if (invoiceTitleId) {
      that.setData(
        {
          invoiceTitleId,
        },
        () => {
          that.getInvoiceInfo();
        }
      );
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {},
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {},
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {},
  methods: {
    switchChange: function (event) {
      const { value } = event.detail;
      this.setData({
        defaultFlag: value ? '1' : '0',
      });
    },

    radioChange: function (e) {
      var checked = e.detail.value;
      var changed = {};
      var that = this;
      for (var i = 0; i < that.radioItems.length; i++) {
        if (checked.indexOf(that.radioItems[i].name) !== -1) {
          changed['radioItems[' + i + '].checked'] = true;
          that.setData(
            {
              personal: true,
              typed: '02',
            },
            () => {
              this.panDuan();
            }
          );
        } else {
          changed['radioItems[' + i + '].checked'] = false;
          that.setData(
            {
              personal: false,
              typed: '01',
            },
            () => {
              this.panDuan();
            }
          );
        }
      }
      that.setData(changed, () => {
        this.panDuan();
      });
    },

    panDuan: function () {
      const { personal, shuihao, taitou } = this;
      let buttonFlag = false;
      if (personal) {
        if (shuihao && taitou) {
          buttonFlag = true;
        } else {
          buttonFlag = false;
        }
      } else {
        if (taitou) {
          buttonFlag = true;
        } else {
          buttonFlag = false;
        }
      }
      this.setData({
        panduan: buttonFlag,
      });
    },

    async getInvoiceInfo() {
      const that = this;
      const { token, invoiceTitleId } = this;
      try {
        that.setData({
          hiddenLoading: true,
        });
        const [err, res] = await getInvoiceTitleInfo({ invoiceTitleId });
        that.setData({
          hiddenLoading: false,
        });
        if (res) {
          const { bankAccount, bankName, defaultFlag, invoiceTitle, invoiceTitleType, taxNum } = res.invoiceTitleInfo;
          const { radioItems } = that;
          radioItems.forEach((item) => {
            item.checked = false;
          });
          if (invoiceTitleType === '02') {
            radioItems[1].checked = true;
          } else {
            radioItems[0].checked = true;
          }
          that.setData({
            hiddenLoading: true,
          });
          that.setData(
            {
              bankAccount,
              bankName,
              defaultFlag,
              typed: invoiceTitleType,
              shuihao: taxNum,
              taitou: invoiceTitle,
              personal: invoiceTitleType === '02' ? true : false,
              radioItems,
            },
            () => {
              this.panDuan();
            }
          );
        }
      } catch (error) {
        that.setData({
          hiddenLoading: false,
        });
      }

      // uni.request({
      //   url: baseUrl + '/wx/v0.2/invoice-title-Info',
      //   data: {
      //     invoiceTitleId,
      //   },
      //   method: 'POST',
      //   header: {
      //     'content-type': 'application/json',
      //     minitProgramToken: token,
      //   },
      //   success: (res) => {
      //     const { bankAccount, bankName, defaultFlag, invoiceTitle, invoiceTitleType, taxNum } =
      //       res.data.invoiceTitleInfo;
      //     const { radioItems } = that;
      //     radioItems.forEach((item) => {
      //       item.checked = false;
      //     });
      //     if (invoiceTitleType === '02') {
      //       radioItems[1].checked = true;
      //     } else {
      //       radioItems[0].checked = true;
      //     }
      //     that.setData({
      //       hiddenLoading: true,
      //     });
      //     that.setData(
      //       {
      //         bankAccount,
      //         bankName,
      //         defaultFlag,
      //         typed: invoiceTitleType,
      //         shuihao: taxNum,
      //         taitou: invoiceTitle,
      //         personal: invoiceTitleType === '02' ? true : false,
      //         radioItems,
      //       },
      //       () => {
      //         this.panDuan();
      //       }
      //     );
      //   },
      //   fail: () => {},
      // });
    },

    taiTou: function (value) {
      console.log(value);
      this.setData(
        {
          taitou: value.detail.value,
        },
        () => {
          this.panDuan();
        }
      );
    },

    shuiHao: function (value) {
      console.log(value);
      this.setData(
        {
          shuihao: value.detail.value,
        },
        () => {
          this.panDuan();
        }
      );
    },

    bankNameInp: function (event) {
      this.setData({
        bankName: event.detail.value,
      });
    },

    bankAccountInp: function (event) {
      this.setData({
        bankAccount: event.detail.value,
      });
    },

    async open() {
      var that = this;
      const { taitou, shuihao, typed, defaultFlag, bankAccount, bankName, token, invoiceTitleId } = this;
      that.setData({
        hiddenLoading: false,
      });

      const [err, res] = await addInvoiceTitle({
        invoiceTitle: taitou,
        invoiceTitleType: typed,
        defaultFlag,
        bankName,
        bankAccount,
        taxNum: shuihao,
        invoiceTitleId,
      });
      if (err) {
        uni.showToast({
          title: invoiceTitleId ? err.msg || '保存抬头失败！' : err.msg || '新增抬头失败！',
          icon: 'none',
          duration: 1200,
        });
        return;
      }
      if (res) {
        const { ret, msg } = res;
        that.setData({
          hiddenLoading: true,
        });
        if (ret === 200) {
          uni.showToast({
            title: invoiceTitleId ? msg || '保存抬头成功！' : msg || '新增抬头成功！',
            icon: 'success',
            duration: 1200,
          });
          setTimeout(() => {
            uni.navigateBack({
              delit: -1,
            });
          }, 800);
        } else {
          uni.showToast({
            title: msg || ret,
            icon: 'none',
            duration: 1200,
          });
        }
      }
      if (invoiceTitleId) {
        const [err, res] = await updataInvoiceTitleInfo({
          invoiceTitle: taitou,
          invoiceTitleType: typed,
          defaultFlag,
          bankName,
          bankAccount,
          taxNum: shuihao,
          invoiceTitleId,
        });
        if (err) {
          uni.showToast({
            title: invoiceTitleId ? err.msg || '保存抬头失败！' : err.msg || '新增抬头失败！',
            icon: 'none',
            duration: 1200,
          });
          return;
        }
        if (res) {
          const { ret, msg } = res;
          that.setData({
            hiddenLoading: true,
          });
          if (ret === 200) {
            uni.showToast({
              title: invoiceTitleId ? msg || '保存抬头成功！' : msg || '新增抬头成功！',
              icon: 'success',
              duration: 1200,
            });
            setTimeout(() => {
              uni.navigateBack({
                delit: -1,
              });
            }, 800);
          } else {
            uni.showToast({
              title: msg || ret,
              icon: 'none',
              duration: 1200,
            });
          }
        }
      }

      // let url = '/wx/v0.2/invoice-title-add';
      // if (invoiceTitleId) {
      //   url = '/wx/v0.2/invoice-title-update';
      // }
      // uni.request({
      //   url: baseUrl + url,
      //   data: {
      //     invoiceTitle: taitou,
      //     invoiceTitleType: typed,
      //     defaultFlag,
      //     bankName,
      //     bankAccount,
      //     taxNum: shuihao,
      //     invoiceTitleId,
      //   },
      //   method: 'POST',
      //   header: {
      //     'content-type': 'application/json',
      //     minitProgramToken: token,
      //   },
      //   success: (res) => {
      //     const { ret, msg } = res.data;
      //     that.setData({
      //       hiddenLoading: true,
      //     });
      //     if (ret === 200) {
      //       uni.showToast({
      //         title: invoiceTitleId ? '保存抬头成功！' : '新增抬头成功！',
      //         icon: 'success',
      //         duration: 1200,
      //       });
      //       setTimeout(() => {
      //         uni.navigateBack({
      //           delit: -1,
      //         });
      //       }, 800);
      //     } else {
      //       uni.showToast({
      //         title: msg || ret,
      //         icon: 'none',
      //         duration: 1200,
      //       });
      //     }
      //   },
      //   fail: () => {
      //     that.setData({
      //       hiddenLoading: true,
      //     });
      //     uni.showToast({
      //       title: invoiceTitleId ? '保存抬头失败！' : '新增抬头失败！',
      //       icon: 'none',
      //       duration: 1200,
      //     });
      //   },
      // });
    },
  },
};
</script>
<style>
@import './detail.css';
</style>
