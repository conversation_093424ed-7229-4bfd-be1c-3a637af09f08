<!--
@name: 续费管理
@description: 续费管理模块
@time: 2024/8/9
-->
<template>
  <view class="renewal-management">
    <!--        续费卡片-->
    <view class="card">
      <text class="title">VIP自动续费</text>
      <view class="card-item">
        <view class="left">
          <text class="left-item">VIP自动续费</text>
          <text class="left-item">未开启</text>
        </view>
        <text class="right" @click="openRenewal">开启自动续费</text>
      </view>
      <view class="line"></view>
      <view class="card-item">
        <view class="left">
          <text class="left-item">开通平台</text>
          <text class="left-item">暂无</text>
        </view>
      </view>
      <view class="line"></view>
    </view>
    <!--        服务说明-->
    <view class="service-description">
      <text class="title">服务说明</text>
      <text class="description">
        · 开通本服务后，将在订阅有效期前1天为您自动续费，续费成功，自动延长相应时长； ·
        续费商品类型以您开通连续扣费的商品类型为准； · 如您还需其他帮助，请拨打05932059687。
      </text>
    </view>
    <!--        关闭续费提示弹框-->
    <uni-popup ref="alertDialog" type="dialog">
      <uni-popup-dialog
        :type="msgType"
        cancelText="确认取消"
        confirmText="我再想想"
        title="温馨提示"
        content="取消自动续费后，可能无法获得自动续费优惠"
        @confirm="dialogConfirm"
        @close="dialogClose"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup';
import uniPopupDialog from '@/components/uni-ui/uni-popup-dialog/uni-popup-dialog';

export default {
  name: 'index',
  components: {
    uniPopup,
    uniPopupDialog,
  },
  data() {
    return {
      msgType: 'info',
    };
  },
  methods: {
    // 打开续费弹框
    openRenewal() {
      this.$refs.alertDialog.open();
    },
    // 确认提交
    dialogConfirm() {},
    // 关闭弹框
    dialogClose() {},
  },
};
</script>

<style scoped lang="scss">
.renewal-management {
  height: 100%;
  background: linear-gradient(180deg, #b9def2 3%, rgba(185, 222, 242, 0) 57%);
  padding: 32rpx 24rpx 0 24rpx;
  .card {
    background: white;
    border-radius: 16rpx;
    display: flex;
    flex-direction: column;
    padding-bottom: 24rpx;
    .title {
      font-size: 32rpx;
      font-weight: bold;
      line-height: 38rpx;
      color: #333333;
      padding: 24rpx 24rpx 0 24rpx;
    }
    .card-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx 24rpx;
      .left {
        display: flex;
        flex-direction: column;
        &-item:nth-child(1) {
          font-size: 24rpx;
          line-height: 32rpx;
          color: #999999;
        }
        &-item:nth-child(2) {
          font-size: 28rpx;
          font-weight: bold;
          line-height: 42rpx;
          color: #333333;
          margin-top: 8rpx;
        }
      }
      .right {
        font-size: 28rpx;
        font-weight: bold;
        line-height: 42rpx;
        color: #0398fd;
      }
    }
    .line {
      height: 1rpx;
      background: #eeeeee;
      margin: 0 4rpx;
    }
  }
  .service-description {
    padding: 24rpx 16rpx;
    .title {
      font-size: 32rpx;
      font-weight: bold;
      line-height: 31rpx;
      color: #333333;
      display: block;
      margin-bottom: 24rpx;
    }
    .description {
      font-size: 26rpx;
      line-height: 39rpx;
      color: #9e9e9e;
    }
  }
}
</style>
