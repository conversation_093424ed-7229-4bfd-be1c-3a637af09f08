<template>
  <view>
    <view v-for="(item, index) in msg" :key="index">
      <view class="month">{{ item.date }}</view>

      <view class="msg_view">
        <view class="msg" v-for="(item, index1) in item.data" :key="index1">
          <view class="msg_img"><image :src="item.Imgsrc"></image></view>

          <view class="msg_page">
            <view>{{ item.remark || item.msg }}</view>
            <view>{{ item.createTime }}</view>
          </view>

          <view class="msg_price">
            <text>{{ item.actAmt }}元</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getAccountsTradelog } from '@/services/index.js';
export default {
  data() {
    return {
      msg: [],
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */ onLoad: function (options) {},
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    let that = this;
    that.getAccountsTradelog();
    // uni.request({
    //   url: baseUrl + '/wx/v0.1/accounts/tradelog',
    //   data: {},
    //   method: 'GET',
    //   header: {
    //     'content-type': 'application/x-www-form-urlencoded',
    //     minitProgramToken: uni.getStorageSync('token'),
    //   },
    //   success: (res) => {
    //     console.log(res, '交易记录');
    //     var a = res.data.tradeList;
    //     var lp = [];
    //     for (var i = 0; i < a.length; i++) {
    //       if (a[i].appType == '01') {
    //         a[i].msg = '充值';
    //         a[i].Imgsrc = this.IMG_PATH + 'c2.png';
    //       } else if (a[i].appType == '02') {
    //         a[i].msg = '消费';
    //         a[i].Imgsrc = this.IMG_PATH + 'c2.png';
    //       } else if (a[i].appType == '05') {
    //         a[i].msg = '提现';
    //         a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
    //       } else if (a[i].appType == '06') {
    //         a[i].msg = '补收';
    //         a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
    //       } else if (a[i].appType == '08') {
    //         a[i].msg = '转账';
    //         a[i].Imgsrc = this.IMG_PATH + '<EMAIL>'; //?
    //       } else if (a[i].appType == '09') {
    //         a[i].msg = '退款';
    //         a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
    //       } else if (a[i].appType == '13') {
    //         a[i].msg = '开票';
    //         a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
    //       } else if (a[i].appType == '14') {
    //         a[i].msg = '冲正';
    //         a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
    //       } else if (a[i].appType == '15') {
    //         a[i].msg = '违章';
    //         a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
    //       } else if (a[i].appType == '16') {
    //         a[i].msg = '车损';
    //         a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
    //       } else if (a[i].appType == '17') {
    //         a[i].msg = '事故赔偿';
    //         a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
    //       } else if (a[i].appType == '18') {
    //         a[i].msg = '物流收入';
    //         a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
    //       } else if (a[i].appType == '21') {
    //         a[i].msg = '原路退款';
    //         a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
    //       } else {
    //         a[i].msg = '--';
    //         a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
    //       }
    //       lp.push(a[i]);
    //     }
    //     console.log(lp, 'lp');
    //     let data = {};
    //     lp.forEach((item) => {
    //       let date = item.createTime.substr(0, 7);
    //       if (!data[date]) {
    //         data[date] = [];
    //       }
    //       data[date].push(item);
    //     });
    //     let arr = [];
    //     Object.keys(data).forEach((date) => {
    //       arr.push({
    //         date,
    //         data: data[date],
    //       });
    //     });
    //     // arr.reverse()
    //     console.log(arr);
    //     that.setData({
    //       msg: arr,
    //     });
    //   },
    //   fail: (err) => {
    //     console.log(err, '请求出问题。');
    //   },
    // });
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {},
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {},
  methods: {
    async getAccountsTradelog(params) {
      let that = this;
      const [err, res] = await getAccountsTradelog(params);
      if (err) {
        return;
      }
      if (res) {
        var a = res.tradeList;
        var lp = [];
        for (var i = 0; i < a.length; i++) {
          if (a[i].appType == '01') {
            a[i].msg = '充值';
            a[i].Imgsrc = this.IMG_PATH + 'c2.png';
          } else if (a[i].appType == '02') {
            a[i].msg = '消费';
            a[i].Imgsrc = this.IMG_PATH + 'c2.png';
          } else if (a[i].appType == '05') {
            a[i].msg = '提现';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '06') {
            a[i].msg = '补收';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '08') {
            a[i].msg = '转账';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>'; //?
          } else if (a[i].appType == '09') {
            a[i].msg = '退款';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '13') {
            a[i].msg = '开票';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '14') {
            a[i].msg = '冲正';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '15') {
            a[i].msg = '违章';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '16') {
            a[i].msg = '车损';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '17') {
            a[i].msg = '事故赔偿';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '18') {
            a[i].msg = '物流收入';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '21') {
            a[i].msg = '原路退款';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else {
            a[i].msg = '--';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          }
          lp.push(a[i]);
        }
        console.log(lp, 'lp');
        let data = {};
        lp.forEach((item) => {
          let date = item.createTime.substr(0, 7);
          if (!data[date]) {
            data[date] = [];
          }
          data[date].push(item);
        });
        let arr = [];
        Object.keys(data).forEach((date) => {
          arr.push({
            date,
            data: data[date],
          });
        });
        // arr.reverse()
        console.log(arr);
        that.setData({
          msg: arr,
        });
      }
    },
  },
};
</script>
<style>
@import './history.css';
</style>
