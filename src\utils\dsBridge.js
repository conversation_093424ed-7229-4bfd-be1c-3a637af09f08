import dsBridge from 'dsbridge';

export default {
  callMethod(name, params, callback) {
    callback(dsBridge.call(name, params, {}));
  },
  registerMethod(tag, callback) {
    dsBridge.register(tag, callback);
  },
  promisifyDsAsync(fnString) {
    return function (content) {
      return new Promise(async (resolve, reject) => {
        if (dsBridge.hasNativeMethod(fnString)) {
          dsBridge.call(fnString, content, (v) => {
            console.log('H5:', v);
            try {
              const value = JSON.parse(v);
              resolve(value);
            } catch (error) {
              resolve(null);
            }
          });
        } else {
          resolve(null);
        }
      });
    };
  },
};
