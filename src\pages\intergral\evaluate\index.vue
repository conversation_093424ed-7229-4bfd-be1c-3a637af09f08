<template>
  <view class="rate-box">
    <view class="rate-card">
      <view class="rate-item" v-for="(item, index) in rateList" :key="index">
        <view>{{ item.rateName }}:</view>
        <select-rate v-model="item.rateValue" @rateOnChange="rateOnChange($event, item)"></select-rate>
      </view>
    </view>
    <view class="evaluate-content">
      <textarea v-model="content" maxlength="100" placeholder="请输入评价内容" style="height: 260rpx" />
      <view class="total-num">{{ showCharacterCount }}</view>
    </view>
    <view class="upload-img">
      <view class="upload-title"> 添加{{ imgList.length }}/3 </view>
      <!-- <view class="imgs-box">
        <view v-for="(item, index) in imgList" :key="index" class="img-item">
          <image :src="item" />
          <view @click="delImg" :data-key="index" class="close-btn">×</view>
        </view>
        <view @click="chooseimage" class="add-box" v-if="imgList.length < 3">
          <view class="add-box-icon">+</view>
          <view class="add-box-span">上传照片</view>
        </view>
      </view> -->
      <view class="imgs-box">
        <l-upload
          @complete="imgChange"
          :limit="3"
          uploadType="img"
          width="160"
          height="160"
          :formData="imgFormData"
          :header="imgHeader"
          :sourceType="['album']"
        ></l-upload>
      </view>
    </view>
    <!--        底部按钮-->
    <view class="footer">
      <BottomPannel>
        <view class="footer-button">
          <AppButton class="button-item" type="primary" @click="submitClick">确定</AppButton>
        </view>
      </BottomPannel>
    </view>
  </view>
</template>

<script>
import selectRate from '@/components/selectRate/index.vue';
const { chooseImg, getImageTobase64_url } = require('../../../utils/upload');
import BottomPannel from '@/components/BottomPannel/index';
import AppButton from '@/components/AppButton/index';
import { baseDateNo } from '@/utils/base.js';
import lUpload from '@/components/l-upload/l-upload';
import { shopOrderEvaApi } from '@/services/index.js';
import { ENVR } from '@/config/global';
const baseUrl = baseDateNo();

export default {
  name: 'evaluate',
  components: {
    selectRate,
    BottomPannel,
    AppButton,
    lUpload,
  },
  data() {
    return {
      selectRateId: 1,
      selectRateNum: null,
      rateList: [
        {
          id: '0',
          rateName: '总体评分',
          rateValue: 0,
        },
        {
          id: '1',
          rateName: '货物质量',
          rateValue: 0,
        },
        {
          id: '2',
          rateName: '描述相符',
          rateValue: 0,
        },
        {
          id: '3',
          rateName: '物流服务',
          rateValue: 0,
        },
      ],
      content: '',
      imgList: [],
      integralId: '',
      goodsId: '',
      // integralId: '',
      actionUrl: baseUrl + 'scan/wx/v0.1/file-upload',
      imgFormData: {
        contentType: '02',
        relaId: this.integralId,
        relaTable: 'orderEva',
      },
      imageList: [],
      imgHeader: {
        // #ifdef MP
        minitProgramToken: uni.getStorageSync('token') || '',
        // #endif
        // #ifdef H5
        authorization: uni.getStorageSync('token') || '',
        // #endif
      },
    };
  },
  computed: {
    showCharacterCount() {
      const count = this.content.length;
      return `${count}/100`; // 展示输入到第几个字符
    },
  },
  onLoad(options) {
    console.log('options', options);
    this.integralId = options.integralId;
    this.goodsId = options.goodsId;
    // this.integralId = options.integralId;
    console.log('ENVR', ENVR);
    if (ENVR == 'wx') {
      this.actionUrl = baseUrl + 'scan/wx/v0.1/file-upload';
    } else {
      this.actionUrl = baseUrl + 'base/api/v0.1/file-upload';
    }
  },
  methods: {
    // 子组件点击评分事件
    rateOnChange(index, item) {
      console.log('子组件点击返回', index, item);
      this.selectRateId = item.id;
      this.selectRateNum = index + 1;
      if (this.selectRateId && this.selectRateNum) {
        this.rateList.forEach((item) => {
          if (item.id == this.selectRateId) {
            item.rateValue = this.selectRateNum;
          }
        });
      }
    },
    // 上传图片
    async chooseImg() {
      const [err, data] = await chooseImg();
      const imgBase64 = await getImageTobase64_url(data[0]);
      this.imgList.push({
        image: imgBase64,
      });
      console.log('this.imgList', this.imgList);
    },
    // 删除图片
    delImg(e) {
      const index = e.currentTarget.dataset.key;
      this.imgList.splice(index, 1);
    },
    // 提交评价
    submitClick() {
      console.log('评分', this.rateList);
      console.log('评价内容', this.content);
      console.log('评论图片', this.imgList);
      var that = this;
      uni.getStorage({
        key: 'token',
        success: function (res) {
          that.setData({
            token: res.data,
          });
          let token = res.data;
          let orderEvaItemList = [
            {
              evaItemCode: '0301',
              evaScore: that.rateList[1].rateValue * 2,
            },
            {
              evaItemCode: '0302',
              evaScore: that.rateList[2].rateValue * 2,
            },
            {
              evaItemCode: '0303',
              evaScore: that.rateList[3].rateValue * 2,
            },
          ];
          let oderEvaPicList = [
            {
              evaPic: that.imgList[0] || '',
            },
            {
              evaPic: that.imgList[1] || '',
            },
            {
              evaPic: that.imgList[2] || '',
            },
          ];
          let data = {
            orderNo: that.integralId, // 订单号
            evaUserType: '02', //评价方类型，01-商家，02-客户
            evaScore: that.rateList[0].rateValue * 2, //总评分
            evaRemark: that.content, //评论
            orderEvaItemList: JSON.stringify(orderEvaItemList), //剩余评分
            evaObjectId: that.goodsId, // 商品id
            evaUrl: '123', // 图片
          };
          console.log(that.integralId);

          console.log('data', data);

          that.submitApi(data);
          // //
          // uni.request({
          //   url: baseUrl + '/wx/v0.1/order-eva',
          //   data: {
          //     orderNo: that.integralId, // 订单号
          //     evaUserType: '02', //评价方类型，01-商家，02-客户
          //     evaScore: that.rateList[0].rateValue * 2, //总评分
          //     evaRemark: that.content, //评论
          //     orderEvaItemList: JSON.stringify(orderEvaItemList), //剩余评分
          //     evaObjectId: that.goodsId, // 商品id
          //     evaUrl: that.imgList.join(', '), // 图片
          //   },
          //   method: 'POST',
          //   header: {
          //     'content-type': 'application/json',
          //     minitProgramToken: token,
          //   },
          //   success: async (res) => {
          //     console.log(res, '评价成功');
          //     if (that.imgList.length > 0) {
          //       await that.imgList.forEach((item) => {
          //         uni.uploadFile({
          //           url: baseUrl + '/wx/v0.1/file-upload',
          //           filePath: item,
          //           //待上传的图片，由 chooseImage获得
          //           name: 'food_image',
          //           formData: {
          //             contentType: '02',
          //             relaId: that.integralId,
          //             relaTable: 'orderEva',
          //           },
          //           // HTTP 请求中其他额外的 form data
          //           success: function (res) {
          //             console.log('addfood success图片上传ok了1111', res);
          //           },
          //           fail: function (res) {
          //             console.log('addfood fail', res);
          //           },
          //         });
          //       });
          //     }
          //     if (res.data.ret == 200) {
          //       uni.navigateBack({
          //         data: -1,
          //       });
          //     }
          //   },
          //   fail: (err) => {
          //     console.log(err, '请求出问题。');
          //   },
          // });
        },
      });
    },
    chooseimage: function (e) {
      var that = this;
      var index = 'imgAll[' + e.currentTarget.dataset.id + ']';
      var indexMsg = 'imgMsg[' + e.currentTarget.dataset.id + ']';
      let imgFirsts = that.imgFirst;
      // imgAll
      uni.chooseImage({
        count: 1,
        // 默认9
        sizeType: ['compressed'],
        // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ['album', 'camera'],
        // 可以指定来源是相册还是相机，默认二者都有
        success: function (res) {
          var lt = index;
          var ltmsg = indexMsg;
          console.log(res.tempFilePaths);
          // 返回选定照片的本地文件路径列表，tempFilePath可以作为img标签的src属性显示图片
          that.imgList.push(res.tempFilePaths[0]);
          console.log('this.imgList', that.imgList);
          // console.log('url', res.tempFilePaths[0]);
          // uni.uploadFile({
          //   url: baseUrl + '/wx/v0.1/file-upload',
          //   filePath: res.tempFilePaths[0],
          //   //待上传的图片，由 chooseImage获得
          //   name: 'food_image',
          //   formData: {
          //     contentType: '02',
          //     relaId: that.integralId,
          //     relaTable: 'orderEva',
          //   },
          //   // HTTP 请求中其他额外的 form data
          //   success: function (res) {
          //     console.log('addfood success图片上传ok了1111', res);
          //   },
          //   fail: function (res) {
          //     console.log('addfood fail', res);
          //   },
          // });
        },
      });
    },
    imgChange(e) {
      console.log('imgChange===', e);
      this.imgList = e.imageArr;
    },
    // 提交接口
    async submitApi(params) {
      var that = this;
      console.log(params, 'params');
      const [err, res] = await shopOrderEvaApi(params);
      if (res) {
        console.log('res', res);
        console.log(that.imgList, 'that.imgList', that.integralId);
        if (that.imgList.length > 0) {
          await that.imgList.forEach((item) => {
            uni.uploadFile({
              // url: baseUrl + '/wx/v0.1/file-upload',
              url: that.actionUrl,
              filePath: item,
              header: that.imgHeader,
              //待上传的图片，由 chooseImage获得
              name: 'food_image',
              formData: {
                contentType: '02',
                relaId: that.integralId,
                relaTable: 'orderEva',
              },
              // HTTP 请求中其他额外的 form data
              success: function (res) {
                console.log('addfood success图片上传ok了1111', res);
              },
              fail: function (res) {
                console.log('addfood fail', res);
              },
            });
          });
        }
        if (res.ret == 200) {
          uni.navigateBack({
            data: -1,
          });
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.rate-box {
  padding: 24rpx;
  .rate-card {
    padding: 40rpx;
    margin-bottom: 24rpx;
    border-radius: 24rpx;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    grid-gap: 24rpx;
    .rate-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 28rpx;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0em;
      color: rgba(51, 51, 51, 0.898);
    }
  }
  .evaluate-content {
    padding: 40rpx;
    margin-bottom: 24rpx;
    border-radius: 24rpx;
    background: #ffffff;
    .total-num {
      text-align: right;
    }
  }
  .upload-img {
    padding: 40rpx;
    border-radius: 24rpx;
    background: #ffffff;
    .imgs-box {
      display: flex;
      flex-wrap: wrap;
      gap: 24rpx;
      grid-gap: 24rpx;
      margin-top: 24rpx;
      .img-item {
        width: 160rpx;
        height: 160rpx;
        image {
          width: 160rpx;
          height: 160rpx;
        }
        .close-btn {
          position: relative;
          top: -155rpx;
          left: 130rpx;
          font-size: 32rpx;
          background: rgba(0, 0, 0, 0.08);
          border-radius: 50%;
          color: #1e9cff;
          width: 30rpx;
          height: 30rpx;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
      .add-box {
        width: 160rpx;
        height: 160rpx;
        box-sizing: border-box;
        padding: 40rpx 32rpx;
        color: #1e9cff;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        border-radius: 16rpx;
        background: rgba(72, 118, 255, 0.08);
        box-sizing: border-box;
        border: 1rpx solid rgba(30, 156, 255, 0.3);
        .add-box-icon {
          font-weight: bold;
          font-size: 60rpx;
        }
        .add-box-span {
          font-size: 24rpx;
          font-weight: normal;
          line-height: 36rpx;
        }
      }
    }
  }

  .footer {
    z-index: 1;
    margin: 0 -24rpx;
    .footer-button {
      display: flex;
      gap: 24rpx;
      grid-gap: 24rpx;
      .button-item {
        flex: 1;
      }
    }
  }
}
</style>
