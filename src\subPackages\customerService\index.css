.mine-page {
  position: relative;
  z-index: 1;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* scroll-view容器样式 */
.feedback-scroll-view {
  flex: 1;
  height: 0;
  /* 配合flex使用 */
  padding-bottom: calc(148rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(148rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

/* 反馈列表容器 */
.feedback-list {
  min-height: 100%;
}

.nodata image {
  width: 586rpx;
  display: block;
  margin: 330rpx auto 0;
}

.nodata text {
  display: block;
  margin: -80rpx auto 0;
  text-align: center;
}

.feedbackCard {
  width: 95%;
  background: #fff;
  padding: 24rpx;
  border-radius: 16rpx;
  margin: 24rpx;
  box-sizing: border-box;
}

.feedbackCard .topbox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #eee;
  padding-bottom: 24rpx;
  margin-bottom: 24rpx;
}

.feedbackCard .topbox text:nth-child(1) {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.feedbackCard .topbox text:nth-child(2).blue {
  font-size: 28rpx;
  color: #1e9cff;
}

.feedbackCard .topbox text:nth-child(2).red {
  color: #ff3b30;
  font-size: 28rpx;
}

.feedback_title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 24rpx;
  word-break: keep-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.feedback_time {
  font-size: 28rpx;
  color: #999;
}

.safe_foot {
  position: fixed;
  bottom: 0;
  width: 100vw;
  padding: 22rpx 24rpx;
  background: #fff;
  box-sizing: border-box;
  padding-bottom: calc(22rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(22rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}

.safe_foot .item {
  flex: 1;
}

.safe_foot .item:not(:nth-child(1)) {
  margin-left: 24rpx;
}

/* 加载状态样式 */
.loading-status {
  padding: 40rpx 0;
  text-align: center;
}

.loading-text,
.no-more-text,
.load-more-text {
  font-size: 28rpx;
  color: #999;
}

.loading-text {
  color: #1e9cff;
}

.no-more-text {
  color: #ccc;
}

.load-more-text {
  color: #666;
}