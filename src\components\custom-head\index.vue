<template>
  <view>
    <view class="foot-navbar">
      <view class="navbar-ul">
        <block v-for="(item, index) in navbarList" :key="index">
          <view class="navbar-li" v-if="!item.isScan" @click="parseEventDynamicCode($event, item.methods)">
            <image class="navbar-li-icon" :src="item.navbar === currentNavbar ? item.checkedIcon : item.icon"></image>
            <view class="navbar-li-text">{{ item.text }}</view>
          </view>

          <view class="navbar-li-scan" @click="parseEventDynamicCode($event, item.methods)" v-if="item.isScan">
            <image class="navbar-scan" :src="item.icon"></image>
            <view class="navbar-scan-text">{{ item.text }}</view>
          </view>
        </block>
      </view>
    </view>
    <view style="height: calc(190rpx + env(safe-area-inset-bottom))"></view>
  </view>
</template>

<script>
export default {
  data() {
    return {};
  },
  computed: {
    navbarList() {
      return [
        {
          icon: this.IMG_PATH_UI + 'navbar-index.png',
          text: '首页',
          checkedIcon: this.IMG_PATH_UI + 'navbar-index-checked.png',
          isScan: false,
          navbar: 'indexPage',
          methods: 'goINdex',
        },
        {
          icon: this.IMG_PATH_UI + 'navbar-station.png',
          text: '电站',
          checkedIcon: this.IMG_PATH_UI + 'navbar-station-checked.png',
          isScan: false,
          navbar: 'stationPage',
          methods: 'goStation',
        },
        {
          icon: this.IMG_PATH_UI + 'navbar-scan.png',
          text: '扫一扫',
          checkedIcon: this.IMG_PATH_UI + 'navbar-scan.png',
          isScan: true,
          navbar: 'scanPage',
          methods: 'goScan',
        },
        {
          icon: this.IMG_PATH_UI + 'navbar-order.png',
          text: '我的订单',
          checkedIcon: this.IMG_PATH_UI + 'navbar-order-checked.png',
          isScan: false,
          navbar: 'orderPage',
          methods: 'goOrder',
        },
        {
          icon: this.IMG_PATH_UI + 'navbar-mine.png',
          text: '我的',
          checkedIcon: this.IMG_PATH_UI + 'navbar-mine-checked.png',
          isScan: false,
          navbar: 'minePage',
          methods: 'goMine',
        },
      ];
    },
  },
  mixins: [],
  props: {
    currentNavbar: {
      type: String,
      default: 'indexPage',
    },
  },
  created() {
    // const { isLoad } = app.globalData.mobileInfo;
    // if (!isLoad) {
    //   this.getMobileInfo();
    // }
  },
  methods: {
    parseEventDynamicCode(e, exp) {
      console.log(exp, e);
      if (typeof this[exp] === 'function') {
        this[exp](e);
      }
    },
    // getMobileInfo() {
    //   const promise = new Promise((resolve, reject) => {
    //     uni.request({
    //       url: baseUrl + '/wx/v0.1/custinfo',
    //       data: {},
    //       method: 'GET',
    //       header: {
    //         'content-type': 'application/json',
    //         minitProgramToken: uni.getStorageSync('token') || '',
    //       },
    //       success: (res) => {
    //         const { ret } = res.data;
    //         if (ret === 200) {
    //           const { mobile } = res.data.data;
    //           app.globalData.mobileInfo = {
    //             isLoad: true,
    //             mobile: mobile || '未绑定',
    //           };
    //         }
    //       },
    //       complete: function () {
    //         resolve(app.globalData.mobileInfo);
    //       },
    //     });
    //   });
    //   return promise;
    // },
    goINdex() {
      const { currentNavbar } = this;
      console.log(currentNavbar, 'currentNavbar');
      if (currentNavbar !== 'indexPage') {
        uni.redirectTo({
          url: '/pages/home/<USER>',
        });
      }
    },
    goStation() {
      const { currentNavbar } = this;
      if (currentNavbar !== 'stationPage') {
        uni.redirectTo({
          url: '/pages/basic/mapStation/map',
        });
      }
    },
    // goScan() {
    //   const { mobile } = app.globalData.mobileInfo;
    //   if (mobile === '未绑定') {
    //     this.getMobileInfo().finally(() => {
    //       if (app.globalData.mobileInfo.mobile === '未绑定') {
    //         uni.navigateTo({
    //           url: '/pages/login/login',
    //         });
    //       } else {
    //         this.scanNow();
    //       }
    //     });
    //   } else {
    //     this.scanNow();
    //   }
    // },
    // scanNow() {
    //   uni.scanCode({
    //     onlyFromCamera: true,
    //     success: (res) => {
    //       function GetUrlParam(paraName) {
    //         const url = res.result;
    //         const arrObj = url.split('?');
    //         if (arrObj.length > 1) {
    //           const arrPara = arrObj[1].split('&');
    //           var arr;
    //           for (var i = 0; i < arrPara.length; i++) {
    //             arr = arrPara[i].split('=');
    //             if (arr != null && arr[0] == paraName) {
    //               return arr[1];
    //             }
    //           }
    //           return '';
    //         } else {
    //           return '';
    //         }
    //       }
    //       const message = GetUrlParam('qrcode') || res.result;
    //       const { mobile } = app.globalData.mobileInfo;
    //       if (mobile === '未绑定') {
    //         uni.navigateTo({
    //           url: '/pages/login/login',
    //         });
    //       } else {
    //         uni.navigateTo({
    //           url: '/subPackages/placeorder/placeorder?msg=' + message + '&titles=10',
    //         });
    //       }
    //     },
    //   });
    // },
    goOrder() {
      const { currentNavbar } = this;
      if (currentNavbar !== 'orderPage') {
        uni.redirectTo({
          url: '/subPackages/myorder/myorder',
        });
      }
    },
    goMine: function () {
      const { currentNavbar } = this;
      if (currentNavbar !== 'minePage') {
        uni.redirectTo({
          url: '/pages/personal/personal',
        });
      }
    },
  },
};
</script>
<style>
@import './index.css';
</style>
