<template>
  <view>
    <view class="mine-page">
      <!-- #ifdef MP-WEIXIN -->
      <view class="banner" :style="'padding-top:' + s_top + 'rpx;line-height:' + s_height + 'rpx'">
        <view class="banner-title">我的</view>
      </view>
      <!-- #endif -->
      <!-- <view :style="'height:' + (s_top + s_height + 44) + 'rpx'"></view> -->
      <!--      头部信息-->
      <UserDetail class="user-detail" :member-info="memberInfo" />
      <view class="open-membership" v-if="memberInfo.vipFlag === '0'">
        <view class="buy-plus" :style="{ backgroundImage: `url(${IMG_PATH}member/open-membership-104.png)` }">
          <text class="buy-plus-text">开通PLUS会员享受专属权益</text>
          <AppButton
            :custom-style="{
              width: '152rpx',
              height: '48rpx',
              'font-size': '26rpx',
              'line-height': '40rpx',
              color: '#1F2247',
            }"
            shape="circle"
            color="linear-gradient(106deg, #E5E6F8 7%, #DDE4FF 33%, #CED8FD 53%, #DFD3F0 68%, #F4D2DB 102%)"
            @click="jumpToBuy"
          >
            立即开通
          </AppButton>
        </view>
      </view>
      <!--      账户余额、优惠券、积分-->
      <view class="account-info">
        <view class="account-li" @tap="pays">
          <view class="li-number">{{ userInfo ? pay : '-' }}</view>
          <view class="li-desc">账户余额</view>
        </view>
        <view class="account-li" @tap="yhj">
          <view class="li-number">{{ userInfo ? couponNum || '-' : '-' }}</view>
          <view class="li-desc">优惠券</view>
          <div class="receive" v-if="showCard">
            <image :src="`${IMG_PATH}unclaimed.png`" mode="widthFix" />
          </div>
        </view>
        <view class="account-li" @tap="jfCallback">
          <view class="li-number">{{ userInfo ? custIntegralNumber : '-' }}</view>
          <view class="li-desc">积分</view>
        </view>
      </view>
      <!--      任务-->
      <TaskList class="task-list" @reset="loadData" />
      <!--      广告-->
      <!--      <image class="banner-advertise" :src="`${IMG_PATH}invite-advertise.png`"></image>-->
      <view class="banner-advertise" v-if="bannerList.length > 0">
        <swiper
          style="height: 230rpx"
          :indicator-dots="bannerList.length > 1"
          indicator-color="rgba(0, 0, 0, .3)"
          indicator-active-color="#fff"
          :autoplay="autoplay"
          :interval="interval"
          :duration="duration"
        >
          <block v-for="(item, index) in bannerList" :key="index">
            <swiper-item>
              <image class="banner-img" :src="item.imgUrl" @click="jumpToLink(item.contentUrl)"></image>
            </swiper-item>
          </block>
        </swiper>
      </view>
      <!--  this.IMG_PATH + -->
      <view class="car-card">
        <view class="car-top" @tap="carL">
          <view class="car-top-title">我的爱车</view>
          <image class="car-top-right" :src="list.modelImg || IMG_PATH + 'care-right.png'"></image>
        </view>
        <view v-if="phone === '未绑定' || !phone" class="car-empty" @tap="add">
          <view class="car-noLogin-text">登陆后可查看或添加爱车</view>
        </view>
        <view v-if="carNos && phone !== '未绑定'" class="car-empty" @tap="add">
          <view class="car-empty-text">添加爱车</view>
          <image class="car-empty-img" :src="`${IMG_PATH}mine-car-add.png`" />
        </view>
        <view v-if="!carNos && phone !== '未绑定'" class="car-mid" @tap="carL">
          <image class="default-car" :src="`${IMG_PATH}default-car.png`" />
          <view class="car-msg">
            <view class="car-number">{{ list.licenseNo }}</view>
            <view class="car-endurance" v-if="list.contMileage && list.contMileage > 0"
              >续航里程：{{ list.contMileage }}km</view
            >
            <view class="car-endurance" v-if="list.carRange && list.carRange > 0">续航里程：{{ list.carRange }}km</view>
            <view class="car-model">{{ list.brandName }} {{ list.modelName }}</view>
          </view>
        </view>
      </view>
      <view class="func-list">
        <view class="func-li" @tap="toCallback(item.methods)" v-for="(item, index) in funcList" :key="index">
          <image class="func-li-icon" :src="item.icon" />

          <view class="func-li-text">{{ item.text }}</view>
        </view>
      </view>

      <custom-nav-bar currentNavbar="minePage"></custom-nav-bar>
    </view>
    <image class="mine-bg" :src="`${IMG_PATH_UI}bg.png`" mode="widthFix"></image>
    <!-- <guideStep :step="step" v-if="userInfo && userInfo.mobile" ref="guideStep"> </guideStep> -->
    <guideStep :step="step" v-if="step" ref="guideStep"> </guideStep>
  </view>
</template>

<script>
import AppButton from '@/components/AppButton/index';
import guideStep from '@/components/xky-guideStep/xky-guideStep.vue';
import customNavBar from '@/components/custom-navbar/index';
import UserDetail from './components/UserDetail.vue';
import { mapState } from 'vuex';
import TaskList from './components/TaskList';
import { getPref, getMsgUnused, getHomeBanner, getMemberInfo } from '@/services/index';
import { startsWithHttp } from '../../utils/util';

export default {
  components: {
    TaskList,
    customNavBar,
    UserDetail,
    guideStep,
    AppButton,
  },
  computed: {
    funcList() {
      return [
        {
          icon: this.IMG_PATH_UI + 'icon-order.png',
          text: '我的订单',
          methods: 'myOrder',
        },
        {
          icon: this.IMG_PATH_UI + 'icon-collect.png',
          text: '我的收藏',
          methods: 'collect',
        },
        {
          icon: this.IMG_PATH_UI + 'icon-charge.png',
          text: '充电指南',
          methods: 'chargeGuide',
        },
        {
          icon: this.IMG_PATH_UI + 'icon-medal.png',
          text: '节能勋章',
          methods: 'energySavingMedal',
        },
        // { icon: this.IMG_PATH_UI + 'icon-instruct-video.png', text: '指导视频', methods: 'instructVideo' },
        {
          icon: this.IMG_PATH_UI + 'icon-service.png',
          text: '客服投诉',
          methods: 'goSevice',
        },
        {
          icon: this.IMG_PATH_UI + 'icon-invite.png',
          text: '电子发票',
          methods: 'invoiceopening',
        },

        {
          icon: this.IMG_PATH_UI + 'icon-question.png',
          text: '常见问题',
          methods: 'commonProblem',
        },
        {
          icon: this.IMG_PATH_UI + 'icon-activities.png',
          text: '平台活动',
          methods: 'waitOpen',
        },
        ...(uni.getStorageSync('app') !== 'i宁德'
          ? [
              {
                icon: this.IMG_PATH_UI + 'icon-share-people.png',
                text: '邀请送',
                methods: 'sharePeople',
              },
            ]
          : []),
        { icon: this.IMG_PATH_UI + 'envelope.png', text: '我的消息', methods: 'message' },
        { icon: this.IMG_PATH_UI + 'icon-exchange.png', text: '卡密兑换', methods: 'password' },
        // #ifdef MP-WEIXIN
        { icon: this.IMG_PATH_UI + 'setting.png', text: '设置', methods: 'setUp' },
        // #endif
      ];
    },
    ...mapState({
      userInfo: (state, getters) => getters['login/getUserInfo'],
    }),
    phone() {
      return this.userInfo?.mobile || '未绑定';
    },
  },
  data() {
    return {
      showCard: false,
      currentTab: 1,
      indicatorDots: true,
      background: ['1', '2', '3'],
      vertical: false,
      autoplay: true,
      interval: 2000,
      duration: 500,
      name: '',
      hasUserInfo: false,
      token: '',
      list: [],
      carNos: true,
      NosCant: false,
      typePeople: '01',
      s_top: '',
      s_height: '',
      pay: '-',
      couponNum: '-',
      custIntegralNumber: '-',
      memberInfo: {},
      bannerList: [],
      isBaseUrl: process.env.VUE_APP_BASE_HOST,
      step: undefined,
    };
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: async function () {
    // #ifdef MP
    this.initTopImg();
    // #endif
    await this.getMyBanner();
    await this.loadData();
    await this.getCoupons();
    console.log('初始化指导');
    setTimeout(() => {
      this.setStep();
    });
    await this.getMemberInfo();
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: async function () {
    try {
      // 统一触发数据加载（H5/小程序通用）
      await this.loadData();

      // #ifdef H5
      // H5 特殊处理：强制设置刷新终止（避免卡住）
      setTimeout(() => {
        uni.stopPullDownRefresh();
      }, 500);
      // #endif

      // #ifndef H5
      // 非 H5 平台正常终止
      uni.stopPullDownRefresh();
      // #endif
    } catch (error) {
      // 错误处理
      console.error('下拉刷新失败:', error);
      uni.stopPullDownRefresh();
    }
  },
  methods: {
    // 获取是否有可以领取的优惠券
    async getCoupons() {
      var that = this;
      var parmeType = {
        prodBusiType: '02',
      };
      const [, res] = await getMsgUnused(parmeType);
      if (res) {
        if (res.cpnList && Array.isArray(res.cpnList) && res.cpnList.length > 0) this.showCard = true;
      }
    },
    //我的 banner
    getMyBanner() {
      return new Promise(async (resolve, reject) => {
        const [, res] = await getHomeBanner();
        if (res) {
          const {
            homeMsgObj: { myBannerList },
          } = res;
          this.bannerList = myBannerList;
        } else {
          uni.showToast({
            icon: 'loading',
            title: '加载数据失败',
          });
        }
        resolve();
      });
    },
    jumpToLink(url) {
      if (!url) return;
      const jumpUrl = startsWithHttp(url);
      const encodedUrl = encodeURIComponent(jumpUrl);
      const navUrl = '/pages/setting/out/index' + '?url=' + encodedUrl + '&title=' + '';
      uni.navigateTo({
        url: navUrl,
      });
    },
    async loadData() {
      var that = this;
      var params = {};
      var token = uni.getStorageSync('token') || '';

      if (!token) return;
      // 进入我的页面就实时查询下用户信息，因为余额、优惠券、积分要实时刷新
      await this.$store.dispatch('login/getUserInfoCallback').then((res) => {
        const { acctotalAmt, couponNum, custIntegralNumber } = res;
        console.log(res, 'res');
        that.setData({
          pay: acctotalAmt,
          couponNum,
          custIntegralNumber: custIntegralNumber != null ? custIntegralNumber : '-',
        });
      });
      return new Promise(async (resolve, reject) => {
        const [, res] = await getPref(params);
        if (res) {
          if (res.custType == '01') {
            that.setData({
              typePeople: '01',
            });
            console.log('个人');
          } else {
            that.setData({
              typePeople: '02',
            });
            console.log('企业');
          }
          if (res.carList.length > 0) {
            that.setData({
              list: res.carList[0],
              carNos: false,
            });
          } else {
            that.setData({
              carNos: true,
            });
          }
        }
        resolve();
      });
    },
    // 去开通
    jumpToBuy() {
      uni.navigateTo({
        url: '/pages/member/buyPlus/index',
      });
    },
    initTopImg() {
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      console.log(menuButtonInfo, 'menuButtonInfo');
      this.s_top = +(menuButtonInfo?.top * 2) + 20;
      this.s_height = +(menuButtonInfo?.height * 2);
    },

    jfCallback() {
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '../login/login',
        });
      } else {
        uni.navigateTo({
          url: '/pages/intergral/myPoints/index',
        });
      }
    },
    yhj: function () {
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '../login/login',
        });
      } else {
        uni.navigateTo({
          url: '/subPackages/yhj/yhj',
        });
      }
    },

    pays: function () {
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '../login/login',
        });
      } else {
        uni.navigateTo({
          url: '/pages/basic/money/money',
        });
      }
    },

    carL: function () {
      var that = this;
      if (!that.userInfo?.mobile) {
        uni.navigateTo({
          url: '../login/login',
        });
      } else {
        if (that.typePeople == '01') {
          uni.navigateTo({
            url: '/subPackages/addCar/addCar',
          });
        } else {
          console.log('企业 不跳转车辆列表');
        }
      }
    },

    add: function () {
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '../login/login',
        });
      } else {
        uni.navigateTo({
          url: '/subPackages/add/add',
        });
      }
    },

    scan: function () {
      var that = this;
      uni.scanCode({
        onlyFromCamera: true,
        success: (res) => {
          function GetUrlParam(paraName) {
            var url = res.result;
            var arrObj = url.split('?');
            if (arrObj.length > 1) {
              var arrPara = arrObj[1].split('&');
              var arr;
              for (var i = 0; i < arrPara.length; i++) {
                arr = arrPara[i].split('=');
                if (arr != null && arr[0] == paraName) {
                  return arr[1];
                }
              }
              return '';
            } else {
              return '';
            }
          }
          var message = GetUrlParam('qrcode') || res.result;
          if (!that.userInfo?.mobile) {
            uni.navigateTo({
              url: '../login/login',
            });
          } else {
            uni.navigateTo({
              url: '/subPackages/placeorder/placeorder?msg=' + message + '&titles=10',
            });
          }
        },
      });
    },

    // getUserInfo: function (e) {
    //   console.log(e, '----');
    //   this.setData({
    //     userInfo: e.detail,
    //     hasUserInfo: true,
    //   });
    // },
    personal: function () {
      this.setData({
        currentTab: 1,
      });
      uni.navigateTo({
        url: '../personal/personal',
      });
    },

    fj: function () {
      this.setData({
        currentTab: 0,
      });
      uni.redirectTo({
        url: '/pages/basic/mapStation/map',
      });
    },

    myorder: function () {
      var that = this;
      if (!that.userInfo?.mobile) {
        uni.navigateTo({
          url: '/pages/login/login',
        });
      } else {
        uni.navigateTo({
          url: '/subPackages/myorder/myorder',
        });
      }
    },
    toCallback(type) {
      if (this.phone === '未绑定') {
        uni.navigateTo({
          url: '/pages/login/login',
        });
      } else {
        switch (type) {
          // 我的收藏
          case 'collect':
            uni.navigateTo({
              url: '/subPackages/myCollection/index',
            });
            break;
          // 充电指南
          case 'chargeGuide':
            uni.navigateTo({
              url: '/subPackages/chargeGuide/index',
            });
            break;
          // 我的订单
          case 'myOrder':
            uni.navigateTo({
              url: '/pages/basic/myorder/myorder',
            });
            break;
          // 节能勋章
          case 'energySavingMedal':
            uni.navigateTo({
              url: '/subPackages/energySavingMedal/index',
            });
            break;
          // 客服投诉
          case 'goSevice':
            uni.navigateTo({
              url: '/subPackages/customerService/index',
            });
            break;
          //电子发票
          case 'invoiceopening':
            uni.navigateTo({
              url: '/subPackages/invoiceopening/invoiceopening',
            });
            break;
          //常见问题
          case 'commonProblem':
            uni.navigateTo({
              url: '/subPackages/commonProblem/index' + `?type=2`,
            });
            break;
          // 平台活动
          case 'sharePeople':
            uni.navigateTo({
              url: '/pages/setting/share/index',
            });
            break;
          // 平台活动
          case 'waitOpen':
            uni.navigateTo({
              url: '/subPackages/envelopeActive/index',
            });
            break;
          //消息
          case 'message':
            uni.navigateTo({
              url: '/subPackages/envelope/index',
            });
            break;
          //k卡密兑换
          case 'password':
            uni.navigateTo({
              url: '/pages/setting/cardPasswordExchange/index',
            });
            break;
          // 设置
          case 'setUp':
            uni.navigateTo({
              url: '/pages/setting/setUp/index',
            });
            break;
          // 指导视频
          case 'instructVideo':
            uni.navigateTo({
              url: '/pages/basic/instructVideo/index',
            });
            break;
        }
      }
    },
    // 获取会员信息
    async getMemberInfo() {
      const [err, res] = await getMemberInfo();
      if (res) {
        this.memberInfo = res;
      } else {
        this.memberInfo = null;
      }
    },
    setStep() {
      this.$nextTick(() => {
        if (this.userInfo?.mobile) return;
        this.step = {
          name: 'personal',
          repeat: false,
          guideList: [
            {
              el: '.open-membership',
              tips: '专享会员站点服务费特惠 充的越多省的越多',
              style: 'border-radius: 8rpx;margin: 0',
              next: '下一步',
              icon: this.IMG_PATH + 'icon-magnifier.png',
            },
            {
              el: '.task-list',
              tips: '补充车辆信息、每日签到、车辆充电，获得海量积分',
              style: 'border-radius: 8rpx;margin: 0',
              next: '下一步',
              icon: this.IMG_PATH + 'icon-rocket.png',
            },
            {
              el: '.car-card',
              tips: '添加爱车，享受更多优惠 活动',
              style: 'border-radius: 8rpx;margin: 0',
              next: '完成',
              noJump: true,
              icon: this.IMG_PATH + 'icon-car.png',
            },
          ],
        };
      });
    },
  },
};
</script>

<style scoped lang="scss">
/* @import '../../common/css/iconfont';
@import '../../common/css/styleColor'; pages/personal/personal.wxss */
.guide-step-tips {
  width: 100%;
  height: auto;
}
.open-membership {
  margin-top: 48rpx;
  padding: 24rpx;
  .buy-plus {
    display: flex;
    align-items: center;
    background-size: cover;
    width: 100%;
    height: 104rpx;
    border-radius: 16rpx;
    padding: 28rpx 29rpx 28rpx 89rpx;
    &-text {
      font-size: 26rpx;
      font-weight: bold;
      line-height: 40rpx;
      font-variation-settings: 'opsz' auto;
      color: #ffffff;
      margin-right: 103rpx;
    }
  }
}
.persoanl-lists {
  margin-top: 20rpx;
  background-color: #fff;
}

.persoanl-lists .list-main {
  position: relative;
  padding: 22rpx 30rpx;
  height: 44rpx;
  line-height: 44rpx;
  color: #333;
  font-size: 30rpx;
}

.persoanl-lists .list-main .icon {
  float: left;
  color: #656565;
  margin-right: 30rpx;
  height: 32rpx;
  padding: 6rpx 0;
}

.persoanl-lists .list-main .icon-right {
  float: right;
  color: #999;
  height: 30rpx;
  padding: 7rpx 0;
}

.caidan {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 9vh;
  background: #fff;
  width: 100%;
  display: flex;
  justify-content: space-around;
  text-align: center;
  align-items: center;
  color: #939393;
}

.caidan .listBtn {
  width: 20%;
  font-size: 24rpx;
}

.caidan .on {
  color: #e00a22;
}

.caidan .scan {
  color: #fff;
  padding: 15rpx 0;
  width: 20%;
  border-radius: 100rpx;
  height: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.addCar-msg {
  width: 100%;
  background: #fff;
  box-sizing: border-box;
  margin-top: 20rpx;
}
.addCar-msg .message {
  width: 90%;
  height: 190rpx;
  margin: 0 auto;
  box-sizing: border-box;
  display: flex;
}
.addCar-msg .message .message-left {
  width: 235rpx;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.addCar-msg .message .message-left image {
  width: 205rpx;
  height: 98rpx;
}
.addCar-msg .message .message-right {
  height: 100%;
  width: 440rpx;
  margin-left: 18rpx;
  display: flex;
  align-content: space-around;
  flex-wrap: wrap;
  box-sizing: border-box;
  padding: 30rpx 20rpx;
  position: relative;
}

.addCar-msg .message .message-right view {
  width: 100%;
  line-height: 30rpx;
  font-size: 26rpx;
  color: #666666;
}

.addCar-msg .message .message-right .icon-right {
  color: #999;
  height: 30rpx;
  padding: 7rpx 0;
  position: absolute;
  right: -10rpx;
  top: 37%;
}
.addCar-msg .message .message-right .message-text {
  width: auto;
}
.addCar-msg .message .message-right .message-linheight {
  line-height: 38rpx;
}
.addCar-msg .message .message-right .message-text text {
  color: #333;
  font-size: 24rpx;
  height: 40rpx;
  background: #eee;
  display: flex;
  text-align: center;
  align-items: center;
  padding: 5rpx;
  box-sizing: border-box;
  border-radius: 4rpx;
}
.addCar-msg .message .message-right .message-name {
  color: #333;
}
.addCar-msg .addCar-delet {
  width: 90%;
  height: 87rpx;
  /* background: red; */
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: 0 30rpx;
}
.addCar-msg .message .message-right .message-ags {
  font-size: 28rpx;
  color: #2096f3;
  font-weight: bold;
}
button {
  background: none;
  padding: 0;
  margin: 0;
  line-height: 0;
}
.mine-bg {
  position: fixed;
  top: 0;
  left: 0;
  top: 0;
  height: 100vh;
  width: 100%;
  z-index: -1;
}
.mine-page {
  position: relative;
  z-index: 1;
  height: 100vh;
  box-sizing: border-box;
  padding-bottom: 200rpx;
  overflow-y: auto;
}
.banner {
  padding: 20rpx 20rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  z-index: 2;
  box-sizing: border-box;
  margin-bottom: 40rpx;
}

.banner-title {
  font-family: 'PingFang SC';
  font-size: 36rpx;
  font-style: normal;
  font-weight: 500;
  flex: 1;
  text-indent: 4rpx;
  color: #000000;
  text-align: left;
  font-weight: 700;
}
.logo-bg {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 44rpx;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}
.logo-edit {
  width: 20rpx;
  height: 20rpx;
}
.account-info {
  margin: 0rpx 24rpx 16rpx;
  height: 172rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  padding: 0 25rpx;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.16) 0%, #ffffff 100%);
  box-sizing: border-box;
  border: 2rpx solid #ffffff;

  backdrop-filter: blur(10rpx);
}
.account-li {
  flex: 1;
  text-align: center;
  padding: 0 38rpx;
  position: relative;
  .receive {
    position: absolute;
    right: -20rpx;
    top: -20rpx;
    color: #fff;
    border-radius: 5rpx;
    border-bottom-left-radius: 0;
    width: 80rpx;
    height: 50rpx;
    image {
      width: 100%;
      height: 100%;
    }
  }
}
.li-number {
  font-family: D-DIN;
  font-weight: bold;
  text-align: center;
  font-size: 48rpx;
  line-height: normal;
  font-variation-settings: 'opsz' auto;
  font-feature-settings: 'kern' on;
  color: #333333;
}
.li-desc {
  margin-top: 12rpx;
  font-family: PingFang SC;
  text-align: center;
  font-size: 26rpx;
  font-weight: normal;
  line-height: normal;
  text-align: center;
  font-variation-settings: 'opsz' auto;
  color: rgba(0, 0, 0, 0.7);
}

.banner-advertise {
  height: 211rpx;
  width: 702rpx;
  margin: 0 24rpx 16rpx;
  display: block;
  .banner-img {
    height: 211rpx;
    width: 100%;
  }
}
.car-card {
  margin: 0 24rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  background: #ffffff;
  box-sizing: border-box;
}
.car-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.car-top-title {
  font-family: PingFang SC;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 38rpx;
  color: #333333;
}
.car-top-right {
  width: 32rpx;
  height: 32rpx;
}
.car-mid {
  margin-top: 16rpx;
  display: flex;
}
.default-car {
  width: 268rpx;
  height: 128rpx;
  margin-left: 46rpx;
}
.car-msg {
  flex: 1;
  margin-left: 64rpx;
  display: flex;
  align-content: center;
  // flex-wrap: wrap;
  flex-direction: column;
  box-sizing: border-box;
  gap: 15rpx;
  grid-gap: 15rpx;
}
.car-number {
  font-family: PingFang SC;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 38rpx;
  color: #333333;
  margin-top: 11rpx;
}
.car-endurance {
  margin-top: 6rpx;
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: normal;
  line-height: 32rpx;
  color: #666666;
}
.car-model {
  margin-top: 6rpx;
  font-family: PingFang SC;
  font-size: 22rpx;
  font-weight: normal;
  line-height: 26rpx;
  color: #999999;
}
.func-list {
  border-radius: 16rpx;
  display: flex;
  flex-wrap: wrap;
  background: linear-gradient(180deg, rgba(45, 161, 218, 0.2) -28%, rgba(255, 255, 255, 0.2) 25%), #ffffff;
  box-sizing: border-box;
  border: 2rpx solid #ffffff;
  margin: 16rpx 24rpx;
  padding: 0 24rpx 36rpx;
}
.func-li {
  margin: 36rpx 36rpx 0;
  display: flex;
  flex-wrap: wrap;
  width: calc(25% - 55rpx);
  justify-content: center;
}
.func-li-icon {
  width: 56rpx;
  height: 56rpx;
}
.func-li-text {
  width: 100%;
  font-family: PingFang SC;
  font-size: 26rpx;
  font-weight: normal;
  line-height: 40rpx;
  text-align: center;
  color: #666666;
  margin-top: 6rpx;
}
.func-li:nth-child(4n + 1) {
  margin-left: 0;
}

.func-li:nth-child(4n) {
  margin-right: 0;
}
.tabbar-height {
  height: 200rpx;
}
.car-empty {
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  background: #f6f7f9;
  padding: 24rpx 0 14rpx;
  margin-top: 16rpx;
}
.car-empty-text {
  font-family: PingFang SC;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 38rpx;
  color: #666666;
  text-align: center;
  width: 100%;
}
.car-noLogin-text {
  text-align: center;
  width: 100%;
  padding: 20rpx 0 30rpx;
  font-family: PingFang SC;
  font-size: 26rpx;
  font-weight: normal;
  line-height: 40rpx;

  color: #666666;
}
.car-empty-img {
  margin-top: 6rpx;
  width: 48rpx;
  height: 48rpx;
}
</style>
