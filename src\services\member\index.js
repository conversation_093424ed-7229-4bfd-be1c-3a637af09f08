import { request as __request } from '../request/request';
import { ENVR, scanBaseurl, defBaseurl, pubBaseurl, astBaseurl, busiBaseurl, bilBaseurl } from '../../config/global';

// 查询购买记录
export const getRecord = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: `${ENVR === 'wx' ? 'POST' : 'GET'}`,
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/vip/record`,
      data: params,
    }
  );
};

// 资讯信息查询
export const openInfo = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/openInfo`,
      data: params,
    }
  );
};

// 查询是否升级
export const getLevelFlag = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: `${ENVR === 'wx' ? 'POST' : 'GET'}`,
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/getLevelFlag`,
      data: params,
    }
  );
};

// 更新升级弹框状态
export const updateLevelFlag = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: `${ENVR === 'wx' ? 'POST' : 'GET'}`,
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/updateLevelFlag`,
      data: params,
    }
  );
};

// 查询用户信息
export const getAccounts = (params, config = {}) => {
  return __request(
    { ...config },
    {
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/accounts`,
      method: `${ENVR === 'wx' ? 'POST' : 'GET'}`,
      data: params,
    }
  );
};

// 查询套餐
export const getConfig = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: `${ENVR === 'wx' ? 'POST' : 'GET'}`,
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/vip/config`,
      data: params,
    }
  );
};

// 查询支付参数
export const recharge = (
  params,
  config = {
    'Content-Type': 'application/x-www-form-urlencoded',
  }
) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/vip/recharge`,
      data: params,
    }
  );
};

// 查询会员信息
export const getMemberInfo = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: `${ENVR === 'wx' ? 'POST' : 'GET'}`,
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/vip/info`,
      data: params,
    }
  );
};

// 获取系统配置

export const getConfigOption = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ENVR === 'wx' ? scanBaseurl : pubBaseurl}/v0.1/paras`,
      data: params,
    }
  );
};
// 省钱攻略

export const saveMoneyStrategy = (
  params,
  config = {
    'Content-Type': 'application/x-www-form-urlencoded',
  }
) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `/pub/api/open/v0.1/vip/saveMoneyStrategy`,
      data: params,
    }
  );
};

// 获取plus 会员权益配置
export const getConfigDesc = (
  params,
  config = {
    'Content-Type': 'application/x-www-form-urlencoded',
  }
) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `/pub/api/open/v0.1/vip/configDesc`,
      data: params,
    }
  );
};
// 获取plus 会员权益配置
export const getUserSaveRecord = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/getUserSaveList`,
      data: params,
    }
  );
};
// 累计省钱
export const getSaveRecordByMobile = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/getSaveRecordByMobile`,
      data: params,
    }
  );
};
// 获取会员开始结束时间
export const qryEffectTime = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/qryEffectTime`,
      data: params,
    }
  );
};
// 获取我的页面 积分信息
export const integralRule = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl + '/v0.1/integralRule' : busiBaseurl + '/open/integral/v0.1/integralRule'}`,
      data: params,
    }
  );
};

// 获取平台活动
export const getMsgCenterList = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ENVR === 'wx' ? '/scan/wx' : '/pub/api'}/v0.1/msg-center-list`,
      data: params,
    }
  );
};
// 获取站点列表
export const getStationListV02 = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${astBaseurl}/v0.2/charging-stations`,
      data: params,
    }
  );
};
export const getStationListV06 = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${astBaseurl}/open/v0.6/charging-stations`,
      data: params,
    }
  );
};

// 查询站点详情
export const getStationDetail = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ENVR === 'wx' ? scanBaseurl + '/v0.1/station-detail-query' : astBaseurl + '/v0.1/charging-station/guns'}`,
      data: params,
    }
  );
};
// 获取推荐站点
export const getRecommendStation = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${astBaseurl}/open/v0.2/recommend_station`,
      data: params,
    }
  );
};

// 20250218174555
// https://ndjtcs.evstyle.cn:6443/ast/api/open/v0.6/charging-stations?positionLon=120.36434&positionLat=31.49055&orderType=02&pageNum=1&totalNum=10
