.orderList {
  width: 690rpx;
  height: 254rpx;
  box-sizing: border-box;
  margin: 16rpx auto;
  border-radius: 20rpx;
  overflow: hidden;
}
.tops {
  width: 100%;
  height: 163rpx;
  box-sizing: border-box;
  background: #fff;
  border-bottom: 1rpx dashed #f4f4f4;
  display: flex;
  align-content: center;
}
.tops .orderList_img {
  width: 124rpx;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tops .orderList_img image {
  width: 68rpx;
  height: 68rpx;
}

.tops .orderList_middle {
  width: 466rpx;
  height: 100%;
  display: flex;
  align-content: center;
  flex-wrap: wrap;
}
.tops .orderList_middle view {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tops .orderList_middle .address {
  font-size: 30rpx;
  color: #333;
}
.tops .orderList_middle .timed {
  font-size: 24rpx;
  color: #666;
  margin-top: 26rpx;
}
.tops .orderList_type {
  font-size: 26rpx;
  color: #1294f7;
  padding-top: 93rpx;
  padding-right: 23rpx;
}
.tops .orage {
  color: #ff9247;
}
.down {
  width: 100%;
  height: 90rpx;
  background: #fcfcfc;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0 23rpx 0 28rpx;
  font-size: 24rpx;
  color: #999;
}
.down button {
  margin: 0;
  width: 164rpx;
  height: 56rpx;
  border-radius: 10rpx;
  color: #fff;
  font-size: 26rpx;
  line-height: 56rpx;
}
.down .blue {
  background: #1294f7;
}
.down .orge {
  background: #ff9247;
}
