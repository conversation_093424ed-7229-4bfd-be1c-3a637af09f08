<!--
@name: 购买记录列表
@description: 购买记录详情
@time: 2024/8/12
-->
<template>
  <view class="order-record">
    <!--        列表-->
    <view class="list" v-if="recordList.length !== 0">
      <view v-for="item in recordList" :key="item.recordId" class="list-item" @click="jumpToDetail(item)">
        <view class="list-item-left">
          <text class="active-content">{{ item.vipTypeName }}</text>
          <text class="active-time">{{ item.payTime }}</text>
        </view>
        <view class="list-item-right">
          <view class="circle"></view>
          <text class="price"> -￥{{ item.amount }} </text>
        </view>
      </view>
    </view>
    <!--        暂无数据-->
    <AEmpty v-else />
  </view>
</template>

<script>
import AEmpty from '@/components/AEmpty/index';
import { getRecord } from '@/services/index.js';

export default {
  name: 'index',
  components: {
    AEmpty,
  },
  data() {
    return {
      recordList: [],
      status: 'more',
      pageNum: 1,
      totalNum: 10,
    };
  },
  onLoad() {
    this.getRecord();
  },
  // 触底分页查询
  onReachBottom() {
    if (this.status === 'noMore') return;
    this.getRecord();
  },
  methods: {
    // 跳转到订单详情页面
    jumpToDetail(item) {
      // 将详情存入缓存后跳转页面
      uni.setStorageSync('recordInfo', item);
      uni.navigateTo({
        url: '/pages/member/orderDetail/index',
      });
    },
    // 查询购买记录
    async getRecord() {
      const params = {
        totalNum: this.totalNum,
        pageNum: this.pageNum,
      };
      const [err, res] = await getRecord(params);
      if (res) {
        this.recordList.push(...res.vipPayRecords);
        this.status = res.vipPayRecords.length >= this.totalNum ? 'more' : 'noMore';
        if (this.status === 'more') {
          this.pageNum++;
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.order-record {
  padding: 24rpx;
  height: 100%;
  .list {
    background: white;
    min-height: calc(100% - 24rpx);
    padding: 40rpx 28rpx;
    &-item {
      padding: 24rpx 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1rpx solid #eeeeee;
      &-left {
        display: flex;
        flex-direction: column;
        .active-content {
          font-size: 28rpx;
          font-weight: bold;
          color: #333333;
          line-height: 42rpx;
        }
        .active-time {
          margin-top: 8rpx;
          font-size: 24rpx;
          line-height: 32rpx;
          color: #999999;
        }
      }
      &-right {
        display: flex;
        align-items: center;
        .circle {
          width: 10rpx;
          height: 10rpx;
          border-radius: 10rpx;
          background: #ff8301;
          margin-right: 6rpx;
        }
        .price {
          font-size: 36rpx;
          font-weight: bold;
          line-height: 42rpx;
          letter-spacing: 0.58rpx;
          color: #333333;
        }
      }
    }
  }
}
</style>
