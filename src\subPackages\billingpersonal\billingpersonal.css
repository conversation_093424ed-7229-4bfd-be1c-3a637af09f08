.billingpersonal-lists {
  background-color: #fff;
  margin-top: 12rpx;
}
.list-main {
  font-size: 28rpx;
  color: #333;
  display: flex;
  height: 96rpx;
  padding: 0px 30rpx;
  align-items: center;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
}

.list-main .list-main-hd {
  width: 160rpx;
}
.list-main .list-main-bd {
  flex: 1;
}

radio {
  transform: scale(0.7);
}

.billingpersonal-lists .list-main .list-main-bd .radio {
  float: left;
  color: #666;
  margin-right: 10rpx;
}

.billingpersonal-lists .list-main .list-main-bd input {
  display: block;
  height: 88rpx;
  width: 100%;
}
.color-tj {
  opacity: 0.5;
}

.billingpersonal-lists radio .wx-radio-input {
  border-color: #87858a;
}
.billingpersonal-lists radio .wx-radio-input.wx-radio-input-checked {
  background: #8bb70c !important;
  border: 2rpx solid #8bb70c !important;
}
.main-switch {
  justify-content: space-between;
}
.bottom-bg {
  background: #fff;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  padding: 20rpx;
  box-sizing: border-box;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}
.blue-bg {
  color: #fff;
  font-family: 'PingFang SC';
  font-size: 32rpx;
  font-weight: 400;
}

.color-tj {
  background: #e1e1e1;
}
.list-img {
  width: 40rpx;
  height: 40rpx;
  margin: 0rpx 0 0 10rpx;
}
.billinghistory-ul {
  max-height: 300rpx;
  overflow-y: auto;
}
.billinghistory-lists {
  background-color: #fff;
  margin-top: 10rpx;
}
::-webkit-scrollbar {
  display: none; /* 将垂直滚动条隐藏起来 */
  width: 0;
}
.billinghistory-lists .list-main {
  position: relative;
  height: 88rpx;
  line-height: 88rpx;
  overflow: hidden;
  font-size: 28rpx;
  color: #333;
}

.list-main .list-content {
  color: #2a2a2a;
}
.list-main .list-right {
  color: #2196f3;
  font-size: 24rpx;
  margin-left: 12rpx;
}
.myorder-image {
  text-align: center;
  color: #b2b2b2;
  font-size: 32rpx;
  margin: 60rpx 0;
}
.myorder-image image {
  display: block;
  width: 70rpx;
  height: 86rpx;
  margin: 0 auto 20rpx auto;
}
.icon-right {
  float: right;
  color: #999;
  height: 30rpx;
  padding: 7rpx 0;
}
.add-commonHeadings {
  margin-top: 30rpx;
  color: #fff;
}
.billing-desc {
  background: #2196f3;
  padding: 20rpx;
  color: #fff;
  font-size: 26rpx;
}
.indent {
  text-indent: 5em;
}
.bottom-bg {
  background: #fff;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  padding: 20rpx;
  box-sizing: border-box;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}
.blue-bg {
  color: #fff;
  font-family: 'PingFang SC';
  font-size: 32rpx;
  font-weight: 400;
}
.invoice-list {
  padding: 32rpx 0;
  height: 600rpx;
  overflow-y: auto;
}
.invoice-li {
  height: 96rpx;
  display: flex;
  align-item: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20rpx;
  line-height: 96rpx;
}
.li-content {
  color: #333;
  font-family: 'PingFang SC';
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
  display: flex;
  align-item: center;
}
.li-overflow {
  max-width: 500rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.li-right {
  color: #2196f3;
  font-size: 24rpx;
  margin-left: 12rpx;
  border-radius: 4rpx;
  background: #ebefde;
  padding: 0px 12rpx;
  color: #8bb70c;
  font-family: 'PingFang SC';
  font-size: 20rpx;
  font-style: normal;
  font-weight: 400;
  height: 46rpx;
  line-height: 46rpx;
  margin-top: 25rpx;
}
.list-main-hd text {
  color: #f33;
  font-family: 'PingFang SC';
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
