import { IMG_URL } from '../config/global';
import { mapState } from 'vuex/dist/vuex.common.js';
export default {
  data() {
    return {
      IMG_URL,
      IMG_PATH: process.env.VUE_APP_IMAGE_PATH_HOST,
      IMG_PATH_BASE: process.env.VUE_APP_BASE_HOST,
    };
  },
  computed: {
    ...mapState('config', ['config']),
    theme() {
      const { paramValue } = this.config;
      console.log(paramValue, 'paramValue');
      return paramValue == 'SpringFestival' ? 'newYear' : 'normal';
    },
    IMG_PATH_UI() {
      return process.env.VUE_APP_IMAGE_PATH_HOST + this.theme + '/';
    },
  },
};
