"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.shopOrderEvaImgApi = exports.shopOrderEvaApi = exports.getExchangeDetail = exports.exchangeGoods = exports.getGoodsDetail = exports.getStandardCode = exports.getCouponIntr = exports.getExchangeList = exports.getGoods = exports.getIntegral = void 0;

var _request = require("../request/request");

var _global = require("../../config/global");

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

var baseurl = _global.ENVR === 'wx' ? _global.scanBaseurl : _global.defBaseurl; // 查询积分流水

var getIntegral = function getIntegral(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'POST',
    url: "".concat(baseurl, "/v0.1/integral"),
    data: params
  });
}; // 查询商品列表


exports.getIntegral = getIntegral;

var getGoods = function getGoods(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    url: "".concat(baseurl, "/v0.1/goods"),
    method: 'POST',
    data: params
  });
}; // 查询兑换记录


exports.getGoods = getGoods;

var getExchangeList = function getExchangeList(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    url: "".concat(baseurl, "/v0.1/exchangeList"),
    method: 'POST',
    data: params
  });
}; // 查询优惠券兑换说明


exports.getExchangeList = getExchangeList;

var getCouponIntr = function getCouponIntr(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    url: "".concat(baseurl, "/v0.1/standardCode?paraCode=goodsVrType"),
    method: 'GET',
    data: params
  });
}; // 查询标准代码


exports.getCouponIntr = getCouponIntr;

var getStandardCode = function getStandardCode(code) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    url: "".concat(baseurl, "/v0.1/standardCode?paraCode=").concat(code),
    method: 'GET'
  });
}; // 查询商品详情


exports.getStandardCode = getStandardCode;

var getGoodsDetail = function getGoodsDetail(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    url: "".concat(baseurl, "/v0.1/goodsDetail"),
    method: 'POST',
    data: params
  });
}; // 兑换商品


exports.getGoodsDetail = getGoodsDetail;

var exchangeGoods = function exchangeGoods(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    url: "".concat(baseurl, "/v0.1/exchange"),
    method: 'POST',
    data: params
  });
}; // 兑换订单详情


exports.exchangeGoods = exchangeGoods;

var getExchangeDetail = function getExchangeDetail(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    url: "".concat(baseurl, "/v0.1/exchangeDetail"),
    method: 'POST',
    data: params
  });
}; // 商品评价


exports.getExchangeDetail = getExchangeDetail;

var shopOrderEvaApi = function shopOrderEvaApi(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    url: _global.ENVR === 'wx' ? "".concat(_global.scanBaseurl, "/v0.1/order-eva") : "".concat(_global.baseBaseurl, "/v0.3/order-eva"),
    method: 'POST',
    data: params
  });
}; // 商品评价图片上传


exports.shopOrderEvaApi = shopOrderEvaApi;

var shopOrderEvaImgApi = function shopOrderEvaImgApi(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    url: _global.ENVR === 'wx' ? "".concat(_global.scanBaseurl, "/v0.1/file-upload") : "".concat(_global.baseBaseurl, "/v0.1/file-upload"),
    method: 'POST',
    data: params
  });
};

exports.shopOrderEvaImgApi = shopOrderEvaImgApi;