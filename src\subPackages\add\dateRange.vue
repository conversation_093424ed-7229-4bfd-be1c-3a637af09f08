<template>
  <uni-popup ref="channelPopup" background-color="#F6F7F9" :isMaskClick="false" :showClose="false">
    <div class="range-date">
      <div class="title">请选择年份</div>
      <div class="time-select">
        <div class="time-item" @click="changeDate('0')" :class="{ active: active == '0' }">
          {{ startDate || '开始时间' }}
        </div>
        <!-- <div class="time-item" @click="changeDate('1')" :class="{ active: active == '1' }">
          {{ endDate || '结束时间' }}
        </div> -->
      </div>
      <div class="date-wrap">
        <DateTimePicker
          :mode="3"
          v-if="active == '0'"
          :defaultDate="startDate"
          @onChange="changeStartDate"
        ></DateTimePicker>
        <DateTimePicker
          :mode="2"
          v-if="active == '1'"
          :defaultDate="endDate"
          @onChange="changeEndDate"
        ></DateTimePicker>
      </div>
      <div class="bottom-btn">
        <div class="btn" @click="closeChannelPopup">取消</div>
        <div class="btn primary" @click="confirm">确认</div>
      </div>
    </div>
  </uni-popup>
</template>

<script>
import dateSelector from './components/dateSelector.vue';
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup';
import uniPopupDialog from '@/components/uni-ui/uni-popup-dialog/uni-popup-dialog';
import DateTimePicker from './components/dateTimePicker/index.vue';
import moment from 'moment';
export default {
  props: {
    value: {
      type: Array,
      default: ['2024-10', '2024-12'],
    },
  },
  components: { uniPopup, dateSelector, DateTimePicker },
  data() {
    return {
      // 0 开始时间 1 结束时间
      active: '0',
      endDate: '',
      startDate: '',
      defaultDateList: [],
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    open(dateList) {
      this.defaultDateList = dateList;
      this.startDate = dateList[0];
      // if (dateList && Array.isArray(dateList) && dateList.length > 1) {
      //   this.startDate = dateList[0];
      //   this.endDate = dateList[1];
      // } else {
      //   this.startDate = '';
      //   this.endDate = '';
      // }
      console.log(dateList, 'dateList', this.startDate, this.endDate);

      this.$refs.channelPopup && this.$refs.channelPopup.open('bottom');
    },
    changeDate(active) {
      this.active = active;
    },
    changeStartDate(start) {
      this.startDate = start;
    },
    changeEndDate(end) {
      this.endDate = end;
    },
    closeChannelPopup() {
      this.$emit('change', this.defaultDateList);
      this.$refs.channelPopup.close();
    },
    confirm() {
      const startDate = moment(this.startDate);
      const endDate = moment(this.endDate);
      let flag = startDate.isAfter(endDate);
      let month = endDate.diff(startDate, 'months');
      console.log(flag, this.startDate, this.endDate, month);
      if (flag) {
        uni.showModal({
          content: '开始时间不能大于结束时间',
        });
        return;
      }
      if (month > 6) {
        uni.showModal({
          content: '开始时间和结束时间的间隔不能大于6个月',
        });
        return;
      }
      this.$emit('change', [this.startDate, this.endDate]);
      this.$refs.channelPopup.close();
    },
  },
};
</script>

<style scoped lang="scss">
.range-date {
  padding: 24rpx;
  height: 650rpx;
  .title {
    line-height: 50rpx;
    height: 50rpx;
    font-size: 30rpx;
    text-align: center;
    font-weight: 700;
    letter-spacing: 5rpx;
    margin-bottom: 20rpx;
  }
  .time-select {
    width: 100%;
    height: 50rpx;
    display: flex;
    gap: 20rpx;
    grid-gap: 20rpx;
    .time-item {
      line-height: 50rpx;
      flex: 1;
      text-align: center;
      border: 1px solid rgba(0, 0, 0, 0.1);
      border-radius: 10rpx;
      &.active {
        border-color: #2a53cd;
      }
    }
  }
  .date-wrap {
    height: 400rpx;
    width: 100%;
  }
  .bottom-btn {
    height: 60rpx;
    width: 100%;
    display: flex;
    justify-content: space-around;
    .btn {
      width: 40%;
      border: 1px solid rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 26rpx;
      border-radius: 10rpx;
      letter-spacing: 10rpx;
      &.primary {
        border: none;
        background: #3d56ff;
        color: #fff;
      }
    }
  }
}
</style>
