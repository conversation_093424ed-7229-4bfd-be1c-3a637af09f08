/**
 * ҳ���л�ȡ���ϸ�ҳ��Ľӿ�����
 */
import { mapActions, mapGetters } from 'vuex';
import Request from "../utils/Ajax/request"
export default {
    onLoad () {
        if (getCurrentPages && typeof getCurrentPages == 'function') {
            var pages = getCurrentPages();
            if (this.getPageCount != pages.length) {
                Request.clear();
            }
            this.setPageCount(pages.length)
        }

    },
    computed: {
        ...mapGetters('common', ['getPageCount'])
    },
    methods: {
        ...mapActions('common', ['setPageCount'])
    }
}