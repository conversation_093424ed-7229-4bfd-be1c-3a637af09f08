
const state = {
    teamInfo: {
        teamCode: '',
        teamName: ''
    }

}
const getters = {
    getTeamInfo: state => {
        return state.teamInfo

    },
}
const actions = {
    /** 
     * 重置订单操作等待时间
    */
    updateTeamInfo ({ commit }, info) {
        commit('SET_TEAM_INFO', info)
    },
    resetTeamInfo ({ commit }) {
        commit('SET_TEAM_INFO', {
            teamCode: '',
            teamName: ''
        })
    },
}
const mutations = {
    ['SET_TEAM_INFO'] (state, info) {
        const { teamCode, teamName } = info
        state.teamInfo.teamCode = teamCode;
        state.teamInfo.teamName = teamName;
    },
}
export const teams = {
    namespaced: true,
    state,
    getters,
    actions,
    mutations
}