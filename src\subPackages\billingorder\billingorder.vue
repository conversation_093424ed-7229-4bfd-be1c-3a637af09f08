<template>
  <view>
    <loading v-if="!hidden" @change="loadingChange">加载中...</loading>
    <!-- <view
      class="banner"
      :style="'padding-top:' + s_top + 'rpx;height:' + s_height + 'rpx;line-height:' + s_height + 'rpx'"
    >
      <image class="banner-img" @tap="goBack" :src="`${IMG_PATH}back.png"></image>
      <view class="banner-title">开票</view>
    </view> -->
    <view class="list-picker">
      <picker mode="date" :value="startTime" @change="bindstartTimeChange" class="picker-li">
        <view class="picker-li">
          <image class="li-img" :src="`${IMG_PATH}calendar.png`"></image>
          <view class="picker">
            {{ startTime ? startTime : '选择开始日期' }}
          </view>
        </view>
      </picker>
      <text class="picker-mid">至</text>
      <picker mode="date" :value="endTime" @change="bindendTimeChange" class="picker-li">
        <view class="picker-li">
          <image class="li-img" :src="`${IMG_PATH}calendar.png`"></image>
          <view class="picker">
            {{ endTime ? endTime : '选择结束日期' }}
          </view>
        </view>
      </picker>
      <!-- <view class="btn-yellow" style="margin-left: 10rpx" @tap="descShow">!</view> -->
    </view>
    <scroll-view
      v-if="have"
      :scroll-y="true"
      :style="'height:calc(100vh - env(safe-area-inset-bottom) - ' + (s_top + s_height) + 'rpx)'"
      @scrolltolower="loadMore"
      @scrolltoupper="refesh"
    >
      <view
        v-if="hasRefesh"
        style="display: flex; flex-direction: row; align-items: center; align-self: center; justify-content: center"
      >
        <text style="font-size: 30rpx">刷新中...</text>
      </view>
      <view v-else style="display: none">
        <text></text>
      </view>
      <!-- <view :style="{ height: s_top + s_height + 170 + 'rpx' }" v-if="ENVR == 'wx'"></view> -->
      <view :style="{ height: '75rpx' }"></view>
      <div class="billing-order-list-wrap">
        <checkbox-group @change="checkboxChange">
          <view class="billingorder-list" v-for="(item, index) in orderList" :key="index">
            <view class="list-one">
              <text>{{ item.orderNo }}</text>
              <text class="fr">{{ item.successTime }}</text>
            </view>

            <view class="list-two">
              <text v-if="invoiceType == '09'">{{ item.goodsName }}</text>
              <text v-else>订单类型：{{ item.orderTypeName }}</text>
              <label class="fr">
                <text class="blue-color">{{ item.invoiceAmt }}元</text>
                <checkbox
                  :data-name="item.invoiceAmt"
                  :value="item.orderNo"
                  :checked="item.checked"
                  :disabled="invoiceType === '09' && checkboxGoodsType && checkboxGoodsType !== item.goodsType"
                />
              </label>
            </view>
          </view>
        </checkbox-group>
      </div>
      <view v-if="have">
        <view
          v-if="hasMore"
          style="display: flex; flex-direction: row; align-items: center; align-self: center; justify-content: center"
        >
          <text style="font-size: 30rpx">玩命的加载中...</text>
        </view>
        <view style="width: 100%; text-align: center; font-size: 30rpx" v-else>
          <text>{{ texts }}</text>
        </view>
      </view>
      <view style="height: 60rpx"></view>
    </scroll-view>
    <view v-if="hist" class="myorder-image">
      <image :src="`${IMG_PATH}main-data-null.png`" mode="heightFix" />
      <text>暂无可开票订单</text>
    </view>
    <view class="ordercharge-btn">
      <view class="ordercharge-desc">
        <view class="desc-left">
          <text class="desc-text">
            <text class="number-highlight">{{ value.length }}</text
            >个订单，共<text class="price-highlight">{{ price }}</text
            >元
          </text>
          <text class="limit-tip" v-if="limitMoney"
            >（满<text class="limit-number">{{ limitMoney }}</text
            >即可开票）</text
          >
        </view>
        <view class="desc-right">
          <text class="help-link" @tap="descShow">《开票说明》</text>
        </view>
      </view>
      <view class="ordercharge-btns">
        <view class="checkbox-li" v-if="invoiceType !== '09'">
          <checkbox-group @change="checkboxChangePage">
            <label class="fr">
              <checkbox :checked="selectPage" />
              <text>本页全选</text>
            </label>
          </checkbox-group>
        </view>
        <view class="checkbox-li" v-if="invoiceType !== '09'">
          <checkbox-group @change="checkboxChangeAll">
            <label class="fr fr-end">
              <checkbox :checked="selectAll" />
              <text>全部全选</text>
            </label>
          </checkbox-group>
        </view>
        <view class="flex-full"></view>
        <button v-if="existence" @tap="Jump" class="btn blue-bg pingjia btn-fixed">开票</button>
        <button v-else class="btn blue-bg pingjia color-pas btn-fixed">开票</button>
      </view>
    </view>
    <loading v-if="!hiddenLoading">请稍后...</loading>

    <modal-popup v-if="!descHidden" @closeModal="descConfirm" title="开票说明">
      <!-- #ifdef MP-WEIXIN -->
      <!-- <web-view :src="titleItems[invoiceType].paramDescValue" style="width: 100%; height: 500px; border: none" /> -->
      <view slot="footer">
        <view class="footer-desc">{{ paramDescValue }}</view>
      </view>
      <!-- #endif -->
      <!-- #ifdef H5 -->
      <iframe :src="titleItems[invoiceType].paramDescValue" style="width: 100%; height: 500px; border: none" />
      <!-- #endif -->
    </modal-popup>
  </view>
</template>

<script>
import { ENVR } from '@/config/global';
import modalPopup from '../../components/modal-popup/index';
import { getOrderInvoicesList, getConfigOption, getServiceprovision } from '@/services/index.js';
// pages/billingorder/billingorder.js
import moment from 'moment';
export default {
  components: {
    modalPopup,
  },
  data() {
    return {
      qian: [],
      hiddenLoading: true,
      hist: false,
      texts: '加载更多',
      have: true,
      pageNum: 1,
      price: 0,
      existence: false,
      value: [],
      hasMore: false,
      hasRefesh: false,
      hidden: true,
      token: '',
      orderList: [],
      items: [
        {
          name: 'JAVA',
          value: 'Android',
        },
        {
          name: 'Object-C',
          value: 'IOS',
        },
        {
          name: 'JSX',
          value: 'ReactNative',
        },
        {
          name: 'JS',
          value: 'wechat',
        },
        {
          name: 'Python',
          value: 'Web',
        },
      ],
      startTime: '',
      endTime: '',
      selectPage: false,
      selectAll: false,
      descHidden: true,
      paramDescValue: '',
      addOrEdit: true,
      isReopen: false,
      s_top: '',
      s_height: '',
      isLoadAll: false,
      invoiceType: '',
      // 订单类型 02 充电列表 10 会员列表  09 商品列表
      titleItems: {
        '02': {
          label: '按订单开票',
          value: '02',
          paramDescValue: 'https://ndjtcs.evstyle.cn:6443/base/api/serviceprovision?id=23032577',
          code: '23032577',
        },
        10: {
          label: '会员',
          value: '10',
          paramDescValue: 'https://ndjtcs.evstyle.cn:6443/base/api/serviceprovision?id=23032579',
          code: '23032579',
        },
        '09': {
          label: '商品',
          value: '09',
          paramDescValue: 'https://ndjtcs.evstyle.cn:6443/base/api/serviceprovision?id=23032578',
          code: '23032578',
        },
      },
      checkboxGoodsType: undefined, // vip 会员  entity 实体商品 coupon优惠卷
      limitMoney: 1,
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const { type = '02' } = options;
    this.invoiceType = type;
    uni.getSys;
    var that = this;
    uni.getStorage({
      key: 'token',
      success: function (res) {
        that.setData({
          token: res.data,
        });
        that.loadData(10);
        that.getInvoiceExplain();
        that.getInvoiceExplainMoney();
      },
    });
    // #ifdef MP
    this.initTopImg();
    uni.setNavigationBarTitle({
      title: that.titleItems[type].label || '发票开具',
    });
    // #endif
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  mounted: function () {
    this.startTime = moment().startOf('month').format('YYYY-MM-DD');
    this.endTime = moment().format('yyyy-MM-DD');
  },
  methods: {
    initTopImg() {
      // const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      const menuButtonInfo = {
        top: 0,
        height: 0,
      };
      this.setData({
        s_top: menuButtonInfo?.top * 2,
        s_height: menuButtonInfo?.height * 2,
      });
    },

    goBack: function () {
      uni.navigateBack({
        delit: -1,
      });
    },

    // getInvoiceExplain: function () {
    //   const { token } = this;
    //   const that = this;
    //   uni.request({
    //     url: baseUrl + `/wx/v0.1/paras?paraCode=invoiceExplain`,
    //     data: {},
    //     method: 'GET',
    //     header: {
    //       'content-type': 'application/json',
    //       minitProgramToken: token,
    //     },
    //     success: (res) => {
    //       that.setData({
    //         paramDescValue: res.data.list[0].paramValue || '',
    //       });
    //     },
    //     fail: (err) => {
    //       console.log(err, '请求出问题。');
    //     },
    //   });
    // },
    async getInvoiceExplain() {
      const that = this;
      const [, res] = await getServiceprovision({
        infoId: this.titleItems[this.invoiceType].code,
      });
      console.log(res, 'res:getServiceprovision');
      if (res) {
        this.paramDescValue = res.info.content || '';
      }
    },
    async getInvoiceExplainMoney() {
      const that = this;
      const [, res] = await getConfigOption({
        paraCode: 'invoice_amt_limit',
      });
      if (res && res.list && res.list.length > 0) {
        this.limitMoney =
          res.list.find((q) => {
            return q.paramCode === 'invoice_amt_limit';
          })?.paramValue || 1;
      } else {
        this.limitMoney = 1;
      }
      uni.setStorage({
        key: 'fullPrice',
        data: this.limitMoney,
      });
    },

    bindstartTimeChange: function (event) {
      const { value } = event.detail;
      this.setData({
        startTime: value,
      });
      this.refesh();
    },

    bindendTimeChange: function (event) {
      const { value } = event.detail;
      this.setData({
        endTime: value,
      });
      this.refesh();
    },

    descConfirm: function () {
      this.setData({
        descHidden: true,
      });
    },

    descShow: function () {
      this.setData({
        descHidden: false,
      });
    },

    loadingChange: function () {
      var that = this;
      setTimeout(function () {
        that.setData({
          hidden: true,
        });
      }, 1200);
    },

    //加载更多
    loadMore: function (e) {
      if (this.have) {
        const that = this;
        const pageNum = that.pageNum + 1;
        that.setData({
          hasMore: true,
          hidden: false,
          pageNum,
        });
        this.loadData(10);
        setTimeout(function () {
          that.setData({
            hidden: true,
            hasMore: false,
          });
        }, 1500);
      }
    },

    loadData: function (totalNum, isAll) {
      this.getOrderInvoicesList(totalNum, isAll);
    },
    // 获取订单
    async getOrderInvoicesList(totalNum, isAll) {
      const { pageNum, orderList, startTime, endTime } = this;
      const that = this;
      this.setData({
        hiddenLoading: false,
      });
      let params = {
        pageNum: pageNum,
        startTime: startTime,
        endTime: endTime,
        orderType: this.invoiceType,
      };
      if (!isAll) {
        params = { ...params, totalNum: totalNum };
      }
      const [err, res] = await getOrderInvoicesList(params);
      that.setData({
        hiddenLoading: true,
      });
      if (res) {
        console.log(res, 'res');
        const { queryList, drawBillPrompt } = res;
        const alls = orderList.concat(queryList);
        const regex = new RegExp(`满(.*?)元`);
        const result = regex.exec(drawBillPrompt);
        console.log(alls, 'alls');
        that.setData({
          orderList: alls,
          have: alls.length > 0 ? true : false,
          hist: alls.length > 0 ? false : true,
          texts: queryList.length > 0 ? '' : '没有更多了',
        });

        if (isAll) {
          that.checkboxChangeAll();
        }
      }
    },

    panDuan: function () {
      const { value, price, limitMoney } = this;
      console.log(value, price, limitMoney, 888);
      if (value.length > 0 && Number(price) >= Number(limitMoney)) {
        this.setData({
          existence: true,
        });
      } else {
        this.setData({
          existence: false,
        });
      }
    },

    checkboxChange: function (e) {
      if (this.invoiceType === '09' && e.detail?.value?.length === 0) {
        this.checkboxGoodsType = undefined;
      }
      this.setData({
        value: e.detail.value,
        hiddenLoading: false,
      });
      const Msg = e.detail.value;
      const that = this;
      const list = that.orderList;
      let arr = [];
      let meh = [];
      let time = [];
      for (let i = 0; i < list.length; i++) {
        list[i].checked = false;
      }
      for (let i = 0; i < list.length; i++) {
        //如果字符串中不包含目标字符会返回-1
        for (let z = 0; z < Msg.length; z++) {
          if (list[i].orderNo === Msg[z]) {
            this.checkboxGoodsType = list[i]?.goodsType ?? '';
            list[i].checked = true;
            arr.push(list[i].invoiceAmt);
            meh.push(list[i].invoiceAmt);
            time.push(list[i].successTime);
          }
        }
      }
      let sum = 0;
      for (let o = 0; o < arr.length; o++) {
        sum += parseFloat(arr[o]);
      }
      uni.setStorage({
        key: 'meh',
        data: meh,
      });
      uni.setStorage({
        key: 'time',
        data: time,
      });
      const prices = sum.toFixed(2);
      that.setData(
        {
          price: prices,
          orderList: list,
          qian: meh,
          hiddenLoading: true,
        },
        () => {
          this.panDuan();
        }
      );
    },

    checkboxChangeAll: function () {
      const { isLoadAll, selectAll, selectPage } = this;
      const that = this;
      const list = that.orderList;
      if (!isLoadAll) {
        this.setData(
          {
            pageNum: 1,
            // orderList: [],
            isLoadAll: true,
          },
          () => {
            this.loadData(1, true);
          }
        );
      } else {
        let arr = [];
        let meh = [];
        let time = [];
        let value = [];
        this.setData({
          hiddenLoading: false,
        });
        if (!selectAll) {
          for (var i = 0; i < list.length; i++) {
            list[i].checked = true;
            arr.push(list[i].invoiceAmt);
            meh.push(list[i].invoiceAmt);
            time.push(list[i].successTime);
            value.push(list[i].orderNo);
          }
        } else {
          for (var i = 0; i < list.length; i++) {
            list[i].checked = false;
          }
        }
        var sum = 0;
        for (var o = 0; o < arr.length; o++) {
          sum += parseFloat(arr[o]);
        }
        uni.setStorage({
          key: 'meh',
          data: meh,
        });
        uni.setStorage({
          key: 'time',
          data: time,
        });
        const prices = sum.toFixed(2);
        that.setData(
          {
            price: prices,
            orderList: list,
            hiddenLoading: true,
            selectAll: !selectAll,
            selectPage: selectAll ? false : isLoadAll ? true : selectPage,
            qian: meh,
            value,
          },
          () => {
            this.panDuan();
          }
        );
      }
    },

    checkboxChangePage: function () {
      const { selectPage, selectAll, isLoadAll } = this;
      const that = this;
      const list = that.orderList;
      let arr = [];
      let meh = [];
      let time = [];
      let value = [];
      this.setData({
        hiddenLoading: false,
      });
      if (!selectPage) {
        for (var i = 0; i < list.length; i++) {
          list[i].checked = true;
          arr.push(list[i].invoiceAmt);
          meh.push(list[i].invoiceAmt);
          time.push(list[i].successTime);
          value.push(list[i].orderNo);
        }
      } else {
        for (var i = 0; i < list.length; i++) {
          list[i].checked = false;
        }
      }
      var sum = 0;
      for (var o = 0; o < arr.length; o++) {
        sum += parseFloat(arr[o]);
      }
      uni.setStorage({
        key: 'meh',
        data: meh,
      });
      uni.setStorage({
        key: 'time',
        data: time,
      });
      const prices = sum.toFixed(2);
      that.setData(
        {
          price: prices,
          orderList: list,
          hiddenLoading: true,
          selectPage: !selectPage,
          qian: meh,
          value,
          selectAll: selectPage ? false : isLoadAll ? true : selectAll,
        },
        () => {
          this.panDuan();
        }
      );
    },

    Jump: function () {
      console.log('开票');
      var that = this;
      var msg = that.value;
      var price = that.price;
      uni.redirectTo({
        url:
          '/subPackages/billingpersonal/billingpersonal?msg=' +
          msg.join(',') +
          '&price=' +
          price +
          '&type=' +
          that.invoiceType +
          '&goodsType=' +
          that.checkboxGoodsType,
      });
    },

    //刷新处理
    refesh: function (e) {
      var that = this;
      that.setData({
        hasRefesh: true,
        hidden: false,
        orderList: [],
        pageNum: 1,
        value: [],
        price: 0,
        selectPage: false,
        selectAll: false,
        isLoadAll: false,
      });
      this.loadData(10);

      // if (!this.data.hasMore) return
      setTimeout(function () {
        that.setData({
          hidden: true,
          hasRefesh: false,
        });
      }, 1500);
    },
  },
};
</script>
<style>
@import './billingorder.css';
</style>
