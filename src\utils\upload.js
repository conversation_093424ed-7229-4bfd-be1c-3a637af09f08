var baseUrl = require('./base').baseDate();
const chooseImg = () => {
  return new Promise((resolve) => {
    uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function (res) {
        resolve([null, res.tempFilePaths]);
      },
      fail: function (err) {
        console.log(err, 'chooseImage');
        resolve([err, null]);
      },
    });
  });
};
const uploadFile = ({ filePath, name, formData }) => {
  return new Promise((resolve) => {
    uni.uploadFile({
      url: baseUrl + '/wx/v0.1/file-upload',
      filePath: filePath,
      name,
      formData,
      success: function (res) {
        resolve([null, res]);
      },
      fail: function (err) {
        resolve([err, null]);
      },
    });
  });
};
// 压缩图片函数
const compressImage = (file, maxWidth = 800, maxHeight = 600, quality = 0.8) => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 计算压缩后的尺寸
      let { width, height } = img;

      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      canvas.width = width;
      canvas.height = height;

      // 绘制压缩后的图片
      ctx.drawImage(img, 0, 0, width, height);

      // 转换为base64，使用指定的质量
      const compressedBase64 = canvas.toDataURL('image/jpeg', quality);
      resolve(compressedBase64);
    };

    img.onerror = () => {
      resolve(null);
    };

    if (file instanceof File || file instanceof Blob) {
      const reader = new FileReader();
      reader.onload = (e) => {
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    } else if (typeof file === 'string') {
      img.src = file;
    }
  });
};

// 检查文件大小
const checkFileSize = (file, maxSizeMB = 1) => {
  if (file instanceof File) {
    const fileSizeMB = file.size / (1024 * 1024);
    return fileSizeMB <= maxSizeMB;
  }
  return true; // 对于非File对象，暂时返回true
};

const getImageToBase64_url = (tempFilePath, compress = true) => {
  // #ifdef H5
  return new Promise((resolve, reject) => {
    // 针对 H5 环境的文件处理
    if (tempFilePath instanceof File || tempFilePath instanceof Blob) {
      // 检查文件大小
      if (!checkFileSize(tempFilePath, 1)) {
        reject(new Error('图片大小不能超过1MB'));
        return;
      }

      if (compress) {
        // 压缩图片
        compressImage(tempFilePath).then(resolve).catch(reject);
      } else {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsDataURL(tempFilePath);
      }
    } else if (typeof tempFilePath === 'string') {
      if (tempFilePath.startsWith('data:')) {
        // 已经是 base64 格式
        resolve(tempFilePath);
      } else if (tempFilePath.startsWith('blob:') || tempFilePath.startsWith('http')) {
        // 处理 blob URL 或网络图片
        fetch(tempFilePath)
          .then((response) => response.blob())
          .then((blob) => {
            // 检查文件大小
            if (!checkFileSize(blob, 1)) {
              reject(new Error('图片大小不能超过1MB'));
              return;
            }

            if (compress) {
              return compressImage(blob);
            } else {
              const reader = new FileReader();
              reader.onload = () => resolve(reader.result);
              reader.onerror = reject;
              reader.readAsDataURL(blob);
            }
          })
          .then((result) => {
            if (compress && result) {
              resolve(result);
            }
          })
          .catch(reject);
      } else {
        // 本地文件路径
        uni.request({
          url: tempFilePath,
          responseType: 'arraybuffer',
          success: (res) => {
            const base64 = 'data:image/jpeg;base64,' + uni.arrayBufferToBase64(res.data);
            resolve(base64);
          },
          fail: reject,
        });
      }
    } else {
      reject(new Error('Unsupported file type'));
    }
  });
  // #endif

  // #ifdef MP-WEIXIN
  // 微信小程序处理逻辑保持不变
  return new Promise((resolve, reject) => {
    wx.getFileSystemManager().readFile({
      filePath: tempFilePath,
      encoding: 'base64',
      success: (res) => resolve(`data:image/jpeg;base64,${res.data}`),
      fail: reject,
    });
  });
  // #endif
};
const choosUserImg = async (tempFilePath) => {
  console.log('开始处理图片:', tempFilePath);

  // try {
  // 通过fetch获取Blob对象
  const response = await fetch(tempFilePath);
  const blob = await response.blob();
  console.log('获取到 blob:', blob);

  // 返回一个新的Promise来处理FileReader的异步操作
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = () => {
      console.log('FileReader 转换成功', reader.result);
      return Promise.resolve(reader.result); // 使用resolve而不是return
    };

    reader.onerror = (error) => {
      console.error('FileReader 转换失败:', error);
      return Promise.resolve(''); // 使用reject而不是return
    };

    reader.readAsDataURL(blob);
  });
  // } catch (error) {
  //   console.error('处理过程出错:', error);
  //   throw error; // 抛出错误以便调用方捕获
  // }
}; // 这里的return没有效果
module.exports = {
  chooseImg,
  uploadFile,
  getImageToBase64_url,
};
