import { request as __request } from '../request/request';
import {
  ENVR,
  scanBaseurl,
  pubBaseurl,
  astBaseurl,
  defBaseurl,
  ordBaseurl,
  busiBaseurl,
  baseBaseurl,
  bilBaseurl,
  cstBaseurl,
} from '../../config/global';
import { get } from 'core-js/core/dict';

// 查询签到能获取的积分
export const signIntegeral = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : bilBaseurl}/v0.1/signIntegeral`,
      data: params,
    }
  );
};
// 签到并获取当月签到
export const signAndGetMonthSignData = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: `POST`,
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/signAndQuerySignInfo`,
      data: params,
    }
  );
};
// 查询总积分
export const queryTotalPoints = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: `${ENVR === 'wx' ? 'POST' : 'GET'}`,
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/vip/record`,
      data: params,
    }
  );
};

// 刷新任务状态
export const refreshTaskStatus = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/getIntegralTask`,
      data: params,
    }
  );
};
// 获取用户配置信息
export const getPref = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ENVR == 'wx' ? scanBaseurl : cstBaseurl}/v0.2/pref`,
      data: params,
    }
  );
};
// 获取用户配置信息
export const setPref = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR == 'wx' ? scanBaseurl : cstBaseurl}/v0.2/pref`,
      data: params,
    }
  );
};

// 查询优惠券
export const getCoupon = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: ENVR === 'wx' ? 'POST' : 'GET',
      url: `${ENVR === 'wx' ? scanBaseurl + '/v0.1/act/actCoupon' : bilBaseurl + '/v0.1/coupons'}`,
      data: params,
    }
  );
};
// 领取优惠券
export const getCouponReceive = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: ENVR === 'wx' ? 'GET' : 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl + '/v0.1/coupons/post' : bilBaseurl + '/v0.1/coupons'}`,
      data: params,
    }
  );
};
// 一键领取
export const getCouponReceiveAll = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : busiBaseurl}/v0.1/coupons/allPick`,
      data: params,
    }
  );
};

// 更新默认车辆
export const updateDefaultCar = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : cstBaseurl}/v0.2/upPrefDefault`,
      data: params,
    }
  );
};
// 删除绑定车辆
export const deletePref = (params, config = {}) => {
  return __request(
    {
      ...config,
    },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl + '/v0.2/deletePref' : cstBaseurl + '/v0.2/delpref'}`,
      data: params,
    }
  );
};
// 订单列表
export const getOrderList = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: `${ENVR === 'wx' ? 'POST' : 'GET'}`,
      url: `${ENVR === 'wx' ? scanBaseurl : ordBaseurl}/v0.1/order-charge-list`,
      data: params,
    }
  );
};

// 获取优惠券列表
export const getAccountsCoupons = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/accounts/coupons`,
      data: params,
    }
  );
};
// 查询7天内充电记录
export const getOpenChargingInfo = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${busiBaseurl}/open/v0.1/chargingInfo`,
      data: params,
    }
  );
};
// 查看退款记录列表
export const getRefundRecordDetail = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/online-refund-log`,
      data: params,
    }
  );
};
// 提交退款
export const submitRefund = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl + '/v0.1/online-refund' : defBaseurl + '/v0.2/online-refund'}`,
      data: params,
    }
  );
};
// 订单提交
export const submitOrder = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl + '/v0.3/order' : ordBaseurl + '/v0.3/order'}`,
      data: params,
    }
  );
};
// 获取订单详情
export const getChargeOrderDtl = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: ENVR === 'wx' ? 'POST' : 'GET',
      url: `${ENVR === 'wx' ? scanBaseurl : busiBaseurl}/v0.1/charge_order_dtl`,
      data: params,
    }
  );
};

// 微信缴费
export const wxMiniOrder = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${scanBaseurl}/v0.1/wx-mini-order`,
      data: params,
    }
  );
};

// 订单统计
export const orderCount = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ENVR === 'wx' ? scanBaseurl : ordBaseurl}/v0.1/charge_order_statistics`,
      data: params,
    }
  );
};

// 评价信息查询
export const getEvaDtl = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: ENVR === 'wx' ? 'POST' : 'GET',
      url: `${ENVR === 'wx' ? scanBaseurl : ordBaseurl}/v0.1/eva-dtl`,
      data: params,
    }
  );
};

// 反馈问题分类列表
export const getFaqType = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ENVR === 'wx' ? scanBaseurl : cstBaseurl}/v0.1/faq-type`,
      data: params,
    }
  );
};

// 提交反馈
export const submitPubFaq = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : cstBaseurl}/v0.1/pub-faq`,
      data: params,
    }
  );
};
// 追加反馈
export const replyPubFaq = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : cstBaseurl}/v0.1/pub-faq-reply`,
      data: params,
    }
  );
};
// 获取7天内充电次数及时间
export const recentChargingInfo = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ordBaseurl}/open/v0.1/recentChargingInfo`,
      data: params,
    }
  );
};
// 收藏站点
export const collectStation = (params, config = {}) => {
  return __request(
    {
      ...config,
      // #ifdef H5
      'content-type': 'application/x-www-form-urlencoded',
      // #endif
    },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : baseBaseurl}/v0.1/pref`,
      data: params,
    }
  );
};
// 取消收藏站点
export const delCollectStation = (params, config = {}) => {
  return __request(
    {
      ...config,
    },
    {
      method: ENVR === 'wx' ? 'POST' : 'GET',
      url: `${ENVR === 'wx' ? scanBaseurl + '/v0.1/pref-del' : cstBaseurl + '/v0.1/delpref'}`,
      data: params,
    }
  );
};
// 消息设置为已读
export const readMsg = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : pubBaseurl}/v0.1/msg-read`,
      data: params,
    }
  );
};

// 获取反馈信息
export const getFaqReply = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ENVR === 'wx' ? scanBaseurl : cstBaseurl}/v0.1/faq-reply`,
      data: params,
    }
  );
};
// 关闭反馈
export const offFaqReply = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : cstBaseurl}/v0.1/faq-reply-off`,
      data: params,
    }
  );
};

// 获取反馈列表
export const getFaqList = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ENVR === 'wx' ? scanBaseurl : baseBaseurl}/v0.1/faq-list`,
      data: params,
    }
  );
};
// 获取常见问题列表
export const getCommonProblemList = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${astBaseurl}/v0.1/faq`,
      data: params,
    }
  );
};
// 获取车型列表
export const getVehicleInfo = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${astBaseurl}/v0.1/vehicle_info`,
      data: params,
    }
  );
};
// 卡密兑换
export const doCharge = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl + '/card' : busiBaseurl + '/rechargeCard'}/v0.1/doCharge`,
      data: params,
    }
  );
};
// 卡密兑换
export const myRechargeCards = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ENVR === 'wx' ? scanBaseurl + '/card' : busiBaseurl + '/rechargeCard'}/v0.1/myRechargeCards`,
      data: params,
    }
  );
};
