<template>
  <view class="login">
    <image :src="`${IMG_PATH}logo.png`"></image>
    <view class="section-phone">
      <text>手机号码{{ time }}</text>
      <input @input="phoneStatus" type="number" maxlength="11" placeholder="请输入手机号码" />
      <button @click="down" v-if="time" class="btn-phone btn-phonesd styleColorB">获取验证码</button>
      <button v-else class="btn-phone">{{ times }}s重新获取</button>
    </view>
    <view class="section-code">
      <text>验证码</text>
      <input @input="passStatus" maxlength="6" type="number" placeholder="请输入验证码" />
    </view>
    <text class="txt-notice">绑定手机后可接收短信通知、登录APP及保护您的账号安全</text>
    <button @click="open" v-if="nice" class="btn-submit styleColorB">确认绑定</button>
    <button v-else style="background: #eee" class="btn-submit">确认绑定</button>
    <!-- <view class="txt-read">绑定，即表示已阅读并同意<view class='inlines' bindtap='use'>用户协议</view></view> -->
  </view>
</template>

<script>
import { getVerifyCode, loginInterface, refreshToken, autoLogin, getUserAgreement } from '@/services/index.js';
export default {
  data() {
    return {
      times: 59,
      time: true,
      phoneMain: false,
      passMain: false,
      phone: '',
      pass: '',
      nice: false,
      token: '',
      loading: false,
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    uni.login({
      success: async (res) => {
        console.log('res.coderes.code', res.code);
        if (res.code) {
          const [, result] = await refreshToken({
            code: res.code,
            // orgCode: '0000001'
          });
          console.log(result, 'result');
          if (result) {
            that.token = result.token;
          }
        } else {
          console.log('获取用户登录态失败！' + res.errMsg);
        }
      },
    });
    // uni.checkSession({
    //   success: function () {
    //     uni.login({
    //       success: (res) => {
    //         if (res.code) {
    //           //token刷新
    //           uni.request({
    //             url: baseUrl + '/wx/v0.1/swapSInfo',
    //             data: {
    //               code: res.code,
    //               // orgCode: '0000001'
    //             },
    //             method: 'GET',
    //             header: {
    //               'content-type': 'application/json',
    //             },
    //             success: (res) => {
    //               console.log(res.data.token, '根据code的回参');
    //               // uni.setStorage({
    //               //   key: "token",
    //               //   data: res.data.token
    //               // })
    //               // uni.setStorageSync('token', res.data.token);
    //               that.token = res.data.token;
    //             },
    //             fail: (err) => {},
    //           });
    //         } else {
    //           console.log('获取用户登录态失败！' + res.errMsg);
    //         }
    //       },
    //     });
    //   },
    //   fail: function () {
    //     //登录态过期
    //     console.log('登录过期ing');
    //   },
    // });

    // uni.getStorage({
    //   key: 'token',
    //   success: function (res) {
    //     _self.setData({
    //       token: res.data
    //     });
    //     console.log(_self.data.token)
    //   }
    // })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    uni.setNavigationBarTitle({
      title: '登录',
    });
  },
  methods: {
    async down() {
      var that = this;
      var sPhone = that.phone;
      if (that.phone == '') {
        uni.showToast({
          title: '号码不能为空',
          icon: 'loading',
          duration: 1000,
        });
        return false;
      } else if (!/^1\d{10}$/.test(sPhone)) {
        console.log('手机号码格式错误');
        uni.showToast({
          icon: 'loading',
          title: '手机号码格式错误',
        });
        return false;
      }
      this.time = false;
      var interval = undefined;
      var time = that.times;
      // clearInterval(interval)
      interval = setInterval(function () {
        if (time == 0) {
          console.log('时间到');
          clearInterval(interval);
          that.time = true;
          that.times = 59;
        } else {
          that.times = time--;
        }
        console.log(time, that.times);
      }, 1000);
      var token = that.token;
      const [, res] = await getVerifyCode({
        mobile: that.phone,
        verifyType: '05',
      });
      if (res) {
        uni.showToast({
          title: '发送成功',
          icon: 'success',
          duration: 1000,
        });
      } else {
        console.log('获取验证码请求出问题。');
      }
    },

    async use() {
      console.log('用户协议');
      var that = this;
      const [, res] = await getUserAgreement({
        code: '0403',
      });
      if (res) {
        console.log(res, '用户协议');
      } else {
        console.log(err, '用户协议出问题。');
      }
    },
    phoneStatus(value) {
      console.log(value);
      var that = this;
      if (!value.detail.value || value.detail.value == ' ') {
        console.log('空的');
        this.phoneMain = false;
      } else {
        this.phoneMain = true;
        this.phone = value.detail.value;
      }
      console.log(that.phone, '电话');
      this.nice = that.phoneMain && that.passMain;
    },
    passStatus(value) {
      console.log(value.detail.value);
      console.log(value);
      var that = this;
      if (!value.detail.value || value.detail.value == ' ') {
        console.log('空的');
        this.passMain = false;
      } else {
        this.passMain = true;
        this.pass = value.detail.value;
      }
      console.log(that.pass, '验证码');
      this.nice = that.phoneMain && that.passMain;
    },
    async open() {
      if (this.loading) return;
      var that = this;
      var sPhone = that.phone;
      if (!/^1\d{10}$/.test(sPhone)) {
        console.log('手机号码格式错误');
        uni.showToast({
          icon: 'loading',
          title: '手机号码格式错误',
        });
        return false;
      }
      console.log('手机号码正确');
      var token = that.token;
      var dataBak = that;
      console.log(token, '登录token');
      var a = {
        mobile: that.phone,
        verifyCode: that.pass,
      };
      console.log(a, '登录入参');
      this.loading = true;
      const [, res] = await loginInterface(
        {
          mobile: that.phone,
          verifyCode: that.pass,
        },
        {
          minitProgramToken: this.token,
        }
      );
      this.loading = false;
      console.log(res, 'res');
      if (res) {
        if (res.ret == 200) {
          uni.showToast({
            title: '登录成功',
            icon: 'success',
            duration: 1200,
          });
          uni.setStorageSync('token', that.token);
          this.$store.dispatch('login/getUserInfoCallback').then(() => {
            setTimeout(function () {
              uni.navigateBack();
            }, 500);
          });
        } else {
          uni.showToast({
            title: '登陆出现问题',
            icon: 'loading',
            duration: 1200,
          });
        }
      }
    },
    // 添加自动登录逻辑
    async autoLogin(params) {
      const that = this;
      const [, res] = await autoLogin();
      if (res) {
        const { data, ret } = res;
        let phone = '未绑定';
        if (ret === 200 && data.mobile) {
          phone = data.mobile;
          uni.setStorageSync('token', that.token);

          this.$store.dispatch('login/getUserInfoCallback').then(() => {
            setTimeout(function () {
              uni.navigateBack();
            }, 1200);
          });
        }
      }
    },
    clear() {
      this.phone = '';
      this.pass = '';
    },
  },
};
</script>
<style scoped lang="scss">
.login image {
  display: block;
  width: 180rpx;
  height: 180rpx;
  margin: 70rpx auto;
  border-radius: 50%;
}
.section-phone {
  position: relative;
  height: 90rpx;
  line-height: 90rpx;
  padding: 0 255rpx 0 200rpx;
  background-color: #fff;
}
.section-phone::after {
  content: '';
  position: absolute;
  left: 30rpx;
  right: 0;
  bottom: 0;
  display: block;
  border-bottom: 1rpx solid #e5e5e5;
}
.section-phone text {
  position: absolute;
  left: 30rpx;
  top: 0;
  width: 170rpx;
  height: 90rpx;
  color: #000;
  font-size: 32rpx;
}
.section-phone input {
  width: 100%;
  height: 90rpx;
  font-size: 32rpx;
}
.section-phone button {
  position: absolute;
  top: 0;
  right: 0;
  display: block;
  width: 255rpx;
  height: 90rpx;
  color: #828282;
  font-size: 32rpx;
  border-left: 1rpx solid #e5e5e5;
}
.btn-phone {
  height: 90rpx;
  line-height: 90rpx;
  color: #fff;
  background-color: #fff;
  border-radius: 0;
  font-size: 26rpx;
}
.section-phone .btn-phonesd {
  background-color: #e00a22;
  color: #fff;
}
.btn-phone::after {
  border-color: transparent;
}
.section-code {
  position: relative;
  height: 90rpx;
  line-height: 90rpx;
  padding: 0 30rpx 0 200rpx;
  background-color: #fff;
}
.section-code text {
  position: absolute;
  left: 30rpx;
  top: 0;
  width: 170rpx;
  height: 90rpx;
  color: #000;
  font-size: 32rpx;
}
.section-code input {
  width: 100%;
  height: 90rpx;
  font-size: 32rpx;
}
.txt-notice {
  display: block;
  padding-top: 26rpx;
  padding-bottom: 40rpx;
  font-size: 25rpx;
  color: #9f9f9f;
  text-align: center;
}
.btn-submit {
  height: 88rpx;
  line-height: 88rpx;
  color: #fff;
  background-color: #e12037;
  border-radius: 6px;
  font-size: 32rpx;
  margin: 0 30rpx;
}
.btn-submit::after {
  border-color: transparent;
}
.txt-read {
  display: block;
  padding-top: 30rpx;
  font-size: 24rpx;
  color: #9f9f9f;
  text-align: center;
}
.txt-read .inlines {
  color: #576b95;
  display: inline-block;
}
</style>
