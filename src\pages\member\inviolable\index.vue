<template>
  <div class="wrap">
    <!-- #ifdef MP-WEIXIN -->
    <view
      class="banner"
      :style="'padding-top:' + s_top + 'rpx;height:' + s_height + 'rpx;line-height:' + s_height + 'rpx'"
    >
      <image class="banner-img" @click="goBack" mode="widthFix" :src="`${IMG_PATH}back.png`"></image>
      <view class="banner-title">会员权益</view>
    </view>
    <!-- #endif -->

    <scroll-view class="horizontal-card" :show-scrollbar="false" :scroll-left="leftPosition" :scroll-x="true">
      <view
        class="horizontal-card-item"
        v-for="(item, index) in cardList"
        @click="check(item, index)"
        :style="{
          backgroundImage: `url(${IMG_PATH}member/${
            active == index ? 'discount-active-card-bg' : 'discount-card-bg'
          }.png)`,
        }"
        :key="index"
      >
        <image :src="`${IMG_PATH}member/discount-icon.png`" mode="scaleToFill" />
        <div class="text" :class="{ active: index === active }">{{ item.label || '-' }}</div>
      </view>
    </scroll-view>
    <div class="card-text">
      <div class="title">{{ content.label || '-' }}</div>
      <div class="content-text">
        <div class="tip-title">权益介绍</div>
        <div class="text yhq-box">
          <view v-for="(item, index) in content.introduce" :key="index">{{ item }}</view>
        </div>
        <div class="tip-title">权益明细</div>
        <div class="text">
          <rich-text class="rich-text" :nodes="content.remark || ''"></rich-text>
        </div>
      </div>
    </div>
    <div class="bottom-btn" @click="toPay">
      <template v-if="memberInfo.vipFlag === '1'">立即续费</template>
      <template v-else> ￥ {{ selectVipPage.price || '' }} 立即开通 </template>
    </div>
    <Pay ref="Pay" :selectVipPage="selectVipPage" :memberInfo="memberInfo" @reset="loadData"></Pay>
  </div>
</template>

<script>
import Pay from '@/components/toPay/index.vue';
import { getMemberInfo, getConfig, getConfigDesc, openInfo, recharge } from '@/services/index.js';
import AppButton from '@/components/AppButton/index';
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup';
import uniPopupDialog from '@/components/uni-ui/uni-popup-dialog/uni-popup-dialog';
export default {
  props: {},
  components: { uniPopup, uniPopupDialog, AppButton, Pay },
  data() {
    return {
      s_top: 0,
      s_height: 0,
      cardList: [],
      active: 0,
      content: {
        introduce: '',
        remark: '',
      },
      vipConfig: {},
      leftPosition: 0,
      // 优惠券
      cashCoupon: [],
      // 折扣券
      discountCoupon: [],
      selectVipId: '',
      menuList: [],
      memberInfo: {},
      memberType: 0,
    };
  },
  onShow() {
    // #ifdef MP
    this.initTopImg();
    // #endif
  },
  computed: {
    memberList() {
      return [
        {
          label: '现金券',
          text: this.cashCoupon
            .sort((a, b) => b.cpnAmt - a.cpnAmt)
            .map((q) => {
              return q.cpnName + '*' + q.cpnNum;
            }),
          show: true,
          code: '0601',
        },
        {
          label: '折扣券',
          text: this.discountCoupon
            .sort((a, b) => b.cpnAmt - a.cpnAmt)
            .map((q) => {
              return q.cpnName + '*' + q.cpnNum;
            }),
          show: true,
          code: '0602',
        },
        {
          label: '开卡礼',
          text: this.vipConfig?.welcomeGift
            ? JSON.parse(this.vipConfig?.welcomeGift)
                .sort((a, b) => b.amt - a.amt)
                .map((q) => {
                  return q.couponName + '*' + q.couponNum;
                })
            : [],
          show: true,
          code: '0603',
        },
        {
          label: '积分兑换',
          text: [this.vipConfig?.isExchangeExplain || '-'],
          show: this.vipConfig?.isExchange == '1',
          code: '0604',
        },
        {
          label: '双倍积分',
          text: [this.vipConfig?.isDoubleExplain || '-'],
          show: this.vipConfig?.isDouble == '1',
          code: '0605',
        },
        {
          label: '专属客服',
          text: [this.vipConfig?.isExclusiveCustomerExplain || '-'],
          show: this.vipConfig?.isExclusiveCustomer == '1',
          code: '0606',
        },
        {
          label: '升级礼包',
          text: [this.vipConfig?.isUpgradePackageExplain || '-'],
          show: this.vipConfig?.isUpgradePackage == '1',
          code: '0607',
        },
        {
          label: '会员场站',
          text: [this.vipConfig?.isMemberStationExplain || '-'],
          show: this.vipConfig?.isMemberStation == '1',
          code: '0608',
        },
        {
          label: '会员折上折',
          text: [this.vipConfig?.isMemberDayAdditionalDiscountExplain || '-'],
          show: this.vipConfig?.isMemberDayAdditionalDiscount == '1',
          code: '0609',
        },
      ];
    },
    selectVipPage() {
      const findData = this.menuList.find((q) => q.vipId == this.selectVipId);
      if (!findData) {
        return {};
      }
      return {
        ...findData,
        price: this.memberInfo.priceType == 'new' ? findData.vipPriceNew : findData.vipPriceOld,
      };
    },
  },
  watch: {},
  onLoad(options) {
    const { title, type = 0 } = options;
    this.title = title;
    this.memberType = type;
    this.loadData();
  },
  mounted() {},
  methods: {
    loadData() {
      this.getConfigDesc();
      this.getMemberInfo();
      this.getMenuList();
    },
    // 获取会员信息
    async getMemberInfo() {
      const params = {
        plusEquity: '1',
      };
      const [err, res] = await getMemberInfo(params);
      if (res) {
        this.memberInfo = res;
        this.benefitList = res.benefitList;
      }
    },
    // 查询套餐
    async getMenuList() {
      const [err, res] = await getConfig();
      if (res) {
        this.menuList = res.vipPayConfigs;
        if (this.menuList.length !== 0) {
          // this.selectedPackage === this.menuList[0];
          this.selectVipId = this.menuList[0].vipId;
        }
      }
    },
    goBack() {
      // #ifdef H5
      this.$router.go(-1);
      // #endif
      // #ifdef MP-WEIXIN
      uni.navigateBack({
        delta: -1,
      });
      // #endif
    },
    initTopImg() {
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      this.setData({
        s_top: menuButtonInfo?.top * 2 + 20,
        s_height: menuButtonInfo?.height * 2,
      });
    },
    check(item, index) {
      this.active = index;
      this.openInfo(item);
    },
    // 获取会员权益配置
    async getConfigDesc() {
      const [, res] = await getConfigDesc({
        vipLevel: this.memberType == 0 ? '5' : '6',
      });
      if (res) {
        this.vipConfig = { ...res.vipConfig };
        this.discountCoupon = res.discountCoupon || [];
        this.cashCoupon = res.cashCoupon || [];
        this.cardList = this.memberList;
        const findData = this.cardList.find((q) => q.label == this.title);
        this.active = findData ? this.cardList.findIndex((q) => q.label == this.title) : 0;
        this.leftPosition = this.active * 160 + 'rpx';
        this.openInfo(findData ? findData : this.cardList[0]);
      }
    },
    async openInfo(findData) {
      if (!findData?.code) {
        this.content = {
          introduce: '',
          remark: '',
        };
        return;
      }
      let arr = [findData?.code];
      const params = {
        infoTypeList: arr,
      };
      const [err, res] = await openInfo(params);
      if (res) {
        let list = res.infoList;
        this.content = {
          remark: list
            .find((item) => item.infoType === findData?.code)
            ?.content.replace(/background-color:\s*[^;]+/g, 'background-color: transparent'),
          label: findData?.label,
          introduce: findData?.text || [],
        };
      }
    },
    // 立即续费 立即开通
    toPay() {
      // i宁德直接跳转
      const appName = uni.getStorageSync('app') || '';
      if (appName == 'i宁德') {
        this.recharge();
        return;
      }
      this.selectVipId = this.menuList.find((q) => q.vipId == '1')?.vipId;
      this.$refs.Pay && this.$refs.Pay.chooseChannel();
    },
    // 查询支付参数
    async recharge() {
      const params = {
        reqType: '01',
        appType: '04',
        payAmount: this.selectVipPage.price,
        vipType: this.selectVipPage.vipType,
        transactionChannel: '12',
        notifyUrl: 'https://ndjtcs.evstyle.cn:6443/base/api/iningde-notify', // 支付成功异步回地址
        redirectUrl: 'https://ndjtcs.evstyle.cn:6443/ndcharge/pages/member/inviolable/index',
      };
      const [err, res] = await recharge(params);
      if (res && res?.orderResultINingDe) {
        window.location.href = res.orderResultINingDe;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.wrap {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(166deg, #504a51 -2%, #1e1f28 30%, #1e1f28 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  .banner {
    padding: 20rpx 20rpx 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
    z-index: 2;
    box-sizing: border-box;
    color: #fff;
    .banner-title {
      font-family: 'PingFang SC';
      font-size: 36rpx;
      font-style: normal;
      font-weight: 500;
      flex: 1;
      text-indent: 4rpx;
      color: #000000;
      font-weight: 700;
      text-align: left;
      color: #fff;
    }
    .banner-img {
      width: 48rpx;
      height: 48rpx;
      margin-right: 20rpx;
    }
  }
  .horizontal-card {
    width: 100%;
    padding: 50rpx 24rpx 33rpx;
    box-sizing: border-box;
    gap: 15rpx;
    grid-gap: 15rpx;
    white-space: nowrap;
    overflow: auto;
    box-sizing: border-box;
    .horizontal-card-item {
      width: 207rpx;
      height: 208rpx;
      padding: 28rpx 24rpx;
      box-sizing: border-box;
      margin-right: 10rpx;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      background-size: 100% 100%;
      gap: 20rpx;
      grid-gap: 20rpx;
      box-sizing: border-box;
      image {
        width: 107rpx;
        height: 107rpx;
      }
      .text {
        font-size: 24rpx;
        text-align: center;
        font-weight: 600;
        color: #eab296;
        max-width: 150rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &.active {
          color: #27272f;
        }
      }
    }
  }

  .yhq-box {
    max-height: 150rpx !important;
    overflow-y: auto;
  }
  .bottom-btn {
    border-radius: 16px;
    opacity: 1;
    background: #d89d70;
    width: 702rpx;
    height: 100rpx;
    color: #fff;
    font-size: 36rpx;
    letter-spacing: 0.58px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 50rpx;
    margin-top: 20rpx;
  }
  .card-text {
    flex: 1;
    width: 100%;
    width: 701rpx;
    border-radius: 24rpx;
    opacity: 1;
    background: linear-gradient(164deg, #ffefe6 29%, #ffd6c1 90%);
    padding: 37rpx 27rpx;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-sizing: border-box;
    .title {
      font-size: 48rpx;
      color: #27272f;
      text-align: center;
      font-weight: 800;
      line-height: 60rpx;
      padding-bottom: 15rpx;
    }
    .content-text {
      width: 100%;
      height: calc(100% - 60rpx);
      overflow-y: auto;
      .tip-title {
        font-size: 32rpx;
        font-weight: 800;
        color: #27272f;
        padding-left: 30rpx;
        position: relative;
        &::before {
          content: '';
          position: absolute;
          width: 10rpx;
          height: 100%;
          left: 0;
          top: 0;
          background-color: #b27353;
          border-radius: 3rpx;
        }
      }
      .text {
        font-size: 24rpx;
        color: #333333;
        padding: 20rpx 0;
        line-height: 30rpx;
        margin-bottom: 10rpx;
        display: flex;
        flex-direction: column;
        gap: 15rpx;
        grid-gap: 15rpx;
      }
    }
  }
}
</style>
