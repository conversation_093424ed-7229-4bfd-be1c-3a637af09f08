const formatTime = (date) => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();
  return [year, month, day].map(formatNumber).join('/') + ' ' + [hour, minute, second].map(formatNumber).join(':');
};
const formatNumber = (n) => {
  n = n.toString();
  return n[1] ? n : '0' + n;
};
function debounce(func, delay) {
  let timer;

  return function (...args) {
    const context = this;
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(context, args);
    }, delay);
  };
}

// 判断当前浏览器环境
function getWxEnv() {
  const isWechatMiniProgram = /miniProgram/i.test(navigator.userAgent.toLowerCase());
  const isWechatBrowser = /MicroMessenger/i.test(navigator.userAgent);
  return isWechatMiniProgram || isWechatBrowser;
  // if (isWechatMiniProgram) {
  //   return 'miniprogram';
  // } else if (isWechatBrowser) {
  //   return 'wechatBrowser';
  // } else {
  //   return 'normal';
  // }
}

function startsWithHttp(str) {
  if (/^www/.test(str)) return 'https://' + str;
  if (/^http/.test(str) || /^https:/.test(str)) return str;
  return process.env.VUE_APP_BASE_HOST + str;
}
function desensitization(phone) {
  if (!phone) return;
  // 检查手机号格式
  const phoneRegex = /^(\d{3})\d{4}(\d{4})$/;
  // 如果手机号符合格式，进行脱敏处理
  return phone.replace(phoneRegex, '$1****$2');
}

// 判断是否在微信环境中

// 判断用户机型

// 校验用户终端环境后，根据条件放行跳转到小程序

// 获取微信生成Url Scheme的token

module.exports = {
  formatTime: formatTime,
  debounce,
  startsWithHttp,
  getWxEnv,
  desensitization,
};
