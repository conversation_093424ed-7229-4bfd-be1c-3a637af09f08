<!--
@name: 购买详情
@description: 购买详情页面
@time: 2024/8/12
-->
<template>
  <view class="order-detail">
    <view class="detail-content">
      <view class="card">
        <view class="card-item">
          <text class="left">消费类型</text>
          <text class="right">{{ recordInfo.vipTypeName }}</text>
        </view>
        <view class="card-item">
          <text class="left">订单编号</text>
          <text class="right">{{ recordInfo.recordId }}</text>
        </view>
        <view class="card-item">
          <text class="left">消费金额</text>
          <view class="price">
            <text class="price-item">-￥</text>
            <text class="price-item">{{ recordInfo.amount }}</text>
          </view>
        </view>
        <view class="card-item">
          <text class="left">购买时间</text>
          <text class="right">{{ recordInfo.payTime }}</text>
        </view>
        <view class="card-item">
          <text class="left">到期时间</text>
          <text class="right">{{ recordInfo.expireTime }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'index',
  data() {
    return {
      recordInfo: {},
    };
  },
  onLoad() {
    this.recordInfo = uni.getStorageSync('recordInfo');
  },
  // 页面销毁前删除缓存
  beforeDestroy() {
    uni.removeStorageSync('recordInfo');
  },
};
</script>

<style scoped lang="scss">
.order-detail {
  background: linear-gradient(180deg, #1e9cff 0%, #f6f7f9 100%);
  height: 100%;
  padding-top: 24rpx;
  .detail-content {
    background: #f6f7f9;
    border-radius: 24rpx 24rpx 0 0;
    padding: 24rpx;
    min-height: calc(100% - 24rpx);
    .card {
      background: white;
      border-radius: 16rpx;
      padding: 24rpx 24rpx 4rpx 24rpx;
      display: flex;
      flex-direction: column;
      &-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20rpx;
        .left {
          font-size: 28rpx;
          line-height: 42rpx;
          color: #999999;
        }
        .right {
          font-size: 28rpx;
          line-height: 42rpx;
          color: #333333;
        }
        .price {
          color: #ff6835;
          &-item:nth-child(1) {
            font-size: 28rpx;
            line-height: 42rpx;
          }
          &-item:nth-child(2) {
            font-size: 40rpx;
            font-weight: bold;
            line-height: 40rpx;
            font-feature-settings: 'kern' on;
          }
        }
      }
    }
  }
}
</style>
