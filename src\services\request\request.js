import { instance } from '../../utils/network';

export const getQueryString = (params) => {
  const qs = [];

  const append = (key, value) => {
    qs.push(`${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`);
  };

  const process = (key, value) => {
    if (value !== undefined && value !== null) {
      if (Array.isArray(value)) {
        value.forEach((v) => {
          process(key, v);
        });
      } else if (typeof value === 'object') {
        Object.entries(value).forEach(([k, v]) => {
          process(`${key}[${k}]`, v);
        });
      } else {
        append(key, value);
      }
    }
  };

  Object.entries(params).forEach(([key, value]) => {
    process(key, value);
  });

  if (qs.length > 0) {
    return `?${qs.join('&')}`;
  }

  return '';
};

export const isBlob = (value) => {
  return (
    typeof value === 'object' &&
    typeof value.type === 'string' &&
    typeof value.stream === 'function' &&
    typeof value.arrayBuffer === 'function' &&
    typeof value.constructor === 'function' &&
    typeof value.constructor.name === 'string' &&
    /^(Blob|File)$/.test(value.constructor.name) &&
    /^(Blob|File)$/.test(value[Symbol.toStringTag])
  );
};

export const isString = (value) => {
  return typeof value === 'string';
};

const getUrl = (config, options) => {
  const encoder = config.ENCODE_PATH || encodeURI;

  const path = options.url.replace(/{(.*?)}/g, (substring, group) => {
    if (options.path?.hasOwnProperty(group)) {
      return encoder(String(options.path[group]));
    }
    return substring;
  });

  const url = `${path}`;
  if (options.query) {
    return `${url}${getQueryString(options.query)}`;
  }
  return url;
};

export const getFormData = (options) => {
  if (options.formData) {
    const formData = {};

    const process = (key, value) => {
      if (isString(value) || isBlob(value)) {
        formData[key] = value;
      } else {
        formData[key] = JSON.stringify(value);
      }
    };

    Object.entries(options.formData)
      .filter(([_, value]) => value !== undefined && value !== null)
      .forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach((v) => process(key, v));
        } else {
          process(key, value);
        }
      });

    return formData;
  }
  return undefined;
};

export const getRequestBody = (options) => {
  if (options.data) {
    return options.data;
  }
  return undefined;
};

export const getHeaders = async (config) => {
  const headers = {
    'content-type': 'application/json;charset=UTF-8',
    minitProgramToken: uni.getStorageSync('token') || '',
    // ...config?.header,
    ...config,
  };

  return headers;
};

export const sendRequest = async (config, options, url, body, formData, headers, axiosClient) => {
  const requestConfig = {
    url,
    headers,
    data: body ?? formData,
    method: options.method,
  };
  try {
    return await axiosClient.request(requestConfig);
  } catch (error) {
    const axiosError = error;
    if (axiosError.response) {
      return axiosError.response;
    }
    throw error;
  }
};

export const request = async (config, options, axiosClient = instance) => {
  const url = getUrl(config, options);
  const formData = getFormData(options);
  const body = getRequestBody(options);
  const headers = await getHeaders(config);
  return await sendRequest(config, options, url, body, formData, headers, axiosClient);
};
