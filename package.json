{"name": "ndcharge", "version": "1.1.1", "private": true, "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:h5:dev": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build --mode development", "build:h5:prod": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build --mode production", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-alipay:prod": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build --mode production", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build:mp-weixin:prod": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build --mode production", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "mock": "node net.js http://**************:12000/mock/5a02d0f450d8b02668bcc454", "fix-memory-limit": "cross-env LIMIT=40960 increase-memory-limit", "prettier": "prettier --config --write ./**.{vue,js}"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@dcloudio/uni-app": "^2.0.2-4020420240722001", "@dcloudio/uni-app-plus": "*", "@dcloudio/uni-h5": "^2.0.2-4020420240722001", "@dcloudio/uni-i18n": "^2.0.2-4020420240722001", "@dcloudio/uni-mp-alipay": "*", "@dcloudio/uni-mp-baidu": "*", "@dcloudio/uni-mp-qq": "*", "@dcloudio/uni-mp-toutiao": "*", "@dcloudio/uni-mp-weixin": "*", "@dcloudio/uni-ui": "^1.5.7", "@qiun/ucharts": "^2.5.0-20230101", "@vue/composition-api": "^1.7.2", "axios": "^1.7.4", "base-64": "^0.1.0", "core-js": "^2.6.12", "crypto-js": "^3.3.0", "dsbridge": "^3.1.4", "flyio": "^0.6.2", "gcoord": "^0.3.1", "html5-qrcode": "^2.3.8", "js-md5": "^0.8.3", "moment": "^2.30.1", "qrcode": "^1.4.4", "regenerator-runtime": "^0.12.1", "sass": "1.32.8", "sass-loader": "7.3.1", "vconsole": "^3.4.0", "vue": "2.6.11", "vuex": "^3.1.2", "vuex-persist": "^2.2.0", "vuex-persistedstate": "^4.1.0", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@dcloudio/uni-cli-shared": "0.2.994", "@dcloudio/uni-template-compiler": "0.9.183", "@dcloudio/vue-cli-plugin-hbuilderx": "1.0.137", "@dcloudio/vue-cli-plugin-uni": "0.9.538", "@dcloudio/webpack-uni-mp-loader": "0.3.644", "@dcloudio/webpack-uni-pages-loader": "0.2.875", "@types/html5plus": "*", "@types/uni-app": "^1.4.1", "@vue/cli-plugin-babel": "3.5.1", "@vue/cli-service": "^3.12.1", "babel-plugin-import": "^1.13.0", "css-loader": "^2.1.1", "postcss-comment": "^2.0.0", "uglifyjs-webpack-plugin": "^2.2.0", "vue-loader": "^15.7.0", "vue-template-compiler": "2.6.11"}, "resolutions": {"vue-template-compiler": "2.6.11"}, "browserslist": ["last 3 versions", "Android >= 4.4", "ios >= 8"]}