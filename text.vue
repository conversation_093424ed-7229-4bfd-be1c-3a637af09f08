<template>
  <div class="text-component">
    <view class="myorder-search-box">
      <text class="myorder-search-text">卡号:xxxxx</text>
      <view class="myorder-search-input">
        <view class="form-item align-center">
          <view class="label">
            <text>支付方式</text>
          </view>
          <view class="value">
            <picker :range="payOptionsList" range-key="typeName" @change="pickerChange">
              <view class="align-center justify-end">
                <text>{{ payType || '请选择支付方式' }}</text>
                <image :src="`${IMG_PATH}right_arrow.png`" class="right_arrow" mode="aspectFill" />
              </view>
            </picker>
          </view>
        </view>
      </view>
    </view>
  </div>
</template>

<script>
export default {
  name: 'TextComponent',
  data() {
    return {
      // 添加你的数据属性
    };
  },
  methods: {
    // 添加你的方法
  },
};
</script>

<style scoped>
.text-component {
  /* 添加你的样式 */
  font-size: 16px;
  color: #333;
}
</style>
