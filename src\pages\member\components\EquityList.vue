<!--
@name: 权益列表
@description: 权益列表
@time: 2024/8/9
-->
<template>
  <view class="equity-list">
    <view class="title" v-if="showTitle">
      <text class="title-name">专属权益</text>
      <text class="title-detail" @click="openRule">会员权益规则</text>
    </view>
    <!--    权益列表-->
    <view class="list" v-if="list.length > 0">
      <view class="list-item" v-for="item in list" :key="item.benefitCode" @click="jumpToCoupon(item)">
        <img class="equity-icon" :src="item.url" />
        <view class="equity-name">
          <text>{{ item.benefitName }}</text>
          <img
            v-if="couponArr.includes(item.benefitCode) && showInfo"
            class="equity-info"
            :src="`${IMG_PATH}member/equity-info.png`"
          />
        </view>
        <text class="description" v-if="item.multiple && description">{{ item.multiple }}倍积分</text>
        <text class="description" v-if="item.upgradePackage && description">{{ item.upgradePackage }}积分</text>
        <text class="description" v-if="item.memberDayAdditionalDiscount && description">
          {{ item.memberDayAdditionalDiscount }}折上折
        </text>
      </view>
    </view>
    <view v-else class="empty">
      <AEmpty :noneText="'暂无权益'" />
    </view>
    <!--    会员权益规则-->
    <uni-popup class="popup-content" ref="equityPopup" background-color="#F6F7F9">
      <view class="title">
        <text class="title-text">会员权益规则</text>
        <img class="close" :src="`${IMG_PATH}close.png`" @click="closeRule" />
      </view>
      <view class="content">
        <rich-text :nodes="ruleDescription"></rich-text>
      </view>
      <view class="line"></view>
      <view class="footer-button">
        <AppButton type="primary" @click="closeRule"> 我知道了 </AppButton>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import AEmpty from '@/components/AEmpty/index';
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup';
import AppButton from '@/components/AppButton/index';
import { openInfo } from '@/services/index.js';

export default {
  name: 'EquityList',
  components: {
    AEmpty,
    uniPopup,
    AppButton,
  },
  props: {
    showTitle: {
      type: Boolean,
      default: true,
    },
    memberInfo: {
      type: Object,
      default: () => {},
    },
    showInfo: {
      type: Boolean,
      default: true,
    },
    description: {
      type: Boolean,
      default: true,
    },
  },
  watch: {
    memberInfo: {
      handler() {
        this.$nextTick(() => {
          this.setUrl();
        });
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      list: [],
      ruleDescription: '',
      couponArr: ['isWelcomeGift', 'isExclusiveOffer'],
      map: {
        isExchange: 'jfdh',
        isDouble: 'jbjf',
        isExclusiveOffer: 'zxyh',
        isExclusiveCustomer: 'zskf',
        isUpgradePackage: 'sjlb',
        isMemberStation: 'hycz',
        isWelcomeGift: 'kkl',
        isMemberDayAdditionalDiscount: 'hyrzsz',
      },
    };
  },
  mounted() {},
  methods: {
    // 设置权益图标
    setUrl() {
      const list = this.memberInfo.benefitList
        .filter((item) => item.benefitFlag === '1')
        .map((q) => {
          const icon = this.map[q.benefitCode];
          const str = `${this.IMG_PATH}member/${icon}.png`;
          return {
            ...q,
            url: str,
          };
        });
      this.list = [...list];
    },
    // 权益跳转优惠券
    jumpToCoupon(item) {
      if (!this.couponArr.includes(item.benefitCode)) {
        return;
      }
      uni.navigateTo({
        url: `/pages/member/coupon/index?pageName=${item.benefitCode}`,
      });
    },
    // 打开权益规则弹框
    openRule() {
      this.openInfo();
      this.$refs.equityPopup.open('bottom');
    },
    // 关闭权益规则弹框
    closeRule() {
      this.$refs.equityPopup.close();
    },
    // 查询会员等级说明
    async openInfo() {
      // 0412 会员协议  0205 会员说明 0210 会员等级说明  0209 积分规则说明
      let arr = ['0209'];
      const params = {
        infoTypeList: arr,
      };
      const [err, res] = await openInfo(params);
      if (res) {
        let list = res.infoList;
        let obj = list.find((item) => item.infoType === '0209');
        this.ruleDescription = obj?.content;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.equity-list {
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #333333;
    &-name {
      font-size: 32rpx;
      font-weight: bold;
      line-height: 38rpx;
    }
    &-detail {
      font-size: 26rpx;
    }
  }
  .empty {
    margin-bottom: 36rpx;
  }
  .list {
    display: flex;
    flex-wrap: wrap;
    padding: 36rpx 40rpx;
    &-item {
      width: 25%;
      margin-bottom: 36rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      .equity-icon {
        width: 100rpx;
        height: 100rpx;
      }
      .equity-name {
        margin-top: 10rpx;
        font-size: 26rpx;
        line-height: 40rpx;
        color: #333333;
        white-space: nowrap;
        display: flex;
        align-items: center;
        .equity-info {
          width: 26rpx;
          height: 26rpx;
          margin-left: 8rpx;
        }
      }
      .description {
        margin-top: 10rpx;
        font-size: 24rpx;
        color: #999999;
      }
    }
  }
  .popup-content {
    .title {
      display: flex;
      justify-content: center;
      margin: 16rpx 0 24rpx 0;
      align-items: center;
      padding: 24rpx;
      position: relative;
      .title-text {
        font-size: 32rpx;
        font-weight: bold;
        line-height: 38rpx;
        color: #333333;
      }
      .close {
        position: absolute;
        top: 24rpx;
        right: 24rpx;
        height: 40rpx;
        width: 40rpx;
      }
    }
    .content {
      padding: 35rpx 24rpx 42rpx 24rpx;
      font-size: 26rpx;
      line-height: 40rpx;
      color: #3d3d3d;
      overflow: scroll;
      max-height: 700rpx;
      overflow-y: auto;
    }
    .line {
      background: #eeeeee;
      width: 100%;
      margin-bottom: 23rpx;
      height: 1rpx;
    }
    .footer-button {
      padding: 0 24rpx 92rpx 24rpx;
    }
  }
}
</style>
