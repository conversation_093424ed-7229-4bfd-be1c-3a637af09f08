.billingpersonal-lists {
  background-color: #fff;
  margin-top: 12rpx;
}
.list-main {
  font-size: 28rpx;
  color: #333;
  display: flex;
  height: 96rpx;
  padding: 0px 30rpx;
  align-items: center;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
}

.list-main .list-main-hd {
  width: 210rpx;
}
.list-main .list-main-bd {
}
.list-main-hd text {
  color: #f33;
  font-family: 'PingFang SC';
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
radio {
  transform: scale(0.7);
}

.billingpersonal-lists .list-main .list-main-bd .radio {
  float: left;
  color: #666;
  margin-right: 60rpx;
}
.billingpersonal-lists .list-main .list-main-bd input {
  display: block;
  width: 100%;
  height: 88rpx;
}
.color-tj {
  opacity: 0.5;
}

radio .wx-radio-input {
  border-color: #87858a;
}
radio .wx-radio-input.wx-radio-input-checked {
  background: #8bb70c !important;
  border: 2rpx solid #8bb70c !important;
}
.main-switch {
  justify-content: space-between;
}
.bottom-bg {
  background: #fff;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  padding: 20rpx;
  box-sizing: border-box;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}
.blue-bg {
  color: #fff;
  font-family: 'PingFang SC';
  font-size: 32rpx;
  font-weight: 400;
}
