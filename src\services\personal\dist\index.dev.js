"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.myRechargeCards = exports.doCharge = exports.getVehicleInfo = exports.getCommonProblemList = exports.getFaqList = exports.offFaqReply = exports.getFaqReply = exports.readMsg = exports.delCollectStation = exports.collectStation = exports.recentChargingInfo = exports.replyPubFaq = exports.submitPubFaq = exports.getFaqType = exports.getEvaDtl = exports.orderCount = exports.wxMiniOrder = exports.getChargeOrderDtl = exports.submitOrder = exports.submitRefund = exports.getRefundRecordDetail = exports.getOpenChargingInfo = exports.getAccountsCoupons = exports.getOrderList = exports.deletePref = exports.updateDefaultCar = exports.getCouponReceiveAll = exports.getCouponReceive = exports.getCoupon = exports.setPref = exports.getPref = exports.refreshTaskStatus = exports.queryTotalPoints = exports.signAndGetMonthSignData = exports.signIntegeral = void 0;

var _request = require("../request/request");

var _global = require("../../config/global");

var _dict = require("core-js/core/dict");

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

// 查询签到能获取的积分
var signIntegeral = function signIntegeral(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'POST',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.bilBaseurl, "/v0.1/signIntegeral"),
    data: params
  });
}; // 签到并获取当月签到


exports.signIntegeral = signIntegeral;

var signAndGetMonthSignData = function signAndGetMonthSignData(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: "POST",
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.defBaseurl, "/v0.1/signAndQuerySignInfo"),
    data: params
  });
}; // 查询总积分


exports.signAndGetMonthSignData = signAndGetMonthSignData;

var queryTotalPoints = function queryTotalPoints(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: "".concat(_global.ENVR === 'wx' ? 'POST' : 'GET'),
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.defBaseurl, "/v0.1/vip/record"),
    data: params
  });
}; // 刷新任务状态


exports.queryTotalPoints = queryTotalPoints;

var refreshTaskStatus = function refreshTaskStatus(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'POST',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.defBaseurl, "/v0.1/getIntegralTask"),
    data: params
  });
}; // 获取用户配置信息


exports.refreshTaskStatus = refreshTaskStatus;

var getPref = function getPref(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'GET',
    url: "".concat(_global.ENVR == 'wx' ? _global.scanBaseurl : _global.cstBaseurl, "/v0.2/pref"),
    data: params
  });
}; // 获取用户配置信息


exports.getPref = getPref;

var setPref = function setPref(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'POST',
    url: "".concat(_global.ENVR == 'wx' ? _global.scanBaseurl : _global.cstBaseurl, "/v0.2/pref"),
    data: params
  });
}; // 查询优惠券


exports.setPref = setPref;

var getCoupon = function getCoupon(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: _global.ENVR === 'wx' ? 'POST' : 'GET',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl + '/v0.1/act/actCoupon' : _global.bilBaseurl + '/v0.1/coupons'),
    data: params
  });
}; // 领取优惠券


exports.getCoupon = getCoupon;

var getCouponReceive = function getCouponReceive(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: _global.ENVR === 'wx' ? 'GET' : 'POST',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl + '/v0.1/coupons/post' : _global.bilBaseurl + '/v0.1/coupons'),
    data: params
  });
}; // 一键领取


exports.getCouponReceive = getCouponReceive;

var getCouponReceiveAll = function getCouponReceiveAll(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'POST',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.busiBaseurl, "/v0.1/coupons/allPick"),
    data: params
  });
}; // 更新默认车辆


exports.getCouponReceiveAll = getCouponReceiveAll;

var updateDefaultCar = function updateDefaultCar(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'POST',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.cstBaseurl, "/v0.2/upPrefDefault"),
    data: params
  });
}; // 删除绑定车辆


exports.updateDefaultCar = updateDefaultCar;

var deletePref = function deletePref(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'POST',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl + '/v0.2/deletePref' : _global.cstBaseurl + '/v0.2/delpref'),
    data: params
  });
}; // 订单列表


exports.deletePref = deletePref;

var getOrderList = function getOrderList(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: "".concat(_global.ENVR === 'wx' ? 'POST' : 'GET'),
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.ordBaseurl, "/v0.1/order-charge-list"),
    data: params
  });
}; // 获取优惠券列表


exports.getOrderList = getOrderList;

var getAccountsCoupons = function getAccountsCoupons(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'GET',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.defBaseurl, "/v0.1/accounts/coupons"),
    data: params
  });
}; // 查询7天内充电记录


exports.getAccountsCoupons = getAccountsCoupons;

var getOpenChargingInfo = function getOpenChargingInfo(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'GET',
    url: "".concat(_global.busiBaseurl, "/open/v0.1/chargingInfo"),
    data: params
  });
}; // 查看退款记录列表


exports.getOpenChargingInfo = getOpenChargingInfo;

var getRefundRecordDetail = function getRefundRecordDetail(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'GET',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.defBaseurl, "/v0.1/online-refund-log"),
    data: params
  });
}; // 提交退款


exports.getRefundRecordDetail = getRefundRecordDetail;

var submitRefund = function submitRefund(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'POST',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl + '/v0.1/online-refund' : _global.defBaseurl + '/v0.2/online-refund'),
    data: params
  });
}; // 订单提交


exports.submitRefund = submitRefund;

var submitOrder = function submitOrder(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'POST',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl + '/v0.3/order' : _global.ordBaseurl + '/v0.3/order'),
    data: params
  });
}; // 获取订单详情


exports.submitOrder = submitOrder;

var getChargeOrderDtl = function getChargeOrderDtl(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: _global.ENVR === 'wx' ? 'POST' : 'GET',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.busiBaseurl, "/v0.1/charge_order_dtl"),
    data: params
  });
}; // 微信缴费


exports.getChargeOrderDtl = getChargeOrderDtl;

var wxMiniOrder = function wxMiniOrder(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'POST',
    url: "".concat(_global.scanBaseurl, "/v0.1/wx-mini-order"),
    data: params
  });
}; // 订单统计


exports.wxMiniOrder = wxMiniOrder;

var orderCount = function orderCount(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'GET',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.ordBaseurl, "/v0.1/charge_order_statistics"),
    data: params
  });
}; // 评价信息查询


exports.orderCount = orderCount;

var getEvaDtl = function getEvaDtl(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: _global.ENVR === 'wx' ? 'POST' : 'GET',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.ordBaseurl, "/v0.1/eva-dtl"),
    data: params
  });
}; // 反馈问题分类列表


exports.getEvaDtl = getEvaDtl;

var getFaqType = function getFaqType(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'GET',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.cstBaseurl, "/v0.1/faq-type"),
    data: params
  });
}; // 提交反馈


exports.getFaqType = getFaqType;

var submitPubFaq = function submitPubFaq(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'POST',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.cstBaseurl, "/v0.1/pub-faq"),
    data: params
  });
}; // 追加反馈


exports.submitPubFaq = submitPubFaq;

var replyPubFaq = function replyPubFaq(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'POST',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.cstBaseurl, "/v0.1/pub-faq-reply"),
    data: params
  });
}; // 获取7天内充电次数及时间


exports.replyPubFaq = replyPubFaq;

var recentChargingInfo = function recentChargingInfo(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'GET',
    url: "".concat(_global.ordBaseurl, "/open/v0.1/recentChargingInfo"),
    data: params
  });
}; // 收藏站点


exports.recentChargingInfo = recentChargingInfo;

var collectStation = function collectStation(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config, {
    // #ifdef H5
    'content-type': 'application/x-www-form-urlencoded' // #endif

  }), {
    method: 'POST',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.baseBaseurl, "/v0.1/pref"),
    data: params
  });
}; // 取消收藏站点


exports.collectStation = collectStation;

var delCollectStation = function delCollectStation(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: _global.ENVR === 'wx' ? 'POST' : 'GET',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl + '/v0.1/pref-del' : _global.cstBaseurl + '/v0.1/delpref'),
    data: params
  });
}; // 消息设置为已读


exports.delCollectStation = delCollectStation;

var readMsg = function readMsg(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'POST',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.pubBaseurl, "/v0.1/msg-read"),
    data: params
  });
}; // 获取反馈信息


exports.readMsg = readMsg;

var getFaqReply = function getFaqReply(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'GET',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.cstBaseurl, "/v0.1/faq-reply"),
    data: params
  });
}; // 关闭反馈


exports.getFaqReply = getFaqReply;

var offFaqReply = function offFaqReply(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'POST',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.cstBaseurl, "/v0.1/faq-reply-off"),
    data: params
  });
}; // 获取反馈列表


exports.offFaqReply = offFaqReply;

var getFaqList = function getFaqList(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'GET',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl : _global.baseBaseurl, "/v0.1/faq-list"),
    data: params
  });
}; // 获取常见问题列表


exports.getFaqList = getFaqList;

var getCommonProblemList = function getCommonProblemList(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'GET',
    url: "".concat(_global.astBaseurl, "/v0.1/faq"),
    data: params
  });
}; // 获取车型列表


exports.getCommonProblemList = getCommonProblemList;

var getVehicleInfo = function getVehicleInfo(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'GET',
    url: "".concat(_global.astBaseurl, "/v0.1/vehicle_info"),
    data: params
  });
}; // 卡密兑换


exports.getVehicleInfo = getVehicleInfo;

var doCharge = function doCharge(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'POST',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl + '/card' : _global.busiBaseurl + '/rechargeCard', "/v0.1/doCharge"),
    data: params
  });
}; // 卡密兑换


exports.doCharge = doCharge;

var myRechargeCards = function myRechargeCards(params) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  return (0, _request.request)(_objectSpread({}, config), {
    method: 'GET',
    url: "".concat(_global.ENVR === 'wx' ? _global.scanBaseurl + '/card' : _global.busiBaseurl + '/rechargeCard', "/v0.1/myRechargeCards"),
    data: params
  });
};

exports.myRechargeCards = myRechargeCards;