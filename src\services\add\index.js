import { request as __request } from '../request/request';
import {
  ENVR,
  scanBaseurl,
  defBaseurl,
  baseBaseurl,
  cstBaseurl,
  astBaseurl,
  baseOpenBaseurl,
  basApiOpenBaseurl,
} from '../../config/global';
const baseurl = ENVR === 'wx' ? scanBaseurl : cstBaseurl;
// 选择
export const upPrefDefault = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      url: `${baseurl}/v0.2/upPrefDefault`,
      method: 'POST',
      data: params,
    }
  );
};
// 获取车型列表
export const getMyCarUsetype = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${basApiOpenBaseurl}/v0.1/codes/myCarUsetype`,
      data: params,
    }
  );
};
