<template>
  <view class="billingpersonal">
    <view class="billingpersonal-lists">
      <view class="list-main">
        <view class="list-main-hd">
          <text>*</text>
          品牌
        </view>
        <label class="list-main-bd">
          <input
            v-model="pingpai"
            type="text"
            @input="change"
            placeholder="请输入品牌"
            placeholder-style="color:#999999"
          />
        </label>
      </view>
      <view class="list-main">
        <view class="list-main-hd">
          <text>*</text>
          型号
        </view>
        <label class="list-main-bd">
          <input
            v-model="xinghao"
            type="text"
            @input="change"
            placeholder="请输入型号"
            placeholder-style="color:#999999"
          />
        </label>
      </view>
    </view>
    <view class="bottom-bg">
      <button v-if="isValid" @tap="open" class="blue-bg" type="">确认</button>
      <button v-else class="blue-bg color-tj" type="">确认</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isValid: false,
      pingpai: '',
      xinghao: '',
    };
  },
  methods: {
    panDuan: function () {
      const { pingpai, xinghao } = this;
      let buttonFlag = false;
      if (pingpai && xinghao) {
        buttonFlag = true;
      }
      this.setData({
        isValid: buttonFlag,
      });
    },

    change: function (value) {
      console.log(value);
      this.panDuan();
    },

    bankNameInp: function (event) {
      this.setData({
        bankName: event.detail.value,
      });
    },

    open: function () {
      const { pingpai, xinghao } = this;
      // 设置数据保留标记，表示要保留当前填写的数据
      const selectedCar = {
        carName: pingpai + ' ' + xinghao,
        modelId: '',
        img: '',
        brandId: '',
      };

      // 将信息存储到本地
      uni.setStorageSync('selectedCar', selectedCar);
      uni.navigateBack({
        delta: 1,
      });
      // uni.redirectTo({
      //   url: '/subPackages/add/add?carName=' + pingpai + '-' + xinghao,
      // });
    },
  },
};
</script>
<style>
@import './index.css';
</style>
