<template>
  <view>
    <view>
      <view class="flex-bow">
        <view class="search-head">
          <input @input="found" placeholder="查找充电站" />
          <text class="icon iconfont icon-search" size="30"></text>
        </view>
      </view>
      <scroll-view scroll-y class="list-found" @scrolltolower="onScrollToLower" lower-threshold="100">
        <view class="station-ul">
          <station-li :currentItem="item" v-for="(item, index) in distanceCup" :key="index"></station-li>
        </view>
        <!-- 加载更多提示 -->
        <view v-if="loading" class="loading-more">
          <text>加载中...</text>
        </view>
        <view v-else-if="!haseMore && distanceCup.length > 0" class="no-more">
          <text>没有更多数据了</text>
        </view>
      </scroll-view>
    </view>
    <loading v-if="!hiddenLoading">正在加载</loading>
  </view>
</template>
<script>
import stationLi from '@/components/station-li/index';
import { getV06ChargingStation } from '@/services/index.js';
import { getLocation } from '@/utils/index.js';
export default {
  components: {
    stationLi,
  },
  data() {
    return {
      hiddenLoading: false,
      distanceCup: [],
      distanceCups: [],
      // 分页参数
      pageNum: 1,
      pageSize: 20,
      haseMore: true,
      loading: false, // 加载状态
      // 位置信息
      longitude: '',
      latitude: '',
      // 搜索参数
      stationName: '',
      searchTimer: null, // 防抖定时器
    };
  },
  async onShow() {
    const that = this;
    const res = await getLocation();
    if (res) {
      that.setData({
        longitude: res.longitude,
        latitude: res.latitude,
      });
      that.loadData();
    }
  },
  onReachBottom() {
    const { haseMore, pageNum } = this;
    console.log(haseMore, 'haseMore');
    if (haseMore) {
      this.setData(
        {
          pageNum: pageNum + 1,
        },
        () => {
          this.loadData();
        }
      );
    }
  },
  methods: {
    async loadData(isRefresh = false) {
      // 防止重复请求
      if (this.loading) return;

      this.loading = true;

      try {
        // 01按照评分排序  02按照距离排序  03 按金额排序
        const { pageNum, pageSize, longitude, latitude, stationName } = this;

        const params = {
          positionLon: longitude,
          positionLat: latitude,
          orderType: '02',
          pageNum,
          totalNum: pageSize,
          stationName,
        };

        const [, res] = await getV06ChargingStation(params);

        if (res) {
          const newList = res.chcList || [];

          if (isRefresh || pageNum === 1) {
            // 刷新或首次加载，直接替换数据
            this.distanceCup = newList;
          } else {
            // 加载更多，追加数据
            this.distanceCup = [...this.distanceCup, ...newList];
          }

          // 判断是否还有更多数据
          this.haseMore = newList.length >= pageSize;
        } else {
          this.haseMore = false;
        }
      } catch (error) {
        console.error('加载数据失败:', error);
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none',
        });
      } finally {
        this.loading = false;
        this.hiddenLoading = true;
      }
    },

    jump(e) {
      console.log(e);
      uni.redirectTo({
        url:
          '/pages/basic/station/station?stationId=' +
          e.currentTarget.dataset.ade +
          '&distance=' +
          e.currentTarget.dataset.km,
      });
    },

    // scroll-view滚动到底部处理
    onScrollToLower() {
      if (this.haseMore && !this.loading) {
        this.pageNum += 1;
        this.loadData();
      }
    },

    back() {
      uni.navigateBack();
    },

    // 搜索输入处理（带防抖）
    found(value) {
      const searchValue = value.detail.value;
      this.stationName = searchValue;

      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 设置防抖定时器
      this.searchTimer = setTimeout(() => {
        // 重置分页参数
        this.pageNum = 1;
        this.haseMore = true;
        // 执行搜索
        this.loadData(true);
      }, 500); // 500ms防抖延迟
    },

    // 重置搜索
    resetSearch() {
      this.stationName = '';
      this.pageNum = 1;
      this.haseMore = true;
      this.loadData(true);
    },

    pageDanceInfo() {},
  },

  // 组件销毁时清理定时器
  beforeDestroy() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
      this.searchTimer = null;
    }
  },
};
</script>
<style>
@import './stationcup.css';
</style>
<style scoped lang="scss">
/* 加载更多和无更多数据样式 */
.loading-more,
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 28rpx;
}

.loading-more text {
  color: #1e9cff;
}

.no-more text {
  color: #ccc;
}

/* #ifdef MP-WEIXIN */
.flex-bow {
  background: #ffffff;
  // .search-head input {
  //   color: #000000;
  // }
  // .input-placeholder {
  //   color: #fff;
  // }
  // ::v-deep .uni-input-input {
  //   color: #fff;
  // }
}
/* #endif */
/* #ifdef H5 */
.flex-bow {
  background: #1e9cff;
  .search-head input {
    color: #fff;
  }
  .input-placeholder {
    color: #fff;
  }
  ::v-deep .uni-input-input {
    color: #fff;
  }
}
/* #endif */
</style>
