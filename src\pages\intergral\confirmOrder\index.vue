<!--
@name: 确认订单
@description: 确认订单页面
@time: 2024/7/25
-->
<template>
  <view class="confirm-order">
    <view class="confirm-order-content">
      <!--        收货地址-->
      <Address
        class="address"
        :address-detail="addressDetail"
        v-if="detail.goodsType === '02' || detail.goodsType === '03'"
      />
      <!--        虚拟商品提示-->
      <view class="fictitious" v-if="detail.goodsType === '01'">
        <img class="fictitious-icon" :src="`${IMG_PATH}propmt.png`" />
        <text class="fictitious-text">虚拟商品无需填写收货地址</text>
      </view>
      <!--        商品卡片-->
      <view>
        <GoodsCard :card-item="detail" :exchange-number="exchangeNumber" />
      </view>
      <!--        我的积分-->
      <view class="my-points">
        <text class="title">我的积分</text>
        <view class="content">
          <text class="num">{{ userInfo.custIntegralNumber || 0 }}</text>
          <text class="unit">积分</text>
          <text class="short" v-if="userInfo.custIntegralNumber < detail.goodsIntegral * exchangeNumber">
            积分不足
          </text>
        </view>
      </view>
    </view>
    <!--        底部按钮-->
    <view class="confirm-order-footer">
      <BottomPannel>
        <view class="footer-button">
          <AppButton class="button-item" type="primary" @click="redeemGoods" :disabled="isDisabled">
            确认兑换
          </AppButton>
        </view>
      </BottomPannel>
    </view>
    <!-- <modal
      v-if="hidden"
      :title="detail.goodsAmt ? '是否确定支付兑换' : '是否确认兑换'"
      confirm-color="blue"
      @cancel="modelCancel"
      @confirm="modelConfirm"
      :no-cancel="nocancel"
      style="border-radius: 24px"
    >
      <view v-if="detail.goodsAmt" style="letter-spacing: 2rpx; line-height: 1.5"
        >当前账户余额：<span style="color: red">{{ pay.toFixed(2) }}</span
        >元</view
      >
      <view v-if="detail.goodsAmt" style="letter-spacing: 2rpx; line-height: 1.5"
        >兑换后账户余额：<span style="color: red">{{ (pay - detail.goodsAmt * exchangeNumber).toFixed(2) }}</span
        >元</view
      >
      <view v-if="detail.goodsAmt" style="letter-spacing: 2rpx; line-height: 1.5"
        >当前积分：<span style="color: red">{{ Number(userInfo.custIntegralNumber).toFixed(2) }}</span
        >积分</view
      >
      <view v-if="detail.goodsAmt" style="letter-spacing: 2rpx; line-height: 1.5"
        >兑换后剩余积分：<span style="color: red">{{
          (userInfo.custIntegralNumber - detail.goodsIntegral * exchangeNumber).toFixed(2)
        }}</span
        >积分</view
      >
      <view v-if="detail.goodsAmt" style="letter-spacing: 2rpx; line-height: 1.5"
        >确认兑换：确认后将从您的账户扣除积分和金额。</view
      >
      <view v-if="detail.goodsAmt" style="letter-spacing: 2rpx; line-height: 1.5"
        >金额&积分不可退：一旦扣款完成，金额和积分无法退还，请谨慎操作。</view
      >
    </modal> -->
    <!-- <modal
      v-if="showRecharge"
      title="温馨提示"
      confirm-text="去充值"
      cancel-text="取消"
      @cancel="rechargeCancel"
      @confirm="rechargeConfirm"
      :no-cancel="nocancel"
      style="border-radius: 24px"
    >
      <view style="text-align: center; letter-spacing: 2rpx; line-height: 1.5">当前账户余额不足！</view>
      <view style="text-align: center; letter-spacing: 2rpx; line-height: 1.5">请先充值后购买</view>
    </modal> -->
    <uni-popup ref="confirmModal">
      <view class="popup-box">
        <view v-if="detail.goodsAmt" class="popup-title">是否确定支付兑换</view>
        <view v-else class="popup-title">是否确认兑换</view>
        <view v-if="detail.goodsAmt" style="letter-spacing: 2rpx; line-height: 1.5; color: gray; padding: 0 24rpx"
          >当前账户余额：<span style="color: red">{{ pay.toFixed(2) }}</span
          >元</view
        >
        <view v-if="detail.goodsAmt" style="letter-spacing: 2rpx; line-height: 1.5; color: gray; padding: 0 24rpx"
          >兑换后账户余额：<span style="color: red">{{ (pay - detail.goodsAmt * exchangeNumber).toFixed(2) }}</span
          >元</view
        >
        <view v-if="detail.goodsAmt" style="letter-spacing: 2rpx; line-height: 1.5; color: gray; padding: 0 24rpx"
          >当前积分：<span style="color: red">{{ Number(userInfo.custIntegralNumber).toFixed(2) }}</span
          >积分</view
        >
        <view v-if="detail.goodsAmt" style="letter-spacing: 2rpx; line-height: 1.5; color: gray; padding: 0 24rpx"
          >兑换后剩余积分：<span style="color: red">{{
            (userInfo.custIntegralNumber - detail.goodsIntegral * exchangeNumber).toFixed(2)
          }}</span
          >积分</view
        >
        <view v-if="detail.goodsAmt" style="letter-spacing: 2rpx; line-height: 1.5; color: gray; padding: 0 24rpx"
          >确认兑换：确认后将从您的账户扣除积分和金额。</view
        >
        <view v-if="detail.goodsAmt" style="letter-spacing: 2rpx; line-height: 1.5; color: gray; padding: 0 24rpx"
          >金额&积分不可退：一旦扣款完成，金额和积分无法退还，请谨慎操作。</view
        >
        <view class="btns">
          <view @click="modelCancel" class="cancel-btn">取消</view>
          <view @click="modelConfirm" class="confirm-btn">确定</view>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="rechargeModal">
      <view class="popup-box">
        <view class="popup-title">温馨提示</view>
        <view style="text-align: center; letter-spacing: 2rpx; line-height: 1.5; color: gray">当前账户余额不足！</view>
        <view style="text-align: center; letter-spacing: 2rpx; line-height: 1.5; color: gray">请先充值后购买</view>
        <view class="btns">
          <view @click="rechargeCancel" class="cancel-btn">取消</view>
          <view @click="rechargeConfirm" class="confirm-btn">去充值</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import GoodsCard from '../components/GoodsCard';
import Address from '@/components/Address';
import BottomPannel from '@/components/BottomPannel/index';
import AppButton from '@/components/AppButton/index';
import { getGoodsDetail, exchangeGoods, getAddressList } from '@/services/index.js';
import { mapState } from 'vuex';
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup';

export default {
  name: 'index',
  components: {
    GoodsCard,
    Address,
    BottomPannel,
    AppButton,
    uniPopup,
  },
  data() {
    return {
      goodsId: '',
      exchangeNumber: 0,
      detail: {},
      addressDetail: {},
      custAddrId: '',
      channel: 1,
      selectPrice: 0,
      hidden: false,
      pay: 0, // 账户金额
      showRecharge: false,
    };
  },
  computed: {
    // 底部按钮是否禁用
    isDisabled() {
      return (
        this.userInfo.custIntegralNumber < this.detail.goodsIntegral * this.exchangeNumber ||
        (!this.custAddrId && (this.detail.goodsType === '02' || this.detail.goodsType === '03'))
      );
    },
    ...mapState({
      userInfo: (state, getters) => getters['login/getUserInfo'],
    }),
  },
  async onLoad(options) {
    this.goodsId = options.goodsId;
    this.exchangeNumber = options.exchangeNumber;
    this.getGoodsDetail().then(() => {
      // 虚拟商品不查询地址
      if (this.detail.goodsType === '02' || this.detail.goodsType === '03') {
        this.getAddressList();
      }
    });
    this.$store.dispatch('login/getUserInfoCallback').then((res) => {
      console.log('store', res);
      this.pay = Number(res.acctotalAmt);
    });
  },
  async onShow() {
    await this.$store.dispatch('login/getUserInfoCallback');
  },
  methods: {
    // 查询商品详情
    async getGoodsDetail() {
      const params = {
        goodsId: this.goodsId,
      };
      const [err, res] = await getGoodsDetail(params);
      if (res) {
        this.detail = res.detail;
      }
    },

    // 兑换商品
    redeemGoods() {
      if (this.userInfo.custIntegralNumber < this.detail.goodsIntegral * this.exchangeNumber) {
        uni.showToast({
          title: '积分不足',
          icon: 'none',
        });
        return;
      } else if (!this.custAddrId && (this.detail.goodsType === '02' || this.detail.goodsType === '03')) {
        uni.showToast({
          title: '请填写收货地址',
          icon: 'none',
        });
        return;
      } else if (this.pay - this.detail.goodsAmt * this.exchangeNumber < 0) {
        // uni.showToast({
        //   title: '账户余额不足',
        //   icon: 'none',
        // });
        // this.showRecharge = true;
        this.$refs.rechargeModal.open();
        return;
      }
      // this.hidden = true;
      this.$refs.confirmModal.open();
    },
    // 弹窗取消
    modelCancel() {
      // this.hidden = false;
      this.$refs.confirmModal.close();
    },
    // 弹窗确定
    modelConfirm() {
      // this.hidden = false;
      console.log('弹窗确定');
      const onClick = this.debounce(() => {
        this.exchangeGoods();
        this.$refs.confirmModal.close();
      }, 500);
      onClick();
    },
    debounce(fn, time = 100) {
      let timer = null;
      return function (...args) {
        if (timer) clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(this, args);
        }, time);
      };
    },
    // 充值弹窗-取消
    rechargeCancel() {
      // this.showRecharge = false;
      this.$refs.rechargeModal.close();
    },
    // 充值弹窗-去充值
    rechargeConfirm() {
      // this.showRecharge = false;
      this.$refs.rechargeModal.close();
      uni.navigateTo({
        url: '/subPackages/recharge/recharge',
      });
    },
    //兑换商品
    async exchangeGoods() {
      const params = {
        goodsId: this.goodsId,
        exchangeNumber: Number(this.exchangeNumber),
        custAddrId: this.custAddrId,
      };
      const [err, res] = await exchangeGoods(params);
      if (res) {
        uni.showToast({
          title: '兑换成功',
          icon: 'success',
        });
        setTimeout(() => {
          uni.navigateTo({
            url: '/pages/intergral/redeemRecord/index',
          });
        });
      }
    },
    // 查询地址详情
    async getAddressList() {
      const params = {
        custAddrId: this.custAddrId,
      };
      const [err, res] = await getAddressList(params);
      if (res.data) {
        // 当无custAddrId时查询出所有地址并挑选默认地址显示，有custAddrId时显示该地址
        if (res.data.queryList.length === 1) {
          this.addressDetail = res.data.queryList[0];
        } else {
          this.addressDetail = res.data.queryList.find((item) => item.isDefault === '1');
        }
        this.custAddrId = this.addressDetail.custAddrId;
      }
    },
    // 获取地址信息
    getAddressInfo(data) {
      this.custAddrId = data.custAddrId;
      if (this.detail.goodsType === '02' || this.detail.goodsType === '03') {
        this.getAddressList();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.confirm-order {
  &-content {
    padding: 24rpx;
    .address {
      margin-bottom: 24rpx;
    }
    .fictitious {
      display: flex;
      margin-bottom: 24rpx;
      .fictitious-icon {
        width: 36rpx;
        height: 36rpx;
        margin-right: 8rpx;
      }
      .fictitious-text {
        font-size: 28rpx;
        line-height: 42rpx;
        color: #333333;
      }
    }
    .my-points {
      margin-top: 24rpx;
      border-radius: 16rpx;
      background: white;
      padding: 24rpx;
      display: flex;
      flex-direction: column;
      .title {
        font-size: 28rpx;
        font-weight: bold;
        line-height: 42rpx;
        color: #333333;
      }
      .content {
        margin-top: 24rpx;
        .num {
          font-size: 40rpx;
          font-weight: bold;
          color: #1e9cff;
          margin-right: 5rpx;
        }
        .unit {
          font-size: 24rpx;
          line-height: 29rx;
          color: #1e9cff;
        }
        .short {
          font-size: 24rpx;
          line-height: 32rpx;
          color: #ff4935;
          margin-left: 16rpx;
        }
      }
    }
  }
  &-footer {
    z-index: 1;
    .footer-button {
      display: flex;
      gap: 24rpx;
      grid-gap: 24rpx;
      .button-item {
        flex: 1;
      }
    }
  }
  .popup-content {
    .title {
      display: flex;
      justify-content: center;
      margin: 16rpx 0 24rpx 0;
      align-items: center;
      padding: 24rpx;
      position: relative;
      .title-text {
        font-size: 32rpx;
        font-weight: bold;
        line-height: 38rpx;
        color: #333333;
      }
      .close {
        position: absolute;
        top: 24rpx;
        right: 24rpx;
        height: 40rpx;
        width: 40rpx;
      }
    }
    .content {
      padding: 35rpx 24rpx 42rpx 24rpx;
      font-size: 26rpx;
      line-height: 40rpx;
      color: #3d3d3d;
      overflow: scroll;
    }
    .line {
      background: #eeeeee;
      width: 100%;
      margin-bottom: 23rpx;
      height: 1rpx;
    }
    .channel {
      display: flex;
      flex-direction: column;
      padding: 24rpx;
      .channel-list {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1rpx solid #eeeeee;
        padding: 24rpx 0;
        .channel-name {
          display: flex;
          align-items: center;
          .channel-text {
            font-size: 36rpx;
            color: #333333;
          }
          .channel-icon {
            width: 40rpx;
            height: 40rpx;
            margin-right: 16rpx;
          }
        }
        .check-icon {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }
  .footer-button {
    padding: 0 24rpx 92rpx 24rpx;
  }
}
.popup-box {
  background: #fff;
  border-radius: 24rpx;
  width: 80vw;
  .popup-title {
    font-size: 36rpx;
    width: 100%;
    text-align: center;
    margin: 24rpx 0;
    padding: 24rpx 0;
  }
  .btns {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 24rpx;
    border-top: 1rpx solid #cbcbcb;
    .cancel-btn {
      font-size: 32rpx;
      font-weight: bold;
      width: 49%;
      box-sizing: border-box;
      padding: 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-right: 1rpx solid #cbcbcb;
    }
    .confirm-btn {
      font-size: 32rpx;
      font-weight: bold;
      color: rgb(84, 179, 255);
      width: 49%;
      box-sizing: border-box;
      padding: 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-left: 1rpx solid #cbcbcb;
    }
  }
}
</style>
