.month {
  width: 100%;
  height: 80rpx;
  color: gray;
  font-size: 34rpx;
  display: flex;
  align-items: center;
  padding-left: 30rpx;
  box-sizing: border-box;
}
.msg_view {
  width: 100%;
  background: #fff;
}
.msg_view .msg {
  width: calc(100% - 30rpx);
  display: flex;
  margin-left: 30rpx;
  align-content: center;
  justify-content: space-around;
  height: 160rpx;
  border-bottom: 1rpx solid #eee;
  box-sizing: border-box;
}
.msg_view .msg .msg_img {
  width: 120rpx;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.msg_view .msg .msg_img image {
  width: 120rpx;
  height: 120rpx;
}
.msg_view .msg .msg_page {
  font-size: 30rpx;
  padding: 24rpx 0;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  width: 370rpx;
  align-content: space-around;
}
.msg_view .msg .msg_page view {
  width: 100%;
}
.msg_view .msg .msg_price {
  height: 100%;
  width: 150rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
}
.msg_view .msg .msg_price text {
  width: 100%;
  text-align: right;
  font-size: 32rpx;
}
