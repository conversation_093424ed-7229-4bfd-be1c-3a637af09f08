<template>
  <view class="map-wrap">
    <!-- pages/mapStation/map.wxml -->
    <div
      class="top-nav"
      :class="{ 'list-top': ple }"
      :style="ENVR == 'wx' ? 'padding-top:' + s_top + 'rpx;line-height:' + s_height + 'rpx;' + 'top:' + s_top : ''"
    >
      <text v-if="ple">电站</text>
      <div v-else class="back-text" @click="tabs()">
        <icon type="icon iconfont icon-arrow_left" size="18" />
        <div class="text">电站列表</div>
      </div>
    </div>
    <view class="head-maps" :style="'top:' + topFixed">
      <div class="search-wrap">
        <view @click="cityChange" class="cityTabs">
          <text class="addre_cty">{{ city || '-' }}</text>
          <view style="display: inline-block">
            <icon type="icon iconfont icon-arrow_down" size="12" />
          </view>
        </view>
        <view @click="mocks" class="head-maps-found">
          <icon type="icon iconfont icon-search" style="color: #eee" size="20" />
          <input :disabled="true" placeholder="查找充电站" />
        </view>
        <view @tap="tabs">
          <!-- <view bindtap='ttd'> -->
          <icon type="icon iconfont icon-list" style="color: #1e9cff" size="24" />
          <!-- <icon v-else type="icon iconfont icon-near" style="color: #1e9cff" size="24" /> -->
        </view>
      </div>
    </view>
    <view v-if="pages" class="page-hed" :style="'top:' + topFixed">
      <view class="page-hed-ul">
        <view @tap="jl" :class="'page-hed-li ' + (start === 2 ? 'hed-li-checked' : '')">距离最近</view>
        <view @tap="jg" :class="'page-hed-li ' + (start === 1 ? 'hed-li-checked' : '')">价格最低</view>
        <view @tap="pj" :class="'page-hed-li ' + (start === 0 ? 'hed-li-checked' : '')">评价最高</view>
      </view>
    </view>

    <scroll-view
      v-if="pages && priC"
      :scroll-top="topCup"
      @scrolltolower="toLowFunNet"
      scroll-y
      class="list-found"
      :style="'top:' + topFixed2"
    >
      <view class="station-ul">
        <station-li
          :memberInfo="memberInfo"
          :currentItem="item"
          v-for="(item, index) in distanceCup"
          :key="index"
        ></station-li>
      </view>
    </scroll-view>

    <scroll-view
      :style="'top:' + topFixed2"
      v-if="pages && danC"
      :scroll-top="topCupNet"
      @scrolltolower="toLowFun"
      scroll-y
      class="list-found"
    >
      <view class="station-ul">
        <station-li :currentItem="item" v-for="(item, index) in pricCup" :key="index"></station-li>
      </view>
    </scroll-view>

    <scroll-view
      :style="'top:' + topFixed2"
      v-if="pages && pjIc"
      :scroll-top="topCupNet"
      @scrolltolower="toLowFun"
      scroll-y
      class="list-found"
    >
      <view class="station-ul">
        <station-li :currentItem="item" v-for="(item, index) in pricCup" :key="index"></station-li>
      </view>
    </scroll-view>
    <!-- #ifdef MP -->
    <map
      :hidden="!mapCop"
      v-else
      @tap="clickMaps"
      :longitude="longitude"
      :scale="scale"
      :latitude="latitude"
      :markers="markers"
      @controltap="controltap"
      style="position: relative"
      :style="'height:' + '100vh' + ';width:100%'"
      id="myMap"
      show-location
      @markertap="markertap"
      @callouttap="markertap"
      @labeltap="markertap"
    >
      <swiper
        v-if="imgBannerType"
        class="modal-alert"
        :indicator-dots="indicatorDots"
        :autoplay="autoplay"
        :interval="interval"
        :duration="duration"
      >
        <block v-for="(item, index) in imgBannerList" :key="index">
          <swiper-item style="text-align: center">
            <image
              class="modal-alert-images"
              style="border-radius: 36rpx"
              @tap="imgUrlrouter"
              :data-url="item.contentUrl"
              :src="item.imgUrl"
            ></image>
          </swiper-item>
        </block>
      </swiper>
      <cover-image
        v-if="imgBannerType"
        class="slide-cover-close"
        @tap="moveBannerType"
        :src="`${IMG_PATH}delect.png`"
      ></cover-image>
      <cover-view slot="callout">
        <template v-for="(item, index) in markers">
          <cover-view style="position: relative; width: 100%; padding-right: 20px" :marker-id="item.id" :key="index">
            <cover-view class="marker-box" :class="{ idle: item.freeNums <= 0, newYear: theme == 'newYear' }">
              <cover-view
                class="wrap"
                :style="{
                  background: theme == 'newYear' ? '#fe5b3e' : '#007fff',
                }"
              >
                <cover-image
                  class="img-wrap"
                  style="margin-right: 5px; width: 50rpx; height: 50rpx"
                  :src="IMG_PATH + 'icon-able.png'"
                ></cover-image>
                <cover-view class="num-wrap">{{ item.freeNums }}</cover-view>
              </cover-view>
            </cover-view>
          </cover-view>
        </template>
      </cover-view>
    </map>
    <!-- #endif -->

    <!-- #ifdef H5 -->
    <div id="myMap"></div>
    <!-- #endif -->

    <view
      class="controls-item"
      style="position: absolute"
      v-for="(item, index) in controlsInfo"
      @click="controlCallback(item)"
      :key="index"
      :style="{
        bottom: item.style.bottom,
        left: item.style.left,
      }"
    >
      <image :src="item.iconPath" mode="scaleToFill" />
    </view>
    <scroll-view
      v-if="cityPage"
      style="z-index: 99999999999; padding-top: 0; height: 100vh; top: 0"
      scroll-y
      class="list-found city-list-found"
    >
      <div
        class="list-top-nav"
        :style="'padding-top:' + s_top + 'rpx;line-height:' + s_height + 'rpx;' + 'height:' + navHeight"
      >
        <icon
          type="icon iconfont icon-arrow_left"
          @click="
            onMyEvent({
              detail: {
                address: city,
                longitudeMap: longitudeMap,
                latitudeMap: latitudeMap,
              },
            })
          "
          size="18"
        />
        <div class="text">设置城市</div>
      </div>
      <my-city @mycity="onMyEvent" class="city-wrap" />
    </scroll-view>

    <view style="z-index: 6666" v-if="condition" class="fotters">
      <view class="fotters-block fotters-block-left">
        <view style="color: #333; font-size: 30rpx; line-height: 60rpx">{{ stationName }}</view>
        <scroll-view
          v-if="msgg.tagList && Array.isArray(msgg.tagList) && msgg.tagList.length > 0"
          class="tag-wrap"
          scroll-x="true"
          scroll-left="120"
        >
          <view class="tag-item" v-for="(item, index) in msgg.tagList" :key="index">{{ item.tagName || '-' }}</view>
        </scroll-view>
        <view class="max-w" style="color: #666; font-size: 28rpx; line-height: 60rpx">{{ stationAddr }}</view>
      </view>

      <view @tap="distanceMap" class="fotters-block fotters-block-right">
        <view>
          <icon type="icon iconfont icon-guide1" size="22" />
        </view>
        <text>{{ distance }}km</text>
      </view>

      <view
        style="
          width: 100%;
          margin: 0 auto;
          height: 150rpx;
          border-top: 1rpx solid #e1e1e1;
          box-sizing: border-box;
          padding: 20rpx;
        "
      >
        <!-- <view class='middle-size'>共{{acNums}}个充电桩</view> -->
        <view style="display: inline-block; width: 50%; padding: 20rpx 0">
          <icon class="map-icon" style="color: #f44336" type="icon iconfont icon-fast" size="25" />
          <text class="middle-size-other">共{{ msgg.dcNums || 0 }}个 空闲{{ msgg.dcFreeNums || 0 }}个</text>
        </view>
        <view style="display: inline-block; width: 50%; padding: 20rpx 0">
          <icon class="map-icon" style="color: #2196f3" type="icon iconfont icon-slow" size="25" />
          <text class="middle-size-other">共{{ msgg.acNums || 0 }}个 空闲{{ msgg.acFreeNums || 0 }}个</text>
        </view>
      </view>
      <div class="describe-wrap">
        <div class="describe-item w50">
          <div class="label">充电服务费:</div>
          <div class="value">{{ msgg.priceRemark || '' }}</div>
        </div>
        <div class="describe-item w50">
          <div class="label">营业时间:</div>
          <div class="value">24小时</div>
        </div>
        <div class="describe-item w100">
          <div class="label">停车信息:</div>
          <div class="value">暂无停车信息,以场地实际费用为准</div>
        </div>
      </div>
      <view
        style="border-top: 1rpx solid #e1e1e1; height: 70rpx; line-height: 70rpx; box-sizing: border-box"
        class="btn-bottom"
      >
        <view @tap="jump" style="width: 100%; font-size: 32rpx">查看详情</view>
        <!-- <view>站点评价(2)</view> -->
      </view>
    </view>
    <custom-nav-bar currentNavbar="stationPage"></custom-nav-bar>
    <!-- <loading hidden="{{hiddenLoading}}">正在加载</loading> -->

    <uni-popup ref="popup" background-color="#fff">
      <div class="popup-content">
        <div class="popup-item">
          <image :src="`${this.IMG_PATH}icon-indicate-active.png`" mode="scaleToFill" />
          图标内数字表示空闲电桩数量
        </div>
        <div class="popup-item">
          <image :src="`${this.IMG_PATH}icon-indicate.png`" mode="scaleToFill" />
          灰色图标表示无空闲充电桩
        </div>
        <div class="popup-bottom" @click="closePopup">我知道了</div>
      </div>
    </uni-popup>
    <uni-popup
      type="right"
      ref="filterPopup"
      :mask-click="true"
      :is-mask-click="true"
      @maskClick="() => $refs.filterPopup.close()"
      background-color="#fff"
    >
      <div class="popup-right-content">
        <div class="title">电桩类型</div>
        <div class="tab-wrap">
          <div class="tab-item" :class="{ 'tab-active': mapParams.elecMode == '' }" @click="tabChange('')">全部</div>
          <div class="tab-item" :class="{ 'tab-active': mapParams.elecMode == '02' }" @click="tabChange('02')">
            直流
          </div>
          <div class="tab-item" :class="{ 'tab-active': mapParams.elecMode == '01' }" @click="tabChange('01')">
            交流
          </div>
          <div class="tab-item" :class="{ 'tab-active': mapParams.elecMode == '03' }" @click="tabChange('03')">
            交直流一体
          </div>
        </div>
        <div class="title">只显示空闲电站</div>
        <div class="switch-wrap">
          <switch
            style="transform: scale(0.7)"
            :checked="mapParams.freeFlag == 1"
            color="#1E9CFF"
            @change="switchChange"
          />
        </div>
      </div>
    </uni-popup>
  </view>
</template>
<script>
import { mapState } from 'vuex';
import uniPopup from '../../components/uni-ui/uni-popup/uni-popup.vue';
import myCity from '../../pages/city/city';
import customNavBar from '@/components/custom-navbar/index';
import stationLi from '@/components/station-li/index';
import { ENVR } from '@/config/global';
import { getLocation, openLocation, makePhoneCall } from '@/utils/index.js';
import { defaultLocationInfo } from '@/config/declare.js';
import {
  getConfigOption,
  getStationListV02,
  getStationListV06,
  getStationDetail,
  getRecommendStation,
  getMemberInfo,
  getGeocoderLocation,
} from '@/services/index';
// #ifdef H5
import AMapLoader from '@amap/amap-jsapi-loader';
// #endif
export default {
  components: {
    myCity,
    customNavBar,
    stationLi,
    uniPopup,
  },
  data() {
    return {
      ENVR,
      imgBannerType: false,
      currentIndex: 0,
      indicatorDots: true,
      imgBannerList: [],
      orderChargeList: [],
      cutListLength: 0,
      topCup: 0,
      topCupNet: 0,
      pageDance_pageNum: 1,
      pageDance_totalNum: 5,
      pagePric_pageNum: 1,
      pagePric_totalNum: 5,
      distanceCup: [],
      pricCup: [],
      pjIcCup: [],
      pageListAdd: false,
      mapCop: true,
      danC: true,
      priC: false,
      pjIc: false,
      cityPage: false,
      remainderPhone: [],
      juliListCup: [],
      pricListCup: [],
      evaScoreListCup: [],
      listFouch: [],
      city: '',
      ple: true,
      lind: true,
      listNear: [],
      listsTall: [],
      listsPrice: [],
      scale: '13',
      start: 2,
      pages: false,
      open: false,
      hiddenLoading: false,
      distance: '',
      distanceL: '10',
      lon: '',
      lat: '',
      list: [],
      lists: [],
      currentTab: 0,
      oio: '82.5vh',
      stationName: '',
      stationAddr: '',
      acNums: '',
      acFreeNums: '',
      dcNums: '',
      stationId: '',
      condition: false,
      msgg: {
        dcNums: 0,
        dcFreeNums: 0,
        acNums: 0,
        acFreeNums: 0,
      },
      msg: '',
      token: '',
      markers: [],
      clusterMarkers: [],
      longitude: '',
      latitude: '',
      longitudeMap: '',
      latitudeMap: '',
      maps: [],
      mapStation: [],
      indicatorDots: true,
      background: ['1', '2', '3'],
      vertical: false,
      autoplay: true,
      interval: 2000,
      duration: 500,
      polyline: '',
      mapParams: {
        elecMode: '',
        freeFlag: '0',
      },
      s_top: '40rpx',
      s_height: '',
      mapControl: null,
      clusterLayer: null,
      memberInfo: null,
    };
  },
  onLoad(options) {
    // #ifdef H5
    this.initH5map();
    // #endif
    console.log('走这里');
    this.getLocationCallbcak(options);
    if (options.q) {
      console.log('微信普通二维码跳转的');
      var urld = decodeURIComponent(options.q);
      var newOpint;
      function GetUrlParam(paraName) {
        var url = urld;
        var arrObj = url.split('?');
        if (arrObj.length > 1) {
          var arrPara = arrObj[1].split('&');
          var arr;
          for (var i = 0; i < arrPara.length; i++) {
            arr = arrPara[i].split('=');
            if (arr != null && arr[0] == paraName) {
              return arr[1];
            }
          }
          return '';
        } else {
          return '';
        }
      }
      newOpint = GetUrlParam('qrcode') || options.qrcode || options;
      // setTimeout(function() {
      // uni.navigateTo({
      //   url: '../placeorder/placeorder?msg=' + newOpint
      // })
      // }, 1500)
      // uni.login({
      //   success: (res) => {
      //     if (res.code) {
      //       uni.request({
      //         url: baseUrl + '/wx/v0.1/swapSInfo',
      //         data: {
      //           code: res.code,
      //         },
      //         method: 'GET',
      //         header: {
      //           'content-type': 'application/json',
      //         },
      //         success: (res) => {
      //           uni.setStorage({
      //             key: 'token',
      //             data: res.data.token,
      //           });
      //           uni.navigateTo({
      //             url: '/subPackages/placeorder/placeorder?titles=10&msg=' + newOpint,
      //           });
      //         },
      //         fail: (err) => {},
      //       });
      //     } else {
      //     }
      //   },
      // });
    }
  },
  async onShow() {
    this.getMemberInfo();
    var that = this;
    const [, res] = await getConfigOption();
    if (res) {
      var list = res.list;
      var availableBalance = [];
      var promptBalance = [];
      var remainderUpMoney = [];
      var remainderPhone = [];
      var serviceTels = [];
      for (var i = 0; i < list.length; i++) {
        if (list[i].paramName.indexOf('可启动充电余额值') >= 0) {
          availableBalance.push(list[i]);
        }
      }
      for (var i = 0; i < list.length; i++) {
        if (list[i].paramName.indexOf('提示充值的余额值') >= 0) {
          promptBalance.push(list[i]);
        }
      }
      for (var i = 0; i < list.length; i++) {
        if (list[i].paramName.indexOf('余额上限金额') >= 0) {
          remainderUpMoney.push(list[i]);
        }
      }
      for (var i = 0; i < list.length; i++) {
        if (list[i].paramName.indexOf('客服电话') >= 0) {
          remainderPhone.push(list[i]);
          serviceTels.push(list[i]);
        }
      }
      that.setData({
        remainderPhone: remainderPhone,
      });
      serviceTels.length > 0 ? uni.setStorageSync('serviceTel', serviceTels[0].paramValue) : null; //""客服电话""
      availableBalance.length > 0 ? uni.setStorageSync('availableBalance', availableBalance[0].paramValue) : null; //"可启动充电余额值"
      promptBalance.length > 0 ? uni.setStorageSync('promptBalance', promptBalance[0].paramValue) : ''; //"提示充值的余额值"
      remainderUpMoney.length > 0 ? uni.setStorageSync('remainderUpMoney', remainderUpMoney[0].paramValue) : ''; //"提示充值的余额值"
    }
    // var that = this;
    // that.tall();
    // #ifdef MP
    that.initTopImg();
    // #endif
  },
  onReady(e) {
    // #ifdef MP
    this.mapCtx = uni.createMapContext('myMap');
    // 2.初始化点聚合的配置，未调用时采用默认配置
    this.mapCtx.initMarkerCluster({
      enableDefaultStyle: false, // 是否启用默认的聚合样式(是否用自定义图标)
      zoomOnClick: true,
      gridSize: 60,
    });

    // 3.发生聚合时，给聚合点设置marker标签
    this.mapCtx.on('markerClusterCreate', (res) => {
      const clusters = res.clusters; // 新产生的聚合簇
      const zhou = clusters.map((item) => {
        const {
          center, // 聚合点的经纬度数组
          clusterId, // 聚合簇id
          markerIds, // 已经聚合了的标记点id数组
        } = item;
        return {
          ...center,
          width: 0,
          height: 0,
          clusterId, // 必须有
          borderRadius: 8,
          joinCluster: true,
          iconPath: `${this.IMG_PATH}/icon-transparent.png`,
          // iconPath: this.IMG_PATH + 'icon-transparent.png',
          label: {
            content: markerIds.length + '',
            color: '#fff',
            fontSize: '14',
            borderRadius: '50',
            bgColor: this.theme == 'newYear' ? '#fe5a41' : '#2196f3',
            borderWidth: 3,
            borderColor: '#fff',
            textAlign: 'center',
            padding: 5,
          },
        };
      });
      // 4. 添加聚合簇标签
      this.mapCtx.addMarkers({
        markers: zhou,
        clear: false, //是否先清空地图上所有的marker
      });
    });
    // 2.初始化点聚合的配置，未调用时采用默认配置
    var that = this;
    setTimeout(() => {
      that.mapCtx.moveToLocation();
    }, 500);
    // #endif

    this.city = uni.getStorageSync('city');
  },
  /**
   * 用户点击右上角分享
   */
  computed: {
    center() {
      if (this.longitude && this.latitudeMap) {
        return [this.longitude, this.latitudeMap];
      }
      return [119.60428994197378, 26.805433317071145];
    },
    ...mapState({
      userInfo: (state, getters) => getters['login/getUserInfo'],
    }),
    controlsInfo() {
      let controls = [
        {
          id: 1,
          iconPath: this.IMG_PATH + 'icon-location.png',
          style: {
            left: '85%',
            bottom: '650rpx',
          },
        },
        {
          id: 2,
          iconPath: this.IMG_PATH + 'icon-info.png',
          style: {
            left: '85%',
            bottom: '550rpx',
          },
        },
        {
          id: 3,
          iconPath: this.IMG_PATH + 'icon-filter.png',
          style: {
            left: '85%',
            bottom: '450rpx',
          },
        },
        {
          id: 4,
          iconPath: this.IMG_PATH + 'icon-complaint.png',
          style: {
            left: '5%',
            bottom: '550rpx',
          },
        },
        {
          id: 5,
          iconPath: this.IMG_PATH + 'icon-recommend.png',
          style: {
            left: '5%',
            bottom: '450rpx',
          },
        },
      ];
      return this.ple ? controls : [];
    },
    topFixed() {
      return 50 + this.s_top + 'rpx';
    },
    topFixed2() {
      return 200 + this.s_top + 'rpx';
    },
    navHeight() {
      return +this.s_top + +this.s_height + 'rpx';
    },
    nowCenter() {
      if (this.mapControl) {
        return this.mapControl.getCenter();
      }
      return '位置';
    },
  },
  onShareAppMessage() {},
  methods: {
    // 获取会员信息
    async getMemberInfo() {
      const [err, res] = await getMemberInfo();
      if (res) {
        this.memberInfo = res;
      }
    },
    closePopup() {
      this.$refs.popup && this.$refs.popup.close();
    },
    initTopImg() {
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      this.s_top = +(menuButtonInfo?.top * 2) + 20;
      this.s_height = +(menuButtonInfo?.height * 2);
      console.log(this.s_top, this.s_height);
    },

    // 聚合功能
    setMarkersAndCluster(markerList) {
      const _this = this;
      let markers = JSON.parse(JSON.stringify(this.markers));
      this.markers = [];
      this.mapCtx.removeMarkers({
        markerIds: markers.map((q) => q.id),
        complete: () =>
          _this.mapCtx.addMarkers({
            markers: markerList,
            clear: true, //是否先清空地图上所有的marker
            success: () => {
              _this.markers = markers;
              // this.mapCtx.moveToLocation({
              //   longitude: this.longitude,
              //   latitude: this.latitude + 0.1,
              // });
              // this.mapCtx.getScale({
              //   success: (res) => {
              //     this.scale = res.scale - 1;
              //   },
              // });
              // this.mapCtx.includePoints({
              //   points: markerList,
              //   success: () => {
              setTimeout(() => {
                _this.mapCtx.getScale({
                  success: (res) => {
                    _this.scale = res.scale + 1;
                  },
                });
              }, 200);
              // },
              //});
            },
          }),
      });
      // 1.组装数据之后，并赋值给地图上的marker
      // this.markers = markerList;
      // setTimeout(() => {
      //   let level = 0;
      //   this.mapCtx.getScale({
      //     success: (res) => {
      //       console.log(_this.mapCtx, 'this.mapCtx', level);
      //       _this.mapCtx.setScale({
      //         scale: res.scale + 1,
      //       });
      //     },
      //   });
      // });
    },
    onMyEvent(e) {
      this.setData({
        city: e.detail.address,
        cityPage: false,
        longitudeMap: e.detail.lon,
        latitudeMap: e.detail.lat,
        pageListAdd: true,
        mapCop: true,
        pages: false,
      });
      // this.regionchange();
    },

    distanceMap() {
      var that = this;
      openLocation({
        latitude: that.lat,
        longitude: that.lon,
        address: that.stationAddr,
      });
    },

    tabs() {
      var that = this;
      that.pageDanceInfo();
      that.pagePricInfo();
      this.pagepjIcCupInfo();
      that.setData(
        {
          // pages: !_self.data.pages,
          condition: false,
          topCup: 0,
          topCupNet: 0,
          oio: '82.5vh',
          ple: !that.ple,
          // cityPage: false,
          pageDance_pageNum: 1,
          pageDance_totalNum: 5,
          pagePric_pageNum: 1,
          pagePric_totalNum: 5,
        },
        () => {
          if (that.pages == true) {
            that.setData({
              pages: false,
              cityPage: false,
              pageListAdd: false,
              mapCop: true,
            });
            that.mapCtx.moveToLocation();
          } else {
            that.setData({
              pages: true,
              cityPage: false,
              pageListAdd: true,
            });
          }
        }
      );

      // _self.julis()
    },

    moveMap(e) {
      var stationId = e.currentTarget.dataset.ade || _self.data.stationId;
      uni.navigateTo({
        url:
          '/pages/basic/station/station?stationId=' +
          e.currentTarget.dataset.ade +
          '&distance=' +
          e.currentTarget.dataset.km,
      });
    },

    // pric() {
    //   var arr = this.data.list
    //   var listTypeAlls = []
    //   var listTypeLack = []
    //   listTypeAlls = arr.filter(function(item, index) {
    //     if (item.priceRemarkNum == '-') {
    //       return item
    //     }
    //   })
    //   listTypeLack = arr.filter(function(item, index) {
    //     if (item.priceRemarkNum !== '-') {
    //       return item
    //     }
    //   })
    //   listTypeLack.sort(function(a, b) {
    //     return a.priceRemarkNum - b.priceRemarkNum;
    //   })
    //   var result = listTypeLack.concat(listTypeAlls)
    //   this.setData({
    //     start: 1,
    //     hiddenLoading: false,
    //     list: result,
    //     pricListCup: result
    //   });
    // },
    // julis() {
    //   var arr = this.data.list
    //   var listTypeAlls = []
    //   var listTypeLack = []
    //   listTypeAlls = arr.filter(function(item, index) {
    //     if (item.juli == '-') {
    //       return item
    //     }
    //   })
    //   listTypeLack = arr.filter(function(item, index) {
    //     if (item.juli !== '-') {
    //       return item
    //     }
    //   })
    //   listTypeLack.sort(function(a, b) {
    //     return a.juli - b.juli;
    //   })
    //   var result = listTypeLack.concat(listTypeAlls)
    //   this.setData({
    //     start: 2,
    //     hiddenLoading: false,
    //     juliListCup: result,
    //     listFouch: result
    //   });
    //   this.jl()
    // },
    // pingjia() {
    //   var arr = this.data.list
    //   var listTypeAlls = []
    //   var listTypeLack = []
    //   listTypeAlls = arr.filter(function(item, index) {
    //     if (item.evaScore == '-') {
    //       return item
    //     }
    //   })
    //   listTypeLack = arr.filter(function(item, index) {
    //     if (item.evaScore !== '-') {
    //       return item
    //     }
    //   })
    //   listTypeLack.sort(function(a, b) {
    //     return b.evaScore - a.evaScore;
    //   })
    //   var result = listTypeLack.concat(listTypeAlls)
    //   this.setData({
    //     start: 0,
    //     hiddenLoading: false,
    //     evaScoreListCup: result,
    //     listFouch: result
    //   });
    //   this.jl()
    // },
    jl() {
      var that = this;
      that.setData({
        start: 2,
        topCup: 0,
        topCupNet: 0,
        danC: true,
        priC: false,
        pjIc: false,
      });
    },

    jg() {
      var that = this;
      that.setData({
        start: 1,
        topCup: 0,
        topCupNet: 0,
        danC: false,
        priC: true,
        pjIc: false,
      });
    },

    pj() {
      var that = this;
      that.setData({
        start: 0,
        danC: false,
        priC: false,
        pjIc: true,
        // list: _self.data.evaScoreListCup,
        // lists: _self.data.evaScoreListCup
      });
    },

    capture() {
      this.setData({
        pages: true,
      });
    },

    getDistance(lat1, lng1, lat2, lng2) {
      lat1 = lat1 || 0;
      lng1 = lng1 || 0;
      lat2 = lat2 || 0;
      lng2 = lng2 || 0;
      var rad1 = (lat1 * Math.PI) / 180;
      var rad2 = (lat2 * Math.PI) / 180;
      var a = rad1 - rad2;
      var b = (lng1 * Math.PI) / 180 - (lng2 * Math.PI) / 180;
      var r = 6378137;
      return (
        r *
        2 *
        Math.asin(
          Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2))
        )
      ).toFixed(0);
    },

    async tall() {
      this.setData({
        start: 2,
        lind: false,
        hiddenLoading: false,
      });
      this.mapCtx = uni.createMapContext('myMap');
      // 2.初始化点聚合的配置，未调用时采用默认配置
      this.mapCtx.initMarkerCluster({
        enableDefaultStyle: false, // 是否启用默认的聚合样式(是否用自定义图标)
        zoomOnClick: true,
        gridSize: 60,
      });

      var that = this;
      var city = uni.getStorageSync('city');
      var params = {
        city: '',
        positionLon: that.longitude,
        positionLat: that.latitude,
        orderType: '02',
        // distance: _self.data.distanceL
      };
      const [, res] = await getStationListV02(params);
      if (res) {
        if (!res || !res.chcGroupList) {
          return;
        }
        that.setData({
          maps: res.chcGroupList,
          msg: '',
          // mapStation: res.chcGroupList.length,
          hiddenLoading: true,
        });
        let mapPark = [];
        var list = that.maps;
        var msg = that.msg;
        var stions = [];
        for (var i = 0; i < res.chcGroupList.length; i++) {
          var listStations = res.chcGroupList[i].chcList;
          stions.push(listStations);
        }
        const arrSource = stions; // 这个是你的数组
        const arrResult = arrSource.reduce((pre, item) => [...pre, ...item], []);
        that.setData({
          mapStation: arrResult.length,
        });
        for (var i = 0; i < arrResult.length; i++) {
          var iconPath;
          if (arrResult[i].freeNums > 0) {
            iconPath = that.IMG_PATH + 'able.png';
          } else {
            iconPath = that.IMG_PATH + 'unable.png';
          }
          var carlist = {
            iconPath: iconPath,
            // iconPath: _self.IMG_PATH + 'icon-circle.png',
            latitude: arrResult[i].lat,
            longitude: arrResult[i].lon,
            id: arrResult[i].stationId,
            width: 30,
            height: 36,
            joinCluster: true,
            freeNums: arrResult[i].freeNums,
          };
          mapPark.push(carlist);
        }
        var km = '附近' + that.distanceL + '公里有' + that.mapStation + '个充电站';
        var lo = {
          latitude: that.latitude,
          longitude: that.longitude,
          id: 0,
          width: 30,
          height: 36,
          callout: {
            content: km,
            color: '#000',
            fontSize: '16',
            borderRadius: '10',
            bgColor: '#ffffff',
            padding: '5',
            display: 'ALWAYS',
          },
        };
        mapPark.push(lo);
        that.setData({
          // markers: mapPark,
          // lists: arrResult,
          // list: arrResult,
          // listsTall: arrResult,
          // hiddenLoading: true
        });
        // for (var z = 0; z < arrResult.length; z++) {
        //   var distance = _self.getDistance(_self.data.latitude, _self.data.longitude, arrResult[z].lat, arrResult[z].lon)
        //   var juli = 'list[' + z + '].juli'
        //   var julis = 'lists[' + z + '].juli'
        //   _self.setData({
        //     [juli]: (distance / 1000).toFixed(1),
        //     [julis]: (distance / 1000).toFixed(1),
        //   })
        // }

        // _self.setData({
        //   lists: _self.data.lists.sort((a, b) => {
        //     return a.juli - b.juli
        //   }),
        //   list: _self.data.lists.sort((a, b) => {
        //     return a.juli - b.juli
        //   }),
        // })
        // var arr = _self.data.list
        // var listTypeAlls = []
        // var listTypeLack = []
        // listTypeAlls = arr.filter(function(item, index) {
        //   if (item.juli == '-') {
        //     return item
        //   }
        // })
        // listTypeLack = arr.filter(function(item, index) {
        //   if (item.juli !== '-') {
        //     return item
        //   }
        // })
        // listTypeLack.sort(function(a, b) {
        //   return a.juli - b.juli;
        // })
        // var result = listTypeLack.concat(listTypeAlls)
        // _self.setData({
        //   juliListCup: result
        // })
        // _self.pric()
        // _self.pingjia()
        // _self.jl()
      } else {
        
      }
    },

    mocks() {
      // stationcup
      console.log('查找');
      uni.navigateTo({
        url: '/pages/basic/stationcup/stationcup',
      });
    },

    // found(value) {
    //   var Msg = value.detail.value
    //   var _self = this
    //   var list = _self.data.lists
    //   var arr = []
    //   for (var i = 0; i < list.length; i++) {
    //     //如果字符串中不包含目标字符会返回-1
    //     if (list[i].stationName.indexOf(Msg) >= 0) {
    //       arr.push(list[i]);
    //     }
    //   }
    //   _self.setData({
    //     list: arr
    //   });
    //   if (!Msg) {
    //     _self.setData({
    //       list: _self.data.lists
    //     });
    //   }
    // },
    tap_ch(e) {
      if (this.open) {
        this.setData({
          open: false,
        });
      } else {
        this.setData({
          open: true,
        });
      }
    },

    jump() {
      var that = this;
      var msg = that.msg;
      uni.setStorage({
        key: 'msg',
        data: msg,
      });
      uni.navigateTo({
        url: '/pages/basic/station/station?stationId=' + that.stationId + '&distance=' + that.distance * 1000,
      });
    },

    scan() {
      uni.scanCode({
        onlyFromCamera: true,
        success: (res) => {
          function GetUrlParam(paraName) {
            var url = res.result;
            var arrObj = url.split('?');
            if (arrObj.length > 1) {
              var arrPara = arrObj[1].split('&');
              var arr;
              for (var i = 0; i < arrPara.length; i++) {
                arr = arrPara[i].split('=');
                if (arr != null && arr[0] == paraName) {
                  return arr[1];
                }
              }
              return '';
            } else {
              return '';
            }
          }
          var message = GetUrlParam('qrcode') || res.result;

          // 判断是否登录
          if (!this.userInfo?.mobile) {
            uni.navigateTo({
              url: '../login/login',
            });
          } else {
            uni.navigateTo({
              url: '/subPackages/placeorder/placeorder?msg=' + message + '&titles=10',
            });
          }
        },
      });
    },

    address(lat1, lng1, lat2, lng2) {
      function toRad(d) {
        return (d * Math.PI) / 180;
      }
      // function getDisance(lat1, lng1, lat2, lng2) {
      var dis = 0;
      var radLat1 = toRad(lat1);
      var radLat2 = toRad(lat2);
      var deltaLat = radLat1 - radLat2;
      var deltaLng = toRad(lng1) - toRad(lng2);
      var dis =
        2 *
        Math.asin(
          Math.sqrt(
            Math.pow(Math.sin(deltaLat / 2), 2) +
              Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(deltaLng / 2), 2)
          )
        );
      // return dis * 6378137;
      var distance = ((dis * 6378137) / 1000).toFixed(2);
      this.setData({
        distance: !isNaN(distance) ? distance : '',
      });
    },

    addressNeth(lat1, lng1, lat2, lng2) {
      function toRad(d) {
        return (d * Math.PI) / 180;
      }
      // function getDisance(lat1, lng1, lat2, lng2) {
      var dis = 0;
      var radLat1 = toRad(lat1);
      var radLat2 = toRad(lat2);
      var deltaLat = radLat1 - radLat2;
      var deltaLng = toRad(lng1) - toRad(lng2);
      var dis =
        2 *
        Math.asin(
          Math.sqrt(
            Math.pow(Math.sin(deltaLat / 2), 2) +
              Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(deltaLng / 2), 2)
          )
        );
      // return dis * 6378137;
      var distance = ((dis * 6378137) / 1000).toFixed(2);
      return distance;
    },

    async loadCity(longitude, latitude) {
      var that = this;
      const [, res] = await getGeocoderLocation({ location: latitude + ',' + longitude });
      if (res) {
        that.setData({
          city: res.data.result.addressComponent.city,
        });
      }

      that.regionchange();
    },

    personal() {
      this.setData({
        currentTab: 1,
      });
      uni.redirectTo({
        url: '../personal/personal',
      });
    },

    fj() {
      this.setData({
        currentTab: 0,
        pages: false,
        ple: true,
      });
    },

    cityChange() {
      var that = this;
      if (that.cityPage == true) {
        that.setData({
          // pageListAdd: true,
          mapCop: true,
          cityPage: false,
        });
      } else if (that.cityPage == false) {
        that.setData({
          // pageListAdd: false,
          mapCop: false,
          cityPage: true,
        });
      }
    },

    imgUrlrouter(e) {
      let url = e.target.dataset.url;
      if (url) {
        uni.navigateTo({
          url: '../webview/webview?countUrl=' + encodeURIComponent(JSON.stringify(url)),
        });
      }
    },

    toLowFun() {
      var _self = this;
      _self.setData({
        pagePric_pageNum: _self.pagePric_pageNum + 1,
        pagePric_totalNum: _self.pagePric_totalNum,
      });
      _self.pagePricInfo();
      this.pagepjIcCupInfo();
    },

    toLowFunNet() {
      var _self = this;
      _self.setData({
        pageDance_pageNum: _self.pageDance_pageNum + 1,
        pageDance_totalNum: _self.pageDance_totalNum,
      });
      _self.pageDanceInfo();
    },

    swiperChange(e) {
      const _self = this;
      let source = e.detail.source;
      if (source == 'touch' || source == 'autoplay') {
        _self.setData({
          currentIndex: e.detail.current,
        });
      }
    },

    moveBannerType() {
      this.setData({
        imgBannerType: false,
      });
    },

    getCenterLocation() {
      this.mapCtx.getCenterLocation({
        success(res) {},
      });
    },

    translateMarker() {
      this.mapCtx.translateMarker({
        markerId: 0,
        autoRotate: true,
        duration: 1000,
        destination: {
          latitude: uni.getStorageSync('latitude'),
          longitude: uni.getStorageSync('longitude'),
        },
        animationEnd() {},
      });
    },

    distanceFun(la1, lo1, la2, lo2) {
      var La1 = (la1 * Math.PI) / 180;
      var La2 = (la2 * Math.PI) / 180;
      var La3 = La1 - La2;
      var Lb3 = (lo1 * Math.PI) / 180 - (lo2 * Math.PI) / 180;
      var s =
        2 *
        Math.asin(
          Math.sqrt(Math.pow(Math.sin(La3 / 2), 2) + Math.cos(La1) * Math.cos(La2) * Math.pow(Math.sin(Lb3 / 2), 2))
        );
      s = s * 6378.137; //地球半径
      s = Math.round(s * 10000) / 10000;
    },

    async regionchange(e) {
      var _self = this;
      // this.mapCtx.getRegion({
      //   success(res) {
      //     var dance = _self.addressNeth(
      //       res.northeast.latitude,
      //       res.northeast.longitude,
      //       res.southwest.latitude,
      //       res.southwest.longitude
      //     );
      //     if (dance < 8) {
      //       var danced = 5;
      //     } else if (dance >= 8 && dance < 15) {
      //       var danced = 8;
      //     } else if (dance >= 15 && dance < 30) {
      //       var danced = 15;
      //     } else {
      //       var danced = 30;
      //     }
      //     _self.setData({
      //       distanceL: danced,
      //     });
      //   },
      // });
      const location = await getLocation();
      const res = location || defaultLocationInfo;
      _self.setData({
        longitude: res.longitude,
        latitude: res.latitude,
      });
      uni.setStorageSync('latitude', res.latitude);
      uni.setStorageSync('longitude', res.longitude);
      var params = {
        // city: _self.city,
        city: '',
        positionLon: res.longitude,
        positionLat: res.latitude,
        orderType: '01',
        // distance: _self.distanceL,
        ..._self.mapParams,
      };
      const [, result] = await getStationListV02(params);
      if (result) {
        _self.setData({
          maps: result.chcGroupList || [],
          // mapStation: result.chcGroupList.length,
          msg: '',
        });
        let mapPark = [];
        var list = _self.maps;
        var msg = _self.msg;
        var stions = [];
        for (var i = 0; i < list.length; i++) {
          var listStations = list[i].chcList;
          stions.push(listStations);
        }
        const arrSource = stions; // 这个是你的数组
        const arrResult = arrSource.reduce((pre, item) => [...pre, ...item], []);
        _self.setData({
          mapStation: arrResult.length,
        });
        for (var i = 0; i < arrResult.length; i++) {
          var iconPath;
          arrResult[i].freeNums > 0
            ? (iconPath = _self.IMG_PATH_UI + 'icon-circle.png')
            : (iconPath = _self.IMG_PATH + 'icon-circle-normal.png');
          // arrResult[i].freeNums > 0
          //   ? (iconPath = _self.IMG_PATH + 'able.png')
          //   : (iconPath = _self.IMG_PATH + 'unable.png');
          var carlist = {
            iconPath: iconPath,
            // iconPath: _self.IMG_PATH + 'icon-circle.png',
            latitude: arrResult[i].lat,
            longitude: arrResult[i].lon,
            id: arrResult[i].stationId,
            joinCluster: true,
            freeNums: arrResult[i].freeNums,
            width: 10,
            height: 10,
            callout: {
              content: arrResult[i].freeNums,
              fontSize: 12,
              padding: 0,
            },
            customCallout: {
              anchorX: 23,
              anchorY: -5,
              display: 'ALWAYS', // 常显
              isShow: 1,
            },
          };
          mapPark.push(carlist);
        }
        // var km = '附近' + _self.distanceL + '公里有' + _self.mapStation + '个充电站';
        // var lo = {
        //   latitude: _self.latitude,
        //   longitude: _self.longitude,
        //   id: 0,
        //   width: 30,
        //   height: 36,
        //   joinCluster: true,
        //   callout: {
        //     content: km,
        //     color: '#000',
        //     fontSize: '16',
        //     borderRadius: '10',
        //     bgColor: '#ffffff',
        //     padding: '5',
        //     display: 'ALWAYS',
        //   },
        // };
        // mapPark.push(lo);
        let mapPotion = mapPark.filter((q) => {
          return q.latitude < 90 && q.latitude != 0 && q.latitude != 1 && q.longitude > 90;
        });
        _self.setData({
          markers: mapPotion,
          listNear: arrResult,
          hiddenLoading: true,
        });
        // #ifdef MP
        _self.setMarkersAndCluster(mapPotion);
        // #endif
        // #ifdef H5
        _self.addH5MarkersCluster(mapPotion);
        // #endif
        // for (var z = 0; z < arrResult.length; z++) {
        //   var distance = _self.getDistance(_self.data.latitude, _self.data.longitude, arrResult[z].lat, arrResult[z].lon)
        //   var juli = 'list[' + z + '].juli'
        //   var julis = 'lists[' + z + '].juli'
        //   _self.setData({
        //     [juli]: (distance / 1000).toFixed(1),
        //     [julis]: (distance / 1000).toFixed(1),
        //   })
        // }
      } else {
        
      }
    },

    async pageDanceInfo() {
      // 01按照评分排序  02按照距离排序  03 按金额排序
      var _self = this;
      var params = {
        positionLon: _self.longitude,
        positionLat: _self.latitude,
        orderType: '02',
        pageNum: _self.pageDance_pageNum,
        totalNum: _self.pageDance_totalNum,
      };
      const [, res] = await getStationListV06(params);
      if (res) {
        if (!res || !res.chcList) {
          return;
        }
        if (res && res.chcList && res.chcList.length > 0) {
        } else if (res && res.chcList) {
          uni.showToast({
            icon: 'none',
            title: '到底啦',
          });
          return false;
        }
        var distanceCup = _self.distanceCup.concat(res.chcList);
        _self.setData({
          distanceCup: _self.beath(distanceCup),
        });
      }
    },

    beath(arr) {
      var result = [];
      var hash = {};
      var cur = null;
      for (var i = 0; i < arr.length; i++) {
        cur = arr[i];
        if (cur.stationNo && !hash[cur.stationNo]) {
          result.push(cur);
          hash[cur.stationNo] = true;
        }
      }
      return result;
    },

    async pagePricInfo() {
      var _self = this;
      var params = {
        positionLon: _self.longitude,
        positionLat: _self.latitude,
        orderType: '03',
        pageNum: _self.pagePric_pageNum,
        totalNum: _self.pagePric_totalNum,
      };
      const [, res] = await getStationListV06(params);
      if (res) {
        if (!res || !res.chcList) {
          return;
        }
        if (res.chcList.length > 0) {
        } else {
          uni.showToast({
            icon: 'none',
            title: '到底啦',
          });
          return false;
        }
        var pricCup = _self.pricCup.concat(res.chcList);
        _self.setData({
          pricCup: _self.beath(pricCup),
        });
      } else {
        
      }
    },
    async pagepjIcCupInfo() {
      var _self = this;
      var params = {
        positionLon: _self.longitude,
        positionLat: _self.latitude,
        orderType: '01',
        pageNum: _self.pagePric_pageNum,
        totalNum: _self.pagePric_totalNum,
      };
      const [, res] = await getStationListV06(params);
      if (res) {
        if (!res || !res.chcList) {
          return;
        }
        if (res.chcList.length > 0) {
        } else {
          uni.showToast({
            icon: 'none',
            title: '到底啦',
          });
          return false;
        }
        var pjIcCup = _self.pjIcCup.concat(res.chcList);
        _self.setData({
          pjIcCup: _self.beath(pjIcCup),
        });
      } else {
        
      }
    },
    clickMaps(event) {
      var _self = this;
      _self.setData({
        condition: false,
        oio: '82.5vh',
      });
    },

    async markertap(e) {
      var _self = this;
      var stationId = e.markerId;
      if (e.markerId == 0) {
        return false;
      }
      _self.setData({
        hiddenLoading: false,
        ple: true,
      });
      const [, res] = await getStationDetail({
        stationId: stationId,
      });
      if (res) {
        _self.address(_self.latitude, _self.longitude, res.lat, res.lon);
        _self.setData({
          condition: true,
          hiddenLoading: true,
          // oio: '720rpx',
          oio: '61vh',
          stationId: stationId,
          lon: res.lon,
          lat: res.lat,
          stationName: res.stationName,
          stationAddr: res.stationAddr,
          dcNums: res.dcNums,
          acFreeNums: res.acFreeNums,
          acNums: res.acNums,
          msgg: res,
        });
      }
    },

    controltap(e) {
      var phone = this.remainderPhone[0].paramValue || '';
      if (e.controlId == 5) {
        this.mapCtx.moveToLocation();
      }
      if (e.controlId == 3) {
        // this.setData({
        //   longitudeMap: '117.65357645298785',
        //   latitudeMap: '24.51892979117087',
        //   scale: '14'
        // });
      }
      if (e.controlId == 2) {
        uni.navigateTo({
          url: '/subPackages/codecharge/codecharge',
        });
      }
      if (e.controlId == 9) {
        if (this.cutListLength > 1) {
          uni.navigateTo({
            url: '/subPackages/orderList/orderList',
          });
        } else {
          uni.navigateTo({
            url: '/subPackages/ordercharge/ordercharge?orderNo=' + this.orderChargeList[0].orderNo,
          });
        }
      }
      if (e.controlId == 10) {
        makePhoneCall(phone);
      }
    },

    includePoints() {
      this.mapCtx.includePoints({
        padding: [10],
        points: [],
      });
    },

    getRegion(e) {
      var that = this;
      this.mapCtx.getRegion({
        success(res) {},
      });
    },
    tabChange(type) {
      this.mapParams.elecMode = type;
      this.regionchange();
    },
    switchChange(val) {
      this.mapParams.freeFlag = val.detail.value ? '1' : '0';
      this.regionchange();
    },
    // 控件点击事件
    async controlCallback(item) {
      const _this = this;
      switch (item.id) {
        case 1:
          // #ifdef MP
          this.mapCtx.moveToLocation();
          // #endif
          // #ifdef H5
          this.mapControl && this.mapControl.setCenter(this.center);
          // #endif
          break;
        case 2:
          this.$refs.popup && this.$refs.popup.open();
          break;
        case 3:
          this.$refs.filterPopup && this.$refs.filterPopup.open();
          break;
        case 4:
          if (!this.userInfo?.mobile) {
            uni.navigateTo({
              url: '/pages/login/login',
            });
          } else {
            uni.navigateTo({
              url: '/subPackages/customerService/index',
            });
          }
          break;
        case 5:
          const [, res] = await getRecommendStation({
            lon: this.longitudeMap,
            lat: this.latitudeMap,
          });
          if (res && res.stationList && res.stationList.length > 0) {
            var latitude = uni.getStorageSync('latitude');
            var longitude = uni.getStorageSync('longitude');
            let locationData = res.stationList[0];
            _this.address(latitude, longitude, locationData.lat, locationData.lon);
            _this.setData({
              condition: true,
              hiddenLoading: true,
              // oio: '720rpx',
              oio: '61vh',
              stationId: locationData.id || '',
              lon: locationData.lon,
              lat: locationData.lat,
              stationName: locationData.stationName,
              stationAddr: locationData.stationAddr,
              dcNums: locationData.dcNums,
              acFreeNums: locationData.acFreeNums,
              acNums: locationData.acNums,
              msgg: locationData,
            });
          } else {
            uni.showToast({
              icon: 'none',
              title: '暂无推荐站点',
            });
          }
          break;
      }
    },
    // 获取距离
    async getLocationCallbcak(options) {
      const that = this;
      if (options && options.lat && options.lon) {
        var distance = options.distance;
        var juli;
        if (distance > 1000) {
          juli = ((distance * 1) / 1000).toFixed(2);
        } else {
          juli = (distance * 1).toFixed(2);
        }
        that.setData(
          {
            longitude: options.lon,
            longitudeMap: options.lon,
            latitude: options.lat,
            latitudeMap: options.lat,
            scale: 16,
          },
          async () => {
            const [, res] = await getStationDetail({
              stationId: options.stationId,
            });
            if (res) {
              that.setData({
                condition: true,
                oio: '61vh',
                stationName: res.stationName,
                stationAddr: res.stationAddr,
                dcNums: res.dcNums,
                stationId: options.stationId,
                acFreeNums: res.acFreeNums,
                acNums: res.acNums,
                msgg: res,
                distance: juli,
              });
            }
            that.loadCity(options.lon, options.lat);
          }
        );
      } else {
        const location = await getLocation();
        console.log(location, 'location');
        if (location) {
          that.setData(
            {
              longitude: location.longitude,
              longitudeMap: location.longitude,
              latitude: location.latitude,
              latitudeMap: location.latitude,
            },
            () => {
              that.loadCity(location.longitude, location.latitude);
              that.$nextTick(() => {
                that.mapControl && that.mapControl.setCenter([location.longitude, location.latitude]);
              });
            }
          );
        } else {
          that.setData(
            {
              longitude: defaultLocationInfo.longitude,
              longitudeMap: defaultLocationInfo.longitude,
              latitude: defaultLocationInfo.latitude,
              latitudeMap: defaultLocationInfo.latitude,
            },
            () => {
              that.loadCity(defaultLocationInfo.longitude, defaultLocationInfo.latitude);
              that.$nextTick(() => {
                that.mapControl &&
                  that.mapControl.setCenter([defaultLocationInfo.longitude, defaultLocationInfo.latitude]);
              });
            }
          );
        }
      }
      if (options.showList) {
        that.tabs();
      }
    },

    // H5 初始化地图
    initH5map() {
      AMapLoader.load({
        key: '5a4fdefa142b1d9cf9d6d0185ff08bdc',
        securityJsCode: '959b1ff8744de7bba975abd222a728c3',
        version: '2.0',
        plugins: ['AMap.Geolocation', 'AMap.Scale', 'AMap.MarkerCluster'],
      }).then((AMap) => {
        console.log(this.center, 'this.center');
        const map = new AMap.Map('myMap', {
          zoom: 14,
          center: this.center,
          viewMode: '3D',
        });
        this.mapControl = map;
        this.mapControl.on('click', (e) => {
          console.log(e, 'e');
        });
      });
    },
    addH5MarkersCluster(markerList) {
      const that = this;
      const count = markerList.length;
      const _renderClusterMarker = function (context) {
        // 聚合中点个数
        var clusterCount = context.count;
        var div = document.createElement('div');
        // 聚合点配色
        div.style.backgroundColor = '#1E9CFF';
        var size = 40 + ((clusterCount + '').length - 1 * 10);
        div.style.width = size + 'px';
        div.style.height = size + 'px';
        div.style.borderRadius = '30px';
        div.innerHTML = clusterCount;
        div.style.lineHeight = size + 'px';
        div.style.color = '#ffffff';
        div.style.fontSize = '14px';
        div.style.textAlign = 'center';
        div.style.border = '2px solid #fff';
        div.style.filter = 'drop-shadow(2px 2px 2px  #1E9CFF)';
        context.marker.setOffset(new AMap.Pixel(-size / 2, -size / 2));
        context.marker.setContent(div);
      };
      const _renderMarker = function (context) {
        const info = context.data && Array.isArray(context.data) && context.data.length > 0 ? context.data[0] : null;
        let content = '';
        if (info) {
          const activeColor = info.freeNums > 0 ? '#1e9cff' : '#cccccc';
          // filter: drop-shadow(0 0 0 #0d87e8) drop-shadow(0 0 0 #0d87e8);
          content = `<div style="position: relative">
        <div
          style="
            padding: 3px 10px;
            display: flex;
            flex-direction: row;
            gap: 8px;
            grid-gap: 8rpx;
            background-color: ${activeColor};
            border-radius: 20px;
            position: absolute;
            left: -15px;
            top: -34px;
          "
        >
          <image src="${that.IMG_PATH + 'icon-able.png'}" mode="scaleToFill" style="width: 25px; height: 25px" />
          <div
            class="text-wrap"
            style="
              width: auto;
              height: 25px;
              padding: 0px 5px;
              border-radius: 15px;
              color: #ff8301;
              background-color: #fff;
              line-height: 25px;
              box-sizing:border-box;
              font-weight:600;
            "
          >
            ${info.freeNums}
          </div>
          <div
            class="triangle-wrap"
            style="
              position: absolute;
              bottom: -5px;
              left: 20px;
              transform: translateX(-50%);
              width: 0;
              height: 0;
              border-left: 5px solid transparent;
              border-right: 5px solid transparent;
              border-top: 6px solid ${activeColor};
            "
          ></div>
        </div>
        <image
          src="${info.iconPath}"
          mode="widthFix"
          style="width: 10px"
        />
      </div>`;
        }
        var offset = new AMap.Pixel(-9, -9);
        context.marker.setContent(content);
        context.marker.setOffset(offset);
      };
      const clusterMarkers = markerList.map((q) => {
        return {
          lnglat: [q.longitude, q.latitude],
          weight: 1,
          ...q,
        };
      });
      if (!this.clusterLayer) {
        this.clusterLayer = new AMap.MarkerCluster(this.mapControl, clusterMarkers, {
          gridSize: 60, // 聚合网格像素大小
          renderClusterMarker: _renderClusterMarker, // 自定义聚合点样式
          renderMarker: _renderMarker, // 自定义非聚合点样式
        });
        this.clusterLayer.on('click', (event) => {
          if (event.clusterData.length < 1) return;
          if (event.clusterData.length == 1) {
            this.markertap({ markerId: event.clusterData[0].id });
          }
        });
      } else {
        this.clusterLayer.setData(clusterMarkers);
      }
    },
  },
};
</script>
<style lang="scss">
@import './map.css';
.list-top {
  background: #fff;
}
.city-list-found {
  display: flex;
  flex-direction: column;
}
.city-wrap {
  flex: 1;
}
.idle {
  &::before {
    content: '';
    width: 0;
    height: 0;
    position: absolute;
    bottom: 2rpx;
    left: 20%;
    z-index: 9;
    border-left: 10rpx solid transparent; /* 左边透明 */
    border-right: 10rpx solid transparent; /* 右边透明 */
    border-top: 10rpx solid #cccccc !important; /* 三角形颜色 */
  }
  .wrap {
    background: #cccccc !important;
  }
}
// 地图标记点
.marker-box {
  position: relative;
  color: black;
  font-size: 28rpx;
  overflow: visible;
  padding-bottom: 10rpx;
  &::before {
    content: '';
    width: 0;
    height: 0;
    position: absolute;
    bottom: 2rpx;
    left: 20%;
    z-index: 9;
    border-left: 10rpx solid transparent; /* 左边透明 */
    border-right: 10rpx solid transparent; /* 右边透明 */
    border-top: 10rpx solid #007fff; /* 三角形颜色 */
  }
  &.newYear {
    &::before {
      border-top: 10rpx solid #fe5b3e; /* 三角形颜色 */
    }
  }
  .wrap {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    // align-items: center;
    justify-content: center;
    padding: 2rpx 10rpx;
    border-radius: 20rpx;
    align-items: center;
  }
  .img-wrap {
    margin-right: 5rpx;
    width: 60rpx;
    height: 60rpx;
  }
  .num-wrap {
    height: 40rpx;
    line-height: 40rpx;
    border-radius: 15rpx;
    flex: 1;
    text-align: center;
    max-width: 100rpx;
    width: auto;
    background: #fff;
    padding: 0 10rpx;
    color: #ff8301;
  }
}
.top-nav {
  position: fixed;
  height: 120rpx;
  background: transparent;
  display: flex;
  padding-left: 30rpx;
  font-size: 36rpx;
  font-weight: 600;
  align-items: center;
  z-index: 1;
  .back-text {
    font-size: 36rpx;
    display: flex;
    align-items: baseline;
    .text {
      margin-left: 25rpx;
    }
  }
}
#myMap {
  z-index: 0;
  width: 100vw;
  height: 100vh;
}
.head-maps {
  width: 100%;
  height: 100rpx;
  position: fixed;
  top: 120rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-sizing: border-box;
  z-index: 1;
  background: transparent;
  .search-wrap {
    width: 100%;
    height: auto;
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 20rpx;
    padding: 5rpx 25rpx;
    .head-maps-found {
      margin-right: 15rpx;
    }
    .cityTabs {
      font-size: 30rpx;
      font-weight: normal;
      line-height: 36rpx;
      text-align: center;
      letter-spacing: 0px;
      /* 蓝色 */
      color: #1e9cff;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .cityTabs .addre_cty {
      display: inline-block;
      width: 110rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .station-ul {
      margin: 0 24rpx;
      padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    }
  }
}
.controls-item {
  z-index: 1;
}
.popup-content {
  background: transparent;
  border-radius: 8rpx;
  .popup-item {
    width: 100%;
    font-size: 26rpx;
    font-weight: normal;
    line-height: 40px;
    letter-spacing: 0px;
    color: #333333;
    padding: 15rpx 25rpx;
    display: flex;
    align-items: center;
    image {
      width: 80rpx;
      height: 90rpx;
      margin-right: 15rpx;
    }
  }
  .popup-bottom {
    font-size: 36rpx;
    font-weight: 500;
    line-height: 43rpx;
    text-align: center;
    letter-spacing: 0.58rpx;
    padding: 10rpx 25rpx 25rpx 25rpx;
    /* 蓝色 */
    color: #1e9cff;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }
}
.popup-right-content {
  width: 400rpx;
  padding-top: 150rpx;
  .title {
    margin-bottom: 20rpx;
    margin-top: 20rpx;
    font-weight: 700;
    font-size: 28rpx;
    line-height: 42rpx;
    text-align: left;
    letter-spacing: 0px;
    color: #333333;
    padding-left: 25rpx;
  }
  .switch-wrap {
    padding: 0 25rpx;
  }
  .tab-wrap {
    width: 100%;
    display: flex;
    padding: 0 25rpx;
    flex-wrap: wrap;
    .tab-item {
      min-width: 152rpx;
      margin-bottom: 10rpx;
      height: 55rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15rpx;
      background: rgba(0, 0, 0, 0.05);
      border-radius: 26.5px;
      color: #333333;
      font-size: 26rpx;
    }
    .tab-active {
      color: #0d87e8;
      background: #e6f4ff;
    }
  }
}
.fotters {
  bottom: 67rpx !important;
  .tag-wrap {
    white-space: nowrap;
    width: 100%;
    .tag-item {
      display: inline-block;
      color: #498fc7;
      border: 1px solid #498fc7;
      border-radius: 20rpx;
      padding: 5rpx 15rpx;
      margin-right: 15rpx;
      max-width: 150rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 24rpx;
    }
  }
  .describe-wrap {
    padding: 15rpx;
    padding-bottom: 35rpx;
    display: flex;
    flex-wrap: wrap;
    gap: 15rpx;
    grid-gap: 15rpx;
    color: #acacac;
    font-size: 24rpx;
    .describe-item {
      display: flex;
      align-items: baseline;
      justify-content: space-between;
      .label {
        width: 130rpx;
        text-align: right;
      }
      .value {
        flex: 1;
        padding-left: 5rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .w50 {
      width: 48%;
    }
    .w100 {
      width: 100%;
    }
  }
}
</style>
