<template>
  <!-- pages/addCar.wxml -->
  <view>
    <view class="alls">
      <view class="addCar-msg" v-for="(item, index) in carList" :key="index">
        <view class="message">
          <view class="message-left">
            <image :src="item.modelImg || `${process.env.VUE_IMAGE_PATH_HOST}default-car.png`" />
          </view>
          <view class="message-right">
            <view class="message-name">{{ item.brandName }} {{ item.modelName }}</view>
            <view class="message-text">
              <text>{{ item.licenseNo }}</text>
            </view>
            <view v-if="item.contMileage && item.contMileage > 0">续航里程：{{ item.contMileage }}km</view>
            <view v-if="item.batCapacity">电池容量：{{ item.batCapacity }}kWh</view>
          </view>
        </view>

        <view class="addCar-delet">
          <view @tap="chack" :data-indexs="item.indexOfs" :data-id="item.prefId">
            <radio class="fr" :checked="item.checkeds"></radio>
            默认车辆
          </view>
          <view @tap="remove" :data-id="item.prefId">
            <image :src="`${IMG_PATH}56e1.png`" />
            删除
          </view>
        </view>
      </view>
    </view>
    <view class="add-button">
      <view @tap="add" class="add-button-bt styleColorB">添加车辆</view>
    </view>
  </view>
</template>

<script>
// pages/addCar.js
import { getPref, updateDefaultCar, deletePref } from '@/services/index';
export default {
  data() {
    return {
      x2: false,
      carList: [],
      list: [],
      a: false,
      b: false,
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */ onLoad(options) {
    this.init();
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.init();
  },
  methods: {
    add() {
      uni.navigateTo({
        url: '/subPackages/add/add',
      });
    },

    async chack(e) {
      var paramd = {
        prefId: e.currentTarget.dataset.id,
      };
      console.log(paramd, 'paramd');
      let that = this;
      const [, res] = await updateDefaultCar(paramd);
      if (res) {
        for (var i = 0; i < that.carList.length; i++) {
          this[a] = false;
        }
        var b = 'carList[' + e.currentTarget.dataset.indexs + '].checkeds';
        this[b] = true;
      } else {
      }
    },

    async remove(e) {
      console.log(e, '删除');
      console.log(e.currentTarget.dataset.id, '选择id');
      var paramd = {
        prefId: e.currentTarget.dataset.id,
      };
      let that = this;
      const [, res] = await deletePref(paramd);
      if (res) {
        this.init();
      } else {
        console.log('失败');
      }
    },

    async init() {
      var paramd = {};
      let that = this;
      const [, res] = await getPref(paramd);
      if (res) {
        if (res.carList.length > 0) {
          var cars = res.carList;
          for (var i = 0; i < res.carList.length; i++) {
            cars[i].checkeds = false;
            cars[i].indexOfs = i;
          }
          setTimeout(function () {
            cars[0].checkeds = true;
            that.setData({
              // list: res.carList[res.carList.length - 1]
              carList: cars,
              list: res.carList,
            });
          }, 200);
        } else {
          console.log('车都删除完了。');
          that.setData({
            carList: [],
            list: [],
          });
        }
      } else {
        console.log('失败');
      }
    },
  },
};
</script>
<style>
@import './addCar.css';
</style>
