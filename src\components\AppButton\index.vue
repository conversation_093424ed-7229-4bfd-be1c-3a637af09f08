<template>
  <button
    :class="[
      {
        'app-btn--plain': plain,
        'app-btn--disabled': disabled,
      },
      `app-btn--${shape}`,
      `app-btn--${type}`,
      `app-btn--${size}`,
      'app-reset-btn',
      'app-btn',
    ]"
    :style="style"
    hover-class="app-btn--hover"
    :size="size"
    @click.native.stop="handleClick"
  >
    <slot></slot>
  </button>
</template>

<script>
export default {
  name: 'index',
  props: {
    // 朴素样式
    plain: {
      type: Boolean,
      default: false,
    },
    // 禁用状态
    disabled: {
      type: Boolean,
      default: false,
    },
    // 按钮颜色
    color: {
      type: String,
      default: '',
    },
    // 自定义样式
    customStyle: {
      type: Object,
      default: () => {},
    },
    shape: {
      type: String,
      default: 'square',
    },
    type: {
      type: String,
      default: '',
    },
    size: {
      type: String,
      default: 'default',
    },
  },
  computed: {
    // 基础背景颜色
    style() {
      const style = {};
      if (this.color) {
        // 针对自定义了color颜色的情况，镂空状态下，就是用自定义的颜色
        style.color = this.plain ? this.color : 'white';
        if (!this.plain) {
          // 非镂空，背景色使用自定义的颜色
          style['background-color'] = this.color;
        }
        if (this.color.indexOf('gradient') !== -1) {
          // 如果自定义的颜色为渐变色，不显示边框，以及通过backgroundImage设置渐变色
          style['border-width'] = 0;
          if (!this.plain) {
            style['background-image'] = this.color;
          }
        } else {
          // 非渐变色，则设置边框相关的属性
          style['border-color'] = this.color;
          style['border-width'] = '1px';
          style['border-style'] = 'solid';
        }
      }
      let obj = { ...style, ...this.customStyle };
      let styleStr = '';
      for (const key in obj) {
        styleStr += `${key}: ${obj[key]};`;
      }
      return styleStr;
    },
  },
  methods: {
    // 点击事件
    handleClick() {
      this.$emit('click');
    },
  },
};
</script>

<style scoped lang="scss">
// 去除button的所有默认样式，让其表现跟普通的view、text元素一样
.app-reset-btn {
  padding: 0;
  background-color: transparent;
}
.app-btn {
  padding-top: 30rpx;
  padding-bottom: 30rpx;
  line-height: 38rpx;
  font-size: 32rpx;
  text-align: center;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  flex: 1;
  &--hover {
    opacity: 0.7;
  }
  &--disabled {
    opacity: 0.4;
  }
  &--plain {
    background-color: #fff;
    border-width: 1rpx;
    border-style: solid;
    color: #333333;
  }
  &--square {
    border-radius: 16rpx;
  }
  &--circle {
    border-radius: 50vw;
  }
  &--primary {
    color: #fff;
    border-color: #1e9cff;
    background-color: #1e9cff;

    &.app-btn--plain {
      color: #1e9cff;
      border-color: #1e9cff;
    }
  }
  &--mini {
    padding: 15rpx 44rpx;
    line-height: 26rpx;
    font-size: 26rpx;
    &.app-btn--square {
      border-radius: 8rpx;
    }
  }
}
</style>
