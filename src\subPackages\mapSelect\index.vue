<template>
  <view class="map-select-container">
    <!-- <view class="map-header">
      <view class="back-btn" @click="goBack">
        <text class="icon iconfont icon-arrow_left" size="18"></text>
        <text>返回</text>
      </view>
      <view class="header-title">选择位置</view>
    </view> -->
    <!-- 地图组件 - 微信小程序 -->
    <!-- #ifdef MP -->
    <map
      id="selectMap"
      class="map"
      :longitude="longitude"
      :latitude="latitude"
      :scale="scale"
      show-location
      @regionchange="onRegionChange"
    >
      <!-- 中心点标记 -->
      <cover-image class="center-marker" :src="`${IMG_PATH}icon-station-location.png`"></cover-image>
    </map>
    <!-- #endif -->

    <!-- 地图组件 - H5 -->
    <!-- #ifdef H5 -->
    <view id="selectMap" class="map">
      <!-- H5环境下使用高德地图的marker，不需要固定标记 -->
    </view>
    <!-- #endif -->

    <view class="location-info">
      <view class="location-text">
        <text>当前位置：</text>
        <text v-if="isLoading" class="loading-text">正在获取地址...</text>
        <text v-else>{{ locationText }}</text>
      </view>
      <view class="drag-hint">
        <!-- #ifdef MP -->
        <text class="hint-text">提示：拖动地图调整位置，中心点即为所选位置</text>
        <!-- #endif -->
        <!-- #ifdef H5 -->
        <text class="hint-text">提示：拖动标记选择位置</text>
        <!-- #endif -->
      </view>
    </view>

    <view class="confirm-btn" @click="confirmLocation">确认选择</view>
  </view>
</template>

<script>
import { getLocation } from '@/utils/index.js';
import { defaultLocationInfo } from '@/config/declare.js';
import { getGeocoderLocation } from '@/services/index';

// #ifdef H5
import AMapLoader from '@amap/amap-jsapi-loader';
// #endif

export default {
  data() {
    return {
      longitude: 0,
      latitude: 0,
      scale: 14,
      markers: [],
      locationText: '未选择位置',
      selectedLocation: null,
      mapCtx: null,
      // H5地图对象
      mapControl: null,
      marker: null,
      isLoading: false,
      isDragging: false,
      addressInfo: null,
      // 图片路径
    };
  },
  onLoad(options) {
    // 检查是否有传入的位置参数
    console.log('onLoad', options);
    if (options && options.latitude && options.longitude) {
      // 使用传入的位置参数
      this.latitude = parseFloat(options.latitude);
      this.longitude = parseFloat(options.longitude);
      // 直接初始化地图
      this.initMap();
    } else {
      // 没有传入位置参数，获取当前位置
      this.initLocation();
    }
  },
  onReady() {
    // #ifdef MP
    this.mapCtx = uni.createMapContext('selectMap');
    // #endif
  },
  methods: {
    // 初始化地图（已知位置）
    initMap() {
      // 设置初始标记
      this.updateMarker(this.latitude, this.longitude);

      // H5环境初始化地图
      // #ifdef H5
      this.initH5map();
      // #endif
    },

    // 获取当前位置并初始化地图
    async initLocation() {
      try {
        // 获取当前位置，使用优化后的getLocation函数
        console.log('获取当前位置');

        // 显示加载提示
        uni.showLoading({
          title: '获取位置中...',
          mask: true,
        });

        // 调用优化后的getLocation函数，传入配置选项
        const location = await getLocation({
          showPermissionDialog: true, // 显示权限请求对话框
          timeout: 15000, // 增加超时时间到15秒
          enableHighAccuracy: true, // 启用高精度
          useIPFallback: true, // 使用IP定位作为备选
          useDefaultFallback: true, // 使用默认位置作为最后备选
        });

        uni.hideLoading();

        // 如果获取到位置信息
        if (location) {
          console.log('获取到位置信息:', location);
          this.longitude = location.longitude;
          this.latitude = location.latitude;

          // 如果是低精度位置，显示提示
          if (location.accuracy === 'low') {
            uni.showToast({
              title: '当前使用的是大致位置，精确度较低',
              icon: 'none',
              duration: 3000,
            });
          }

          // 初始化地图
          this.initMap();
        } else {
          // 使用默认位置
          console.log('未获取到位置信息，使用默认位置');
          this.longitude = defaultLocationInfo.longitude;
          this.latitude = defaultLocationInfo.latitude;
          this.initMap();

          uni.showToast({
            title: '无法获取位置，使用默认位置',
            icon: 'none',
            duration: 2000,
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.log('初始化位置失败:', error);

        uni.showToast({
          title: '获取位置信息失败',
          icon: 'none',
        });

        // 使用默认位置
        this.longitude = defaultLocationInfo.longitude;
        this.latitude = defaultLocationInfo.latitude;
        this.initMap();
      }
    },

    // 更新地图标记
    updateMarker(latitude, longitude) {
      // 微信小程序环境
      // #ifdef MP
      this.markers = [
        {
          id: 1,
          latitude: latitude,
          longitude: longitude,
          width: 32,
          height: 32,
          iconPath: `${this.IMG_PATH}icon-station-location.png`, // 确保有这个图标
          anchor: {
            x: 0.5,
            y: 1.0,
          },
          callout: {
            content: '所选位置',
            color: '#000000',
            fontSize: 14,
            borderRadius: 3,
            borderWidth: 1,
            borderColor: '#CCCCCC',
            bgColor: '#FFFFFF',
            padding: 5,
            display: 'ALWAYS',
          },
        },
      ];
      // #endif

      // H5环境 - 使用可拖动的marker
      // #ifdef H5
      if (this.mapControl) {
        if (!this.marker) {
          // 创建可拖动的marker
          this.marker = new AMap.Marker({
            position: [longitude, latitude],
            draggable: true,
            cursor: 'move',
            icon: new AMap.Icon({
              image: `${this.IMG_PATH}icon-station-location.png`,
              size: new AMap.Size(32, 32),
              imageSize: new AMap.Size(32, 32),
            }),
            offset: new AMap.Pixel(-16, -32), // 使标记底部对准选择点
          });
          console.log([longitude, latitude], this.marker);

          this.marker.setMap(this.mapControl);

          // 添加拖拽结束事件监听
          this.marker.on('dragend', () => {
            const position = this.marker.getPosition();
            this.latitude = position.lat;
            this.longitude = position.lng;
            this.updateLocationInfo(position.lat, position.lng);
          });
        } else {
          // 更新marker位置
          this.marker.setPosition([longitude, latitude]);
        }

        // 移动地图到指定位置
        this.mapControl.setCenter([longitude, latitude]);
      }
      // #endif

      this.updateLocationInfo(latitude, longitude);
    },

    // 更新位置信息
    updateLocationInfo(latitude, longitude) {
      this.selectedLocation = { latitude, longitude };

      // 获取地址信息
      this.getAddressFromLocation(latitude, longitude);
    },

    // 根据经纬度获取地址信息
    async getAddressFromLocation(latitude, longitude) {
      if (this.isDragging) return;

      this.isLoading = true;
      this.locationText = '正在获取地址...';

      try {
        // 调用地址解析服务 - 百度地图API需要经度在前，纬度在后
        const [err, res] = await getGeocoderLocation({
          location: `${latitude},${longitude}`,
          // output: 'json', // 确保返回JSON格式
          // ak: '4ErNWWeUHXTAlUe0jXktF5OHeXjNswSb', // 使用项目中已有的百度地图key
        });

        console.log('地址解析结果:', res);

        // 百度地图API返回格式处理
        if (res && res.data && res.data.status === 0 && res.data.result) {
          const addressInfo = res.data.result;
          this.addressInfo = addressInfo;

          // 百度地图API返回的地址信息格式
          let formattedAddress =
            addressInfo.formatted_address ||
            addressInfo.sematic_description ||
            (addressInfo.addressComponent
              ? [
                  addressInfo.addressComponent.province,
                  addressInfo.addressComponent.city,
                  addressInfo.addressComponent.district,
                  addressInfo.addressComponent.street,
                  addressInfo.addressComponent.street_number,
                ]
                  .filter(Boolean)
                  .join('')
              : '');

          if (formattedAddress) {
            this.locationText = formattedAddress;
          } else {
            // 如果地址为空或未知，显示经纬度
            this.locationText = `经度: ${longitude.toFixed(6)}, 纬度: ${latitude.toFixed(6)}`;
          }
        } else {
          // 无法获取地址信息时，显示经纬度坐标
          this.locationText = `经度: ${longitude.toFixed(6)}, 纬度: ${latitude.toFixed(6)}`;
        }
      } catch (error) {
        console.error('获取地址信息失败:', error);
        // 获取地址失败时，显示经纬度坐标
        this.locationText = `经度: ${longitude.toFixed(6)}, 纬度: ${latitude.toFixed(6)}`;
      } finally {
        this.isLoading = false;
      }
    },

    // 地图区域变化事件
    onRegionChange(e) {
      // 微信小程序环境
      // #ifdef MP
      if (e.type === 'begin' && e.causedBy === 'drag') {
        // 开始拖动
        this.isDragging = true;
        this.locationText = '正在移动地图...';
      } else if (e.type === 'end' && e.causedBy === 'drag') {
        // 结束拖动
        this.isDragging = false;

        // 获取地图中心点位置
        this.mapCtx.getCenterLocation({
          success: (res) => {
            const { latitude, longitude } = res;
            this.latitude = latitude;
            this.longitude = longitude;
            this.updateLocationInfo(latitude, longitude);
          },
        });
      }
      // #endif
    },

    // 确认选择位置
    confirmLocation() {
      if (!this.selectedLocation) {
        uni.showToast({
          title: '请先选择位置',
          icon: 'none',
        });
        return;
      }

      // 返回到add.vue页面，并传递选中的位置信息
      const { latitude, longitude } = this.selectedLocation;

      // 判断locationText是否为经纬度格式（如果地址解析失败）
      const isCoordinates =
        this.locationText && this.locationText.includes('经度:') && this.locationText.includes('纬度:');

      // 如果是经纬度格式，则使用格式化的经纬度字符串；否则使用地址文本
      const locationStr = isCoordinates
        ? `${latitude.toFixed(6)},${longitude.toFixed(6)}`
        : this.locationText || `${latitude.toFixed(6)},${longitude.toFixed(6)}`;

      uni.navigateBack({
        success: () => {
          // 使用事件通信将数据传回上一页
          uni.$emit('locationSelected', {
            location: locationStr,
            latitude,
            longitude,
            address: isCoordinates ? `${latitude.toFixed(6)},${longitude.toFixed(6)}` : this.locationText,
          });
        },
      });
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // H5 初始化地图
    // #ifdef H5
    initH5map() {
      console.log('加载高德地图');
      AMapLoader.load({
        key: '5a4fdefa142b1d9cf9d6d0185ff08bdc', // 使用项目中已有的高德地图key
        securityJsCode: '959b1ff8744de7bba975abd222a728c3',
        version: '2.0',
        plugins: ['AMap.Geolocation'],
      })
        .then((AMap) => {
          // 创建地图实例
          this.mapControl = new AMap.Map('selectMap', {
            zoom: this.scale,
            center: [this.longitude, this.latitude],
            viewMode: '2D',
            resizeEnable: true,
          });

          // 初始化位置信息和标记
          this.updateMarker(this.latitude, this.longitude);

          // 添加提示信息
          uni.showToast({
            title: '拖动标记选择位置',
            icon: 'none',
            duration: 2000,
          });
        })
        .catch((e) => {
          console.error('高德地图加载失败:', e);
          // 显示错误提示
          uni.showToast({
            title: '地图加载失败，请重试',
            icon: 'none',
            duration: 2000,
          });
        });
    },
    // #endif
  },
};
</script>

<style lang="scss" scoped>
.map-select-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
}

.map-header {
  height: 88rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  background-color: #fff;
  position: relative;
  z-index: 10;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.back-btn {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
}

.map {
  flex: 1;
  width: 100%;
  position: relative;
}

.center-marker {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 32px;
  height: 32px;
  margin-left: -16px;
  margin-top: -32px; /* 使标记底部对准中心点 */
  z-index: 100;
}

.center-marker-container {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000; /* 确保在地图上层 */
}

.center-marker-h5 {
  width: 32px;
  height: 32px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -100%); /* 使标记底部对准中心点 */
  z-index: 1000;
  pointer-events: none;
}

.location-info {
  padding: 20rpx 30rpx;
  background-color: #fff;
}

.location-text {
  font-size: 28rpx;
  line-height: 1.5;
}

.drag-hint {
  margin-top: 10rpx;
}

.hint-text {
  font-size: 24rpx;
  color: #1e9cff;
  line-height: 1.5;
}

.confirm-btn {
  margin: 30rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background-color: #1e9cff;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
}
</style>
