
/********************js验证方法**************************/

/**
 * 
 * @desc   判断`obj`是否为空
 * @param  {Object} obj
 * @return {Boolean}
 */
export const isEmptyObject = (obj) => {
    if (!obj || typeof obj !== 'object' || Array.isArray(obj))
        return false
    return !Object.keys(obj).length
}
/**
* 检测手机号码
* @param num
* @returns {*}
*/
export const isMobile = (num) => {
    // const re = /^1[3,4,5,7,8,9]\d{9}$/;
    const re = /^[0-9]{7,11}$/;
    return re.test(num);
}
/**
 * 检测座机号码
 * @param num
 * @returns {*}
 */
export const isTelephone = (num) => {
    const phoneReg = /(^\+86\.\d{3,5}\d{6,8}$)|(^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$)/;
    return phoneReg.test(num);
}

/**
 * 检测联系方式
 * @param num
 * @returns {*}
 */
export const isPhone = (num) => {
    return isMobile(num) && isTelephone(num);
}
/**
 * 校验是否字符串包含中文字符
 *
 * @returns
 */
export const containChinese = (str) => {
    const re = /[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi;
    return re.test(str);
}
/**
 * 判断是否为URL地址
 *
 * @returns
 */
export const isUrl = (str) => {
    const re = /[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/i;
    return re.test(str);
}
/**
 * 
 * @desc  判断是否为身份证号
 * @param  {String|Number} str 
 * @return {Boolean}
 */
export const isIdCard = (str) => {
    return /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/.test(str)
}
/**
 * 
 * @desc   判断是否为邮箱地址
 * @param  {String}  str
 * @return {Boolean} 
 */
export const isEmail = (str) => {
    return /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/.test(str);
}

/** 
 * 判断是否数值
*/
export const isNumber = (str) => {
    return /^\d*\.{0,1}\d*$/.test(str)
}


/************************************************************** */
let TraversalObject = (obj, o = {}, n = 0) => {
    for (let key in obj) {
        if (typeof (obj[key]) === "object") {
            n++
            TraversalObject(obj[key], o, n)
        }
        else {
            o[n + key] = obj[key]
        }
    }
    return o
}
/**
 * 判断两个对象是否值相等
 * @param {*} a 
 * @param {*} b 
 */
export const isObjectEqual = (a, b) => JSON.stringify(Object.assign({}, TraversalObject(a), TraversalObject(b))) === JSON.stringify(TraversalObject(a)) ? true : false;
