import { request as __request } from '../request/request';
import { ENVR, scanBaseurl, defBaseurl, baseBaseurl } from '../../config/global';
const baseurl = ENVR === 'wx' ? scanBaseurl : defBaseurl;

// 查询积分流水
export const getIntegral = (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${baseurl}/v0.1/integral`,
      data: params,
    }
  );
};
// 查询商品列表
export const getGoods = (params, config = {}) => {
  return __request(
    { ...config },
    {
      url: `${baseurl}/v0.1/goods`,
      method: 'POST',
      data: params,
    }
  );
};

// 查询兑换记录
export const getExchangeList = (params, config = {}) => {
  return __request(
    { ...config },
    {
      url: `${baseurl}/v0.1/exchangeList`,
      method: 'POST',
      data: params,
    }
  );
};

// 查询优惠券兑换说明
export const getCouponIntr = (params, config = {}) => {
  return __request(
    { ...config },
    {
      url: `${baseurl}/v0.1/standardCode?paraCode=goodsVrType`,
      method: 'GET',
      data: params,
    }
  );
};
// 查询标准代码
export const getStandardCode = (code, config = {}) => {
  return __request(
    { ...config },
    {
      url: `${baseurl}/v0.1/standardCode?paraCode=${code}`,
      method: 'GET',
    }
  );
};

// 查询商品详情
export const getGoodsDetail = (params, config = {}) => {
  return __request(
    { ...config },
    {
      url: `${baseurl}/v0.1/goodsDetail`,
      method: 'POST',
      data: params,
    }
  );
};

// 兑换商品
export const exchangeGoods = (params, config = {}) => {
  return __request(
    { ...config },
    {
      url: `${baseurl}/v0.1/exchange`,
      method: 'POST',
      data: params,
    }
  );
};

// 兑换订单详情
export const getExchangeDetail = (params, config = {}) => {
  return __request(
    { ...config },
    {
      url: `${baseurl}/v0.1/exchangeDetail`,
      method: 'POST',
      data: params,
    }
  );
};
// 商品评价
export const shopOrderEvaApi = (params, config = {}) => {
  return __request(
    { ...config },
    {
      url: ENVR === 'wx' ? `${scanBaseurl}/v0.1/order-eva` : `${baseBaseurl}/v0.3/order-eva`,
      method: 'POST',
      data: params,
    }
  );
};
// 商品评价图片上传
export const shopOrderEvaImgApi = (params, config = {}) => {
  return __request(
    { ...config },
    {
      url: ENVR === 'wx' ? `${scanBaseurl}/v0.1/file-upload` : `${baseBaseurl}/v0.1/file-upload`,
      method: 'POST',
      data: params,
    }
  );
};
