<template>
  <div class="wrap">
    <!-- #ifdef MP-WEIXIN -->
    <view
      class="banner"
      :style="'padding-top:' + s_top + 'rpx;height:' + s_height + 'rpx;line-height:' + s_height + 'rpx'"
    >
      <image class="banner-img" @click="goBack" mode="widthFix" :src="`${IMG_PATH}back.png`"></image>
      <view class="banner-title">累计节省</view>
    </view>
    <!-- #endif -->

    <div class="content">
      <div class="user-info">
        <div class="user-logo">
          <image :src="userInfo.imgUrl || `${IMG_PATH}defaultAvatar.png`" />
        </div>
        <div class="info">
          <div class="phone">{{ desensitization(userInfo.mobile || '-') }}</div>
          <div class="text">
            自{{ accumulateDate.startDate || '' }}起，已尊享PLUS会员服务
            <span>{{ accumulateDate.countDays || 0 }}</span> 天
          </div>
        </div>
      </div>

      <div class="tab-wrap">
        <div class="tab-item" :class="{ active: active == '0' }" @click="changeActive('0')">月度省钱</div>
        <div class="tab-item" :class="{ active: active == '1' }" @click="changeActive('1')">累计省钱</div>
      </div>
      <div class="chart-wrap">
        <div class="border"></div>
        <div class="time-select" @click.stop="openDate" v-if="active == '0'">
          <div class="date-select">
            <span>{{ date.startDate || '' }}</span>
            <span class="center">--</span>
            <span>{{ date.endDate || '' }}</span>
            <div class="icon">
              <image :src="`${IMG_PATH}member/member-icon/icon-date.png`" mode="widthFix" />
            </div>
          </div>
        </div>
        <div class="time-select" v-if="active == '1'">
          <div class="date-select">
            <span>{{ accumulateDate.startDate || '' }}</span>
            <span class="center">--</span>
            <span>{{ accumulateDate.endDate || '' }}</span>
            <div class="icon">
              <image :src="`${IMG_PATH}member/member-icon/icon-date.png`" mode="widthFix" />
            </div>
          </div>
        </div>
        <div class="info">
          <div class="info-text">
            累计节省
            <span v-if="active == '0'">{{ monthSaveMoney || 0 }}</span>
            <span v-if="active == '1'">{{ saveMoney || 0 }}</span
            >元
          </div>
          <!-- <div class="info-tip">最多选择8个月的省钱记录</div> -->
        </div>
        <div class="chart">
          <chart ref="echarts" :chartData="chartData" v-if="active == '0'"></chart>
          <pieChart v-if="active == '1'" :chartData="pieChartData"></pieChart>
        </div>
        <div class="table-wrap">
          <div class="table-box">
            <div class="th-wrap">
              <div class="th-item time">时间</div>
              <div class="th-item">现金券</div>
              <div class="th-item">折扣券</div>
              <div class="th-item">会员折扣</div>
              <div class="th-item discount">会员折上折</div>
            </div>
            <div class="tr-wrap">
              <div class="th-wrap" v-for="(item, index) in tableData" :key="index">
                <div class="th-item num time">{{ item.time === -1 ? '-' : '2024-12-12' }}</div>
                <div class="th-item num">{{ item.cashAmt || '-' }}</div>
                <div class="th-item num">{{ item.discountAmt || '-' }}</div>
                <div class="th-item num">{{ item.vipDiscount || '-' }}</div>
                <div class="th-item num discount">{{ item.vipDiscountUp || '-' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <dateRange ref="dateRange" @change="changeDate"></dateRange>
    </div>
  </div>
</template>

<script>
import dateRange from './dateRange.vue';
import { qryEffectTime, getMemberInfo, getSaveRecordByMobile, getUserSaveRecord } from '@/services/index.js';
import chart from './chart.vue';
import pieChart from './pieChart.vue';
import moment from 'moment';
import { mapState } from 'vuex';
const defaultTableData = [
  {
    time: -1,
    cashAmt: '',
    discountAmt: '',
    vipDiscount: '',
    vipDiscountUp: '',
  },
  {
    time: -1,
    cashAmt: '',
    discountAmt: '',
    vipDiscount: '',
    vipDiscountUp: '',
  },
  {
    time: -1,
    cashAmt: '',
    discountAmt: '',
    vipDiscount: '',
    vipDiscountUp: '',
  },
];
export default {
  props: {},
  components: { chart, dateRange, pieChart },
  data() {
    return {
      s_top: '',
      s_height: '',

      tableData: defaultTableData,
      dateTime: [moment(), moment()],
      // dateTime: ['2024-10', '2024-12'],
      canvaLineA: null,
      chartData: {
        categories: [],
        series: [],
      },
      memberInfo: {},
      active: '0',
      accumulateDate: {
        startDate: '',
        endDate: '',
        countDays: 0,
      },
      pieChartData: {},
      saveMoney: 0,
      monthSaveMoney: 0,
    };
  },
  computed: {
    ...mapState({
      userInfoOBJ: (state, getters) => getters['login/getUserInfo'],
    }),
    userInfo() {
      return {
        imgUrl: '',
        ...(this.userInfoOBJ ? this.userInfoOBJ : {}),
      };
    },
    date() {
      return {
        startDate: this.dateTime.length > 1 ? moment(this.dateTime[0]).format('yyyy-MM') : '',
        endDate: this.dateTime.length > 1 ? moment(this.dateTime[1]).format('yyyy-MM') : '',
      };
    },
  },
  onLoad() {},
  onShow() {
    // #ifdef MP
    this.initTopImg();
    // #endif
  },
  onReady() {
    this.loadData();
    // 用户信息
    this.getUserInfo();
    // 会员信息
    this.getMemberInfo();
    // 获取累计省钱数据
    this.loadPieData();
    // 获取会员日期
    this.qryEffectTime();
  },
  mounted() {},
  methods: {
    // 获取会员有效期
    async qryEffectTime() {
      const [, res] = await qryEffectTime();
      console.log(res, 'res');
      if (res) {
        this.accumulateDate = {
          startDate: res.startTime || '',
          endDate: res.endTime || '',
          countDays: res.countDays || 0,
        };
      }
    },
    changeActive(type) {
      this.active = type;
      if (type == '0') {
        this.loadData();
      }
    },
    // 获取会员信息
    async getMemberInfo() {
      const params = {
        plusEquity: '1',
      };
      const [err, res] = await getMemberInfo(params);
      if (res) {
        this.memberInfo = res;
      }
    },
    // 脱敏
    desensitization(phone) {
      if (!phone) return;
      // 检查手机号格式
      const phoneRegex = /^(\d{3})\d{4}(\d{4})$/;
      // 如果手机号符合格式，进行脱敏处理
      return phone.replace(phoneRegex, '$1****$2');
    },
    // 获取用户信息
    getUserInfo() {
      this.$store.dispatch('login/getUserInfoCallback');
    },
    goBack() {
      // #ifdef H5
      this.$router.go(-1);
      // #endif
      // #ifdef MP-WEIXIN
      uni.navigateBack({
        delta: -1,
      });
      // #endif
    },
    initTopImg() {
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      this.setData({
        s_top: menuButtonInfo?.top * 2,
        s_height: menuButtonInfo?.height * 2,
      });
    },
    openDate() {
      const dateList = this.dateTime.map((q) => moment(q).format('yyyy-MM'));
      this.$refs.dateRange && this.$refs.dateRange.open(dateList);
      // this.$refs.date && this.$refs.date.show(dateList);
    },
    changeDate(date) {
      this.dateTime = date.map((q) => moment(q).format('yyyy-MM'));
      this.loadData();
    },
    // 获取月度省钱数据
    async loadData() {
      if (this.dateTime.length > 1) {
        uni.showLoading({
          title: '加载中',
        });
        const [, res] = await getSaveRecordByMobile({
          startTime: this.dateTime[0] ? moment(this.dateTime[0]).format('yyy-MM') : '',
          endTime: this.dateTime[1] ? moment(this.dateTime[1]).format('yyy-MM') : '',
        });
        uni.hideLoading();
        if (res) {
          const list = res.saveRecords || [];
          this.tableData = list && list.length > 0 ? list : defaultTableData;
          this.monthSaveMoney = parseFloat(
            list
              .reduce((cur, nex) => {
                cur += nex.totalSave || 0;
                return cur;
              }, 0)
              .toFixed(2)
          );
          console.log(this.monthSaveMoney, 'this.monthSaveMoney');
          this.chartData = {
            categories: list.map((q) => q.payMonth || ''),
            series: [
              {
                name: '月度省钱',
                data: list.map((q) => q.totalSave || 0),
              },
            ],
          };
        } else {
          this.reset();
        }
      } else {
        this.reset();
      }
    },
    async loadPieData() {
      uni.showLoading({
        title: '加载中',
      });
      const [err, res] = await getUserSaveRecord({});
      uni.hideLoading();
      if (res) {
        this.saveMoney = res?.saveMoney || 0;
        let data = [
          { name: '会员折扣', value: res.vipSaveMoney || 0, labelText: '会员折扣' },
          { name: '卡券优惠', value: res.cardsSaveMoney || 0, labelText: '卡券优惠' },
        ];
        // 计算总值
        let totalValue = data.reduce((sum, item) => sum + item.value, 0);

        // 计算每一项的原始百分比（保留两位小数）
        data.forEach((item) => {
          item.percentage = ((item.value / totalValue) * 100).toFixed(2);
        });

        // 计算所有百分比的总和
        let totalPercentage = data.reduce((sum, item) => sum + parseFloat(item.percentage), 0);

        // 计算差值，确保总和为100%
        let discrepancy = (100 - totalPercentage).toFixed(2);

        // 调整差值到最小的项，确保百分比之和为100%
        if (discrepancy !== 0) {
          let minItem = data.reduce(
            (min, item) => (parseFloat(item.percentage) < parseFloat(min.percentage) ? item : min),
            data[0]
          );
          minItem.percentage = (parseFloat(minItem.percentage) + parseFloat(discrepancy)).toFixed(2);
        }
        console.log(data, 'data');
        this.pieChartData = {
          series: [
            {
              data: data.map((q) => {
                return {
                  ...q,
                  labelText: `${q.labelText}${isNaN(q.percentage) ? 0 : q.percentage}%`,
                };
              }),
            },
          ],
        };
      }
    },
    reset() {
      this.tableData = defaultTableData;
      this.monthSaveMoney = 0;
      this.chartData = {
        categories: [],
        series: [],
      };
    },
  },
};
</script>

<style scoped lang="scss">
.wrap {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(166deg, #504a51 -2%, #1e1f28 30%, #1e1f28 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  .banner {
    padding: 20rpx 20rpx 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
    z-index: 2;
    box-sizing: border-box;
    color: #fff;
    .banner-title {
      font-family: 'PingFang SC';
      font-size: 36rpx;
      font-style: normal;
      font-weight: 500;
      flex: 1;
      text-indent: 4rpx;
      color: #000000;
      font-weight: 700;
      text-align: left;
      color: #fff;
    }
    .banner-img {
      width: 48rpx;
      height: 48rpx;
      margin-right: 20rpx;
    }
  }
  .content {
    flex: 1;
    padding: 50rpx 32rpx 32rpx 32rpx;
    width: 100%;
    box-sizing: border-box;
    .user-info {
      width: 638rpx;
      height: 107rpx;
      display: flex;
      box-sizing: border-box;
      padding: 20rpx 0;
      .user-logo {
        width: 77rpx;
        height: 77rpx;
        border-radius: 50%;
        overflow: hidden;
        image {
          width: 100%;
          height: 100%;
        }
      }
      .info {
        flex: 1;
        padding-left: 20rpx;
        .phone {
          font-size: 32rpx;
          font-weight: 600;
          color: #ffe3c1;
          margin-bottom: 15rpx;
        }
        .text {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 24rpx;
          color: #fff;
          span {
            color: #ffe3c1;
          }
        }
      }
    }
    .tab-wrap {
      margin-top: 97rpx;
      display: flex;
      flex-direction: row;
      justify-content: center;
      gap: 40rpx;
      grid-gap: 40rpx;
      box-sizing: border-box;
      .tab-item {
        font-size: 32rpx;
        color: rgba(255, 255, 255, 0.8);
        &.active {
          color: #ffe3c1;
          position: relative;
          &::before {
            content: '';
            position: absolute;
            width: 50%;
            left: 50%;
            bottom: -15rpx;
            height: 5rpx;
            border-radius: 5rpx;
            transform: translateX(-50%);
            background: #ffe3c189;
          }
        }
      }
    }
    .chart-wrap {
      margin-top: 30rpx;
      width: 702rpx;
      padding: 100rpx 24rpx;
      position: relative;
      border-radius: 24rpx; /* 圆角 */
      overflow: hidden;
      background: #282934;
      box-sizing: border-box;
      .border {
        position: absolute; /* 绝对定位 */
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border: 2px solid;
        z-index: 0;
        border-image: linear-gradient(
            270deg,
            rgba(241, 209, 190, 0) 0%,
            rgba(241, 209, 190, 0.16) 30%,
            #f0cebc 48%,
            rgba(235, 186, 160, 0.2) 70%,
            rgba(235, 186, 160, 0) 100%
          )
          3;
      }
      .time-select {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        margin: 10rpx 0;
        color: #fff;
        z-index: 2;
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        box-sizing: border-box;
        .date-select {
          padding: 8rpx 30rpx;
          border-radius: 100rpx;
          opacity: 1;
          background: rgba(234, 177, 147, 0.2);
          display: flex;
          align-items: center;
          gap: 10rpx;
          grid-gap: 10rpx;
          span {
            font-size: 24rpx;
            color: #ffffff;
          }
          .center {
            color: #72696c;
            font-weight: 800;
          }
          .icon {
            width: 32rpx;
            height: 32rpx;
            image {
              width: 100%;
            }
          }
        }
      }
      .info {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        z-index: 2;
        .info-text {
          font-size: 24rpx;
          color: #ffffff;
          span {
            color: #ffe3c1;
          }
        }
        .info-tip {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
        }
      }
      .chart {
        width: 100%;
        height: 450rpx;
        z-index: 2;
        .charts {
          width: 100%;
          height: 100%;
        }
      }
      .table-wrap {
        border-radius: 24rpx;
        opacity: 1;
        box-sizing: border-box;
        width: 100%;
        margin: 0 auto;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 16rpx;
        grid-gap: 16rpx;
        justify-content: space-around;
        margin-bottom: 10rpx;
        position: relative;
        overflow: hidden;
        .table-box {
          border: 1px solid #e8ac8d;
          border-radius: 24rpx;
          width: 100%;
          .tr-wrap {
            width: 100%;
            height: auto;
            max-height: 300rpx;
            overflow-y: auto;
          }
          .th-wrap {
            width: 100%;
            box-sizing: border-box;
            display: flex;
            border-bottom: 1px solid #e8ac8d;
            .th-item {
              flex: 1;
              max-width: 33%;
              display: flex;
              justify-content: center;
              align-items: center;
              font-size: 20rpx;
              color: #e8ac8d;
              padding: 18rpx 10rpx;
              border-right: 1px solid #e8ac8d;
              overflow: hidden;
              width: 15%;
              white-space: nowrap;
              text-overflow: ellipsis;
              &:last-child {
                border-right: none;
              }
              &.num {
                color: #fff;
              }
              &.time {
                width: 30%;
                white-space: nowrap;
              }
              &.discount {
                width: 26%;
              }
            }
            &:last-child {
              border-bottom: none;
            }
          }
        }
      }
    }
  }
}
</style>
