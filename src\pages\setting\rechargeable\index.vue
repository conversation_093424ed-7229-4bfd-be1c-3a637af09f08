<template>
  <div class="mine-page">
    <div class="card">
      <span class="money">{{ total }}</span>
      <span>总余额 (元)</span>
    </div>
    <!-- <div class="tab-content">
      <view class="myorder-tab">
        <div class="tab-item" :class="' ' + (currentTab == 0 ? 'active' : '')" @click="swichNav('0')">可用卡</div>
        <text>|</text>
        <div class="tab-item" :class="' ' + (currentTab == 1 ? 'active' : '')" @click="swichNav('1')">不可用卡</div>
      </view>
    </div> -->
    <div class="card-list">
      <div
        class="card-item"
        v-for="(item, index) in cardList"
        :key="index"
        :class="{ active: item.cardDetailStatus == '1' }"
      >
        <div class="card-title">
          <div class="card-title-text">{{ item.cardName || '--' }}</div>
          <div class="card-title-btn" @click="lookDetail(item)">使用明细</div>
        </div>
        <div class="card-content">
          <div class="card-money">
            余额：￥<span v-if="item.cardDetailStatus != '3'" class="card-money-text">{{
              item.totalBalance || 0
            }}</span>
            <template v-else>
              <span class="card-money-text"
                >0.00
                <span style="font-size: 30rpx; margin-left: 10rpx; text-decoration: line-through">{{
                  item.totalAmt || 0
                }}</span></span
              >
            </template>
          </div>
          <div class="card-tip">
            <span>卡号: {{ item.cardNo || '' }}</span>
            <span>面值: {{ item.totalAmt || '' }}</span>
          </div>
        </div>
        <template v-if="item.cardDetailStatus != '1'">
          <div class="tip-text">{{ item.cardDetailStatusName || '' }}</div>
          <!-- <div class="modal"></div> -->
        </template>
      </div>
    </div>
    <div class="btn-item-blue" @click="lookAgreement">使用规则</div>
    <div class="btn-wrap">
      <!-- <div class="btn-item-vertical-line">|</div> -->
      <div class="btn-item" @click="pointsClick">积分购卡</div>
      <div class="btn-item-vertical-line">|</div>
      <div class="btn-item" @click="passwordClick">卡密绑卡</div>
    </div>
  </div>
</template>

<script>
import { myRechargeCards } from '@/services/index';
export default {
  props: {},
  components: {},
  data() {
    return {
      currentTab: '0',
      cardList: [],
      total: 0,
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.loadData();
  },
  methods: {
    lookAgreement() {
      uni.navigateTo({
        url: `/pages/setting/agreement/index?&code=0415&title=${'《使用规则》'}`,
      });
    },
    lookDetail(item) {
      uni.navigateTo({
        url: `/pages/basic/myorder/myorder?&cardId=${item.cardId}&cardDetailId=${item.cardDetailId}`,
      });
    },
    swichNav(current) {
      this.currentTab = current;
    },
    async loadData() {
      const [, res] = await myRechargeCards();
      // console.log(res, 'res');
      if (res && res.ret == 200) {
        this.cardList = res.myCards;
        this.total = res.amt;
      }
    },
    passwordClick() {
      uni.navigateTo({
        url: '/pages/setting/cardPasswordExchange/index',
      });
    },
    pointsClick() {
      uni.navigateTo({
        url: '/pages/intergral/shop/index',
      });
    },
  },
};
</script>

<style scoped lang="scss">
.mine-page {
  position: relative;
  z-index: 1;
  height: 100vh;
  box-sizing: border-box;
  overflow-y: auto;
  background-color: #ecebeb;
  display: flex;
  flex-direction: column;
  align-items: center;
  .tab-content {
    width: 100%;
    margin-top: 10rpx;
    padding: 0 15rpx;
    .myorder-tab {
      padding: 0 2.7%;
      height: 88rpx;
      line-height: 88rpx;
      text-align: center;
      background-color: #fff;
      color: #333;
      font-size: 30rpx;
      display: flex;
      width: 100%;
    }
    .myorder-tab .tab-item {
      position: relative;
      display: inline-block;
      flex: 1;
    }
    .myorder-tab .tab-item.active {
      border-bottom: 1rpx solid #2196f3;
    }
    .myorder-tab .btn {
      color: #2196f3;
    }
  }
  .card {
    width: calc(100% - 30rpx);
    border-radius: 10rpx;
    padding: 36rpx 60rpx;
    height: 172rpx;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 24rpx;
    color: rgba(0, 0, 0, 0.7);
    font-size: 26rpx;
    margin-top: 10rpx;
    .money {
      font-size: 48rpx;
      font-weight: 700;
      color: #333;
    }
  }
  .card-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    gap: 20rpx;
    width: 100%;
    padding: 15rpx;
    box-sizing: border-box;
    .card-item {
      display: flex;
      flex-direction: column;
      padding: 32rpx 32rpx 40rpx 32rpx;
      // gap: 40rpx;
      border-radius: 24rpx;
      background-color: #b3b3b3;
      box-sizing: border-box;
      width: 100%;
      height: 266rpx;
      position: relative;
      &.active {
        background-color: #1e9cff;
      }
      // .modal {
      //   position: absolute;
      //   inset: 0;
      //   background: rgba(0, 0, 0, 0.1);
      //   border-radius: 24rpx;
      // }
      .tip-text {
        position: absolute;
        color: #fff;
        font-size: 60rpx;
        font-weight: 700;
        rotate: 45deg;
        right: 140rpx;
        top: 70rpx;
      }
      .card-title {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 60rpx;
        .card-title-text {
          flex: 1;
          text-align: left;
          font-size: 28rpx;
          color: #ffffff;
        }
        .card-title-btn {
          border-radius: 50rpx;
          background: rgba(255, 255, 255, 0.1);
          box-sizing: border-box;
          border: 1px solid #ffffff;
          display: flex;
          flex-direction: column;
          width: 120rpx;
          height: 50rpx;
          font-size: 22rpx;
          line-height: 42rpx;
          text-align: center;
          color: #ffffff;
        }
      }
      .card-content {
        .card-money {
          font-size: 28rpx;
          color: #ffffff;
          display: flex;
          align-items: center;
          margin-bottom: 10rpx;
          .card-money-text {
            font-weight: 700;
            font-size: 56rpx;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .card-tip {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 24rpx;
          color: #fff;
        }
      }
    }
  }
  .tip {
    font-size: 26rpx;
    text-align: center;
    text-decoration: underline;
    /* 蓝色 */
    color: #1e9cff;
    width: 100%;
    margin: 10rpx 0;
  }
  .btn-item-blue {
    margin-bottom: 20rpx;
    color: #1e9cff;
    margin-top: 20rpx;
  }
  .btn-wrap {
    width: calc(100% - 30rpx);
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: #fff;
    height: 80rpx;
    margin-bottom: 40rpx;
    border-radius: 10rpx;
    .btn-item-vertical-line {
      width: 5px;
    }
    .btn-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
