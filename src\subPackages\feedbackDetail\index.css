.mine-page {
  position: relative;
  z-index: 1;
  min-height: 100vh;
}

.statusline {
  background-color: #fff;
  padding: 23rpx 48rpx;
}

.statusline text:nth-child(1) {
  font-size: 32rpx;
  color: #333;
  flex: 1;
  word-break: keep-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.statusline text:nth-child(2) {
  font-size: 28rpx;
  color: var(--theme);
}

.statusline text:nth-child(2).red {
  color: #ff3b30;
}

.log {
  display: flex;
  margin: 20rpx 24rpx 40rpx;
}

.log .avatar {
  width: 94rpx;
  height: 94rpx;
  border-radius: 50%;
  background: #fff;
}

.log .content {
  padding: 27rpx;
  border-radius: 12rpx;
  flex: 1;
  overflow: hidden;
  word-break: break-all;
  white-space: pre-wrap;
}

.log.left .avatar {
  margin: 0 20rpx 0 0;
}

.log.left .content {
  background-color: #e6f4ff;
  margin-right: 48rpx;
}
.log.right {
  justify-content: flex-end;
}
.log.right .avatar {
  margin: 0 0 0 20rpx;
}

.log.right .content {
  background-color: #fff;
  margin-left: 48rpx;
}
.content .content-img {
  max-width: 100%;
  margin-top: 20rpx;
}

.nodata image {
  width: 586rpx;
  display: block;
  margin: 330rpx auto 0;
}

.nodata text {
  display: block;
  margin: -80rpx auto 0;
  text-align: center;
}
