<!--
@name: tab选择器
@description: 描述
@time: 2024/8/7
-->
<template>
  <scroll-view :scroll-x="true" :show-scrollbar="false">
    <view class="tabs">
      <view v-for="(item, id) in options" :key="id" class="tabs-item" @click="handleClick(item, id)">
        <text :class="{ 'is-active': selectedId === id }">{{ item.name }}</text>
        <view :class="{ line: selectedId === id }"></view>
      </view>
    </view>
  </scroll-view>
</template>

<script>
export default {
  name: 'index',
  props: {
    options: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      selectedId: 0,
    };
  },
  methods: {
    // 切换tab
    handleClick(item, id) {
      this.selectedId = id;
      this.$emit('change', item);
    },
  },
};
</script>

<style scoped lang="scss">
.tabs {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  overflow: scroll;
  white-space: nowrap;
  gap: 50rpx;
  grid-gap: 50rpx;
  &-item {
    display: flex;
    flex-direction: column;
    font-size: 28rpx;
    line-height: 42rpx;
    color: #666666;
    align-items: center;
    .is-active {
      font-size: 32rpx;
      font-weight: bold;
      line-height: 38rpx;
      color: #333333;
    }
    .line {
      margin-top: 12rpx;
      height: 6rpx;
      width: 37rpx;
      background: #1e9cff;
      border-radius: 2rpx;
    }
  }
}
</style>
