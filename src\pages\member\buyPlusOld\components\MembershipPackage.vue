<!--
@name: 会员套餐
@description: 会员套餐组件
@time: 2024/8/9
-->
<template>
  <view class="membership-package">
    <text class="title">选择会员套餐</text>
    <view class="list">
      <view
        :class="[
          'list-item',
          {
            selected: selectedPackage.vipId === item.vipId,
          },
        ]"
        v-for="item in list"
        :key="item.vipId"
        @click="choosePrice(item)"
      >
        <text class="logo" v-if="memberInfo.priceType === 'new'">新客优惠</text>
        <text class="name">{{ item.vipTypeName }}</text>
        <view class="price">
          <text class="first-month" v-if="item.firstMonth">首月</text>
          <text class="unit">￥</text>
          <text class="price-item">{{ memberInfo.priceType === 'old' ? item.vipPriceOld : item.vipPriceNew }}</text>
        </view>
        <text class="first-month" v-if="item.firstMonth">首月特惠</text>
        <text class="origin-price" v-else>原价￥{{ item.vipPrice }}</text>
      </view>
    </view>
    <!--        <text class="propmt">到期9.9元/月自动续费，随时取消</text>-->
    <view class="pay-button">
      <AppButton color="#FF6835" :disabled="!selectedPackage.vipId" @click="chooseChannel"
        >确认协议并支付￥{{ selectPrice || 0 }}</AppButton
      >
    </view>
    <view class="protocol">
      <view>
        <text class="protocol-item">开通前请阅读</text>
        <text class="protocol-item" @click="openProtocol('member')">《宁德会员服务协议》</text>
      </view>
      <!--            <text class="protocol-item" @click="openProtocol('renew')">-->
      <!--                《自动续费服务协议》-->
      <!--            </text>-->
    </view>
    <!--        协议弹框-->
    <uni-popup class="popup-content" ref="protocolPopup" background-color="#F6F7F9">
      <view class="title">
        <text class="title-text">{{ serviceType === 'member' ? '宁德会员服务协议' : '自动续费服务协议' }}</text>
        <img class="close" :src="`${IMG_PATH}close.png`" @click="closeProtocol" />
      </view>
      <view class="content">
        <rich-text :nodes="content"></rich-text>
      </view>
      <view class="line"></view>
      <view class="footer-button">
        <AppButton type="primary" @click="closeProtocol"> 我知道了 </AppButton>
      </view>
    </uni-popup>
    <!--        选择支付方式弹框-->
    <uni-popup ref="channelPopup" background-color="#F6F7F9">
      <view class="popup-content">
        <view class="title">
          <text class="title-text">选择支付方式</text>
          <img class="close" :src="`${IMG_PATH}close.png`" @click="closeChannelPopup" />
        </view>
        <!--                支付渠道列表-->
        <view class="channel">
          <view class="channel-list" v-for="item in channelList" :key="item.type">
            <view class="channel-name">
              <img class="channel-icon" :src="`${IMG_PATH}member/${item.url}.png`" />
              <text>{{ item.name }}</text>
            </view>
            <img
              @click="getChannel(item)"
              class="check-icon"
              :src="`${IMG_PATH}${channel === item.type ? 'check' : 'circle'}.png`"
            />
          </view>
        </view>
      </view>
      <view class="footer-button">
        <AppButton type="primary" @click="payWay"> 立即支付 </AppButton>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import AppButton from '@/components/AppButton/index';
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup';
import uniPopupDialog from '@/components/uni-ui/uni-popup-dialog/uni-popup-dialog';
import { getConfig, openInfo } from '@/services/index.js';
import { ToPay } from '@/utils/bridge/index.js';
export default {
  name: 'MembershipPackage',
  components: {
    AppButton,
    uniPopup,
    uniPopupDialog,
  },
  props: {
    memberInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      serviceType: '',
      list: [],
      content: '',
      map: {
        member: '0412',
      },
      selectedPackage: {},
      selectPrice: '',
      channel: 1,
      channelList: [
        {
          type: 2,
          name: '微信支付',
          url: 'wx',
        },
        {
          type: 1,
          name: '支付宝支付',
          url: 'zfb',
        },
        {
          type: 3,
          name: '银联支付',
          url: 'yl',
        },
      ],
      channelMap: {
        1: '1302',
        2: '1303',
        3: '1301',
      },
    };
  },
  mounted() {
    this.getConfig();
  },
  methods: {
    // 打开协议弹框
    openProtocol(type) {
      this.serviceType = type;
      this.openInfo();
    },
    // 关闭协议弹框
    closeProtocol() {
      this.$refs.protocolPopup.close();
    },
    // 查询套餐
    async getConfig() {
      const [err, res] = await getConfig();
      if (res) {
        this.list = res.vipPayConfigs;
        if (this.list.length !== 0) {
          this.selectedPackage === this.list[0];
        }
      }
    },
    // 查询会员页面的配置信息，例如权益列表、协议说明、等级说明
    async openInfo() {
      // 0412 会员协议
      let arr = ['0412'];
      const params = {
        infoTypeList: arr,
      };
      const [err, res] = await openInfo(params);
      if (res) {
        let list = res.infoList;
        this.content = list.find((item) => item.infoType === this.map[this.serviceType])?.content;
        this.$refs.protocolPopup.open('bottom');
      }
    },
    // 选择套餐
    choosePrice(item) {
      this.selectedPackage = item;
      this.selectPrice = this.memberInfo.priceType === 'old' ? item.vipPriceOld : item.vipPriceNew;
    },
    // 小程序直接拉收银台，h5打开选择支付渠道弹框
    chooseChannel() {
      if (!this.selectedPackage.vipId) {
        return;
      }
      // #ifdef H5
      this.$refs.channelPopup.open('bottom');
      // #endif
      // #ifdef MP
      this.recharge();
      // #endif
    },
    // 选择支付方式后：微信支付直接调桥接器，其他支付方式先查询支付参数后调用桥接器
    payWay() {
      if (this.channel === 2) {
        this.payMoney({});
      } else {
        this.recharge();
      }
    },
    // 关闭选择渠道弹框
    closeChannelPopup() {
      this.$refs.channelPopup.close();
    },
    // 选择支付渠道
    getChannel(item) {
      this.channel = item.type;
    },
    // 查询支付参数
    async recharge() {
      const params = {
        reqType: '01',
        appType: '04',
        payAmount: this.selectPrice,
        vipType: this.selectedPackage.vipType,
      };

      // #ifdef MP
      let extend = '';
      uni.login({
        success: async (info) => {
          extend = info.code;
        },
      });
      params.extend = extend;
      params.transactionChannel = '1303';
      // #endif

      // #ifdef H5
      params.transactionChannel = this.channelMap[this.channel];
      // #endif

      const [err, res] = await Api.recharge(params);
      if (res) {
        let data = '';
        // #ifdef MP
        data = res.orderResultChinaUms.miniPayRequest;
        // #endif
        // #ifdef H5
        data = res;
        // #endif
        this.payMoney(data);
      }
    },
    // 支付
    payMoney(params) {
      // #ifdef MP
      this.wxPay(params);
      // #endif
      // #ifdef H5
      this.appPay(params);
      // #endif
    },
    // 微信支付
    wxPay(params) {
      const that = this;
      uni.requestPayment({
        timeStamp: params.timeStamp,
        nonceStr: params.nonceStr,
        package: params.package,
        signType: 'MD5',
        paySign: params.paySign,
        success: function () {
          that.paySuccess();
        },
        fail: function (err) {
          that.payFail(err.msg);
        },
      });
    },
    // app支付
    async appPay(params) {
      // payChannel 1 支付宝 ，2微信 ，3 银联
      const getPayResult = ToPay({
        isNeedLogin: true,
        payChannel: this.channel,
        serviceType: 'member',
        payAmount: this.selectPrice,
        vipType: this.selectedPackage.vipType,
        ...params,
      });

      const that = this;
      getPayResult.then((result) => {
        if (result.code === '9999') {
          that.paySuccess();
        } else {
          that.payFail(result.msg);
        }
      });
    },
    // 付款成功后操作
    paySuccess() {
      uni.showModal({
        title: '开通成功',
        showCancel: false,
        content: '欢迎您成为尊贵的VIP会员',
        confirmText: '我知道了',
      });
      this.$store.dispatch('login/getUserInfoCallback');
    },
    // 支付失败操作
    payFail(msg) {
      uni.showModal({
        title: '开通失败',
        showCancel: false,
        content: msg,
        confirmText: '确定',
      });
    },
  },
};
</script>

<style scoped lang="scss">
.membership-package {
  .title {
    font-size: 32rpx;
    font-weight: bold;
    line-height: 38rpx;
    color: #333333;
  }
  .list {
    margin-top: 24rpx;
    display: flex;
    gap: 24rpx;
    grid-gap: 24rpx;
    overflow: scroll;
    &-item {
      min-width: 240rpx;
      height: 220rpx;
      border: 2rpx solid #ff6835;
      border-radius: 16rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      .logo {
        background: linear-gradient(118deg, #ff6835 16%, #ff6b6d 81%);
        color: white;
        padding: 3rpx 7rpx;
        border-radius: 16rpx 0 16rpx 0;
        margin-right: 137rpx;
        font-size: 22rpx;
        line-height: 26rpx;
        color: #ffffff;
      }
      .name {
        font-size: 28rpx;
        font-weight: bold;
        line-height: 42rpx;
        color: #333333;
        margin: 8rpx 0 4rpx 0;
      }
      .price {
        color: #ff6835;
        .first-month {
          font-size: 26rpx;
          line-height: 40rpx;
        }
        .unit {
          font-size: 28rpx;
          font-weight: bold;
          line-height: 42rpx;
        }
        .price-item {
          font-size: 46rpx;
          font-weight: bold;
          line-height: 48rpx;
          letter-spacing: 0.58rpx;
          font-variation-settings: 'opsz' auto;
        }
      }
      .first-month {
        font-size: 22rpx;
        line-height: 26rpx;
        color: #ff8301;
        padding: 4rpx 12rpx;
        background: rgba(255, 131, 1, 0.1);
        margin-top: 26rpx;
        border-radius: 8rpx;
      }
      .origin-price {
        font-size: 22rpx;
        line-height: 26rpx;
        text-decoration: line-through;
        color: #999999;
        margin-top: 32rpx;
      }
    }
    .selected {
      background: #fff9f3;
    }
  }
  .pay-button {
    margin-top: 40rpx;
  }
  .propmt {
    font-size: 24rpx;
    line-height: 32rpx;
    color: #999999;
    display: block;
    margin-top: 24rpx;
  }
  .protocol {
    margin-top: 16rpx;
    font-size: 24rpx;
    line-height: 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    &-item:nth-child(1) {
      color: #999999;
    }
    &-item:nth-child(2),
    &-item:nth-child(3) {
      color: #ff6835;
    }
  }
  .popup-content {
    .title {
      display: flex;
      justify-content: center;
      margin: 16rpx 0 24rpx 0;
      align-items: center;
      padding: 24rpx;
      position: relative;
      .title-text {
        font-size: 32rpx;
        font-weight: bold;
        line-height: 38rpx;
        color: #333333;
      }
      .close {
        position: absolute;
        top: 24rpx;
        right: 24rpx;
        height: 40rpx;
        width: 40rpx;
      }
    }
    .content {
      padding: 35rpx 24rpx 42rpx 24rpx;
      font-size: 26rpx;
      line-height: 40rpx;
      color: #3d3d3d;
      overflow: scroll;
    }
    .line {
      background: #eeeeee;
      width: 100%;
      margin-bottom: 23rpx;
      height: 1rpx;
    }
    .channel {
      display: flex;
      flex-direction: column;
      padding: 24rpx;
      .channel-list {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1rpx solid #eeeeee;
        padding: 24rpx 0;
        .channel-name {
          display: flex;
          align-items: center;
          .channel-text {
            font-size: 36rpx;
            color: #333333;
          }
          .channel-icon {
            width: 40rpx;
            height: 40rpx;
            margin-right: 16rpx;
          }
        }
        .check-icon {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }
  .footer-button {
    padding: 0 24rpx 92rpx 24rpx;
  }
}
</style>
