<template>
  <view>
    <view class="login-header styleColorB">
      <view class="login-img">
        <image :src="`${IMG_PATH}logo.png`"></image>
      </view>
      <view class="login-header-message">
        <view class="login-message1">
          {{ chargeOrderList.stationName }}
          <text>￥{{ msg.orderTBal }}</text>
        </view>
        <view class="login-message2">
          <image :src="`${IMG_PATH}<EMAIL>`"></image>
          {{ chargeOrderList.stationAddr }}
        </view>
        <view class="login-message3">
          <image :src="`${IMG_PATH}<EMAIL>`"></image>
          {{ applyTime }}
        </view>
        <view @tap="goMaps" id="login-text">
          <image :src="`${IMG_PATH}<EMAIL>`"></image>
        </view>
      </view>
    </view>
    <view class="comt-evaluate">
      <view class="comt-list">
        <text>综合满意度</text>
        <view
          class="comt-img"
          @tap="starTapAll"
          data-name="综合满意度"
          :data-index="index"
          v-for="(item, index) in userStarsAllInfo"
          :key="index"
        >
          <image :src="item"></image>
        </view>
      </view>
      <view class="comt-list">
        <text>车位情况</text>
        <view
          class="comt-img"
          @tap="starTapCar"
          data-name="车位情况"
          :data-index="index"
          v-for="(item, index) in userStarsCarInfo"
          :key="index"
        >
          <image :src="item"></image>
        </view>
      </view>
      <view class="comt-list">
        <text>新旧程度</text>
        <view
          class="comt-img"
          @tap="starTapNew"
          data-name="新旧程度"
          :data-index="index"
          v-for="(item, index) in userStarsNewInfo"
          :key="index"
        >
          <image :src="item"></image>
        </view>
      </view>
      <view class="comt-list">
        <text>充电速度</text>
        <view
          class="comt-img"
          @tap="starTapLoop"
          data-name="充电速度"
          :data-index="index"
          v-for="(item, index) in userStarsLoopInfo"
          :key="index"
        >
          <image :src="item"></image>
        </view>
      </view>
    </view>
    <view class="section">
      <textarea @blur="bindTextAreaBlur" :value="ceshi" auto-focus placeholder="请留言" />
    </view>
    <!-- <view class="upload-img">上传图片</view> -->
    <view class="imgs-box">
      <l-upload
        @complete="imgChange"
        :limit="3"
        uploadType="img"
        width="160"
        height="160"
        :formData="imgFormData"
        :header="imgHeader"
        :sourceType="['album']"
      ></l-upload>
      <!-- <view data-id="0" @tap="chooseimage"><image :src="imgimgAllInfoAll[0]"></image></view>
      <view data-id="1" @tap="chooseimage"><image :src="imgAllInfo[1]"></image></view>
      <view data-id="2" @tap="chooseimage"><image :src="imgAllInfo[2]"></image></view> -->
    </view>
    <view class="comt-footer">
      <text>最多可上传3张图片，点评提交后无法修改</text>
      <button @tap="alertMsg">提交</button>
      <toast v-if="!bookToastHidden" @change="hideToast">提交成功</toast>
    </view>
  </view>
</template>

<script>
import { baseDateNo } from '@/utils/base.js';
import { getChargeOrderDtl, shopOrderEvaApi } from '@/services/index.js';
import lUpload from '@/components/l-upload/l-upload';
import { openLocation } from '@/utils/index.js';
export default {
  components: { lUpload },
  data() {
    return {
      lon: '',
      lat: '',
      bookToastHidden: true,
      orderId: '',
      chargeOrderList: '',
      applyTime: '',
      ceshi: '',
      orderNo: '',
      fraction: 10,

      //综合满意度
      fraction1: 10,

      //车位情况
      fraction2: 10,

      //新旧程度
      fraction3: 10,

      //充电速度
      userStarsAll: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],

      imgAll: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
      imgMsg: [],
      userStarsCar: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      userStarsNew: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      userStarsLoop: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],

      msg: {
        orderTBal: '',
      },

      prepayBal: '',
      stationAddr: '',
      lt: '',
      ltmsg: '',
      imageList: [],
      actionUrl: '',
    };
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    // #ifdef MP
    this.actionUrl = baseDateNo() + 'scan/wx/v0.1/file-upload';
    // #endif
    // #ifdef H5
    this.actionUrl = baseDateNo() + 'base/api/v0.1/file-upload';
    // #endif
    this.orderNo = options.orderNo;
    var that = this;
    const [, res] = await getChargeOrderDtl({
      orderNo: this.orderNo,
    });
    if (res && res.ret == '200') {
      that.msg = res;
      that.chargeOrderList = res.chargeOrderList[0];
      that.applyTime = res.applyTime;
      that.prepayBal = res.prepayBal;
      that.orderId = res.orderId;
      that.lon = res.chargeOrderList[0].lon;
      that.lat = res.chargeOrderList[0].lat;
      that.stationAddr = res.chargeOrderList[0].stationAddr;
    }
  },
  computed: {
    userStarsAllInfo() {
      return this.userStarsAll.map((q) => this.IMG_PATH + q);
    },
    imgAllInfo() {
      return this.imgAll.map((q) => this.IMG_PATH + q);
    },
    userStarsCarInfo() {
      return this.userStarsCar.map((q) => this.IMG_PATH + q);
    },
    userStarsNewInfo() {
      return this.userStarsNew.map((q) => this.IMG_PATH + q);
    },
    userStarsLoopInfo() {
      return this.userStarsLoop.map((q) => this.IMG_PATH + q);
    },
    imgFormData() {
      return {
        contentType: '02',
        relaId: this.orderId,
        relaTable: 'orderEva',
      };
    },
    imgHeader() {
      return {
        // #ifdef MP
        minitProgramToken: uni.getStorageSync('token') || '',
        // #endif
        // #ifdef H5
        authorization: uni.getStorageSync('token') || '',
        // #endif
      };
    },
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
  methods: {
    imgChange(e) {
      console.log('imgChange===', e);
      this.imgList = e.imageArr;
    },
    goMaps() {
      console.log('地图导航');
      console.log(this.lon, 'lon');
      console.log(this.lat, 'lat');
      console.log(this.stationAddr, 'stationAddr');
      var that = this;
      openLocation({
        latitude: parseFloat(that.lat),
        longitude: parseFloat(that.lon),
        address: that.stationAddr,
      });
    },

    starTapAll(e) {
      var index = e.currentTarget.dataset.index; // 获取当前点击的是第几颗星星
      console.log(e.currentTarget.dataset.name + ':' + ' ' + (index + 1) * 2 + '分');

      // 创建新的数组以触发响应式更新
      var tempUserStars = [...this.userStarsAll]; // 使用扩展运算符创建新数组
      var len = tempUserStars.length; // 获取星星数组的长度

      for (var i = 0; i < len; i++) {
        if (i <= index) {
          // 小于等于index的是满心
          tempUserStars[i] = '<EMAIL>';
        } else {
          // 其他是空心
          tempUserStars[i] = '<EMAIL>';
        }
      }
      var indexFraction = (index + 1) * 2;

      // Vue.js中直接赋值触发响应式更新
      this.userStarsAll = tempUserStars;
      this.fraction = indexFraction;

      console.log(this.fraction, this.userStarsAll);
    },

    starTapCar(e) {
      var index = e.currentTarget.dataset.index; // 获取当前点击的是第几颗星星
      console.log(e.currentTarget.dataset.name + ':' + ' ' + (index + 1) * 2 + '分');

      // 创建新的数组以触发响应式更新
      var tempUserStars = [...this.userStarsCar]; // 使用扩展运算符创建新数组
      var len = tempUserStars.length; // 获取星星数组的长度

      for (var i = 0; i < len; i++) {
        if (i <= index) {
          // 小于等于index的是满心
          tempUserStars[i] = '<EMAIL>';
        } else {
          // 其他是空心
          tempUserStars[i] = '<EMAIL>';
        }
      }
      var indexFraction = (index + 1) * 2;

      // Vue.js中直接赋值触发响应式更新
      this.userStarsCar = tempUserStars;
      this.fraction1 = indexFraction;

      console.log(this.fraction1);
    },

    starTapNew(e) {
      var index = e.currentTarget.dataset.index; // 获取当前点击的是第几颗星星
      console.log(e.currentTarget.dataset.name + ':' + ' ' + (index + 1) * 2 + '分');

      // 创建新的数组以触发响应式更新
      var tempUserStars = [...this.userStarsNew]; // 使用扩展运算符创建新数组
      var len = tempUserStars.length; // 获取星星数组的长度

      for (var i = 0; i < len; i++) {
        if (i <= index) {
          // 小于等于index的是满心
          tempUserStars[i] = '<EMAIL>';
        } else {
          // 其他是空心
          tempUserStars[i] = '<EMAIL>';
        }
      }
      var indexFraction = (index + 1) * 2;

      // Vue.js中直接赋值触发响应式更新
      this.userStarsNew = tempUserStars;
      this.fraction2 = indexFraction;

      console.log(this.fraction2);
    },

    starTapLoop(e) {
      var index = e.currentTarget.dataset.index; // 获取当前点击的是第几颗星星
      console.log(e.currentTarget.dataset.name + ':' + ' ' + (index + 1) * 2 + '分');

      // 创建新的数组以触发响应式更新
      var tempUserStars = [...this.userStarsLoop]; // 使用扩展运算符创建新数组
      var len = tempUserStars.length; // 获取星星数组的长度

      for (var i = 0; i < len; i++) {
        if (i <= index) {
          // 小于等于index的是满心
          tempUserStars[i] = '<EMAIL>';
        } else {
          // 其他是空心
          tempUserStars[i] = '<EMAIL>';
        }
      }
      var indexFraction = (index + 1) * 2;

      // Vue.js中直接赋值触发响应式更新
      this.userStarsLoop = tempUserStars;
      this.fraction3 = indexFraction;

      console.log(this.fraction3);
    },

    chooseimage(e) {
      var that = this;
      var index = 'imgAll[' + e.currentTarget.dataset.id + ']';
      var indexMsg = 'imgMsg[' + e.currentTarget.dataset.id + ']';
      let imgFirsts = that.imgFirst;
      // imgAll
      uni.chooseImage({
        count: 1,
        // 默认9
        sizeType: ['compressed'],
        // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ['album', 'camera'],
        // 可以指定来源是相册还是相机，默认二者都有
        success(res) {
          var lt = index;
          var ltmsg = indexMsg;
          console.log(res.tempFilePaths);
          // 返回选定照片的本地文件路径列表，tempFilePath可以作为img标签的src属性显示图片
          that.setData({
            [lt]: res.tempFilePaths,
            [ltmsg]: res.tempFilePaths,
          });
        },
      });
    },

    bindTextAreaBlur(e) {
      this.ceshi = e.detail.value;
      console.log(this.ceshi);
    },

    async alertMsg() {
      this.bookToastHidden = false;

      var that = this;

      var orderEvaItemList = [
        {
          evaItemCode: '0101',
          evaScore: that.fraction1,
        },
        {
          evaItemCode: '0102',
          evaScore: that.fraction2,
        },
        {
          evaItemCode: '0103',
          evaScore: that.fraction3,
        },
      ];

      const [, result] = await shopOrderEvaApi({
        orderNo: that.orderNo,
        evaUserType: '02',
        evaScore: that.fraction,
        //综合满意度
        evaRemark: that.ceshi,
        //评论
        orderEvaItemList: JSON.stringify(orderEvaItemList),
      });
      console.log(result, '提交评价');
      if (result && result.ret == '200') {
        this.bookToastHidden = false;
        this.hideToast();
      } else {
        this.bookToastHidden = true;
        console.log('请求出问题。');
      }
      if (that.imgList.length > 0) {
        await that.imgList.forEach((item) => {
          uni.uploadFile({
            url: that.actionUrl,
            filePath: item,
            header: that.imgHeader,
            //待上传的图片，由 chooseImage获得
            name: 'food_image',
            formData: {
              contentType: '02',
              relaId: that.orderId,
              relaTable: 'orderEva',
            },
            success: function (res) {
              console.log('addfood success图片上传ok了1111', res);
            },
            fail: function (res) {
              console.log('addfood fail', res);
            },
          });
        });
      }
    },

    hideToast() {
      console.log('隐藏');
      this.bookToastHidden = true;
      console.log('跳转');
      uni.navigateBack({
        delta: 1,
      });
    },
  },
};
</script>
<style>
@import './comment.css';
</style>
<style scoped lang="scss">
.imgs-box {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  grid-gap: 24rpx;
  margin-top: 24rpx;
  padding: 20rpx;
}
</style>
