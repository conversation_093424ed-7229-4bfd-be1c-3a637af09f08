<template>
  <div class="wrap">
    <div class="wrap-item mt10">
      <!-- #ifdef MP-WEIXIN -->
      <button open-type="chooseAvatar" class="wrap-button" @chooseavatar="getUserInfo">
        <div class="text">头像</div>
        <div class="profile">
          <image
            :src="avatarSrc"
            alt=""
            @error="onAvatarError"
            @load="onAvatarLoad"
            :class="{ loading: avatarLoading }"
          />
          <div v-if="avatarLoading" class="loading-mask">
            <text>加载中...</text>
          </div>
        </div>
      </button>
      <!-- #endif -->

      <!-- #ifdef H5 -->
      <div class="wrap-button" @click="chooseH5Avatar">
        <div class="text">头像</div>
        <div class="profile">
          <image
            :src="avatarSrc"
            alt=""
            @error="onAvatarError"
            @load="onAvatarLoad"
            :class="{ loading: avatarLoading }"
          />
          <div v-if="avatarLoading" class="loading-mask">
            <text>加载中...</text>
          </div>
        </div>
      </div>
      <!-- #endif -->
      <image :src="arrow" alt="" />
    </div>
    <div class="wrap-item" v-for="(item, index) in list" :key="index" @click="toCallBack(item)">
      <div class="text">{{ item.title }}</div>
      <div class="value">
        {{ item.value || '-' }}
      </div>
      <image :src="arrow" alt="" />
    </div>
    <uni-popup ref="popup" type="dialog" @close="close">
      <div class="input-wrap">
        <div class="title">更换昵称</div>
        <div class="input-box">
          <uni-easyinput v-model="title" focus placeholder="请输入内容"></uni-easyinput>
        </div>
        <div class="bottom-btn">
          <div class="btn-item first" @click="close">取消</div>
          <div class="btn-item" @click="confirm">确认</div>
        </div>
      </div>
    </uni-popup>
    <uni-popup ref="popupSex" background-color="transparent" type="bottom" @close="close">
      <view class="sex-wrap">
        <div class="sex-select-wrap">
          <div class="sex-item center" :class="{ activeSex: sex == '01' }" @click="check('01')">男</div>
          <div class="sex-item center" :class="{ activeSex: sex == '02' }" @click="check('02')">女</div>
        </div>
        <div class="sex-btn center" @click="close">取消</div>
      </view>
    </uni-popup>
  </div>
</template>

<script>
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup.vue';
import uniEasyinput from '@/components/uni-ui/uni-easyinput/uni-easyinput.vue';
import { baseDateNo, baseDate } from '@/utils/base.js';
import { updateUserInfo } from '@/services/index';
import network from '@/utils/network.js';
import { getImageToBase64_url } from '@/utils/upload.js';
export default {
  props: {},
  components: { uniPopup, uniEasyinput },
  data() {
    return {
      userInfo: {},
      title: '',
      sex: '',
      avatarLoading: false,
      avatarError: false,
    };
  },
  computed: {
    arrow() {
      return this.IMG_PATH + 'right_arrow.png';
    },
    profile() {
      return this.IMG_PATH + 'defaultAvatar.png';
    },
    // 头像显示源，优化base64过大的问题
    avatarSrc() {
      if (this.avatarError) {
        return this.profile;
      }

      const imgUrl = this.userInfo.imgUrl;
      if (!imgUrl) {
        return this.profile;
      }

      // 如果是base64且过大，使用默认头像
      if (imgUrl.startsWith('data:') && imgUrl.length > 500000) {
        // 500KB限制
        console.warn('头像base64过大，使用默认头像');
        return this.profile;
      }

      return imgUrl;
    },
    list() {
      return [
        {
          title: '昵称',
          value: this.userInfo.custNickname || '-',
        },
        {
          title: '手机',
          value: this.userInfo.mobile || '-',
        },
        {
          title: '性别',
          sex: this.userInfo.sex,
          value: this.userInfo.sex == '01' ? '男' : this.userInfo.sex == '02' ? '女' : '-',
        },
      ];
    },
  },
  watch: {},
  created() {},
  /**
   * 生命周期函数--监听页面加载
   */
  onShow() {
    const _this = this;
    this.$store.dispatch('login/getUserInfoCallback').then((res) => {
      _this.userInfo = res;
    });
  },
  methods: {
    toCallBack(item) {
      console.log(item, 'item');
      if (item.title == '昵称') {
        if (item.value == '-') {
          this.title = '';
        } else {
          this.title = item.value;
        }
        this.$refs.popup && this.$refs.popup.open();
      } else if (item.title == '性别') {
        if (item.value == '-') {
          this.sex = '';
        } else {
          this.sex = item.sex;
        }
        this.$refs.popupSex && this.$refs.popupSex.open();
      }
    },
    async confirm() {
      if (!this.title) return;
      const _this = this;
      const [, res] = await updateUserInfo({ custNickname: _this.title });
      if (res) {
        _this.$store.dispatch('login/getUserInfoCallback').then((info) => {
          _this.userInfo = info;
          _this.close();
        });
      }
    },
    async check(type) {
      this.sex = type;
      const _this = this;
      const [, res] = await updateUserInfo({ sex: _this.sex });
      if (res) {
        _this.$store.dispatch('login/getUserInfoCallback').then((info) => {
          _this.userInfo = info;
          _this.close();
        });
      }
    },
    close() {
      this.$refs.popup && this.$refs.popup.close();
      this.title = '';
      this.$refs.popupSex && this.$refs.popupSex.close();
      this.sex = '';
    },
    // 头像加载成功
    onAvatarLoad() {
      this.avatarLoading = false;
      this.avatarError = false;
    },

    // 头像加载失败
    onAvatarError() {
      this.avatarLoading = false;
      this.avatarError = true;
      console.warn('头像加载失败，使用默认头像');
    },

    getUserInfo(e) {
      const _this = this;
      this.avatarLoading = true;

      getImageToBase64_url(e.detail.avatarUrl, true)
        .then(async (res) => {
          const [, result] = await updateUserInfo({ userImage: res });
          if (result) {
            _this.$store.dispatch('login/getUserInfoCallback').then((info) => {
              _this.userInfo = info;
              _this.avatarLoading = false;
              uni.showToast({
                title: '头像更新成功',
                icon: 'success',
              });
            });
          } else {
            _this.avatarLoading = false;
            uni.showToast({
              title: '头像更新失败',
              icon: 'none',
            });
          }
        })
        .catch((error) => {
          _this.avatarLoading = false;
          console.error('头像处理失败:', error);
          uni.showToast({
            title: error.message || '头像处理失败',
            icon: 'none',
          });
        });
    },
    chooseH5Avatar() {
      const _this = this;
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          try {
            console.log('选择图片成功，开始转换', res);
            _this.avatarLoading = true;

            // H5环境下，res.tempFilePaths[0] 可能是 blob URL
            // 使用压缩功能处理图片
            const base64 = await getImageToBase64_url(res.tempFilePaths[0], true);

            if (base64) {
              console.log('图片转换成功');
              const [err, result] = await updateUserInfo({ userImage: base64 });

              if (err) {
                console.error('更新用户信息失败:', err);
                _this.avatarLoading = false;
                uni.showToast({
                  title: '更新头像失败',
                  icon: 'none',
                });
                return;
              }

              if (result) {
                _this.$store.dispatch('login/getUserInfoCallback').then((info) => {
                  _this.userInfo = info;
                  _this.avatarLoading = false;
                  uni.showToast({
                    title: '更新头像成功',
                    icon: 'success',
                  });
                });
              } else {
                _this.avatarLoading = false;
                uni.showToast({
                  title: '更新头像失败',
                  icon: 'none',
                });
              }
            }
          } catch (error) {
            console.error('处理图片失败:', error);
            _this.avatarLoading = false;

            // 根据错误类型显示不同提示
            let errorMessage = '处理图片失败';
            if (error.message && error.message.includes('1MB')) {
              errorMessage = '图片大小不能超过1MB，请选择较小的图片';
            }

            uni.showToast({
              title: errorMessage,
              icon: 'none',
              duration: 3000,
            });
          }
        },
        fail: (err) => {
          console.error('选择图片失败:', err);
          _this.avatarLoading = false;
          uni.showToast({
            title: '选择图片失败',
            icon: 'none',
          });
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.input-wrap {
  width: 400rpx;
  height: 250rpx;
  background: #fff;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  .title {
    width: 100%;
    height: 60rpx;
    font-size: 30rpx;
    font-weight: 600;
    line-height: 60rpx;
    padding-left: 20rpx;
  }
  .input-box {
    padding: 0 20rpx;
    // width: 100%;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .bottom-btn {
    width: 100%;
    height: 80rpx;
    display: flex;
    justify-content: space-around;
    align-items: center;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    .first {
      border-right: 1px solid rgba(0, 0, 0, 0.1);
    }
    .btn-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
  }
}
.wrap-button {
  display: flex;
  width: 100%;
  background: #fff;
  padding: 0;
  border: none;
  cursor: pointer; // 添加鼠标指针样式，增强 H5 下的交互性
}
.wrap-button::after,
.wrap-button::before {
  content: none;
}
.mt10 {
  margin-top: 10rpx;
}
.wrap {
  background: #efefef;
  width: 100vw;
  height: 100vh;
}
.wrap-item {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  background: #fff;
  padding: 15rpx 20rpx;
  box-sizing: border-box;
  image {
    width: 40rpx;
    height: 40rpx;
  }
  .value {
    margin-left: auto;
    margin-right: 10rpx;
  }
  .profile {
    margin-left: auto;
    margin-right: 10rpx;
    position: relative;

    image {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
      overflow: hidden;
      transition: opacity 0.3s ease;

      &.loading {
        opacity: 0.6;
      }
    }

    .loading-mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;

      text {
        color: #fff;
        font-size: 20rpx;
      }
    }
  }
  .text {
    color: #2c3032;
    font-size: 26rpx;
  }
}
.sex-wrap {
  width: 100%;
  padding: 20rpx;
  .center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .sex-btn {
    margin-top: 10rpx;
    height: 80rpx;
    border-radius: 10rpx;
    overflow: hidden;
    background: #fff;
  }
  .sex-select-wrap {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 10rpx;
    overflow: hidden;
    .activeSex {
      color: #1b88ed;
      border-color: #1b88ed;
    }
    .sex-item {
      width: 100%;
      height: 80rpx;
      border: 1px solid rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
