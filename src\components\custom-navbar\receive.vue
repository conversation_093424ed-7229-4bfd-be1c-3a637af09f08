<template>
  <div class="receive-card" v-if="showCard">
    您还有优惠券待领取
    <div class="btn" @click.stop="jumpCup">去领取</div>
    <div class="close" @click.stop="close">X</div>
  </div>
</template>

<script>
export default {
  name: 'receive',
  data() {
    return {
      showCard: true,
    };
  },
  onReady() {},
  methods: {
    close() {
      this.showCard = false;
    },
    jumpCup: function () {
      uni.navigateTo({
        url: '/subPackages/gocup/gocup',
      });
    },
  },
};
</script>

<style scoped lang="scss">
.receive-card {
  width: 100%;
  background-color: #fdecd8;
  color: #f79f4d;
  display: flex;
  align-items: center;
  height: 25px;
  line-height: 25rpx;
  padding: 0 30rpx;
  position: absolute;
  top: -40rpx;
  font-size: 24rpx;
  // position: fixed;
  // bottom: 170rpx;
  // left: 0;
  // right: 0;
  .btn {
    margin-left: auto;
    color: #fff;
    background: #c8723c;
    font-size: 24rpx;
    padding: 5rpx 10rpx;
    box-sizing: border-box;
    border-radius: 5rpx;
  }
  .close {
    margin-left: 20rpx;
    height: 100%;
    width: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
