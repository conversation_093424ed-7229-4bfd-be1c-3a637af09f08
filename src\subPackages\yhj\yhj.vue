<template>
  <!-- pages/yhj.wxml -->
  <view>
    <view class="yhj">
      <view @tap="change" data-type="0" :class="changeType == 0 ? 'active' : ''">未使用({{ queryLength0 }})</view>
      <view @tap="change" data-type="1" :class="changeType == 1 ? 'active' : ''">已使用({{ queryLength1 }})</view>
      <view @tap="change" data-type="2" :class="changeType == 2 ? 'active' : ''">已过期({{ queryLength2 }})</view>
    </view>
    <view v-if="cls" @tap="jump" class="goCupe"
      >去领券
      <div class="num" v-if="cpnList.length > 0">{{ cpnList.length }}</div>
    </view>
    <template v-if="dataUse == 0">
      <view class="list" v-for="(item, index) in queryList" :key="index">
        <view class="listL">
          <text class="net1">{{ item.cpnName }}</text>
          <text class="net2">有效期：{{ item.effectTime }}</text>
          <text class="net3">{{ item.cpnMarks }}</text>
        </view>

        <view class="listR">
          <image :src="`${IMG_PATH}bg_discount_red_2.png`"></image>
          <view>{{ item.cpnAmt }}</view>
        </view>
      </view>
    </template>
    <view v-if="dataUse == 0 && queryList.length == 0" class="nothImg">
      <image :src="`${IMG_PATH}main-data-null.png`"></image>
      <text>暂无数据</text>
    </view>
    <view v-if="dataUse == 1 && queryList1.length == 0" class="nothImg">
      <image :src="`${IMG_PATH}main-data-null.png`"></image>
      <text>暂无数据</text>
    </view>
    <view v-if="dataUse == 2 && queryList2.length == 0" class="nothImg">
      <image :src="`${IMG_PATH}main-data-null.png`"></image>
      <text>暂无数据</text>
    </view>

    <view v-if="dataUse == 1" class="list" v-for="(item, index) in queryList1" :key="index">
      <view class="listL">
        <text class="net1">{{ item.cpnName }}</text>
        <text class="net2">有效期：{{ item.effectTime }}</text>
        <text class="net3">{{ item.cpnMarks }}</text>
      </view>

      <view class="listR">
        <image :src="`${IMG_PATH}pic_used_bg_1.png`"></image>
        <view>{{ item.cpnAmt }}</view>
      </view>
    </view>

    <view v-if="dataUse == 2" class="list" v-for="(item, index) in queryList2" :key="index">
      <view class="listL">
        <text class="net1">{{ item.cpnName }}</text>
        <text class="net2">有效期：{{ item.effectTime }}</text>
        <text class="net3">{{ item.cpnMarks }}</text>
      </view>

      <view class="listR">
        <image :src="`${IMG_PATH}pic_overtime_2.png`"></image>
        <view>{{ item.cpnAmt }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import { getMsgUnused, getAccountsCoupons } from '@/services/index.js';
export default {
  data() {
    return {
      cls: true,
      changeType: 0,
      queryList: [],
      queryLength0: 0,
      queryLength1: 0,
      queryLength2: 0,
      token: '',
      dataUse: 0,
      queryList1: [],
      queryList2: [],
      cpnList: [],
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */ onLoad(options) {},
  onReady() {},
  onShow() {
    this.loadData();
    this.getNum();
    setTimeout(() => {
      uni.stopPullDownRefresh();
    });
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: async function () {
    try {
      // 统一触发数据加载（H5/小程序通用）
      await this.loadData();

      // #ifdef H5
      // H5 特殊处理：强制设置刷新终止（避免卡住）
      setTimeout(() => {
        uni.stopPullDownRefresh();
      }, 500);
      // #endif

      // #ifndef H5
      // 非 H5 平台正常终止
      uni.stopPullDownRefresh();
      // #endif
    } catch (error) {
      // 错误处理
      console.error('下拉刷新失败:', error);
      uni.stopPullDownRefresh();
    }
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
  methods: {
    async getNum() {
      var that = this;
      var parmes1 = {
        prodBusiType: '02',
      };
      const [, res] = await getMsgUnused(parmes1);
      if (res) {
        console.log(res, '优惠券-可领取的');
        that.setData({
          cpnList: res.cpnList || [],
        });
      }
    },
    loadData() {
      this.setData({
        changeType: 0,
      });
      var that = this;
      that.setData({
        token: uni.getStorageSync('token'),
      });
      that.tab0();
      that.tab1();
      that.tab2();
    },
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    change(e) {
      console.log(e.currentTarget.dataset.type);
      this.setData({
        changeType: e.currentTarget.dataset.type,
      });
      var that = this;
      if (e.currentTarget.dataset.type == 0) {
        that.setData({
          cls: true,
        });
        that.tab0();
      } else if (e.currentTarget.dataset.type == 1) {
        that.setData({
          cls: false,
        });
        that.tab1();
      } else if (e.currentTarget.dataset.type == 2) {
        that.setData({
          cls: false,
        });
        that.tab2();
      }
      that.setData({
        dataUse: e.currentTarget.dataset.type,
      });
    },

    jump() {
      uni.navigateTo({
        url: '/subPackages/gocup/gocup',
      });
    },

    /**
     * 生命周期函数--监听页面显示
     */
    async tab0() {
      var that = this;
      var parmes1 = {
        // getChannel: '02',
        useStatus: '01',
        pageNum: '1',
        totalNum: '100',
      };
      const [, res] = await getAccountsCoupons(parmes1);
      console.log(res, 'res');
      if (res) {
        that.setData({
          queryList: res.queryList || [],
          queryLength0: res.queryList.length,
        });
      }

      // network.requestLoading(baseUrl + '/wx/v0.1/accounts/coupons', parmes1, '正在加载数据', 'POST', function (res) {
      //   console.log(res, '优惠券-未使用')
      //   _self.setData({
      //     queryList: res.data.queryList,
      //     queryLength0: res.data.queryList.length
      //   })
      // }, function () {
      //   console.log('失败')
      //   wx.showToast({
      //     icon: 'loading',
      //     title: '加载数据失败',
      //   })
      // })
    },

    async tab1() {
      var that = this;
      var parmes2 = {
        // getChannel: '02',
        useStatus: '03',
        pageNum: '1',
        totalNum: '100',
      };
      const [, res] = await getAccountsCoupons(parmes2);
      if (res) {
        that.setData({
          queryList1: res.queryList || [],
          queryLength1: res.queryList.length,
        });
      }
      // network.requestLoading(baseUrl + '/wx/v0.1/accounts/coupons', parmes2, '正在加载数据', 'POST', function (res) {
      //   console.log(res, '优惠券-已使用')
      //   _self.setData({
      //     queryLength1: res.data.queryList.length
      //   })
      // }, function () {
      //   console.log('失败')
      //   wx.showToast({
      //     icon: 'loading',
      //     title: '加载数据失败',
      //   })
      // })
    },

    async tab2() {
      var that = this;
      var parmes3 = {
        // getChannel: '02',
        useStatus: '04',
        pageNum: '1',
        totalNum: '100',
      };
      const [, res] = await getAccountsCoupons(parmes3);
      if (res) {
        that.setData({
          queryList2: res.queryList || [],
          queryLength2: res.queryList.length,
        });
      }
    },
  },
};
</script>
<style>
@import './yhj.css';
</style>
