import { openNavigation, HarmonyOsMakePhoneCall } from './bridge/index';
import { transformFromWGSToGCJ } from './coordinate';

export async function addDomainToImgSrc(htmlContent, domain) {
  const imgRegex = /<img\s+([^>]*?)src=(["'])([^"']*?)\2([^>]*?)\/?>/gi;

  // 提取所有匹配的图片标签和关键信息
  const matches = [];
  let match;
  while ((match = imgRegex.exec(htmlContent)) !== null) {
    const [fullMatch, attrsBefore, quote, src, attrsAfter] = match;
    matches.push({ fullMatch, attrsBefore, quote, src, attrsAfter });
  }

  // 并行处理所有图片
  const processedMatches = await Promise.all(
    matches.map(async ({ fullMatch, attrsBefore, quote, src, attrsAfter }) => {
      // 合并前后属性字符串以检测 width/height
      const allAttrs = `${attrsBefore} ${attrsAfter}`;

      // ✅ 检测是否已存在 width 或 height 属性（支持任意格式：带引号、不带引号）
      const hasSize = /\b(width|height)=/i.test(allAttrs);

      // 处理域名逻辑
      const separator = !/^(https?:)?\/\//i.test(src) && !src.startsWith('/') ? '/' : '';
      const newSrc = `${!/^(https?:)?\/\//i.test(src) ? domain : ''}${separator}${src}`;

      // 如果已有尺寸属性，直接返回修改后的标签（仅替换域名）
      if (hasSize) {
        return {
          original: fullMatch,
          replaced: `<img ${attrsBefore}src=${quote}${newSrc}${quote}${attrsAfter}>`,
        };
      }

      // 无尺寸属性时获取并添加
      try {
        console.log('正在获取尺寸:', newSrc);
        const { width, height } = await getImageSize(newSrc);
        return {
          original: fullMatch,
          replaced: `<img ${attrsBefore}src=${quote}${newSrc}${quote}${attrsAfter} width="${width}" height="${height}">`,
        };
      } catch (err) {
        console.error('获取尺寸失败:', err);
        return { original: fullMatch, replaced: fullMatch };
      }
    })
  );

  // 统一替换所有匹配项
  let result = htmlContent;
  processedMatches.forEach(({ original, replaced }) => {
    result = result.replace(original, replaced);
  });

  return result;
}
// utils/image.js
export const getImageSize = (imgUrl) => {
  return new Promise((resolve, reject) => {
    uni.getImageInfo({
      src: imgUrl,
      success: (res) => resolve({ width: res.width, height: res.height }),
      fail: (err) => reject(err),
    });
  });
};

// 获取当前定位
// H5默认使用浏览器的navigator 获取位置信息 因为navigator获取位置信息需要https 本地无法调试 所以本地可以使用uni.getLocation H5情况下需要配置腾讯地图的key 目前用的我自己的 打包发布记得打开注释
// 检测是否在App内嵌WebView中
function isInAppWebView() {
  // 检测常见的App WebView标识
  const userAgent = navigator.userAgent.toLowerCase();
  return (
    /micromessenger/.test(userAgent) || // 微信浏览器
    /alipayclient/.test(userAgent) || // 支付宝浏览器
    /qq\//.test(userAgent) || // QQ浏览器
    /weibo/.test(userAgent) || // 微博浏览器
    /bytedance/.test(userAgent) || // 字节跳动系App
    /miniprogram/.test(userAgent) || // 小程序WebView
    window.webkit || // iOS WebView
    window.android // Android WebView
  );
}

// 使用IP地址获取大致位置（作为备选方案）
async function getLocationByIP() {
  try {
    // 使用免费的IP定位服务
    const response = await fetch('https://ipapi.co/json/');
    const data = await response.json();
    console.log('IP定位结果:', data);

    if (data && data.latitude && data.longitude) {
      // IP定位通常已经是GCJ02坐标系，但精度较低
      return {
        longitude: data.longitude,
        latitude: data.latitude,
        accuracy: 'low', // 标记为低精度
        source: 'ip', // 标记来源
      };
    }
    return null;
  } catch (error) {
    console.error('IP定位失败:', error);
    return null;
  }
}

// 获取默认位置（最后的备选方案）
function getDefaultLocation() {
  // 返回一个默认位置，例如城市中心
  return {
    longitude: 119.6499, // 宁德市中心经度
    latitude: 26.6617, // 宁德市中心纬度
    accuracy: 'low', // 标记为低精度
    source: 'default', // 标记来源
  };
}

// 检查位置权限状态
function checkLocationPermissionStatus() {
  try {
    // 从本地存储获取权限状态
    const permissionStatus = uni.getStorageSync('locationPermissionStatus');
    return permissionStatus || null;
  } catch (e) {
    console.error('获取位置权限状态失败:', e);
    return null;
  }
}

// 保存位置权限状态
function saveLocationPermissionStatus(status) {
  try {
    uni.setStorageSync('locationPermissionStatus', status);
  } catch (e) {
    console.error('保存位置权限状态失败:', e);
  }
}

// 清除位置权限状态（用于重置权限）
export function clearLocationPermissionStatus() {
  try {
    uni.removeStorageSync('locationPermissionStatus');
    console.log('位置权限状态已重置');
    return true;
  } catch (e) {
    console.error('清除位置权限状态失败:', e);
    return false;
  }
}

// 主定位函数
export function getLocation(options = {}) {
  const {
    showPermissionDialog = true, // 是否显示权限请求对话框
    timeout = 10000, // 超时时间，默认10秒
    enableHighAccuracy = true, // 是否启用高精度
    useIPFallback = true, // 是否使用IP定位作为备选
    useDefaultFallback = true, // 是否使用默认位置作为最后备选
    forcePermissionDialog = false, // 是否强制显示权限对话框（忽略缓存的权限状态）
  } = options;

  return new Promise((resolve) => {
    // H5环境（浏览器）
    // #ifdef H5
    // 检查是否支持地理定位
    if (!navigator.geolocation) {
      console.error('浏览器不支持地理定位');
      // 尝试备选方案
      tryFallbackMethods(resolve, useIPFallback, useDefaultFallback);
      return;
    }

    // 检测是否在App内嵌WebView中
    const inAppWebView = isInAppWebView();
    if (inAppWebView) {
      console.log('检测到App内嵌WebView环境');
    }

    // 获取缓存的权限状态
    const permissionStatus = forcePermissionDialog ? null : checkLocationPermissionStatus();

    // 根据权限状态决定是否显示对话框
    if (permissionStatus === 'granted') {
      // 用户已授权，直接请求位置
      console.log('用户已授权位置权限，直接请求位置');
      requestBrowserLocation(resolve, timeout, enableHighAccuracy, useIPFallback, useDefaultFallback);
    } else if (permissionStatus === 'denied') {
      // 用户已拒绝，直接使用备选方案
      console.log('用户已拒绝位置权限，使用备选方案');
      tryFallbackMethods(resolve, useIPFallback, useDefaultFallback);
    } else if (showPermissionDialog && inAppWebView) {
      // 首次请求或强制请求，显示权限对话框
      uni.showModal({
        title: '位置权限请求',
        content: '需要获取您的位置信息以提供更好的服务体验',
        confirmText: '授权',
        cancelText: '拒绝',
        success: (res) => {
          if (res.confirm) {
            // 用户同意，保存权限状态并请求位置
            saveLocationPermissionStatus('granted');
            requestBrowserLocation(resolve, timeout, enableHighAccuracy, useIPFallback, useDefaultFallback);
          } else {
            // 用户拒绝，保存权限状态并使用备选方案
            saveLocationPermissionStatus('denied');
            console.log('用户拒绝位置权限请求');
            tryFallbackMethods(resolve, useIPFallback, useDefaultFallback);
          }
        },
      });
    } else {
      // 其他情况，直接请求位置
      requestBrowserLocation(resolve, timeout, enableHighAccuracy, useIPFallback, useDefaultFallback);
    }
    // #endif

    // 微信小程序环境
    // #ifdef MP-WEIXIN
    // 获取缓存的权限状态
    const wxPermissionStatus = forcePermissionDialog ? null : checkLocationPermissionStatus();

    if (wxPermissionStatus === 'denied') {
      // 用户已拒绝，直接使用备选方案
      console.log('用户已拒绝位置权限，使用备选方案');
      tryFallbackMethods(resolve, useIPFallback, useDefaultFallback);
      return;
    }

    // 尝试获取位置
    uni.getLocation({
      type: 'wgs84',
      isHighAccuracy: true,
      success: (res) => {
        // 成功获取位置，保存权限状态
        saveLocationPermissionStatus('granted');

        try {
          const { latitude, longitude } = transformFromWGSToGCJ(res.latitude, res.longitude);
          resolve({
            longitude: longitude,
            latitude: latitude,
            accuracy: 'high',
            source: 'wechat',
          });
        } catch (e) {
          console.error('坐标转换失败:', e);
          resolve({
            longitude: res.longitude,
            latitude: res.latitude,
            accuracy: 'high',
            source: 'wechat',
          });
        }
      },
      fail: (err) => {
        console.error('小程序定位失败:', err);

        // 处理权限拒绝
        if (err.errMsg.includes('auth deny')) {
          console.log('需要获取定位权限');

          // 如果需要显示权限对话框且没有之前拒绝过
          if (showPermissionDialog && wxPermissionStatus !== 'denied') {
            uni.showModal({
              title: '位置权限请求',
              content: '需要获取您的位置信息以提供更好的服务体验',
              success: (res) => {
                if (res.confirm) {
                  // 用户同意，打开设置页面
                  uni.openSetting({
                    success: (settingRes) => {
                      if (settingRes.authSetting['scope.userLocation']) {
                        // 用户在设置页面打开了定位权限
                        saveLocationPermissionStatus('granted');

                        uni.getLocation({
                          type: 'wgs84',
                          isHighAccuracy: true,
                          success: (res) => {
                            const { latitude, longitude } = transformFromWGSToGCJ(res.latitude, res.longitude);
                            resolve({
                              longitude: longitude,
                              latitude: latitude,
                              accuracy: 'high',
                              source: 'wechat',
                            });
                          },
                          fail: () => {
                            tryFallbackMethods(resolve, useIPFallback, useDefaultFallback);
                          },
                        });
                      } else {
                        // 用户在设置页面未打开权限
                        saveLocationPermissionStatus('denied');
                        tryFallbackMethods(resolve, useIPFallback, useDefaultFallback);
                      }
                    },
                    fail: () => {
                      // 打开设置页面失败
                      saveLocationPermissionStatus('denied');
                      tryFallbackMethods(resolve, useIPFallback, useDefaultFallback);
                    },
                  });
                } else {
                  // 用户拒绝，保存权限状态
                  saveLocationPermissionStatus('denied');
                  tryFallbackMethods(resolve, useIPFallback, useDefaultFallback);
                }
              },
            });
          } else {
            // 不显示对话框，直接使用备选方案
            tryFallbackMethods(resolve, useIPFallback, useDefaultFallback);
          }
        } else {
          // 其他错误，使用备选方案
          tryFallbackMethods(resolve, useIPFallback, useDefaultFallback);
        }
      },
    });
    // #endif
  });
}

// 请求浏览器位置
function requestBrowserLocation(resolve, timeout, enableHighAccuracy, useIPFallback, useDefaultFallback) {
  const positionOptions = {
    enableHighAccuracy: enableHighAccuracy,
    timeout: timeout,
    maximumAge: 0, // 不使用缓存
  };

  navigator.geolocation.getCurrentPosition(
    (position) => {
      console.log('浏览器定位成功:', position.coords);

      // 成功获取位置，保存权限状态为已授权
      saveLocationPermissionStatus('granted');

      try {
        // H5默认返回WGS84坐标，需转换为GCJ02
        const { latitude, longitude } = transformFromWGSToGCJ(position.coords.latitude, position.coords.longitude);
        resolve({
          longitude: longitude,
          latitude: latitude,
          accuracy: 'high',
          source: 'browser',
        });
      } catch (e) {
        console.error('坐标转换失败:', e);
        resolve({
          longitude: position.coords.longitude,
          latitude: position.coords.latitude,
          accuracy: 'high',
          source: 'browser',
        });
      }
    },
    (error) => {
      console.error('浏览器定位失败:', error.message, error.code);

      // 处理不同类型的错误
      let errorMsg = '';
      switch (error.code) {
        case error.PERMISSION_DENIED:
          errorMsg = '用户拒绝了位置请求权限';
          // 用户明确拒绝了权限，保存状态
          saveLocationPermissionStatus('denied');

          // 可以在这里显示引导用户开启权限的提示
          if (isInAppWebView()) {
            console.log('App内嵌WebView环境下的权限被拒绝');
          }
          break;
        case error.POSITION_UNAVAILABLE:
          errorMsg = '位置信息不可用';
          // 这种情况不一定是权限问题，可能是设备问题
          break;
        case error.TIMEOUT:
          errorMsg = '获取位置超时';
          // 超时不是权限问题
          break;
        default:
          errorMsg = '未知错误';
      }

      console.log(errorMsg);

      // 尝试备选方案
      tryFallbackMethods(resolve, useIPFallback, useDefaultFallback);
    },
    positionOptions
  );
}

// 尝试备选定位方法
async function tryFallbackMethods(resolve, useIPFallback, useDefaultFallback) {
  // 尝试使用IP定位
  if (useIPFallback) {
    const ipLocation = await getLocationByIP();
    if (ipLocation) {
      console.log('使用IP定位作为备选');
      return resolve(ipLocation);
    }
  }

  // 最后使用默认位置
  if (useDefaultFallback) {
    console.log('使用默认位置作为最后备选');
    return resolve(getDefaultLocation());
  }

  // 如果所有方法都失败，返回null
  resolve(null);
}

// 拉起导航
export function openLocation(info) {
  const { latitude, longitude, address } = info;
  // #ifdef MP-WEIXIN
  uni.openLocation({
    latitude,
    longitude,
    address,
    scale: 28,
    success: (res) => {
      console.log('导航成功:', res);
    },
    fail: (err) => {
      console.error('导航失败:', err);
    },
  });
  // #endif
  // #ifdef H5
  const appName = uni.getStorageSync('app') || '';
  if (appName == '鸿蒙电动宁德') {
    openNavigation({
      latitude: latitude,
      longitude: longitude,
      address: address,
    });
  } else {
    getLocation().then((res) => {
      let trimAddress = address.replace(/\s+/g, '');
      let src = `https://uri.amap.com/navigation?from=${res.longitude},${res.latitude},当前位置&to=${longitude},${latitude},${trimAddress}&policy=1&coordinate=gaode&callnative=0`;
      window.location.href = src;
    });
  }
  // #endif
}

// 拨打电话
export function makePhoneCall(phoneNumber) {
  // #ifdef H5
  const appName = uni.getStorageSync('app') || '';
  if (appName == '电动宁德') {
  } else if (appName === '鸿蒙电动宁德') {
    HarmonyOsMakePhoneCall({ phone: phoneNumber });
  }
  ls.callNo({ tel: phoneNumber });
  // #endif
  // #ifndef MP-WEIXIN
  uni.makePhoneCall({
    phoneNumber: phoneNumber, //仅为示例
  });
  // #endif
}
