function getIndexNavData() {
  var arr = [
      {
          name: '亨通电桩-01号枪',
          pice: '100',
          address: '厦门软件园站',
          time: '2017.10.18 06:30',
          state: '待充电'
      },
      {
          name: '亨通电桩-02号枪',
          pice: '110',
          address: '南宁软件园站',
          time: '2017.11.18 04:00',
          state: '未支付'
      },
      {
          name: '亨通电桩-03号枪',
          pice: '120',
          address: '中东软件园站',
          time: '2017.12.18 16:00',
          state: '待点评'
      },
      {
          name: '亨通电桩-03号枪',
          pice: '120',
          address: '湖南软件园站',
          time: '2017.12.18 16:00',
          state: '待充电'
      },
      {
          name: '亨通电桩-03号枪',
          pice: '120',
          address: '深圳软件园站',
          time: '2017.12.18 16:00',
          state: '待充电'
      },
      {
          name: '亨通电桩-03号枪',
          pice: '120',
          address: '东亚软件园站',
          time: '2017.12.18 16:00',
          state: '待充电'
      },
      {
          name: '亨通电桩-03号枪',
          pice: '120',
          address: '南极软件园站',
          time: '2017.12.18 16:00',
          state: '待充电'
      },
      {
          name: '亨通电桩-03号枪',
          pice: '120',
          address: '澳门软件园站',
          time: '2017.12.18 16:00',
          state: '待充电'
      },
      {
          name: '亨通电桩-03号枪',
          pice: '120',
          address: '香港软件园站',
          time: '2017.12.18 16:00',
          state: '待充电'
      },
      {
          name: '亨通电桩-03号枪',
          pice: '120',
          address: '宁德软件园站',
          time: '2017.12.18 16:00',
          state: '待充电'
      },
      {
          name: '亨通电桩-03号枪',
          pice: '120',
          address: '郑州软件园站',
          time: '2017.12.18 16:00',
          state: '待充电'
      }
  ];
  return arr;
}
function getIndexNavDataNo() {
  var arrNo = [
      {
          name: '亨通电桩-01号枪',
          pice: '100',
          address: '厦门软件园站',
          time: '2017.10.18 06:30',
          state: '未支付'
      },
      {
          name: '亨通电桩-02号枪',
          pice: '110',
          address: '南宁软件园站',
          time: '2017.11.18 04:00',
          state: '未支付'
      },
      {
          name: '亨通电桩-03号枪',
          pice: '120',
          address: '中东软件园站',
          time: '2017.12.18 16:00',
          state: '未支付'
      },
      {
          name: '亨通电桩-03号枪',
          pice: '120',
          address: '中东软件园站',
          time: '2017.12.18 16:00',
          state: '未支付'
      }
  ];
  return arrNo;
}
function getIndexNavCharge() {
  var arrCharge = [
      {
          name: '亨通电桩-01号枪',
          pice: '100',
          address: '厦门软件园站',
          time: '2017.10.18 06:30',
          state: '待充电'
      },
      {
          name: '亨通电桩-02号枪',
          pice: '110',
          address: '南宁软件园站',
          time: '2017.11.18 04:00',
          state: '待充电'
      },
      {
          name: '亨通电桩-03号枪',
          pice: '120',
          address: '中东软件园站',
          time: '2017.12.18 16:00',
          state: '待充电'
      },
      {
          name: '亨通电桩-03号枪',
          pice: '120',
          address: '中东软件园站',
          time: '2017.12.18 16:00',
          state: '待充电'
      }
  ];
  return arrCharge;
}
module.exports = {
  getIndexNavData: getIndexNavData,
  getIndexNavDataNo: getIndexNavDataNo,
  getIndexNavCharge: getIndexNavCharge
};
