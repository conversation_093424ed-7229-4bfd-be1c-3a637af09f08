.myorder-tab {
  /* position: fixed;
  top: 0;
  left: 0;
  right: 0; */
  padding: 0 2.7%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background-color: #fff;
  color: #333;
  font-size: 30rpx;
  z-index: 999;
}

.myorder-tab view {
  position: relative;
  display: inline-block;
  width: 30%;
}

.myorder-tab view.active {
  border-bottom: 1rpx solid #2196f3;
}

/* .myorder-tab .tab-dot::after {
  content: '';
  position: absolute;
  width: 11rpx;
  height: 11rpx;
  border-radius: 50%;
  background-color: #ff3b30;
  right: -22rpx;
  top: 40rpx;
} */

.text-wrap {
  position: relative;
}

.text-num {
  position: absolute !important;
  top: 0;
  right: -15rpx;
  border-radius: 50%;
  background: red;
  line-height: 30rpx;
  font-size: 25.34rpx;
  font-weight: 500;
  color: #fff;
  text-align: center;
  min-width: 30rpx;
  width: auto !important;
  padding: 0 10rpx;
}


.myorder-main {
  padding-top: 10rpx;
  height: calc(100vh - 88rpx);
}

.myorder-image {
  text-align: center;
  color: #b2b2b2;
  font-size: 32rpx;
  margin-top: 50%;
}

.myorder-image image {
  display: block;
  width: 150rpx;
  height: 176rpx;
  margin: 0 auto 20rpx auto;
}

.order-wrap {
  height: 100%;
  overflow-y: auto;
}

.myorder-list {
  background-color: #fff;
  margin-top: 20rpx;
}

/* .list-header {
  padding: 0 30rpx;
  height: 70rpx;
  line-height: 70rpx;
  border-bottom: 1rpx solid #e1e1e1;
  overflow: hidden;
  color: #333;
  font-size: 30rpx;
}
.list-header .order-state {
  width: 140rpx;
  height: 42rpx;
  line-height: 42rpx;
  text-align: center;
  border-radius: 42rpx;
  color: #fff;
  font-size: 24rpx;
  margin: 14rpx 0;
} */
.list-main {
  position: relative;
  padding: 12rpx 20rpx 12rpx 40rpx;
  color: #666;
  font-size: 28rpx;
}

.list-main .content {
  padding: 10px 0;
  font-size: 14px;
  line-height: 18px;
}

.notice-title {
  line-height: 42rpx;
  color: #333333;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.hot {
  display: inline-block;
  width: 10rpx;
  height: 10rpx;
  background-color: rgb(242, 55, 55);
  border-radius: 50%;
  margin-right: 10rpx;
}

.notice-time {
  font-size: 22rpx;
  line-height: 26rpx;
  color: #999999;
}

/* .list-main .order-address,
.list-main .order-time {
  padding: 15rpx 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}
.list-main .order-address icon {
  vertical-align: middle;
  color: #1e82d2;
  margin-right: 20rpx;
}
.list-main .order-time icon {
  vertical-align: middle;
  color: #fc2332;
  margin-right: 20rpx;
} */
/* .list-main .order-money {
  position: absolute;
  right: 30rpx;
  top: 50rpx;
  font-size: 32rpx;
  color: #3e3e3e;
} */

/* dd */

.body-color {
  background: #eef2f6;
  padding-top: 3rpx;
}

.wxswiper-header {
  background: #fff;
  padding: 17rpx 0;
}

.log-list {
  display: flex;
  flex-direction: column;
  padding: 40rpx;
}

.log-item {
  margin: 10rpx;
}

.swiper-tab {
  width: 690rpx;
  border-bottom: 2rpx solid #777777;
  text-align: center;
  line-height: 66rpx;
  margin: 0 auto;
  border: 3rpx solid #de3031;
  border-radius: 10rpx;
  display: flex;
  height: 66rpx;
}

.swiper-tab-list {
  font-size: 28rpx;
  width: 33.33%;
  color: #de3031;
  font-weight: 600;
}

.on {
  color: #fff;
  background: #de3031;
}

.swiper-box {
  display: block;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.swiper-box view {
  /* text-align: center;   */
}

#infos {
  border-left: 5rpx solid #de3031;
  border-right: 5rpx solid #de3031;
}

.wx-msg-tab {
  height: 180rpx;
  padding: 25rpx 0 0 0;
  box-sizing: border-box;
  display: flex;
}

.wx-msg-tab .wx-msg-img {
  width: 130rpx;
  height: 100%;
}

.wx-msg-tab .wx-msg-img image {
  height: 80rpx;
  width: 80rpx;
}

.wx-msg-tab .wx-msg-message {
  width: 590rpx;
  height: 100%;
  padding-right: 30rpx;
  border-bottom: 2rpx solid #d9d9d9;
  position: relative;
}

.wx-msg-message .wx-msg-messageAll {
  width: 50%;
  display: inline-block;
  font-size: 30rpx;
  color: #333;
}

.wx-msg-message .wx-msg-message1 {
  text-align: left;
}

.wx-msg-message .wx-msg-message2 {
  text-align: right;
}

.wx-msg-message .wx-msg-messageBlock {
  font-size: 24rpx;
  color: #888;
  display: block;
  text-align: left;
  line-height: 45rpx;
}

.wx-msg-message .wx-msg-messageBlock image {
  width: 16rpx;
  height: 16rpx;
  margin-right: 10rpx;
}

.wx-msg-message .wx-msg-messageimg image {
  height: 20rpx;
}

.wx-msg-message .wx-absoicon {
  position: absolute;
  right: 30rpx;
  font-size: 24rpx;
  text-align: center;
  width: 90rpx;
  height: 40rpx;
  border-radius: 10rpx;
  bottom: 47rpx;
  box-sizing: border-box;
  line-height: 40rpx;
}

.wx-msg-message .wx-absoicon1 {
  color: #70c7e9;
  border: 1rpx solid #70c7e9;
}

.wx-msg-message .wx-absoicon2 {
  color: #de3031;
  border: 1rpx solid #de3031;
}

/* .list-header .blue {
  border: 1rpx solid #2196f3;
  background: #2196f3;
}
.list-header .red {
  border: 1rpx solid #fc2332;
  background: #fc2332;
}
.list-header .gray {
  border: 1rpx solid #bebcba;
  background: #bebcba;
}
.list-header .orange {
  border: 1rpx solid #ff9800;
  background: #ff9800;
} */
.swiper-box {
  margin-top: 0;
}

.noticeBar-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24rpx;
  height: 72rpx;
  background-color: #e9f6fe;
  margin-bottom: 24rpx;
}

.noticeBar-top .noticeBar-title {
  font-size: 26rpx;
  color: #333333;
  line-height: 40rpx;
  display: flex;
  align-items: center;
}

.noticeBar-top .noticeBar-right {
  display: flex;
  align-items: center;
}

.noticeBar-top .noticeBar-btn {
  background-color: #1e9cff;
  border-radius: 100rpx;
  width: 76rpx;
  height: 38rpx;
  text-align: center;
  line-height: 38rpx;
  color: #fff;
  font-size: 22rpx;
  margin-right: 20rpx;
}

.noticeBar-top .noticeBar-icon {
  width: 24rpx;
  height: 24rpx;
}

.noticeBar-top .noticeBar-left {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}