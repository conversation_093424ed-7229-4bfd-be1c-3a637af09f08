<!--
@name: 我的积分
@description: 我的积分页面
@time: 2024/7/25
-->
<template>
  <view class="my-points" :style="{ backgroundImage: `url(${IMG_PATH}/my-points-bg.png)` }">
    <view class="my-points-header">
      <view class="header-content">
        <text class="number">{{ userInfo.custIntegralNumber || 0 }}</text>
        <text class="current-points">当前积分</text>
        <view class="header-button-box">
          <view class="header-button">
            <AppButton
              :custom-style="{ height: '70rpx' }"
              shape="circle"
              color="linear-gradient(90deg, #1ECEFF 0%, #1E9CFF 100%)"
              @click="jumpToShop"
            >
              积分商城
            </AppButton>
          </view>
          <view class="header-button">
            <AppButton
              :custom-style="{ height: '70rpx', 'border-color': '#E5E5E5', color: '#0D87E8' }"
              shape="circle"
              plain
              @click="jumpToExchange"
            >
              兑换说明
            </AppButton>
          </view>
        </view>
      </view>
    </view>
    <!--积分明细-->
    <view class="points-list">
      <text class="points-list-title">积分明细</text>
      <tab-select class="tag-list" :options="tabList" @change="changeTab"></tab-select>
      <view class="points-list-content" v-if="pointsList.length !== 0">
        <view class="list-item" v-for="item in pointsList" :key="item.id">
          <view class="list-item-left">
            <view>
              <text class="active-content">{{ item.eventName }}</text>
              <span class="active-name" v-if="item.goodsName">{{ item.goodsName }}</span>
            </view>
            <text class="active-time">{{ item.chargeTime }}</text>
          </view>
          <view class="list-item-right column-box">
            <view class="list-item-right">
              <view
                :class="[
                  'circle',
                  {
                    orange: item.chargeType === '02',
                  },
                ]"
              >
              </view>
              <text class="name">积分</text>
              <text class="points"> {{ item.chargeType === '01' ? '+' : '-' }}{{ parseFloat(item.chargeNum) }} </text>
            </view>
            <view class="remaining">
              剩余积分<span>{{ parseFloat(item.integralNumber) || ' -' }}</span>
            </view>
          </view>
        </view>
      </view>
      <AEmpty v-else />
    </view>
  </view>
</template>

<script>
import AppButton from '@/components/AppButton/index';
import AEmpty from '@/components/AEmpty/index';
import { getIntegral } from '@/services/index.js';
import { mapState } from 'vuex';
import tabSelect from './components/tabSelect';

export default {
  name: 'index',
  components: {
    AppButton,
    AEmpty,
    tabSelect,
  },
  data() {
    return {
      pointsList: [],
      status: 'more',
      pageNum: 1,
      totalNum: 10,
      tabList: [
        {
          id: '',
          name: '全部',
        },
        {
          id: '01',
          name: '收入',
        },
        {
          id: '02',
          name: '支出',
        },
      ],
      tab: '',
      // findData: {
      //   code: '0604',
      //   label: '积分兑换',
      //   show: true,
      //   text: ['积分兑换说明'],
      // },
    };
  },
  computed: {
    ...mapState({
      userInfoOBJ: (state, getters) => getters['login/getUserInfo'],
    }),
    userInfo() {
      return {
        imgUrl: '',
        ...(this.userInfoOBJ ? this.userInfoOBJ : {}),
      };
    },
  },
  onLoad() {
    this.getPointList();
    // 用户信息
    this.getUserInfo();
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: async function () {
    try {
      // 统一触发数据加载（H5/小程序通用）
      console.log('下拉');
      this.pointsList = [];
      this.pageNum = 1;
      await this.getPointList();

      // #ifdef H5
      // H5 特殊处理：强制设置刷新终止（避免卡住）
      setTimeout(() => {
        uni.stopPullDownRefresh();
      }, 500);
      // #endif

      // #ifndef H5
      // 非 H5 平台正常终止
      uni.stopPullDownRefresh();
      // #endif
    } catch (error) {
      // 错误处理
      console.error('下拉刷新失败:', error);
      uni.stopPullDownRefresh();
    }
  },
  // 触底分页查询
  onReachBottom() {
    if (this.status === 'noMore') return;
    this.getPointList();
  },
  methods: {
    // 获取用户信息
    getUserInfo() {
      this.$store.dispatch('login/getUserInfoCallback');
    },
    // 跳转到积分商城
    jumpToShop() {
      uni.navigateTo({
        url: '/pages/intergral/shop/index',
      });
    },
    // 跳转积分说明
    jumpToExchange() {
      uni.navigateTo({
        url: '/pages/intergral/exchangeExplain/index',
      });
    },
    // 获取积分流水列表
    async getPointList() {
      const params = {
        totalNum: this.totalNum,
        pageNum: this.pageNum,
        chargeType: this.tab,
      };
      const [err, res] = await getIntegral(params);
      if (res) {
        this.pointsList.push(...res.integralList);
        this.status = res.integralList.length >= this.totalNum ? 'more' : 'noMore';
        if (this.status === 'more') {
          this.pageNum++;
        }
      }
    },
    // tab切换
    changeTab(data) {
      this.tab = data.id;
      this.pointsList = [];
      this.pageNum = 1;
      this.getPointList();
    },
  },
};
</script>

<style scoped lang="scss">
.my-points {
  height: 100%;
  background-size: 750rpx 600rpx;
  background-position: center top -88px;
  background-repeat: no-repeat;
  &-header {
    padding: 29rpx 0 0 67rpx;
    .header-content {
      display: flex;
      flex-direction: column;
      .number {
        font-size: 56rpx;
        font-weight: bold;
        margin-bottom: 10rpx;
        line-height: 42rpx;
        color: #333333;
      }
      .current-points {
        color: #333333;
        font-size: 28rpx;
        font-weight: bold;
        line-height: 42rpx;
      }
      .header-button-box {
        display: flex;
        gap: 28rpx;
        grid-gap: 28rpx;
        .header-button {
          width: 184rpx;
          margin-top: 28rpx;
        }
      }
    }
  }
  .points-list {
    margin-top: 62rpx;
    padding: 24rpx;
    background: white;
    min-height: calc(100% - 283rpx);
    border-radius: 32rpx 32rpx 0 0;
    &-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333333;
    }
    .tag-list {
      margin: 24rpx 0 0rpx 0;
      display: block;
    }
    &-content {
      margin-top: 24rpx;
      .list-item {
        padding: 24rpx 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1rpx solid #eeeeee;
        &-left {
          display: flex;
          flex-direction: column;
          .active-content {
            font-size: 28rpx;
            font-weight: bold;
            color: #333333;
            line-height: 42rpx;
          }
          .active-name {
            font-size: 24rpx;
            font-weight: normal;
            line-height: 32rpx;
            letter-spacing: 0rpx;
            color: #999999;
            display: inline-block;
            margin-left: 10rpx;
          }
          .active-time {
            margin-top: 8rpx;
            font-size: 24rpx;
            line-height: 32rpx;
            color: #999999;
          }
        }
        &-right {
          display: flex;
          align-items: center;
          .circle {
            width: 10rpx;
            height: 10rpx;
            border-radius: 10rpx;
            background: #1e9cff;
            margin-right: 6rpx;
          }
          .orange {
            background: #ff8301;
          }
          .name {
            font-size: 26rpx;
            line-height: 40rpx;
            color: #999999;
            margin-right: 6rpx;
          }
          .points {
            font-size: 36rpx;
            font-weight: bold;
            line-height: 42rpx;
            color: #333333;
          }
          .remaining {
            font-size: 24rpx;
            font-weight: normal;
            line-height: 32rpx;
            letter-spacing: 0rpx;
            color: #999999;
            span {
              color: #1e9cff;
            }
          }
        }
        .column-box {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 8rpx;
          grid-gap: 8rpx;
        }
      }
    }
  }
}
</style>
