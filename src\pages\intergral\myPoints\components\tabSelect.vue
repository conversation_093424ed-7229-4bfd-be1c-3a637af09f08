<!--
@name: tab选择器
@description: 描述
@time: 2025/1/3
-->
<template>
  <scroll-view :scroll-x="true" :show-scrollbar="false">
    <view class="tabs">
      <view
        v-for="(item, id) in options"
        :key="id"
        :class="selectedId == id ? 'is-active-box' : 'tabs-item'"
        @click="handleClick(item, id)"
      >
        <text :class="{ 'is-active': selectedId === id }">{{ item.name }}</text>
      </view>
    </view>
  </scroll-view>
</template>

<script>
export default {
  name: 'index',
  props: {
    options: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      selectedId: 0,
    };
  },
  methods: {
    // 切换tab
    handleClick(item, id) {
      this.selectedId = id;
      this.$emit('change', item);
    },
  },
};
</script>

<style scoped lang="scss">
.tabs {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  overflow: scroll;
  white-space: nowrap;
  background: rgb(245, 245, 245);
  border-radius: 8rpx;
  padding: 11rpx;
  //   gap: 50rpx;
  &-item {
    display: flex;
    flex-direction: column;
    font-size: 28rpx;
    line-height: 42rpx;
    color: #666666;
    align-items: center;
    padding: 11rpx 85rpx;
    background: rgb(245, 245, 245);
    border-radius: 8rpx;

    .is-active {
      font-size: 28rpx;
      font-weight: bold;
      line-height: 42rpx;
      color: #666666;
      color: #333333;
    }
    .line {
      margin-top: 12rpx;
      height: 6rpx;
      width: 37rpx;
      background: #1e9cff;
      border-radius: 2rpx;
    }
  }
  .is-active-box {
    display: flex;
    flex-direction: column;
    font-size: 28rpx;
    line-height: 42rpx;
    color: #666666;
    align-items: center;
    padding: 11rpx 85rpx;
    background: #fff;
    border-radius: 8rpx;
    .is-active {
      font-size: 28rpx;
      font-weight: bold;
      line-height: 42rpx;
      color: #666666;
      color: #333333;
    }
  }
}
</style>
