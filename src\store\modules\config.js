import { requestLoading } from '@/utils/network.js';
import { anthodsDate } from '@/utils/base.js';
const state = {
  config: {
    paramCode: 'appTheme',
    paramName: 'APP主题',
    paramSortCode: 'Cust',
    paramValue: '1',
  },
};
const getters = {
  getConfigInfo: (state) => {
    return state.config;
  },
};
const actions = {
  async initConfig({ commit }) {
    requestLoading(
      anthodsDate() + '/api/v0.1/paras',
      {
        paraCode: 'appTheme',
      },
      '',
      'GET',
      function (res) {
        console.log(res.list, 'list');
        if (res && res.data && res.data.list && Array.isArray(res.data.list) && res.data.list.length > 0) {
          const config = res.data.list[0];
          commit('SET_CONFIG', config);
        }
      }
    );
  },
};
const mutations = {
  ['SET_CONFIG'](state, info) {
    state.config = info;
  },
};
export const config = {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
};
