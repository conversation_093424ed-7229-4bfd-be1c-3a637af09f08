<!--
@name: 积分商城卡片
@description: 子组件
@time: 2024/7/25
-->
<template>
  <view class="points-card" @click="jumpToDetail">
    <view class="card-image">
      <img class="goods-img" :src="getImage" />
      <!--        售罄-->
      <img v-if="cardItem.goodsNumber === '0'" class="sell-out" :src="`${IMG_PATH}sell-out.png`" />
    </view>
    <text class="goods-name">{{ cardItem.goodsName }}</text>
    <view class="goods-value">
      <text class="goods-value-item">{{ cardItem.goodsIntegral }}</text>
      <text class="goods-value-item">积分</text>
      <text class="goods-value-item" v-if="cardItem.goodsMode == '02'">+{{ cardItem.goodsAmt }}元</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'pointsCard',
  props: {
    cardItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  computed: {
    // 商品图片路径
    getImage() {
      const str = `${process.env.VUE_APP_BASE_HOST}/pub/api/v0.1/attachs/${this.cardItem.fileId}`;
      console.log('str', str);
      return str;
    },
  },
  methods: {
    // 跳转到商品详情页
    jumpToDetail() {
      uni.navigateTo({
        url: `/pages/intergral/redeemPoints/index?goodsId=${this.cardItem.goodsId}`,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.points-card {
  display: flex;
  flex-direction: column;
  border-radius: 16rpx;
  padding-bottom: 24rpx;
  background: white;
  margin-bottom: 20rpx;
  .card-image {
    width: 341rpx;
    height: 341rpx;
    border-radius: 16rpx 16rpx 0 0;
    position: relative;
    .goods-img {
      height: 100%;
      width: 100%;
    }
    .sell-out {
      position: absolute;
      width: 200rpx;
      height: 200rpx;
      top: 70rpx;
      left: 70rpx;
    }
  }
  .card-image-sell-out {
    opacity: 0.5;
  }
  .goods-name {
    margin: 16rpx 0 12rpx 0;
    font-size: 28rpx;
    font-weight: bold;
    line-height: 42rpx;
    color: #333333;
    margin-left: 16rpx;
    width: 329rpx;
    white-space: nowrap; /* 禁止换行 */
    overflow: hidden; /* 隐藏溢出部分 */
    text-overflow: ellipsis; /* 显示省略号 */
  }
  .goods-value {
    line-height: 29rpx;
    color: #1e9cff;
    display: flex;
    align-items: center;
    &-item:nth-child(1) {
      font-size: 40rpx;
      font-weight: bold;
      margin-left: 16rpx;
    }
    &-item:nth-child(2) {
      font-size: 24rpx;
      margin-left: 5rpx;
    }
  }
}
</style>
