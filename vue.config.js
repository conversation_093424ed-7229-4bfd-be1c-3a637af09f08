/**
 * UniApp项目的webpack配置
 * 使用最简配置，避免与UniApp内部配置冲突
 */
module.exports = {
  // 生产环境不生成sourceMap
  productionSourceMap: false,

  // 配置webpack性能提示
  configureWebpack: {
    performance: {
      maxEntrypointSize: 2000000,
      maxAssetSize: 1000000,
      hints: 'warning',
    },
  },

  // 开发服务器配置
  devServer: {
    port: 8080,
    open: true,
    overlay: {
      warnings: false,
      errors: true,
    },
  },
};
