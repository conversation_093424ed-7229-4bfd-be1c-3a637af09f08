<template>
  <view class="invoiceopening">
    <!-- <uni-nav-bar
      height="50px"
      :shadow="false"
      left-icon="left"
      borderColor="#fff"
      title="发票开具"
      @clickLeft="clickLeft"
    >
      <template slot="left">
        <uni-icons type="left" size="30"></uni-icons>
      </template>
    </uni-nav-bar> -->
    <view class="invoiceopening-list">
      <view @tap="billingorder('02')" class="list-main">
        <text>按订单开票</text>
        <uni-icons type="right" size="25"></uni-icons>
        <!-- <icon
          type="icon iconfont icon-arrow_right"
          size="18"
          style="color: #333"
        /> -->
      </view>
      <view @tap="billinghistory" class="list-main">
        <text>开票历史</text>
        <uni-icons type="right" size="25"></uni-icons>
        <!-- <icon
          type="icon iconfont icon-arrow_right"
          size="18"
          style="color: #333"
        /> -->
      </view>
      <view @tap="billingUsually" class="list-main">
        <text>常用发票抬头</text>
        <uni-icons type="right" size="25"></uni-icons>
        <!-- <icon
          type="icon iconfont icon-arrow_right"
          size="18"
          style="color: #333"
        />
      </view> -->
        <!-- <view bindtap='billingexplain' class="list-main">
      <text>开票说明</text><icon type="icon iconfont icon-arrow_right" size="18" style='color:#333' />
    </view> -->
      </view>
      <view @tap="billingorder('10')" class="list-main">
        <text>会员开票</text>
        <uni-icons type="right" size="25"></uni-icons>
      </view>
      <view @tap="billingorder('09')" class="list-main">
        <text>商品开票</text>
        <uni-icons type="right" size="25"></uni-icons>
      </view>
    </view>
  </view>
</template>

<script>
import uniNavBar from '@dcloudio/uni-ui/lib/uni-nav-bar/uni-nav-bar.vue';
import uniIcons from '@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue';
export default {
  components: { uniNavBar, uniIcons },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    clickLeft() {
      uni.navigateBack({
        delta: 1,
      });
    },
    billingexplain() {
      uni.navigateTo({
        url: '/subPackages/billingexplain/billingexplain',
      });
    },

    billingorder(type) {
      console.log('211');
      // 带个type  02 充电列表 10 会员列表  09 商品列表
      uni.navigateTo({
        url: `/subPackages/billingorder/billingorder?type=${type}`,
      });
    },

    billinghistory() {
      uni.navigateTo({
        url: '/subPackages/billinghistory/billinghistory',
      });
    },

    billingUsually() {
      uni.navigateTo({
        url: '/subPackages/billingUsually/commonHeadingsList/list',
      });
    },
    invoicing() {
      uni.showToast({
        title: '此服务暂未开放, 计划于3月开放',
        icon: 'none',
        duration: 2000,
      });
    },
  },
};
</script>

<style scoped>
/* pages/personal/personal.wxss */
page {
  background: #fff;
}
.invoiceopening-list {
  border-top: 2rpx solid #e1e1e1;
}
.list-main {
  position: relative;
  margin: 0 30rpx;
  height: 100rpx;
  line-height: 100rpx;
  color: #333;
  background-color: #fff;
  font-family: 'PingFang SC';
  font-size: 32rpx;
  font-weight: 400;
  border-bottom: 2rpx solid #e1e1e1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.invoiceopening-list icon {
  float: right;
  color: #999;
  height: 30rpx;
  padding: 7rpx 0;
}
</style>
