<template>
  <ul class="wrap">
    <!-- <div class="tab-wrap">
      <div class="tab-item" v-if="select == 4" @click="changeTab(4)" :class="{ active: select == 4 }">会员</div>
      <div class="tab-item" v-if="select == 2" @click="changeTab(2)" :class="{ active: select == 2 }">其他</div>
    </div> -->
    <li v-for="(item, index) in list" :key="index" :style="{ maxHeight: active.includes(index) ? '460rpx' : '80rpx' }">
      <div class="title" @click="open(index)">
        <div class="text">{{ item.title || '-' }}</div>
        <div class="icon" :class="{ active: active.includes(index) }">
          <img :src="icon" mode="scaleToFill" />
        </div>
      </div>
      <div class="content">
        {{ item.content || '-' }}
      </div>
    </li>
  </ul>
</template>

<script>
// /api/v0.1/faq
import network from '@/utils/network.js';
import { baseDate, baseDateNo } from '@/utils/base.js';
import tabSelect from '@/components/TabSelect/index';
const baseUrl = baseDate();

import { getCommonProblemList } from '@/services/index.js';
export default {
  props: {},
  components: { tabSelect },
  data() {
    return {
      list: [],
      active: [],
      select: 4,
    };
  },
  computed: {
    icon() {
      return this.IMG_PATH + 'icon-up.png';
    },
  },
  watch: {},
  onLoad(options) {
    const { type } = options;
    this.changeTab(type);
  },
  mounted() {},
  methods: {
    changeTab(num) {
      this.select = num;
      this.initData();
    },
    open(index) {
      if (this.active.includes(index)) {
        this.active = this.active.filter((q) => q !== index);
      } else {
        this.active.push(index);
      }
    },
    async initData() {
      const _this = this;
      const [, res] = await getCommonProblemList({
        typeId: this.select,
      });
      if (res && res.ret == '200') {
        console.log(res, 'res');
        if (res && res.faqList) {
          _this.list = res.faqList;
        } else {
          _this.list = [];
        }
      } else {
      }
    },
  },
};
</script>

<style scoped lang="scss">
.wrap {
  display: flex;
  flex-direction: column;
  width: 100vw;
  background: #efefef;
  height: 100vh;
  overflow-y: auto;
  padding-top: 20rpx;
  position: relative;
  .tab-wrap {
    position: absolute;
    top: 0;
    width: 100%;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10rpx 30rpx;
    .tab-item {
      text-align: center;
      height: 100%;
      flex: 1;
      position: relative;
      display: flex;
      align-items: center;
      // justify-content: center;
      justify-content: flex-start;
      border-radius: 10rpx;
      &.active {
        // background: #42a5f5;
        // color: #fff;
      }
    }
  }
  li {
    width: 100%;
    overflow: hidden;
    transition: 0.3s;
  }
  .title {
    width: 100%;
    padding: 20rpx 30rpx;
    box-sizing: border-box;
    color: #6b6b6b;
    background: #fff;
    display: flex;
    letter-spacing: 5rpx;
    font-size: 30rpx;
    flex-direction: row;
    align-items: center;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    .text {
      width: 100%;
      padding: 5px 0;
      max-width: 80%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .icon {
      margin-left: auto;
      margin-right: 15rpx;
      transform: rotate(0);
      img {
        width: 40rpx;
        height: 40rpx;
      }
    }
    .active {
      transform: rotate(90deg);
    }
  }
  .content {
    padding: 10rpx;
    width: 100%;
    white-space: wrap;
    overflow: auto;
    max-height: 400rpx;
    background: transparent;
    padding: 20rpx 35rpx;
    box-sizing: border-box;
    letter-spacing: 3rpx;
    line-height: 35rpx;
    font-size: 26rpx;
  }
}
</style>
