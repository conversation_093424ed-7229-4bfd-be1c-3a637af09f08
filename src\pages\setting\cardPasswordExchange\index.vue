<template>
  <div class="mine-page">
    <div class="card">
      <uni-forms
        ref="form"
        :model-value="form"
        err-show-type="toast"
        validate-trigger="blur"
        :label-width="100"
        label-align="right"
        :border="true"
        class="form-wrap"
      >
        <uni-forms-item label="输入卡密：" name="password">
          <uni-easyinput
            v-model="form.password"
            :clearable="false"
            size="small"
            :maxlength="20"
            placeholder="请输入卡密"
            placeholder-style="font-size: 32rpx; color: #999;"
          ></uni-easyinput>
        </uni-forms-item>
      </uni-forms>
      <div @click="pointClick" class="tip-text">
        <!-- <span class="icon">!</span>  -->
        使用积分兑换请<span>点击前往积分商城 ></span>
      </div>

      <div class="agreement-tip">
        <radio-group @change="radioChange">
          <label class="uni-list-cell uni-list-cell-pd">
            <view>
              <radio value="1" :checked="checked" @change="changeRadio" />
              <text class="protocol-item">兑换券请阅读下方</text>
              <text class="protocol-item2" @click="openProtocol('member')">《充值卡使用规则》</text>
            </view>
          </label>
        </radio-group>
      </div>
    </div>
    <div class="btn" @click="exchange">确认兑换</div>
  </div>
</template>

<script>
import { doCharge } from '@/services/index';
import uniForms from '@/components/uni-ui/uni-forms/uni-forms';
import uniFormsItem from '@/components/uni-ui/uni-forms-item/uni-forms-item';
import uniEasyinput from '@/components/uni-ui/uni-easyinput/uni-easyinput';
export default {
  props: {},
  components: { uniForms, uniFormsItem, uniEasyinput },
  data() {
    return {
      form: {
        password: '',
      },
      checked: false,
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    async exchange() {
      if (!this.checked) {
        uni.showModal({
          content: '兑换前请先阅读并同意充值卡使用规则',
          showCancel: false,
          confirmText: '我知道了',
        });
        return;
      }
      console.log(this.form, 'form');
      if (!this.form.password) {
        uni.showToast('请输入卡密');
        return;
      }
      const [, res] = await doCharge({
        cardSecret: this.form.password,
      });
      if (res && res.ret == '200') {
        uni.showToast({
          title: '兑换成功',
          icon: 'success',
        });
      }
    },
    openProtocol() {
      uni.navigateTo({
        url: `/pages/setting/agreement/index?&code=0415&title=${'充值卡使用规则'}`,
      });
    },
    radioChange(val) {
      console.log(val.detail.value, 'val2');
      this.checked = val.detail.value == '1';
    },
    pointClick() {
      uni.navigateTo({
        url: '/pages/intergral/shop/index',
      });
    },
  },
};
</script>

<style scoped lang="scss">
.mine-page {
  position: relative;
  z-index: 1;
  height: 100vh;
  box-sizing: border-box;
  overflow-y: auto;
  background-color: #f6f4f4;
  display: flex;
  flex-direction: column;
  .card {
    width: 100%;
    padding: 40rpx 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    box-sizing: border-box;
    background-color: #fff;
  }
  .form-wrap {
    width: 100%;
  }
  .btn {
    // margin-top: 10rpx;
    margin-top: auto;
    margin-right: auto;
    margin-left: auto;
    height: 80rpx;
    margin-bottom: 40rpx;
    width: calc(100% - 40rpx);
    background-color: #2196f3;
    font-weight: 600;
    color: #fff;
    font-size: 40rpx;
    border-radius: 10rpx;
    text-align: center;
    line-height: 80rpx;
  }
  .agreement-tip {
    margin-top: 20px;
    margin-bottom: 10rpx;
    width: 100%;
    padding: 0 20rpx;
    font-size: 24rpx;
    padding-left: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    .protocol-item {
      color: #898888;
    }
    .protocol-item2 {
      color: #61b4f6;
    }
  }
  .tip-text {
    margin-top: 20rpx;
    .icon {
      padding: 4rpx 18rpx;
      border-radius: 50%;
      color: #fff !important;
      background-color: #ff552a;
      margin-right: 5rpx;
    }
    span {
      color: #2196f3;
      font-weight: 600;
    }
  }
}
</style>
