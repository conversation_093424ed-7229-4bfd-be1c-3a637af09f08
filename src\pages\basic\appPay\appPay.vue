<template>
  <!-- 充值.wxml -->
  <view class="appPay">
    <image class="pay-status-img" v-if="payStatus == 200" :src="`${IMG_PATH}success.png`" />
    <image class="pay-status-img" v-if="payStatus == 400" :src="`${IMG_PATH}errror.png`" />
    <view class="btn-payfor-name">{{ payTypeName }}</view>
    <button class="btn-payfor" open-type="launchApp" :app-parameter="payStatus" @error="launchAppError">
      点此返回APP
    </button>
    <loading v-if="!hiddenLoading">正在唤起支付...</loading>
  </view>
</template>

<script>
import { baseDateNo } from '@/utils/base.js';
const baseUrl = baseDateNo();
export default {
  data() {
    return {
      hiddenLoading: false,
      payStatus: null,
      payTypeName: '支付中....',
      ojbk: false,
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */ onLoad: function (params) {
    console.log('入参', params);
    // const params = {
    //   queryParams: JSON.stringify({ isNeedLogin: true, payChannel: 2, payAmount: 1, transactionChannel: '1303' }),
    //   token:
    //     'eyJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************.8WjXBkRTT4zVidrdiHFXkAlIDHeXf1JJovj6akpaoFY',
    // };
    let options = {};
    if (params.queryParams) {
      try {
        options = {
          ...JSON.parse(params.queryParams),
          token: params.token,
        };
      } catch {}
    } else {
    }
    console.log('app 参数：', options);
    const that = this;
    uni.login({
      success: (res) => {
        if (res.code) {
          console.log('入参:', {
            transactionChannel: options.transactionChannel,
            payAmount: options.payAmount,
            appType: '01',
            reqType: '01',
            extend: res.code,
          });
          that.setData({
            hiddenLoading: false,
          });
          let url = 'account/balance';
          const params = {
            transactionChannel: options.transactionChannel,
            payAmount: options.payAmount,
            appType: '01',
            reqType: '01',
            extend: res.code,
          };
          // 会员套餐和接口地址
          if (options.serviceType === 'member') {
            params.vipType = options.vipType;
            url = 'vip/recharge';
          }
          uni.request({
            url: `${baseUrl}def/api/v0.1/${url}`,
            data: params,
            method: 'POST',
            header: {
              'content-type': 'application/x-www-form-urlencoded',
              Authorization: 'Bearer ' + options.token,
            },
            success: (res) => {
              console.log(res, '支付接口', res);
              if (res.data.ret == 200) {
                console.log('进入下个环节。');
                const miniPayRequest = res.data.orderResultChinaUms.miniPayRequest;
                that.handClickAppPay(miniPayRequest);
              }
            },
            fail: (err) => {
              console.log('失败2', err);
            },
            complete: function () {
              that.setData({
                hiddenLoading: true,
              });
            },
          });
        } else {
        }
      },
    });
  },
  methods: {
    handClickAppPay(miniPayRequest) {
      const that = this;
      const obj = {
        timeStamp: miniPayRequest.timeStamp,
        nonceStr: miniPayRequest.nonceStr,
        package: miniPayRequest.package,
        signType: 'MD5',
        paySign: miniPayRequest.paySign,
      };
      console.log('入参：', obj);
      uni.requestPayment({
        timeStamp: miniPayRequest.timeStamp,
        nonceStr: miniPayRequest.nonceStr,
        package: miniPayRequest.package,
        signType: 'MD5',
        paySign: miniPayRequest.paySign,
        success: function (res) {
          that.setData({
            payStatus: 200,
            payTypeName: '支付成功！',
          });
        },
        fail: function (err) {
          console.log('支付失败了。', err);
          that.setData({
            payStatus: 400,
            payTypeName: '支付失败',
          });
        },
      });
    },

    launchAppError(e) {
      console.log(e.detail.errMsg, '失败1');
    },
  },
};
</script>
<style>
@import './appPay.css';
</style>
