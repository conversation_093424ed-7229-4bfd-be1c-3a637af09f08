<template>
  <view class="applications">
    <view class="application_record" v-for="(item, index) in orderList" :key="index">
      <image
        class="zfb"
        v-if="item.transactionChannel == '1302' || item.transactionChannel == '01' || item.transactionChannel == '10'"
        :src="`${IMG_PATH}zfb2x.png`"
      ></image>

      <image
        class="wx"
        v-if="item.transactionChannel == '07' || item.transactionChannel == '03' || item.transactionChannel == '1303'"
        :src="`${IMG_PATH}wx2x.png`"
      ></image>

      <image
        class="wx"
        v-if="item.transactionChannel == '13' || item.transactionChannel == '1301'"
        :src="`${IMG_PATH}ysf.png`"
      ></image>

      <!-- <view wx:if="{{item.dealStatus != '01'}}" class="record_meeage">
        <view class="record_title">
          <view class="title">{{ item.rechargeAmt || 0 }}</view>
          <view class="price">{{ item.refundableAmt || 0 }}元</view>
        </view>
        <view class="record_price">
          <view class="time">{{ item.createTime }}</view>
          <view class="type">{{ item.dealStatusName }}</view>
        </view>
      </view> -->

      <!-- <template wx:if="{{item.dealStatus == '01'}}"> -->

      <view class="record_meeage">
        <view class="record_title">
          <view class="title">{{ item.transactionChannelName }}</view>
        </view>
        <view class="record_price">
          <view class="time">{{ item.createTime }}</view>
        </view>
      </view>

      <view class="record_rest">
        <view class="rest_status">
          <view class="money">{{ item.refundableAmt || 0 }}元</view>
          <view class="status">{{ item.dealStatusName }}</view>
        </view>
        <view class="rest_btn" v-if="item.dealStatus == '01'">撤销</view>
      </view>

      <!-- </template> -->
    </view>
    <view v-if="orderList.length == 0" class="nothing">
      <image :src="`${IMG_PATH}nofount.png`"></image>
      <view>暂无数据</view>
    </view>
  </view>
</template>

<script>
import { getRefundRecordDetail } from '@/services/index.js';
export default {
  data() {
    return {
      orderList: [],
    };
  },
  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    var that = this;
    const [, res] = await getRefundRecordDetail({
      pageNum: 1,
      totalNum: 999,
    });
    if (res) {
      this.orderList = res.data || [];
    }
  },
};
</script>
<style>
@import './refundhs.css';
</style>
