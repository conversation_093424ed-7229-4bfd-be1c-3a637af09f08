<template>
  <view class="redeem-record">
    <div class="customer-icon" @click.stop="toCustomer">
      <image :src="`${IMG_PATH_UI}icon-service.png`" mode="widthFix"></image>
      客服投诉
    </div>
    <tab-select class="tag-list" :options="tabList" @change="changeTab"></tab-select>
    <view v-if="cardList.length !== 0">
      <GoodsCard v-for="cardItem in cardList" :key="cardItem.id" :card-item="cardItem" :couponIntrMap="couponIntrMap" />
    </view>
    <AEmpty v-else />
  </view>
</template>

<script>
import GoodsCard from './components/GoodsCard';
import { getExchangeList, getCouponIntr } from '@/services/index.js';
import AEmpty from '@/components/AEmpty/index';
import tabSelect from '@/components/TabSelect/index';
import { toCustomer } from '@/utils/bridge/index.js';

export default {
  name: 'index',
  components: {
    GoodsCard,
    AEmpty,
    tabSelect,
  },
  data() {
    return {
      tab: '',
      status: 'more',
      pageNum: 1,
      totalNum: 10,
      tabList: [
        {
          id: '',
          name: '全部',
        },
        {
          id: '01',
          name: '待发货',
        },
        {
          id: '02',
          name: '已发货',
        },
        {
          id: '03',
          name: '已评价',
        },
      ],
      cardList: [],
      couponIntrMap: null,
    };
  },
  async onShow() {
    this.cardList = [];
    await this.getCouponIntr();
    this.getExchangeList();
  },
  // 触底分页查询
  onReachBottom() {
    if (this.status === 'noMore') return;
    this.getExchangeList();
  },
  methods: {
    toCustomer() {
      if (uni.getStorageSync('app') === '电动宁德') {
        toCustomer();
      } else {
        uni.navigateTo({
          url: '/subPackages/customerService/index',
        });
      }
    },
    // tab切换
    changeTab(data) {
      this.tab = data.id;
      this.cardList = [];
      this.pageNum = 1;
      this.getExchangeList();
    },
    // 查询兑换记录
    async getExchangeList() {
      const params = {
        totalNum: this.totalNum,
        pageNum: this.pageNum,
        exchangeStatus: this.tab,
      };
      const [err, res] = await getExchangeList(params);
      if (res) {
        this.cardList.push(...res.exchangeList);
        this.status = res.exchangeList.length >= this.totalNum ? 'more' : 'noMore';
        if (this.status === 'more') {
          this.pageNum++;
        }
      }
    },
    // 获取优惠券说明
    async getCouponIntr() {
      const [err, res] = await getCouponIntr();
      if (res) {
        const { list = [] } = res;
        const couponIntrMap = this.list2Map(list, 'value');
        this.couponIntrMap = couponIntrMap;
      }
    },
    list2Map(list, keyName) {
      const map = {};
      let item;
      for (let i = 0, len = list.length; i < len; i++) {
        item = list[i];
        map[item[keyName]] = item;
      }
      return map;
    },
  },
};
</script>

<style scoped lang="scss">
.redeem-record {
  padding: 24rpx;
  height: 100%;
  background: linear-gradient(180deg, #dcf0ff 0%, #f6f7f9 100%);
  position: relative;
  .customer-icon {
    position: absolute;
    right: 10rpx;
    top: 50rpx;
    z-index: 999;
    image {
      width: 30rpx;
      margin-right: 5rpx;
    }
  }
  .tag-list {
    margin: 24rpx 0 30rpx 0;
    display: block;
  }
}
</style>
