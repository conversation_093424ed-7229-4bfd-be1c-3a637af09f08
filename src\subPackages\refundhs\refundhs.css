.applications {
  width: 100%;
  min-height: 100vh;
  background: rgba(238, 242, 246, 1);
  padding-bottom: 40rpx;
}

.applications .nothing {
  position: absolute;
  left: 0;
  width: 100%;
  height: 70%;
  top: 20%;
  display: flex;
  flex-wrap: wrap;
  align-content: center;
}

.applications .nothing image {
  display: inline-block;
  margin: 0 auto;
}

.applications .nothing view {
  font-size: 30rpx;
  color: #999;
  font-weight: 500;
  width: 100%;
  text-align: center;
  margin-top: -60rpx;
}

.applications .application_head {
  width: 100%;
  height: 338rpx;
  background: #fff;
  display: flex;
  align-content: space-between;
  flex-wrap: wrap;
  justify-content: center;
  box-sizing: border-box;
  padding: 50rpx 0;
  margin-bottom: 16rpx;
}

.applications .application_head image {
  width: 116rpx;
  height: 116rpx;
}

.applications .application_head .head_title {
  width: 100%;
  font-size: 30rpx;
  color: rgba(102, 102, 102, 1);
  font-weight: 400;
  text-align: center;
}

.applications .application_head .head_price {
  font-size: 60rpx;
  color: #2196f3;
  font-weight: 500;
}

.applications .application_head .head_price text {
  font-size: 40rpx;
  margin-right: 11rpx;
}

.applications .application_head .head_name {
  width: 100%;
  text-align: center;
  font-size: 36rpx;
  color: rgba(51, 51, 51, 1);
  font-weight: 500;
}

.applications .application_head .head_msg {
  width: 565rpx;
  text-align: center;
  line-height: 40rpx;
  font-weight: 400;
  font-size: 26rpx;
  color: rgba(153, 153, 153, 1);
}

.applications .application_hight {
  height: 388rpx;
}

.applications .application_record {
  width: 100%;
  height: 157rpx;
  background: #fff;
  box-sizing: border-box;
  padding: 0 27rpx;
  display: flex;
  align-items: center;
}

.applications .application_record image {
  margin-right: 31rpx;
}

.applications .application_record .zfb {
  width: 74rpx;
  height: 74rpx;
}

.applications .application_record .wx {
  width: 84rpx;
  height: 65rpx;
}

.applications .application_record .record_meeage {
  height: 100%;
  border-bottom: 1rpx solid rgba(198, 200, 201, 0.9);
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  align-content: space-between;
  padding: 31rpx 0;
}

.applications .application_record .record_meeage .record_title {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.applications .application_record .record_meeage .title {
  font-size: 34rpx;
}

.applications .application_record .record_meeage .price {
  font-size: 36rpx;
}

.applications .application_record .record_rest {
  /* width: 240rpx; */
  height: 100%;
  box-sizing: border-box;
  padding: 31rpx 0;
  border-bottom: 1rpx solid rgba(198, 200, 201, 0.9);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.applications .application_record .record_rest .rest_btn {
  width: 88rpx;
  height: 54rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26rpx;
  color: rgba(222, 48, 49, 1);
  font-weight: 400;
  border-radius: 9rpx;
  border: 1rpx solid rgba(222, 48, 49, 1);
  margin-left: 28rpx;
}

.applications .application_record .record_rest .rest_status {
  flex: 1;
  height: 100%;
  display: flex;
  align-content: space-between;
  flex-wrap: wrap;
}

.applications .application_record .record_rest .rest_status view {
  width: 100%;
  text-align: right;
}

.applications .application_record .record_rest .rest_status .money {
  font-size: 36rpx;
  color: rgba(51, 51, 51, 1);
  font-weight: 500;
}

.applications .application_record .record_rest .rest_status .status {
  font-size: 28rpx;
  color: rgba(222, 48, 49, 1);
  font-weight: 400;
}

.applications .application_flex {
  margin-top: 30rpx;
  width: 100%;
}

.applications .application_flex .flex_btn {
  width: 690rpx;
  height: 88rpx;
  background: #2196f3;
  border-radius: 4rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
  color: rgba(255, 255, 255, 1);
  font-weight: 400;
  margin: 0 auto;
}

.applications .application_flex .flex_title {
  width: 100%;
  text-align: center;
  margin-top: 25rpx;
  font-size: 22rpx;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
}
.applications .application_flex .flex_title text {
  color: #2196f3;
}
