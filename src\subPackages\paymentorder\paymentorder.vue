<template>
  <view>
    <view class="paymentorder">
      <view class="paymentorder-main">
        <view class="main-icon blue-color">
          <icon type="icon iconfont icon-success" size="22" />
          恭喜您，预约成功！
        </view>
        <view class="main-no">
          <text class="name">订单编号：</text>
          {{ orderNo }}
        </view>
        <view class="main-money">
          <text class="name">待支付金额：</text>
          {{ price }}元
        </view>
      </view>
      <view class="paymentorder-state">
        <view class="state-pile">您已成功预约{{ equipName }}电站</view>
        <view class="blue-color">{{ name }}</view>
        <view class="state-pay">
          支付后，请在
          <text>15分钟</text>
          内前往本站享受服务
        </view>
      </view>
      <view class="pay-name">请选择支付方式：</view>
      <view v-if="pars" class="pay-method">
        <icon class="icon-wechart" type="icon iconfont icon-online" />
        余额 ( {{ yueP }} )
      </view>
      <view v-else @tap="yuePay" class="pay-method">
        <icon class="icon-wechart" type="icon iconfont icon-online" />
        余额 ( {{ yueP }} )
        <radio class="fr" :checked="x1"></radio>
      </view>
      <view @tap="weixinPay" style="margin-top: 1rpx" class="pay-method">
        <icon class="icon-wechart" type="icon iconfont icon-pay_wechat" />
        微信支付
        <radio class="fr" :checked="x2"></radio>
      </view>

      <button @tap="pay" class="btn btn-fiexd blue-bg">确认支付</button>
    </view>
    <loading v-if="!hiddenLoading">正在加载</loading>
  </view>
</template>

<script>
import { wxMiniOrder, getChargeOrderDtl, getUserInfo } from '@/services/index.js';
export default {
  data() {
    return {
      itemsd: [
        {
          name: 'yue',
          value: '01',
          checked: 'true',
        },
        {
          name: 'weixin',
          value: '02',
        },
      ],
      pars: false,
      x1: true,
      x2: false,
      code: '',
      token: '',
      price: '',
      yueP: '',
      orderNo: '',
      equipName: '',
      name: '',
      hiddenLoading: false,
      typePay: true,
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    const that = this;
    uni.getStorage({
      key: 'price',
      success(res) {
        that.setData({
          price: res.data,
          orderNo: options.orderNo,
          // equipName: options.equipName,
          name: options.name,
          hiddenLoading: true,
        });
      },
    });
    // console.log(options.orderNo, '000000000000');
    //示例
    const res = await getChargeOrderDtl({
      orderNo: that.orderNo,
    });
    if (res && res.chargeOrderList && Array.isArray(res.chargeOrderList) && res.chargeOrderList.length > 0) {
      that.setData({
        equipName: res.chargeOrderList[0]?.stationName,
      });
    }
    // this.hiddenLoading = false;
    const [, result] = await getUserInfo();
    if (result) {
      that.setData({
        yueP: result.accFreeAmt,
      });
      if (result.accFreeAmt < that.price) {
        console.log('余额不足');
        that.setData({
          pars: true,
          x1: false,
          x2: true,
          typePay: false,
        });
      }
    }
  },
  methods: {
    yuePay() {
      this.setData({
        x1: true,
        x2: false,
        typePay: true,
      });
    },

    weixinPay() {
      this.setData({
        x1: false,
        x2: true,
        typePay: false,
      });
    },

    pay() {
      var that = this;
      // wx.showToast({
      //   title: '正在加载...',
      //   icon: 'loading',
      //   duration: 1500
      // })
      that.setData({
        hiddenLoading: false,
      });
      console.log(that.typePay, '_self.typePay状态是');
      if (that.typePay) {
        //这里是余额支付
        console.log('这里是余额支付');
        uni.checkSession({
          success() {
            uni.login({
              success: (res) => {
                console.log(res.code, '重新获取的code');
                that.setData({
                  code: res.code,
                  // orgCode:'0000001'
                });
                var queryList = [];
                queryList.push({
                  payType: '0106',
                  detailAmt: that.price,
                  extend: that.code,
                });
                const [, result] = wxMiniOrder({
                  orderNo: that.orderNo,
                  reqType: '07',
                  queryList: JSON.stringify(queryList),
                  // queryList: null
                });
                if (result) {
                  that.setData({
                    hiddenLoading: true,
                  });
                  var orderNo = that.orderNo;
                  console.log(orderNo, '`-`-`-`--`-`-`-`-`-`-`--``--`');
                  uni.redirectTo({
                    url: '/pages/basic/charge/charge?orderNo=' + orderNo,
                  });
                }
              },
            });
          },
          fail() {},
        });
      } else {
        uni.checkSession({
          success() {
            uni.login({
              success: (res) => {
                console.log(res.code, '重新获取的code');
                that.setData({
                  code: res.code,
                  // orgCode:'0000001'
                });
                var queryList = [];
                queryList.push({
                  payType: '0106',
                  detailAmt: that.price,
                  extend: that.code,
                });
                const [, result] = wxMiniOrder({
                  orderNo: that.orderNo,
                  reqType: '07',
                  queryList: JSON.stringify(queryList),
                  // queryList: null
                });
                if (result) {
                  console.log(res, '微信缴费');
                  var orderParams = result.orderResultWeixin;
                  that.setData({
                    hiddenLoading: true,
                  });
                  uni.requestPayment({
                    timeStamp: orderParams.timeStamp,
                    nonceStr: orderParams.nonceStr,
                    package: orderParams.package,
                    signType: 'MD5',
                    paySign: orderParams.sign,
                    success(res) {
                      console.log('支付成功');
                      var orderNo = that.orderNo;
                      console.log(orderNo, '`-`-`-`--`-`-`-`-`-`-`--``--`');
                      uni.redirectTo({
                        url: '/pages/basic/charge/charge?orderNo=' + orderNo,
                      });
                    },
                    fail(res) {
                      console.log('支付失败了。');
                      that.setData({
                        hiddenLoading: true,
                      });
                    },
                  });
                }
              },
            });
          },
          fail() {
            //登录态过期
            console.log('登录过期ing');
          },
        });
      }
    },
  },
};
</script>
<style>
@import './paymentorder.css';
</style>
