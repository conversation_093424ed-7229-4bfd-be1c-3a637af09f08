<template>
  <view>
    <!-- <uni-nav-bar
      height="50px"
      :shadow="false"
      left-icon="left"
      borderColor="#fff"
      title="自定义高度"
      @clickLeft="clickLeft"
    >
      <template slot="left">
        <uni-icons type="left" size="30"></uni-icons>
      </template>
    </uni-nav-bar> -->
    <!-- 充值.wxml -->
    <view class="reaharge_top">
      <!-- <view class="reaharge"> -->
      <!-- {{ actList.actMarks }}
      </view> -->
      <scroll-view scroll-x>
        <view v-if="msg == 1" class="reaharge_top_lend">
          <view class="reaharge_top_nhead">
            {{ price || 0 }}
            <text>+</text>
            {{ gifts || 0 }}
            <text>=</text>
            <text class="alls">{{ price + gifts || 0 }}</text>
          </view>
          <view class="reaharge_top_btn">
            <view>充值</view>
            <view>赠送</view>
            <view>到账</view>
          </view>
        </view>

        <view v-if="msg == 2" class="reaharge_top_lend">
          <view class="unqualified">充值金额未达到优惠条件！</view>
        </view>

        <view v-if="msg == 3" class="rend">
          <!-- <view style="background-image: url(../../image/<EMAIL>)" class='red_img'>
    <view class='int'>
      <text>100元</text>
      <text>满200元减100</text>
    </view>
    <text class='ints'>此优惠券使用有效期为7天</text>
  </view>  -->
          <view class="red_img" v-for="(item, index) in itemsForeachs" :key="index">
            <view class="int">
              <text class="tab1">{{ item.cpnAmt || '' }}</text>
              <text class="tab2">元</text>
              <text class="tab3">{{ item.cpnName || '' }}</text>
            </view>

            <text class="ints">{{ item.eftDate || '' }}~{{ item.expDate || '' }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <view class="rectangle">
      <view class="middle">充值金额{{ price }}</view>

      <!-- <view class='middle_money'>
    <view>300元</view>
    <view>200元</view>
    <view>100元</view>
    <view>50元</view>
  </view> -->

      <view class="rectangle_type">
        <button
          v-for="item in moneyList"
          :key="item.index"
          @tap="active(item.value)"
          :data-price="item.value"
          :class="currentTab == item.index ? 'active' : ''"
        >
          <view class="button-content">
            <text class="amount-text">{{ item.displayLabel }}</text>
            <text v-if="item.giftText" class="gift-text">{{ item.giftText }}</text>
          </view>
        </button>
      </view>
    </view>

    <view class="middle_input" v-if="currentTab == 'other'">
      <view>￥</view>
      <input :value="displayPrice" maxlength="6" @input="inpt" placeholder="请输入您要的充值金额" type="number" />
    </view>
    <!-- #ifdef MP-WEIXIN -->
    <view class="hint">请选择支付方式：</view>
    <view class="pay-method">
      <view>
        <icon class="icon-wechart" type="icon iconfont icon-pay_wechat" size="30" />
        微信支付
      </view>
      <radio class="fr" :checked="x2"></radio>
    </view>
    <!-- #endif -->

    <view class="pay">
      <button class="d" v-if="disabledInput" @tap="cz">充值</button>
      <button class="noClick" v-else>充值</button>
    </view>
    <view class="msg">
      点击充值，即表示已阅读并同意
      <text>《充值协议》</text>
    </view>

    <uni-popup ref="channelPopup" type="bottom" background-color="#F6F7F9">
      <view class="popup-content">
        <view class="title">
          <text class="title-text">选择支付方式</text>
          <img class="close" :src="`${IMG_PATH}close.png`" @click="closeChannelPopup" />
        </view>
        <!--                支付渠道列表-->
        <view class="channel">
          <view class="channel-list" v-for="item in channelList" :key="item.type">
            <view class="channel-name">
              <img class="channel-icon" :src="`${IMG_PATH}member/${item.url}.png`" />
              <text>{{ item.name }}</text>
            </view>
            <img
              @click="getChannel(item)"
              class="check-icon"
              :src="`${IMG_PATH}${channel === item.type ? 'check' : 'circle'}.png`"
            />
          </view>
        </view>
      </view>
      <view class="footer-button">
        <AppButton type="primary" @click="payWay"> 立即支付</AppButton>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import uniNavBar from '@dcloudio/uni-ui/lib/uni-nav-bar/uni-nav-bar.vue';
import uniIcons from '@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue';
import { subscribeMessage } from '@/utils/messageCode.js';
import { getAccountsActs, wxH5Pay, recharge } from '@/services/index.js';
import { ENVR } from '@/config/global';
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup';
import uniPopupDialog from '@/components/uni-ui/uni-popup-dialog/uni-popup-dialog';
import AppButton from '@/components/AppButton/index';
import { ToPay } from '@/utils/bridge/index.js';
export default {
  components: { uniNavBar, uniIcons, uniPopup, uniPopupDialog, AppButton },
  data() {
    return {
      msg: 1,
      currentTab: -1,
      price: 0,
      x2: true,
      token: '',
      actList: [],
      list: [],
      gifts: 0,
      ojbk: false,
      itemsForeach: [],
      itemsForeachs: [],
      true: false,
      prc: '',
      hiddenLoading: false,
      channelList: [
        {
          type: 2,
          name: '微信支付',
          url: 'wx',
        },
        {
          type: 1,
          name: '支付宝支付',
          url: 'zfb',
        },
        {
          type: 3,
          name: '银联支付',
          url: 'yl',
        },
      ],
      channel: 2,
      channelMap: {
        1: '1302',
        2: '1303',
        3: '1301',
      },
      orderParams: null,
      sectionList: [],
      currentGiftAmount: 0, // 当前充值金额对应的赠送金额
      participateActivity: false, // 是否参与活动
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('---------');
    var that = this;
    uni.getStorage({
      key: 'token',
      success(res) {
        console.log(1111111, res);
        that.token = res.token;
      },
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('---------1111');
  },
  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    console.log('---------2222');
    let that = this;
    const [, res] = await getAccountsActs({
      actType: '07',
    });
    if (res) {
      console.log(res.actList, '优惠活动');
      that.actList = res.actList[0];
      that.list = res.actList;
      // res.actList[0]?.sectionList ||
      this.sectionList = [
        {
          actId: ********,
          actSubType: '0701',
          baseValue: '50',
          cpnIds: '',
          presentBalType: '0701',
          presentSectionId: '********',
          refCeil: '50',
          refFloor: '100',
          sectionDetId: '********',
          sectionMarks: '充值50~100送50',
        },
        {
          actId: ********,
          actSubType: '0701',
          baseValue: '50',
          cpnIds: '',
          presentBalType: '0701',
          presentSectionId: '********',
          refCeil: '100',
          refFloor: '200',
          sectionDetId: '********',
          sectionMarks: '充值100~200送50',
        },
        {
          actId: ********,
          actSubType: '0701',
          baseValue: '50',
          cpnIds: '',
          presentBalType: '0701',
          presentSectionId: '********',
          refCeil: '200',
          refFloor: '300',
          sectionDetId: '********',
          sectionMarks: '充值200~300送50',
        },
        {
          actId: ********,
          actSubType: '0701',
          baseValue: '150',
          cpnIds: '',
          presentBalType: '0701',
          presentSectionId: '********',
          refCeil: '300',
          refFloor: '500',
          sectionDetId: '********',
          sectionMarks: '充值300~500送50',
        },
      ];
      console.log(this.sectionList, 'this.sectionList');
      var arr = [];
      var prices = that.price;
      for (var i = 0; i < res.actList[0].sectionList.length; i++) {
        var item = res.actList[0].sectionList[i];
        if (Number(prices) > Number(item.refCeil) - 1 && Number(prices) < Number(item.refFloor)) {
          console.log('存在', i, item);
          arr.push(item);
        }
      }
      if (res.actList[0].actSubType == '0701') {
        console.log('充值送 馈赠金模式', arr[0]?.baseValue);
        if (arr && arr.length > 0) {
          that.gifts = Number(arr[0]?.baseValue);
          that.msg = 1;
        } else {
          console.log('异常');
          that.msg = 2;
        }
      } else {
        if (arr && arr.length > 0) {
          that.itemsForeachs = arr.flatMap((q) => q.cpnList);
          that.msg = 3;
          console.log('显示', that.itemsForeachs);
        } else {
          console.log('异常');
          that.msg = 2;
        }
      }
    } else {
      this.sectionList = [];
    }
  },
  computed: {
    disabledInput() {
      return this.price && Number(this.price) !== 0;
    },
    // 计算属性：用于输入框显示，确保不会出现undefined
    displayPrice() {
      // 严格检查price值
      if (this.price === 0 || this.price === null || this.price === undefined || this.price === '') {
        return '';
      }

      // 转换为字符串并确保是有效数字
      let priceStr = String(this.price);

      // 移除非数字字符
      priceStr = priceStr.replace(/[^0-9]/g, '');

      // 如果处理后为空，返回空字符串
      if (!priceStr) {
        return '';
      }

      // 限制最多6位数字
      return priceStr.length > 6 ? priceStr.slice(0, 6) : priceStr;
    },
    moneyList() {
      const baseList = [
        { value: 300, label: '300元', index: 0 },
        { value: 200, label: '200元', index: 1 },
        { value: 100, label: '100元', index: 2 },
        { value: 50, label: '50元', index: 3 },
        { value: 'other', label: '其他', index: 'other' },
      ];

      // 为每个固定金额选项计算赠送金额
      return baseList.map((item) => {
        if (item.value !== 'other') {
          const giftAmount = this.getGiftAmount(item.value);
          return {
            ...item,
            giftAmount,
            // 优化显示格式：300元赠送30
            displayLabel: item.label,
            giftText: giftAmount > 0 ? `赠送${giftAmount}` : '',
          };
        } else {
          return {
            ...item,
            giftAmount: 0,
            displayLabel: item.label,
            giftText: '',
          };
        }
      });
    },
  },
  methods: {
    /**
     * 根据充值金额获取对应的赠送金额
     * @param {number} amount - 充值金额
     * @returns {number} - 赠送金额，如果没有匹配的规则则返回0
     */
    getGiftAmount(amount) {
      // 确保 sectionList 存在且不为空
      if (!this.sectionList || this.sectionList.length === 0) {
        return 0;
      }

      // 遍历 sectionList 查找匹配的赠送规则
      for (let i = 0; i < this.sectionList.length; i++) {
        const section = this.sectionList[i];
        const refCeil = Number(section.refCeil);
        const refFloor = Number(section.refFloor);

        // 匹配条件：充值金额在 refCeil（起始金额）到 refFloor（结束金额）范围内
        // 注意：根据原有逻辑，这里使用 > refCeil - 1 和 < refFloor
        if (Number(amount) > refCeil - 1 && Number(amount) < refFloor) {
          return Number(section.baseValue) || 0;
        }
      }

      // 如果没有找到匹配的规则，返回0
      return 0;
    },

    /**
     * 显示活动确认弹窗
     */
    showActivityPopup() {
      this.currentGiftAmount = this.getGiftAmount(this.price);
      uni.showModal({
        title: '请确认是否参与充值送活动',
        content: '参与馈赠金活动的充值将不予以退款！',
        cancelText: '暂不参与',
        confirmText: '参与',
        success: (res) => {
          if (res.confirm) {
            // 用户点击了"参与"
            this.proceedWithRecharge(true);
          } else if (res.cancel) {
            // 用户点击了"暂不参与"
            this.proceedWithRecharge(false);
          }
        },
      });
    },

    /**
     * 继续充值流程
     * @param {boolean} participate - 是否参与活动
     */
    proceedWithRecharge(participate) {
      console.log('继续充值流程，参与活动:', participate);
      this.participateActivity = participate;
      // 这里继续原有的充值逻辑
      this.executeRecharge();
    },

    // 选择支付渠道
    getChannel(item) {
      this.channel = item.type;
    },
    closeChannelPopup() {
      this.$refs.channelPopup.close();
    },
    // 查询支付参数
    async recharge() {
      console.log('获取code');
      const param = {
        transactionChannel: this.channelMap[this.channel],
        reqType: '01',
        appType: '01',
        // orgCode: '',
        // operNo: '',
        payAmount: this.price,
        appType: '01',
        joinFlag: this.participateActivity ? '1' : '0',
      };

      const [err, res] = await wxH5Pay(param);
      console.log(res, 'res');
      if (res) {
        this.appPay(res);
      }
    },
    payWay() {
      const app = uni.getStorageSync('app');
      if (app == '鸿蒙电动宁德') {
        if (this.channel === 2) {
          this.appPay({});
        } else {
          console.log('走这里');
          this.recharge();
        }
      } else if (app == '电动宁德') {
      } else {
        // i宁德支付
      }
    },
    // app支付
    async appPay(params) {
      // payChannel 1 支付宝 ，2微信 ，3 银联
      const options = {
        isNeedLogin: true,
        payChannel: this.channel + '',
        payAmount: this.price,
        transactionChannel: this.channelMap[this.channel],
        joinFlag: this.participateActivity ? '1' : '0',
        ...params,
      };
      console.log('支付参数', options);
      const result = await ToPay(options);
      console.log('支付后的返参', result);
      if (result && result.code == '9999') {
        this.paySuccess();
      } else {
        this.payFail(result.msg);
      }
    },
    payFail(msg) {
      console.log(msg, 'msg');
      uni.showModal({
        title: '提示',
        content: msg || '支付失败',
        showCancel: false,
        success: function (res) {
          this.closeChannelPopup();
        },
      });
    },
    paySuccess() {
      uni.showModal({
        title: '提示',
        content: '充值成功',
        showCancel: false,
        success: function (res) {
          uni.navigateBack();
        },
      });
    },
    clickLeft() {
      console.log(uni, 'uni');
      uni.navigateBack({
        delta: 1,
      });
    },
    async inpt(event) {
      console.log('输入事件:', event);

      // 获取输入值，确保不为undefined
      let inputValue = '';

      // 兼容不同平台的事件对象
      if (event && event.detail && typeof event.detail.value !== 'undefined') {
        inputValue = event.detail.value;
      } else if (event && event.target && typeof event.target.value !== 'undefined') {
        inputValue = event.target.value;
      }

      // 确保inputValue是字符串
      inputValue = String(inputValue || '');

      console.log('原始输入值:', inputValue);

      // 只保留数字，移除所有非数字字符（包括小数点、空格等）
      const cleanValue = inputValue.replace(/[^0-9]/g, '');

      // 限制最多6位数字
      const limitedValue = cleanValue.slice(0, 6);

      // 转换为整数，如果为空则为0
      const numericValue = limitedValue ? parseInt(limitedValue, 10) : 0;

      console.log('处理后的值:', numericValue, '限制后的字符串:', limitedValue);

      // 更新price值，displayPrice会通过计算属性自动更新
      this.price = numericValue;

      // 更新ojbk状态
      if (numericValue > 0) {
        console.log('有效值:', numericValue);
        this.ojbk = true;
      } else {
        console.log('无效值或为0');
        this.ojbk = false;
      }

      // 获取优惠信息
      this.getMoney();
    },

    async active(price) {
      console.log(price);
      if (price == 'other') {
        this.price = 0;
        this.currentTab = 'other';
        return;
      }
      var that = this;
      that.setData({
        prc: Number(price) + Number(price / 5),
        ojbk: true,
      });

      if (price == 300) {
        that.setData({
          currentTab: 0,
        });
      } else if (price == 200) {
        that.setData({
          currentTab: 1,
        });
      } else if (price == 100) {
        that.setData({
          currentTab: 2,
        });
      } else {
        that.setData({
          currentTab: 3,
        });
      }
      that.setData({
        price: Number(price),
      });
      this.getMoney();
    },
    async getMoney() {
      const that = this;
      const price = this.price;
      var value = Number(price);
      const [, res] = await getAccountsActs({
        actType: '07',
      });
      if (res) {
        console.log(res.actList, '优惠活动');

        // 更新 sectionList 以确保数据是最新的
        this.sectionList = res.actList[0]?.sectionList || [];

        // 使用新的 getGiftAmount 方法获取赠送金额
        const giftAmount = this.getGiftAmount(price);

        if (res.actList[0].actSubType == '0701') {
          console.log('充值送 馈赠金模式', giftAmount);
          if (giftAmount > 0) {
            that.setData({
              gifts: giftAmount,
              msg: 1,
            });
          } else {
            console.log('异常');
            that.setData({
              msg: 2,
            });
          }
        } else {
          console.log('优惠券模式');
          // 对于优惠券模式，仍然使用原有逻辑
          var arr = [];
          var prices = that.price;
          for (var i = 0; i < res.actList[0].sectionList.length; i++) {
            var item = res.actList[0].sectionList[i];
            if (Number(prices) > Number(item.refCeil) - 1 && Number(prices) < Number(item.refFloor)) {
              console.log('存在', i, item);
              arr.push(item);
            }
          }
          if (arr && arr.length > 0) {
            that.setData({
              itemsForeachs: arr.flatMap((q) => q.cpnList),
              msg: 3,
            });
            console.log(
              '显示',
              arr.flatMap((q) => q.cpnList)
            );
          } else {
            console.log('异常');
            that.setData({
              msg: 2,
            });
          }
        }
      }
    },

    binbluer() {
      this.setData({
        price: 0,
        ojbk: false,
      });
    },
    // 订阅消息
    subscribeMessage(callback) {
      return subscribeMessage(['充值状态通知'], callback);
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    async cz() {
      console.log('充值', ENVR);
      var that = this;
      that.setData({
        ojbk: false,
      });
      if (!that.price || that.price == '') {
        uni.showToast({
          title: '请输入充值金额',
          icon: 'none',
          duration: 1200,
        });
        return false;
      }

      // 检查是否有赠送金额
      const giftAmount = this.getGiftAmount(this.price);
      if (giftAmount > 0) {
        // 有赠送金额，显示活动确认弹窗
        this.showActivityPopup();
        return;
      }
      // 没有赠送金额，直接执行充值  那么就为不参与活动
      this.proceedWithRecharge(false);
      // this.executeRecharge();
    },

    /**
     * 执行充值逻辑
     */
    executeRecharge() {
      const that = this;
      if (ENVR === 'wx') {
        that.subscribeMessage(() => {
          uni.login({
            success: async (info) => {
              var param = {
                transactionChannel: '1303',
                // orgCode: '',
                // operNo: '',
                payAmount: that.price,
                reqType: '01',
                appType: '01',
                extend: info.code,
                joinFlag: this.participateActivity ? '1' : '0',
              };
              console.log(that.price, '当前充值的金额');
              const [, res] = await wxH5Pay(param);
              if (res) {
                console.log(res, '微信缴费');
                var orderParams = res.orderResultChinaUms.miniPayRequest;
                that.setData({
                  hiddenLoading: true,
                });
                if (res.ret == 400) {
                  uni.showToast({
                    icon: 'loading',
                    title: res.msg || '操作失败',
                  });
                  return false;
                }
                uni.requestPayment({
                  timeStamp: orderParams.timeStamp,
                  nonceStr: orderParams.nonceStr,
                  package: orderParams.package,
                  signType: 'MD5',
                  paySign: orderParams.paySign,
                  success(res) {
                    uni.showToast({
                      title: '充值成功',
                      icon: 'success',
                      duration: 1200,
                    });
                    setTimeout(function () {
                      uni.navigateBack();
                    }, 1200);
                  },
                  fail(err) {
                    console.log('支付失败了。', err);
                    that.setData({
                      hiddenLoading: true,
                      ojbk: true,
                    });
                  },
                });
              } else {
                console.log('支付失败了。', err);
                that.setData({
                  hiddenLoading: true,
                  ojbk: true,
                });
              }
            },
          });
        });
      } else {
        const app = uni.getStorageSync('app');
        console.log(app, 'app');
        if (app == '鸿蒙电动宁德') {
          this.$refs.channelPopup && this.$refs.channelPopup.open();
        } else {
          // 宁德原生端未使用改逻辑 所以这里默认是i宁德
          // !这里是i宁德的支付
          this.rechargeI();
        }
      }
    },
    // 查询支付参数
    async rechargeI() {
      const params = {
        transactionChannel: '12',
        payAmount: this.price,
        reqType: '01',
        appType: '01',
        redirectUrl: 'https://ndjtcs.evstyle.cn:6443/ndcharge/pages/basic/money/money',
        joinFlag: this.participateActivity ? '1' : '0',
      };
      const [, res] = await wxH5Pay(params);
      if (res && res?.orderResultINingDe) {
        window.location.href = res.orderResultINingDe;
      }
    },
  },
};
</script>
<style scoped lang="scss">
.popup-content {
  .title {
    display: flex;
    justify-content: center;
    margin: 16rpx 0 24rpx 0;
    align-items: center;
    padding: 24rpx;
    position: relative;
    .title-text {
      font-size: 32rpx;
      font-weight: bold;
      line-height: 38rpx;
      color: #333333;
    }
    .close {
      position: absolute;
      top: 24rpx;
      right: 24rpx;
      height: 40rpx;
      width: 40rpx;
    }
  }
  .popup-footer-button {
    padding: 0 24rpx 92rpx 24rpx;
  }
  .content-wrap {
    padding: 35rpx 24rpx 42rpx 24rpx;
    font-size: 26rpx;
    line-height: 40rpx;
    color: #3d3d3d;
    overflow: scroll;
  }
  .line {
    background: #eeeeee;
    width: 100%;
    margin-bottom: 23rpx;
    height: 1rpx;
  }
  .channel {
    display: flex;
    flex-direction: column;
    padding: 24rpx;
    .channel-list {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1rpx solid #eeeeee;
      padding: 24rpx 0;
      .channel-name {
        display: flex;
        align-items: center;
        .channel-text {
          font-size: 36rpx;
          color: #333333;
        }
        .channel-icon {
          width: 40rpx;
          height: 40rpx;
          margin-right: 16rpx;
        }
      }
      .check-icon {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
}

.alery {
  width: 300rpx;
  font-size: 30rpx;
  height: 100rpx;
  border: 1rpx solid #999;
  background: #fff;
  color: #000;
  position: absolute;
  z-index: 99;
  top: 30%;
  left: calc(100% - 150rpx);
}
.reaharge_top {
  width: 100%;
  background: #fff;
  box-sizing: border-box;
  padding-top: 24rpx;
}
.reaharge_top .reaharge {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #ff9802;
  font-weight: 500;
}
.reaharge_top .reaharge_top_lend {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}
.reaharge_top .rend {
  width: 1800rpx;
  /* height: 132rpx; */
  display: flex;
  /* justify-content: center; */
  padding: 30rpx;
  box-sizing: border-box;
  overflow: auto;
}
.reaharge_top .rend .red_img {
  width: 522rpx;
  height: 132rpx;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  box-sizing: border-box;
  padding-left: 30rpx;
  padding-top: 10rpx;
  background-image: url('data:image/png;base64,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');
}
.reaharge_top .rend .red_img .int {
  font-size: 22rpx;
  color: #fff;
}
.reaharge_top .rend .red_img .int .tab1 {
  font-size: 42rpx;
}
.reaharge_top .rend .red_img .int .tab2 {
  font-size: 24rpx;
}
.reaharge_top .rend .red_img .int .tab3 {
  font-size: 22rpx;
  margin-left: 25rpx;
}
.reaharge_top .rend .red_img .ints {
  font-size: 22rpx;
  color: #fff;
}
.reaharge_top_nhead {
  color: #2196f3;
  font-size: 72rpx;
  box-sizing: border-box;
  width: 100%;
  display: flex;
  justify-content: center;
  height: 100rpx;
}
.reaharge_top_nhead text {
  display: inline-block;
  margin: 0 30rpx;
}
.reaharge_top_nhead .alls {
  color: #ff9802;
  margin: 0;
}
.reaharge_top .reaharge_top_lend .reaharge_top_btn {
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 100%;
  box-sizing: border-box;
  padding: 5rpx 40rpx 35rpx 40rpx;
}
.reaharge_top .reaharge_top_lend .reaharge_top_btn view {
  width: 95rpx;
  height: 40rpx;
  background: #ededed;
  font-size: 24rpx;
  color: #000;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reaharge_top .reaharge_top_lend .unqualified {
  font-size: 32rpx;
  color: #000;
  font-weight: bold;
  width: 100%;
  height: 126rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.rectangle {
  width: 100%;
  height: 400rpx;
  background: #fff;
  margin-top: 27rpx;
  box-sizing: border-box;
  padding-top: 20rpx;
}
.rectangle .middle {
  font-size: 28rpx;
  color: #666;
  padding-left: 30rpx;
}
.middle_input {
  width: 92%;
  margin-left: 30rpx;
  box-sizing: border-box;
  height: 90rpx;
  display: flex;
  border-bottom: 1rpx solid #ccc;
}
.middle_input view {
  font-size: 64rpx;
  color: #333;
  font-weight: 200;
  width: 64rpx;
  height: 100%;
  display: flex;
  align-content: center;
  justify-content: center;
  border-right: 2rpx solid #ccc;
}
.middle_input input {
  display: inline-block;
  width: 100rxp;
  height: 100%;
  padding-left: 10rpx;
  color: #999999;
}
.middle_money {
  width: 100%;
  height: 239rpx;
}

.rectangle .rectangle_type {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 15rpx 15rpx;
  box-sizing: border-box;
  justify-content: space-between;
}
.rectangle .rectangle_type button {
  width: calc(50% - 10rpx);
  height: 80rpx;
  margin: 15rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1rpx solid #ccc;
  border-radius: 4rpx;
  color: #666666;
  font-size: 32rpx;
  background: #fff;
  padding: 0;
}

.button-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  gap: 8rpx;
}

.amount-text {
  font-size: 28rpx;
  line-height: 1;
}

.gift-text {
  font-size: 20rpx;
  color: #f59a23;
  line-height: 1;
}

.rectangle .rectangle_type .active {
  color: #fff;
  background: #2196f3;
}

.rectangle .rectangle_type .active .gift-text {
  color: #f59a23;
}
.hint {
  color: #888888;
  font-size: 28rpx;
  margin-top: 30rpx;
  margin-bottom: 10rpx;
  padding-left: 30rpx;
  box-sizing: border-box;
}
.pay-method {
  padding: 0 30rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  color: #333;
  font-size: 30rpx;
}
.pay-method view {
  height: 100%;
  display: flex;
  align-items: center;
}
.pay-method icon {
  color: #1da442;
  margin-right: 10rpx;
}
.pay {
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
  margin-top: 30rpx;
}
.pay button {
  width: 100%;
  color: #fff;
}
.pay .d {
  background: #2196f3;
}
.pay .noClick {
  background: #eee;
}
.msg {
  color: #666666;
  font-size: 20rpx;
  width: 100%;
  text-align: center;
  margin-top: 10rpx;
}
.msg text {
  color: #2196f3;
}
</style>
