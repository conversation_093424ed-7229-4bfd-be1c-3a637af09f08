<template>
  <view
    class="page-wrap"
    :style="{
      backgroundImage: `url(${IMG_PATH_ACTIVITY}bg.png)`,
    }"
  >
    <!--  #ifdef MP -->
    <view
      class="banner"
      :style="'padding-top:' + s_top + 'rpx;height:' + s_height + 'rpx;line-height:' + s_height + 'rpx'"
    >
      <image
        class="banner-block-img"
        @tap="goBack"
        mode="widthFix"
        :src="`${IMG_PATH}icon_arrow_right_black.png`"
      ></image>
      <view class="banner-title">每日签到</view>
    </view>
    <!-- #endif -->
    <view style="position: relative">
      <!-- <view class="star-image">
        <image :src="`${IMG_PATH}img_calendar.png`" />
      </view> -->

      <view class="content-wrap">
        <view class="points">
          <view>
            <image :src="`${IMG_PATH}icon_uncheckin.png`" />
            <text>{{ totalPoint }}</text>
          </view>
        </view>
        <div class="btn-signin-rule" @click="openProtocol">签到规则</div>

        <view class="calendar">
          <view class="sign-days">
            <text>本月已累计签到 {{ signInDays }} 天</text>
            <view style="display: flex; align-items: center">
              <image :src="`${IMG_PATH}icon_arrow_right_black.png`" @click="preMonth" />
              <text class="current-date">{{ currentMonth }}</text>
              <image :src="`${IMG_PATH}icon_arrow_right_black.png`" @click="nextMonth" />
            </view>
          </view>
          <view class="days">
            <view class="days-week">
              <text v-for="(item, index) in weekDays" :key="index">
                {{ item }}
              </text>
            </view>

            <view class="days-item-list">
              <view
                v-for="(item, index) in monthDays"
                :key="index"
                :class="{
                  'days-item': true,
                  // 'days-item-sign-in': item.signIn == 1,
                  'days-is-today': item.isToday,
                }"
                :style="{
                  visibility: item.date.length > 0 ? 'visible' : 'hidden',
                }"
                @click="dateClick(item)"
              >
                <div class="mask" v-if="!item.isFuture && !item.isToday"></div>
                <div class="img-wrap">
                  <image :src="getDayIcon(item)" />
                  <div class="num" v-if="item.isFuture">+{{ item.integralNum || 0 }}</div>
                </div>
                <view class="days-date">{{ item.isToday ? '今日' : item.date }}</view>
              </view>
            </view>
          </view>
          <view class="sign-progress">
            <text>本月进度</text>
            <progress
              :percent="signInProgress"
              stroke-width="7"
              activeColor="#3774FF"
              backgroundColor="#F5F5F5"
              border-radius="14"
            />
            <view>
              <text class="sign-progress-days">{{ signInDays }}</text>
              /{{ monthDaySize }}
            </view>
          </view>
        </view>
      </view>
    </view>
    <uni-popup ref="popup" type="dialog" :mask-click="false">
      <view
        class="popup-content"
        :style="{
          backgroundImage: `url(${IMG_PATH}${integration ? 'img_sign_in_intergral_dialog' : 'img_sign_in_dialog'}.png)`,
        }"
      >
        <view class="intergral" v-if="integration">
          积分 <span>+{{ integration }}</span>
        </view>
        <view class="btn" @click="hideSignInDialog"></view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import moment, { months } from 'moment';
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup';
import uniPopupDialog from '@/components/uni-ui/uni-popup-dialog/uni-popup-dialog';
import { signAndGetMonthSignData } from '@/services/index.js';

export default {
  name: 'index',
  components: { uniPopup, uniPopupDialog },
  data() {
    return {
      s_top: 0,
      s_height: 0,
      totalPoint: 0, //总积分
      currentMonth: '', //当前年月
      weekDays: ['日', '一', '二', '三', '四', '五', '六'],
      monthDays: [],
      monthDaySize: 0,
      signInDays: 0,
      signInList: [],
      integration: null,
    };
  },
  computed: {
    /**
     * 签到进度
     * @returns {number}
     */
    signInProgress() {
      return Number(this.signInDays / this.monthDaySize).toFixed(2) * 100;
    },
  },
  onShow() {
    // #ifdef MP
    this.initTopImg();
    // #endif
  },
  onLoad(query) {
    //设置当前月的所有日期
    this.setMonthDays(new Date());
    //获取总积分
  },
  methods: {
    // 签到并获取签到记录
    async singin() {
      const [, res] = await signAndGetMonthSignData({
        monthDate: moment(this.currentMonth, 'YYYY.MM').format('yyyy-MM'),
      });
      if (res && res.data) {
        this.totalPoint = res.data?.integrationTotal || 0;
        this.signInList = (res.data?.signDTOList || []).map((q) => {
          return {
            ...q,
            signDate: moment(q.signDate, 'YYYY-MM-DD', true).date(),
          };
        });
        this.getMonthSignData();
        const arr = this.monthDays.reduce((cur, nex) => {
          const findData = this.signInList.find((q) => q.signDate == nex.date);
          cur.push({
            ...nex,
            integralNum: findData?.integralNum,
            isSignIn: findData?.flag ? 1 : 0,
          });
          return cur;
        }, []);
        this.monthDays = arr;
        if (res.data.integration || res.data.integration == 0) {
          this.$nextTick(() => {
            this.integration = res.data.integration || 0;
            this.$refs.popup && this.$refs.popup.open();
          });
        }
      }
    },
    goBack() {
      uni.navigateBack({
        delit: -1,
      });
    },
    initTopImg() {
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      this.setData({
        s_top: menuButtonInfo?.top * 2 + 20,
        s_height: menuButtonInfo?.height * 2,
      });
    },
    /**
     * 设置一个月所有的日期以及这个月的总天数
     * @param date
     */
    setMonthDays(date) {
      const firstDayOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
      const lastDayOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);
      const dates = [];

      //第一天星期几
      const day = firstDayOfMonth.getDay(); //0 星期天 1-6星期一到星期六
      for (let i = 0; i < day; i++) {
        dates.push({
          date: '',
          signIn: 0, //是否已签到
        });
      }

      for (let i = firstDayOfMonth; i <= lastDayOfMonth; ) {
        const date = new Date(i);
        dates.push({
          date: date.getDate().toString(),
          signIn: 0, //是否已签到
          isToday: moment(i).isSame(moment(), 'day'), // 判断是否为今天
          isFuture: moment(i).isAfter(moment(), 'day'), // 判断是否大于今天
          integralNum: null,
        });
        i.setDate(i.getDate() + 1);
      }

      this.monthDays = dates;
      this.monthDaySize = dates.filter((item) => item.date.length > 0).length;
      this.currentMonth = moment(date).format('YYYY.MM');

      //获取月度签到数据
      this.singin();
    },
    /**
     * 上一个月：最近三月的数据，含当月
     */
    preMonth() {
      const today = new Date();
      const currentMonth = new Date(this.currentMonth.replace('.', '/') + '/01');

      const lastMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1);

      if (this.getMonthDifference(today, lastMonth) > 2) {
        uni.showToast({
          title: '最多查询近三个月的签到数据',
          duration: 2000,
          icon: 'none',
        });
        return;
      }

      this.setMonthDays(lastMonth);
    },
    /**
     * 下一个月；不能查询下一月
     */
    nextMonth() {
      const today = new Date();
      const currentMonth = new Date(this.currentMonth.replace('.', '/') + '/01');
      const nextMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1);
      if (nextMonth.getTime() > today.getTime()) {
        uni.showToast({
          title: '最多查询近三个月的签到数据',
          duration: 2000,
          icon: 'none',
        });
        return;
      }
      this.setMonthDays(nextMonth);
    },
    /**
     * 月份差
     * @param startDate
     * @param endDate
     * @returns {number}
     */
    getMonthDifference(startDate, endDate) {
      // 创建Date对象
      const start = new Date(startDate);
      const end = new Date(endDate);

      // 计算年份和月份的差值
      const yearDiff = end.getFullYear() - start.getFullYear();
      const monthDiff = end.getMonth() - start.getMonth();

      // 计算总月份差
      let totalMonthDiff = yearDiff * 12 + monthDiff;

      // 调整天数差异
      // if (end.getDate() < start.getDate()) {
      //   // 结束日期的天数小于起始日期的天数，需要减去一个月
      //   totalMonthDiff -= 1;
      // }

      return Math.abs(totalMonthDiff);
    },
    /**
     * 签到状态icon
     * @param day
     * @returns {string}
     */
    getDayIcon(item) {
      if (item.isFuture) {
        return `${this.IMG_PATH}icon-circle.png`;
      }
      return item.isSignIn == '1' ? `${this.IMG_PATH}icon_checked.png` : `${this.IMG_PATH}icon_uncheckin.png`;
    },
    /**
     * 获取月度签到数据
     */
    async getMonthSignData() {
      //已签天数
      this.signInDays = this.signInList.filter((item) => item.flag)?.length;
    },
    dateClick(day) {
      const todayDate = new Date().getDate().toString();
      if (todayDate == day.date && day.isSignIn == 0) {
        //只有当天可以签到，不补签
        this.singin();
      }
    },
    hideSignInDialog() {
      this.$refs.popup && this.$refs.popup.close();
    },
    openProtocol() {
      uni.navigateTo({
        url: `/pages/setting/agreement/index?&code=0414&title=${'《签到规则》'}`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.page-wrap {
  width: 100%;
  min-height: 100vh;
  background: #f0f5ff;
  background-size: 100% auto;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  .banner {
    padding: 20rpx 20rpx 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
    z-index: 2;
    box-sizing: border-box;
    color: #090909;
    .banner-title {
      font-family: 'PingFang SC';
      font-size: 36rpx;
      font-style: normal;
      font-weight: 500;
      flex: 1;
      text-indent: 4rpx;
      font-weight: 700;
      text-align: left;
      color: #000000;
    }
    .banner-block-img {
      width: 80rpx;
      height: 80rpx;
      color: #000000;
      transform: rotate(180deg);
    }
  }

  .star-image {
    width: 235rpx;
    height: 235rpx;
    position: absolute;
    top: -20rpx;
    right: 46rpx;
    z-index: 1;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .content-wrap {
    position: absolute;
    top: 0;
    z-index: 2;
    padding: 24rpx;
    width: 100%;
    box-sizing: border-box;
    .btn-signin-rule {
      display: inline-block;
      margin-left: auto;
      color: #000;
      background: rgba(255, 255, 255, 0.6);
      padding: 30rpx;
      box-shadow: border-box;
      border-radius: 20rpx;
      position: absolute;
      right: 35rpx;
      top: 20rpx;
    }
    .points {
      display: inline-block;

      view {
        display: flex;
        align-items: center;
        padding: 16rpx 32rpx;
        border-radius: 100rpx;
        background: rgba(255, 255, 255, 0.6);
        box-sizing: border-box;
      }

      image {
        width: 56rpx;
        height: 56rpx;
      }

      text {
        font-size: 50rpx;
        font-weight: bold;
        color: #333333;
        margin-left: 16rpx;
      }
    }

    .calendar {
      border-radius: 32rpx;
      background: #ffffff;
      padding: 32rpx 24rpx;
      margin-top: 24rpx;

      .sign-days {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 20rpx;
        border-radius: 16rpx;
        background: #eaf6ff;
        font-size: 28rpx;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.9);

        .current-date {
          font-size: 22rpx;
          font-weight: 500;
          color: #333333;
        }

        image {
          width: 40rpx;
          height: 40rpx;
        }

        image:first-child {
          transform: rotate(180deg);
        }
      }

      .days {
        &-date {
          padding: 10rpx 0;
          width: 100%;
        }
        &-week {
          display: grid;
          grid-template-columns: repeat(7, 1fr);
          grid-gap: 24rpx 18rpx;
          font-size: 28rpx;
          font-weight: 500;
          color: #3774ff;
          padding: 24rpx 0;

          text {
            text-align: center;
          }
        }
        &-is-today {
          // border: 3px solid #f4b07a;
          box-sizing: border-box;
          .days-date {
            white-space: nowrap;
            font-size: 20rpx;
            background-color: #1e9cff;
            color: #fff;
            border-radius: 8rpx;
          }
        }
        &-item-list {
          display: grid;
          grid-template-columns: repeat(7, 1fr);
          grid-gap: 24rpx 18rpx;
        }

        &-item {
          padding: 8rpx 0rpx 0 0;
          border-radius: 8rpx;
          background: #f8f8f8;
          font-size: 28rpx;
          font-weight: 500;
          text-align: center;
          color: #9e9e9e;
          position: relative;
          .img-wrap {
            width: 80rpx;
            height: 80rpx;
            position: relative;
            margin: auto;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 5rpx;
            position: relative;
            .num {
              position: absolute;
              margin: auto;
              // left: 50%;
              // top: 50%;
              // transform: translate(-50%, -50%);
              font-size: 20rpx;
              color: #963f00;
            }
            image {
              width: 60rpx;
              height: 60rpx;
            }
          }
          .mask {
            position: absolute;
            background: rgba(255, 255, 255, 0.6);
            inset: 0;
          }
        }

        &-item-sign-in {
          background: rgba(243, 176, 121, 0.2);
          border: 2rpx solid #f4b07a;
        }
      }

      .sign-progress {
        display: flex;
        align-items: center;
        font-size: 26rpx;
        font-weight: 500;
        color: #666666;
        margin-top: 24rpx;

        progress {
          margin: 0 21rpx;
          flex: 1;
        }

        &-days {
          color: #3774ff;
        }
      }
    }
  }
}

.popup-content {
  width: 468rpx;
  height: 468rpx;
  background-size: 100% 100%;
  display: flex;
  align-items: flex-end;
  flex-direction: column;
  justify-content: flex-end;
  .intergral {
    width: 100%;
    height: 40rpx;
    text-align: center;
    span {
      color: #4598fe;
      font-weight: 600;
    }
  }
  .btn {
    width: 100%;
    height: 90rpx;
    margin-bottom: 48rpx;
  }
}
</style>
