<template>
  <div class="img-wrap">
    <image mode="widthFix" :src="item" v-for="(item, index) in imgList" :key="index" alt="" />
  </div>
</template>

<script>
export default {
  props: {},
  components: {},
  data() {
    return {};
  },
  computed: {
    imgList() {
      return [
        this.IMG_PATH + 'chargeGuide-1.png',
        this.IMG_PATH + 'chargeGuide-2.png',
        this.IMG_PATH + 'chargeGuide-3.png',
        this.IMG_PATH + 'chargeGuide-4.png',
        this.IMG_PATH + 'chargeGuide-5.png',
      ];
    },
  },
};
</script>

<style scoped lang="scss">
.img-wrap {
  display: flex;
  flex-direction: column;
  image {
    width: 100%;
  }
}
</style>
