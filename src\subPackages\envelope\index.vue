<template>
  <view>
    <view v-if="!hideNoticeBar" class="noticeBar-top" @click="handleLink('/subPackages/envelopeSet/index')">
      <view class="noticeBar-title">
        <image class="noticeBar-left" :src="`${IMG_PATH}icon-envelope.png`"></image>
        小程序消息通知开启指南
      </view>
      <view class="noticeBar-right">
        <view class="noticeBar-btn">查看</view>
        <image @click.stop="closeNoticeBar" class="noticeBar-icon" :src="`${IMG_PATH}icon-close.png`"></image>
      </view>
    </view>
    <view class="myorder">
      <view class="myorder-tab">
        <view
          v-for="(item, index) in tabList"
          :key="item.code"
          :class="{ active: currentTab == index }"
          :data-id="index"
          :data-current="index"
          @tap="swichNav"
        >
          <view class="text-wrap" :class="{ 'tab-dot': item.hasDot }"
            >{{ item.name }}
            <view class="text-num" v-if="item.list.filter((q) => q.readFlag == '01').length > 0">
              {{
                item.list.filter((q) => q.readFlag == '01').length > 99
                  ? '99+'
                  : item.list.filter((q) => q.readFlag == '01').length
              }}
            </view>
          </view>
        </view>
      </view>
      <view class="myorder-main">
        <!-- 全部 -->
        <swiper :current="currentTab" class="swiper-box" duration="300" @change="bindChange">
          <swiper-item v-for="(item, index) in tabList" :key="index">
            <view v-if="!item.list.length" class="myorder-image">
              <image :src="`${IMG_PATH}main-data-null.png`" mode="heightFix" />
              <text>暂无消息</text>
            </view>
            <view class="order-wrap">
              <view
                @click="jump(it, item.type)"
                :data-msg="item.orderNo"
                class="myorder-list"
                v-for="(it, i) in item.list"
                :key="i"
              >
                <view class="list-main">
                  <view class="notice-title">
                    <div class="hot" v-if="it.readFlag == '01'"></div>
                    {{ it.noticeTitle || '-' }}</view
                  >
                  <div class="content">
                    {{ it.remark || '' }}
                  </div>
                  <view class="notice-time">{{ it.sendTime || '-' }}</view>
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>
    </view>
    <loading v-if="!hiddenLoading">正在加载</loading>
  </view>
</template>

<script>
import { getChargingOrder, getMsgCenterList } from '@/services/index.js';
export default {
  components: {},
  data() {
    return {
      hiddenLoading: true,
      winWidth: 0,
      winHeight: 2000,
      currentTab: 0,
      orderNo: '',
      tabList: [
        {
          name: '服务',
          code: 'service',
          type: '0301',
          hasDot: false,
          list: [],
        },
        {
          name: '公告',
          code: 'notice',
          type: '0103',
          hasDot: false,
          list: [],
        },
        {
          name: '活动',
          code: 'active',
          type: '0201',
          hasDot: false,
          list: [],
        },
      ],
      dataList: {
        service: {
          list: [],
          hasDot: false,
        },
        notice: {
          list: [],
          hasDot: false,
        },
        active: {
          list: [],
          hasDot: false,
        },
      },
      typeEnum: [
        { type: '0301', name: 'service' },
        { type: '0103', name: 'notice' },
        { type: '0201', name: 'active' },
      ],
      hideNoticeBar: uni.getStorageSync('hideNoticeBar') || false,
    };
  },
  onLoad(options) {
    if (options.currentTab) {
      this.currentTab = options.currentTab;
    }
    // this.init();
  },
  onReady(e) {},
  onShow() {
    this.init();
    this.getOrderInfo();
  },
  methods: {
    async getOrderInfo() {
      let params = {};
      const [, res] = await getChargingOrder(params);
      if (res && res.ret == '200') {
        this.orderNo = res?.orderNo ? res.orderNo.split(',')[0] : '';
      }
    },
    jump(item, type) {
      if (this.currentTab != '0') {
        const url = `/subPackages/envelopeDetail/index?msgType=${type}&noticeId=${item.noticeId}`;
        uni.navigateTo({ url: url });
      } else {
        let pathList = [
          {
            title: '充电开始通知',
            url: `/pages/basic/charge/charge?orderNo=${this.orderNo}`,
            needOrderNo: true,
          },
          {
            title: '充电费用不足提醒',
            url: `/pages/basic/charge/charge?orderNo=${this.orderNo}`,
            needOrderNo: true,
          },
          {
            title: '启动充电通知',
            url: `/pages/basic/charge/charge?orderNo=${this.orderNo}`,
            needOrderNo: true,
          },
          {
            title: '充电开始通知',
            url: `/pages/basic/charge/charge?orderNo=${this.orderNo}`,
            needOrderNo: true,
          },
          {
            title: '充电即将结束提醒',
            url: `/pages/basic/charge/charge?orderNo=${this.orderNo}`,
            needOrderNo: true,
          },
          {
            title: '充电结算通知',
            url: `/pages/myorder/myorder`,
            needOrderNo: false,
          },
          {
            title: '充值成功通知',
            url: `/pages/basic/money/money`,
            needOrderNo: false,
          },
          {
            title: '退款成功通知',
            url: `/subPackages/history/history`,
            needOrderNo: false,
          },
          {
            title: '退款失败通知',
            url: `/subPackages/history/history`,
            needOrderNo: false,
          },
          {
            title: '开票成功通知',
            url: `/subPackages/invoiceopening/invoiceopening`,
            needOrderNo: false,
          },
          {
            title: '开票失败通知',
            url: `/subPackages/invoiceopening/invoiceopening`,
            needOrderNo: false,
          },
        ];
        let findData = pathList.find((q) => q.title == item.noticeTitle);
        console.log(findData, this.orderNo);
        if (findData) {
          if (findData.needOrderNo && !this.orderNo) return;
          uni.navigateTo({
            url: findData.url,
          });
        }
      }
    },

    init: async function () {
      var that = this;
      /**
       * 获取系统信息
       */
      uni.getSystemInfo({
        success(res) {
          console.log(res);
          that.setData({
            winWidth: res.windowWidth,
          });
        },
      });

      that.setData({
        hiddenLoading: false,
      });
      try {
        await Promise.all(this.tabList.map((item, index) => this.getData(index)));
      } finally {
        that.setData({
          hiddenLoading: true,
        });
      }

      // that.getData(0);
    },
    bindChange(e) {
      var that = this;
      that.setData({
        currentTab: e.detail.current,
      });
      this.swichNav({ currentTarget: { dataset: { current: e.detail.current } } });
    },
    swichNav(e) {
      var that = this;
      const current = e.currentTarget.dataset.current;
      console.log('====================================');
      console.log(e, current);
      console.log('====================================');
      if (this.currentTab === current) {
        return false;
      } else {
        that.setData({
          currentTab: current,
        });
      }
      // if (current === 2) {
      //   this.currentTab = 2;
      //   that.getData(Number(current));
      //   // uni.navigateTo({
      //   //   url: '/subPackages/envelopeActive/index',
      //   // });
      //   // this.currentTab = 1;
      // }
      // that.getData(Number(current));
    },
    getData(index) {
      return new Promise(async (resolve, reject) => {
        var that = this;
        const [, res] = await getMsgCenterList({
          msgType: that.tabList[index].type, // 0201 活动  0301 服务
          // orderStatus: '',
          province: '江苏省',
          pageNum: 1,
          totalNum: 100,
          // province: '江苏省',
        });
        if (res && res.ret == '200') {
          that.tabList[index].list = (res.msgList || []).sort((a, b) => a.readFlag - b.readFlag);
          that.tabList[index].hasDot = res?.msgList && res.msgList.find((item) => item.readFlag === '02');
          console.log(that.tabList, 'that.tabList');
          resolve(res);
        }
      });
    },
    handleLink(url) {
      uni.navigateTo({
        url,
      });
    },
    closeNoticeBar(url) {
      this.hideNoticeBar = true;
      uni.setStorageSync('hideNoticeBar', true);
    },
  },
};
</script>
<style>
@import './index.css';
</style>
