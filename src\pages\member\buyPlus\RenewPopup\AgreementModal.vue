<template>
  <div class="agreement-modal-mask" v-if="visible" @click="handleMaskClick">
    <div class="agreement-modal" @click.stop>
      <div class="modal-title">是否开通自动续费</div>
      
      <div class="modal-content">
        <div class="agreement-section" @click="toggleAgreement">
          <div class="checkbox-wrapper">
            <div :class="['checkbox', { 'checked': isAgreed }]">
              <div class="checkbox-icon" v-if="isAgreed">✓</div>
            </div>
          </div>
          <span class="agreement-text">开通前阅读</span>
          <span class="agreement-link" @click.stop="openProtocol">《自动续费协议》</span>
        </div>
      </div>
      
      <div class="modal-buttons">
        <div class="modal-btn cancel-btn" @click="handleCancel">取消开通</div>
        <div class="modal-btn confirm-btn" @click="handleConfirm">确认开通</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AgreementModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isAgreed: false,
    };
  },
  methods: {
    // 切换协议勾选状态
    toggleAgreement() {
      this.isAgreed = !this.isAgreed;
    },
    // 打开协议页面
    openProtocol() {
      uni.navigateTo({
        url: `/pages/setting/agreement/index?&code=0413&title=${'《自动续费服务协议》'}`,
      });
    },
    // 点击遮罩关闭
    handleMaskClick() {
      this.handleCancel();
    },
    // 取消开通
    handleCancel() {
      this.isAgreed = false;
      this.$emit('cancel');
    },
    // 确认开通
    handleConfirm() {
      if (!this.isAgreed) {
        uni.showToast({
          title: '请先阅读并同意自动续费协议',
          icon: 'none',
          duration: 2000,
        });
        return;
      }
      this.$emit('confirm');
      this.isAgreed = false;
    },
  },
};
</script>

<style scoped lang="scss">
.agreement-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.agreement-modal {
  width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
}

.modal-title {
  padding: 40rpx 40rpx 20rpx;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.modal-content {
  padding: 20rpx 40rpx 40rpx;
}

.agreement-section {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox-wrapper {
  margin-right: 16rpx;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #ddd;
  border-radius: 6rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #007aff;
  border-color: #007aff;
}

.checkbox-icon {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

.agreement-text {
  font-size: 28rpx;
  color: #333;
  margin-right: 8rpx;
}

.agreement-link {
  font-size: 28rpx;
  color: #007aff;
  text-decoration: underline;
  cursor: pointer;
}

.modal-buttons {
  display: flex;
  border-top: 1rpx solid #eee;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cancel-btn {
  color: #666;
  border-right: 1rpx solid #eee;
}

.cancel-btn:active {
  background: #f5f5f5;
}

.confirm-btn {
  color: #007aff;
  font-weight: 600;
}

.confirm-btn:active {
  background: #f0f8ff;
}
</style>
