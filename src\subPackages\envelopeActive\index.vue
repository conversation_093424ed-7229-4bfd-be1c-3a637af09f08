<template>
  <div class="wrap" :style="{ backgroundImage: `url(${IMG_PATH_UI}bg.png)` }">
    <!-- #ifdef MP-WEIXIN -->
    <view
      class="banner"
      :style="'padding-top:' + s_top + 'rpx;height:' + s_height + 'rpx;line-height:' + s_height + 'rpx'"
    >
      <!-- <image class="banner-img" @tap="goBack" mode="widthFix" :src="`${IMG_PATH}back.png`"></image> -->
      <view class="banner-title"
        ><icon type="icon iconfont icon-arrow_left" class="mr15" @click="goBack" size="18" /> 平台活动</view
      >
    </view>
    <!-- #endif -->

    <div class="content">
      <scroll-view
        v-if="tableData.length > 0"
        class="list-found"
        scroll-y="true"
        refresher-enabled="true"
        :refresher-triggered="triggered"
        :refresher-threshold="100"
        @scrolltolower="toLowFun"
        :lower-threshold="50"
        refresher-background="rgba(0,0,0,0)"
        :show-scrollbar="false"
        @refresherrefresh="onRefresh"
        @refresherrestore="onRestore"
      >
        <div class="station-item" v-for="(item, index) in tableData" :key="index" @click="toPreview(item)">
          <div class="img-wrap">
            <image v-if="item.imgUrl" :src="item.imgUrl" mode="widthFix" />
            <view class="empty-text" v-else>改活动暂无banner图片</view>
          </div>
          <div class="title">{{ item.noticeTitle || '' }}</div>
          <div class="time">{{ item.sendTime || '' }}</div>
        </div>
      </scroll-view>
      <AEmpty :noneText="'暂无活动'" v-else></AEmpty>
    </div>
  </div>
</template>

<script>
import { getMsgCenterList } from '@/services/index.js';
import AEmpty from '@/components/AEmpty';
import { startsWithHttp } from '@/utils/util';
export default {
  props: {},
  components: { AEmpty },
  data() {
    return {
      s_top: 0,
      s_height: 0,
      tableData: [],
      tablePage: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      triggered: 'restore',
      freshing: false,
      memberInfo: {},
      loading: false,
    };
  },
  onLoad() {},
  onShow() {
    this.initTopImg();
  },
  computed: {
    topFixed2() {
      return 200 + this.s_top + 'rpx';
    },
  },
  watch: {},
  created() {
    this.loadData();
  },
  mounted() {},
  methods: {
    toPreview(item) {
      console.log(item.contentUrl, 'item');
      if (!item.contentUrl) return;
      uni.navigateTo({
        url:
          '/pages/setting/out/index' + '?url=' + encodeURIComponent(JSON.stringify(item.contentUrl)) + '&title=' + '',
      });
      // const url = `/subPackages/envelopeDetail/index?msgType=0201&noticeId=${item.noticeId}`;
      // uni.navigateTo({ url: url });
    },
    goBack() {
      // #ifdef H5
      this.$router.go(-1);
      // #endif
      // #ifdef MP-WEIXIN
      uni.navigateBack({
        delta: -1,
      });
      // #endif
    },
    initTopImg() {
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      this.setData({
        s_top: menuButtonInfo?.top * 2 + 20,
        s_height: menuButtonInfo?.height * 2,
      });
    },
    toLowFun() {
      if (this.tablePage.total <= this.tableData.length || this.freshing || this.loading) return;
      this.tablePage.current += 1;
      this.loadData();
    },
    async loadData() {
      const params = {
        totalNum: this.tablePage.pageSize,
        pageNum: this.tablePage.current,
        msgType: '0201',
      };
      this.loading = true;
      const [err, res] = await getMsgCenterList(params);
      this.loading = false;
      if (res) {
        res.msgList.forEach((item) => {
          if (!this.tableData.find((q) => q.noticeId === item.noticeId)) {
            this.tableData.push({
              ...item,
              imgUrl: item.imgUrl ? startsWithHttp(item.imgUrl) : '',
            });
          }
        });
        this.tablePage.total = res.count;
      }
    },
    onRefresh() {
      if (this.freshing) return;
      this.freshing = true;
      this.tablePage = {
        current: 1,
        pageSize: 20,
        total: 0,
      };
      this.loadData();
      this.freshing = false;
      this.triggered = false;
    },

    onRestore() {
      this.triggered = 'restore'; // 需要重置
    },
  },
};
</script>

<style scoped lang="scss">
.wrap {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  background-size: 100% 100%;
  .banner {
    padding: 20rpx 20rpx 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
    z-index: 2;
    box-sizing: border-box;
    color: #000000;
    .banner-title {
      font-family: 'PingFang SC';
      font-size: 36rpx;
      font-style: normal;
      font-weight: 500;
      flex: 1;
      text-indent: 4rpx;
      color: #000000;
      font-weight: 700;
      text-align: left;
      color: #000000;
      display: flex;
      align-items: center;
      line-height: 0;
    }
    .banner-img {
      width: 48rpx;
      height: 48rpx;
      margin-right: 20rpx;
    }
    .mr15 {
      margin-right: 15rpx;
    }
  }
  .info {
    padding: 50rpx 24rpx 24rpx 24rpx;
    display: flex;
    width: 100%;
    align-items: center;
    .btn {
      margin-right: 15rpx;
      border-radius: 19rpx;
      background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0) -3%,
          rgba(255, 255, 255, 0.19) 11%,
          rgba(10, 9, 9, 0) 65%
        ),
        linear-gradient(115deg, #5e5a54 0%, #1e1f28 93%);
      box-shadow: inset 0px 1px 0px 0px #fff9ea;
      font-size: 24rpx;
      padding: 12rpx 16rpx;
      color: #ffe3c1;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 110rpx;
    }
    .code {
      max-width: 500rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .content {
    width: 100%;
    flex: 1;
    /* #ifdef MP-WEIXIN */
    padding: 5rpx 24rpx;
    /* #endif */
    /* #ifdef H5 */
    padding: 10rpx 24rpx;
    /* #endif */
    font-size: 32rpx;
    color: #333333;
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    grid-gap: 24rpx;
    overflow: hidden;
    .list-found {
      width: 100%;
      height: 100%;
      padding-bottom: 40rpx;
      .station-item {
        width: 100%;
        border-radius: 15rpx;
        overflow: hidden;
        margin-bottom: 20rpx;
        border: 1px solid rgba(0, 0, 0, 0.1);
        .title {
          font-size: 30rpx;
          font-weight: 600;
          padding: 10rpx 20rpx;
        }
        .time {
          font-size: 24rpx;
          font-weight: 600;
          padding: 10rpx 20rpx;
        }
        .img-wrap {
          width: 100%;
          // height: 250rpx;
          background: #fff;
          .empty-text {
            color: #fff;
            width: 100%;
            height: 100%;
            text-align: center;
            line-height: 60rpx;
          }
          image {
            width: 100%;
            height: auto;
          }
        }
      }
    }
  }
}
</style>
