<!--
@name: 地址新增
@description: 地址新增、编辑的页面
@author: LiuQian<PERSON>en
@time: 2024/8/8
-->
<template>
  <view class="address-edit">
    <view class="address-edit-content">
      <uni-forms
        ref="addressForm"
        :model-value="addressForm"
        err-show-type="toast"
        validate-trigger="blur"
        :label-width="90"
        :border="true"
      >
        <uni-forms-item label="收件人" name="contacts">
          <uni-easyinput
            v-model="addressForm.contacts"
            :clearable="false"
            :input-border="false"
            :maxlength="20"
            placeholder="请输入收件人名字"
            placeholder-style="font-size: 32rpx; color: #999;"
          ></uni-easyinput>
        </uni-forms-item>
        <uni-forms-item label="手机号码" name="mobile">
          <uni-easyinput
            v-model="addressForm.mobile"
            :clearable="false"
            :input-border="false"
            placeholder="11位手机号码"
            placeholder-style="font-size: 32rpx; color: #999;"
          ></uni-easyinput>
        </uni-forms-item>
        <uni-forms-item label="选择地区" name="area">
          <uni-data-picker
            v-model="addressForm.area"
            :clearable="false"
            :map="{ text: 'areaName', value: 'areaCode' }"
            :clear-icon="false"
            placeholder-style="font-size: 32rpx; color: #999;"
            placeholder="请选择地区"
            popup-title="请选择地区"
            :localdata="country"
            @change="changeAddress"
          ></uni-data-picker>
        </uni-forms-item>
        <uni-forms-item label="详细地址" name="address">
          <uni-easyinput
            v-model="addressForm.address"
            :clearable="false"
            :input-border="false"
            type="textarea"
            auto-height
            placeholder="请输入详细地址"
            placeholder-style="font-size: 32rpx; color: #999;"
          ></uni-easyinput>
        </uni-forms-item>
      </uni-forms>
    </view>
    <!--        底部按钮-->
    <view class="footer">
      <BottomPannel>
        <view class="footer-button">
          <AppButton class="button-item" type="primary" @click="saveAddress">保存</AppButton>
        </view>
      </BottomPannel>
    </view>
  </view>
</template>

<script>
import uniForms from '@/components/uni-ui/uni-forms/uni-forms';
import uniFormsItem from '@/components/uni-ui/uni-forms-item/uni-forms-item';
import uniEasyinput from '@/components/uni-ui/uni-easyinput/uni-easyinput';
import uniDataPicker from '@/components/uni-ui/uni-data-picker/uni-data-picker';
import BottomPannel from '@/components/BottomPannel/index';
import AppButton from '@/components/AppButton/index';
import { getCountry, editAddress, getAddressList } from '@/services/index.js';

export default {
  name: 'index',
  components: {
    uniForms,
    uniEasyinput,
    uniDataPicker,
    uniFormsItem,
    BottomPannel,
    AppButton,
  },
  data() {
    return {
      addressForm: {
        contacts: '',
        mobile: '',
        area: '',
        address: '',
      },
      selectArr: [],
      custAddrId: '',
      country: [],
      detail: {},
      selectPostCode: '',
      rules: {
        contacts: {
          rules: [{ required: true, errorMessage: '收件人名字不能为空' }],
        },
        mobile: {
          rules: [
            { required: true, errorMessage: '手机号码不能为空' },
            {
              validateFunction: function (rule, value, data, callback) {
                const iphoneReg = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/; //手机号码
                if (!iphoneReg.test(value)) {
                  callback('手机号格式不正确');
                }
              },
            },
          ],
        },
        area: {
          rules: [{ required: true, errorMessage: '选择地区不能为空' }],
        },
        address: {
          rules: [
            {
              required: (value) => {
                if (/^[a-zA-Z]+$/.test(value)) {
                  return false;
                } else {
                  return true;
                }
              },
              errorMessage: '详细地址不能为空',
            },
          ],
        },
      },
    };
  },
  onLoad(options) {
    this.custAddrId = options.custAddrId || '';
    if (this.custAddrId) {
      this.getAddressList();
    }
    this.getCountry();
  },
  onReady() {
    //在onReady中设置规则
    this.$refs.addressForm.setRules(this.rules);
  },
  methods: {
    // 查询省市区
    async getCountry() {
      const [err, res] = await getCountry({});
      if (res) {
        this.country = res.areaNode;
      }
    },
    // 保存地址
    saveAddress() {
      this.$refs.addressForm.validate().then(async () => {
        const params = {
          operType: '1',
          custAddrId: this.custAddrId,
          contactName: this.addressForm.contacts,
          contactMobile: this.addressForm.mobile,
          addr: this.addressForm.address,
        };
        if (this.custAddrId) {
          params.operType = '2';
          params.isDefault = this.detail.isDefault;
          params.postCode = this.detail.postCode;
          params.procode = this.detail.procode;
          params.citycode = this.detail.citycode;
          params.countycode = this.detail.countycode;
        } else {
          params.operType = '1';
          params.isDefault = '0';
          params.postCode = this.selectPostCode;
          params.procode = this.selectArr[0].value;
          params.citycode = this.selectArr[1].value;
          params.countycode = this.selectArr[2].value;
        }
        const [err, res] = await editAddress(params);
        if (res) {
          uni.showToast({
            title: '保存成功！',
            icon: 'none',
          });
          uni.navigateBack({
            success() {
              uni.$emit('refreshData', {});
            },
          });
        }
      });
    },
    // 查询地址详情
    async getAddressList() {
      const params = {
        custAddrId: this.custAddrId,
      };
      const [err, res] = await getAddressList(params);
      if (res.data) {
        const { contactName, contactMobile, addr, countycode } = res.data?.queryList[0];
        this.addressForm.contacts = contactName;
        this.addressForm.mobile = contactMobile;
        this.addressForm.area = countycode;
        this.addressForm.address = addr;
        this.detail = res.data.queryList[0];
      }
    },
    // 切换地址
    changeAddress(e) {
      this.selectArr = e.detail.value;
      this.findNodeFromTreeById(this.country, this.selectArr[this.selectArr.length - 1].value);
      this.detail.procode = this.selectArr[0].value;
      this.detail.citycode = this.selectArr[1].value;
      this.detail.countycode = this.selectArr[2].value;
    },
    // 递归获取邮编
    findNodeFromTreeById(root, id) {
      try {
        root.forEach((item) => {
          if (item.areaCode === id) {
            throw item;
          } else {
            if (item && item.children && item.children.length) {
              this.findNodeFromTreeById(item.children, id);
            }
          }
        });
      } catch (e) {
        this.selectPostCode = e.areaNo;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.address-edit {
  &-content {
    padding: 40rpx 24rpx 24rpx 24rpx;
    background: white;
  }
  .footer {
    z-index: 1;
    .footer-button {
      display: flex;
      gap: 24rpx;
      grid-gap: 24rpx;
      .button-item {
        flex: 1;
      }
    }
  }
}
</style>
