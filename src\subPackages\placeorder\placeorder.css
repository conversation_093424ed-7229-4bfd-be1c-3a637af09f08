.placeorder {
  padding-bottom: 240rpx;
}

.placeorder-state {
  line-height: 74rpx;
  padding: 10rpx 0;
  text-align: center;
  background-color: #fff;
  font-size: 32rpx;
}

.placeorderr-state .state-pile {
  color: #333;
  font-size: 30rpx;
}

.placeorder-state .state-id {
  padding-bottom: 10rpx;
  color: #999;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeorder-state .state-id .fault {
  width: 102.26rpx;
  color: #fff;
  font-size: 20.4rpx;
  border-radius: 4rpx;
  background: #ffe5e5;
  background: #ff8301;
  margin-left: 12rpx;
  line-height: 34rpx;
  text-align: center;
}

.placeorder-state .state-id .fault-icon {
  color: white;
  background: #ff8301;
  width: 30rpx;
  height: 30rpx;
  border-radius: 30rpx;
  font-size: 20.4rpx;
  margin-left: 12rpx;
  text-align: center;
  line-height: 30rpx;
}

.rated-main {
  display: flex;
  line-height: 54rpx;
  padding: 20rpx 0;
  border-top: 1rpx solid #e1e1e1;
}

.rated-main view {
  flex: 1;
  text-align: center;
  color: #666;
  font-size: 28rpx;
}

.rated-main view text {
  display: block;
}

.cost-main {
  padding: 20rpx 30rpx;
  line-height: 54rpx;
  background-color: #fff;
  margin-top: 20rpx;
  color: #333;
  font-size: 28rpx;
}

.cost-main .cost-price icon {
  color: #999;
  margin-left: 10rpx;
}

.recharge-amount {
  background-color: #fff;
  margin-top: 20rpx;
  color: #333;
  font-size: 30rpx;
}

.recharge-amount .other-amount {
  position: relative;
  padding: 0 30rpx 0 210rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.recharge-amount .other-amount text {
  position: absolute;
  left: 30rpx;
  top: 0;
  display: block;
  height: 88rpx;
  line-height: 88rpx;
}

.recharge-amount .other-amount input {
  width: 100%;
  height: 88rpx;
  text-align: left;
}

.amount-btn {
  display: flex;
  height: 68rpx;
  padding: 30rpx 0;
  margin-left: 30rpx;
}

.amount-btn button {
  flex: 1;
  height: 68rpx;
  line-height: 68rpx;
  text-align: center;
  margin-right: 30rpx;
  color: #999;
  font-size: 30rpx;
  background-color: #fff;
}

.amount-btn button.active {
  color: #fff;
  background-color: #2196f3;
}

.cars {
  width: 100%;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20rpx;
}

.carNop {
  margin-top: 0;
}

.cars .Carbtn {
  width: 691rpx;
  height: 100%;
  background: #fff;
  color: #2196f3;
  text-align: center;
  line-height: 88rpx;
  border-radius: 8rpx;
}

.cars-explain {
  width: 100%;
  box-sizing: border-box;
  padding: 0 34rpx;
  margin-top: 40rpx;
}

.cars-explain .cars-explain-top {
  font-size: 26rpx;
  color: #2196f3;
  margin-bottom: 12rpx;
}

.cars-explain .cars-explain-msg {
  font-size: 24rpx;
  color: #666;
  line-height: 35rpx;
}

.cars .Carhav {
  width: 100%;
  height: 88rpx;
  color: #333;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0 30rpx;
  background: #fff;
}

.cars .Carhav .Carhav-right {
  color: #2195f2;
}

.cars .Carhav .Carhav-right .icon {
  margin-left: 16rpx;
}

.cars .Carhav .carsIcon {
  color: #ff5e03;
  font-size: 24rpx;
}

.cars .Carhav .carsIcon text {
  font-size: 32rpx;
}

.cars .Carhav .commons {
  font-size: 26rpx;
  color: #333;
}

.cars .Carhav .commons text {
  font-size: 38rpx;
  color: #ff5e03;
}

.cars .Carhav .commons button {
  display: inline;
  width: 68rpx;
  height: 34rpx;
  border: 2rpx solid #c3c3c3;
  font-size: 24rpx;
  color: #c3c3c3;
  background: #fff;
  margin: 0 0 0 18rpx;
  line-height: 34rpx;
}

.cars .Carhav .Carhav-lefts {
  width: 214rpx;
}

.cars .Carhav .Carhav-fonts {
  font-size: 30rpx;
  color: #666;
  box-sizing: border-box;
  padding-left: 61rpx;
}

.cars .Carhav .Carhav-left image {
  width: 37rpx;
  height: 29rpx;
  margin-right: 26rpx;
  vertical-align: middle;
  padding-bottom: 6rpx;
}

.mask {
  position: absolute;
  width: 100%;
  height: 100%;
  background: #000;
  opacity: 0.5;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.mask-alers {
  position: absolute;
  width: 100%;
  height: 622rpx;
  background: #fff;
  z-index: 1000;
  bottom: 0;
  left: 0;
  right: 0;
}

.mask-alers .mask-alers-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  box-sizing: border-box;
  padding: 0 30rpx;
  height: 88rpx;
  background: #2196f3;
  color: #fff;
}

.mask-alers .mask-list {
  width: 100%;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mask-alers .mask-list {
  width: 100%;
  height: 88rpx;
}

.mask-alers .mask-list .mask-list-li .mask-list-lf {
  width: 420rpx;
}

.mask-alers .mask-list .mask-list-li {
  width: 690rpx;
  height: 100%;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #383838;
  border-bottom: 1rpx solid #979797;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mask-alers .mask-list .mask-list-li .x {
  width: 60rpx;
  font-size: 50rpx;
  text-align: right;
  color: red;
}

.mask-alers .mask-list .mask-list-li .mask-list-rt .changes {
  font-size: 50rpx !important;
}

.mask-alers .mask-list .mask-list-li .mask-list-rt .icon-checked {
  color: #2196f3 !important;
}

.mask-list-rt image {
  width: 40rpx;
  height: 40rpx;
}

.charging {
  width: 100%;
  background: #fff;
  margin-top: 18rpx;
}

.charging .charging_head {
  display: flex;
  /* justify-content: space-around; */
  box-sizing: border-box;
  font-size: 26rpx;
  color: #333;
  height: 88rpx;
  width: 100%;
}

.charging .charging_head .head {
  width: 100rpx;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.charging .charging_head .head image {
  width: 37rpx;
  height: 37rpx;
}

.charging .charging_head .middles {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 30rpx;
  box-sizing: border-box;
  border-bottom: 1rpx dotted #e1e1e1;
}

.charging .charging_head .middles .middle_number text {
  font-size: 34rpx;
  color: #ff9802;
}

.charging .charging_middle {
  width: 100%;
  height: 234rpx;
  box-sizing: border-box;
  margin-left: 100rpx;
  display: flex;
  flex-wrap: wrap;
  align-content: space-around;
}

.charging .charging_middle .middle_list {
  width: calc(100% - 100rpx);
  font-size: 26rpx;
  color: #666;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 30rpx;
  box-sizing: border-box;
}

.charging .charging_middle .middle_list view {
  color: #2196f3;
}

.charging .charging_middle .middle_list view image {
  height: 20rpx;
  width: 24rpx;
  margin-left: 13rpx;
}

.charging .charging_middles {
  width: calc(100% - 100rpx);
  box-sizing: border-box;
  margin-left: 100rpx;
  display: flex;
  flex-wrap: wrap;
  align-content: space-around;
  font-size: 26rpx;
  color: #666;
  padding: 23rpx 0;
}

.charging .charging_middles .middle_list {
  display: flex;
  width: 100%;
  align-content: center;
}

.charging .charging_middles .middle_list .list {
  height: 100%;
  padding: 23rpx 0;
  display: flex;
  font-size: 26rpx;
  color: #333;
  flex-wrap: wrap;
  box-sizing: border-box;
  justify-content: center;
  align-items: center;
  width: 20%;
}

.charging .charging_middles .middle_list .list image {
  display: block;
}

.charging .charging_middles .middle_list .list view {
  width: 100%;
  text-align: center;
  margin-top: 20rpx;
}

.charging .charging_middles .middle_list .list .image1 {
  width: 47rpx;
  height: 55rpx;
}

.charging .charging_middles .middle_list .list .image2 {
  width: 55rpx;
  height: 39rpx;
}

.charging .charging_middles .middle_list .list .image3 {
  width: 36rpx;
  height: 60rpx;
}

.charging .charging_middles .middle_list .list .image4 {
  width: 59rpx;
  height: 48rpx;
}

.charging .charging_middles .middle_list .list .image5 {
  width: 57rpx;
  height: 43rpx;
}

.fixed_btn {
  position: fixed;
  bottom: 0;
  left: 0;
  padding-bottom: 20rpx;
  background: #fff;
}

.fixed_btn button {
  width: 556rpx;
  height: 72rpx;
  margin: 0 auto;
  color: #fff;
  line-height: 72rpx;
}

/* .fixed_btn view {
  font-size: 24rpx;
  color: #999;
  width: 100%;
  text-align: center;
  line-height: 40rpx;
} */
.fixed_btn .gray {
  background: #eee;
}

.payMes {
  width: 100%;
  background: #fff;
  box-sizing: border-box;
}

.payMes .payMes_head {
  width: calc(100% - 30rpx);
  margin-left: 30rpx;
  height: 88rpx;
  border-bottom: 1rpx solid #efefef;
  font-size: 26rpx;
  color: #333;
  display: flex;
  align-items: center;
}

.payMes .payMes_head image {
  width: 37rpx;
  height: 35rpx;
  margin-right: 24rpx;
}

.payMes .payMes_head text {
  color: #ff5e03;
  font-size: 24rpx;
  margin-left: 27rpx;
}

.payMes .payMes_bottom {
  background: #fff;
  padding: 0 30rpx 0 90rpx;
  box-sizing: border-box;
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 26rpx;
}

.payMes .payMes_bottom .balan {
  color: #666;
}

.payMes .payMes_bottom .balance {
  font-size: 36rpx;
  color: #ff5e03;
  display: flex;
  align-items: center;
}

.payMes .payMes_bottom .balance text {
  font-size: 24rpx;
}

.payMes .payMes_bottom .balance button {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 44rpx;
  color: #0d7aff;
  font-size: 24rpx;
  text-align: center;
  border-radius: 6rpx;
  border: 1rpx solid #0d7aff;
  margin-left: 27rpx;
}

.aletMsg {
  width: 100%;
  height: 100%;
  background: #000;
  z-index: 99;
  position: absolute;
  opacity: 0.5;
  left: 0;
  top: 0;
}

.count {
  width: 400rpx;
  height: 300rpx;
  background: #fff;
  position: absolute;
  left: calc(50% - 200rpx);
  border-radius: 8rpx;
  top: 30%;
  z-index: 100;
  box-sizing: border-box;
  padding-top: 20rpx;
}

.count .count_head {
  width: 100%;
  font-size: 32rpx;
  color: #000;
  text-align: center;
  line-height: 40rpx;
}

.count .count_msg {
  width: 100%;
  font-size: 26rpx;
  color: #000;
  text-align: center;
  line-height: 40rpx;
}

.count .count_net {
  width: 100%;
  font-size: 26rpx;
  color: #000;
  text-align: center;
  line-height: 40rpx;
}

.count .pop {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #999;
  background: #fff;
  margin-top: 10rpx;
}

.count .pop view {
  width: 110rpx;
  height: 110rpx;
  background: #1eb5ee;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.charging_note {
  font-size: 24rpx;
  color: #666;
  text-align: left;
  padding: 20rpx;
}

.refund_note {
  display: inline;
  color: #2196f3;
}

.calling {
  display: inline;
  color: #2196f3;
}

.call_img {
  display: inline-block;
  height: 20rpx;
  width: 24rpx;
  margin-left: 13rpx;
}

.start_charge {
  font-size: 36rpx;
  padding: 20rpx;
  background-color: #fff;
}