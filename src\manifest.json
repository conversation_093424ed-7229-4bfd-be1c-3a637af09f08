{
  "name": "",
  "appid": "__UNI__1C04B60",
  "description": "",
  "versionName": "1.1.1",
  "versionCode": "100",
  "transformPx": false,
  "networkTimeout": {
    "request": 20000
  },
  "app-plus": {
    /* 5+App特有相关 */
    "usingComponents": true,
    "splashscreen": {
      "alwaysShowBeforeRender": true,
      "waiting": true,
      "autoclose": true,
      "delay": 0
    },
    "modules": {},
    "plugins": {},
    /* 模块配置 */
    "distribute": {
      /* 应用发布信息 */
      "android": {
        /* android打包配置 */
        "permissions": [
          "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
          "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
          "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
          "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
          "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
          "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CAMERA\"/>",
          "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
          "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
          "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
          "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
          "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
          "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
          "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
          "<uses-feature android:name=\"android.hardware.camera\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
          "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
          "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>",
          "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>"
        ]
      },
      "ios": {},
      /* ios打包配置 */
      "sdkConfigs": {}
    },
    "screenOrientation": ["portrait-primary", "portrait-secondary", "landscape-primary", "landscape-secondary"]
  },
  /* SDK配置 */
  "quickapp": {},
  /* 快应用特有相关 */
  "mp-weixin": {
    /* 小程序特有相关 */
    "usingComponents": true,
    "appid": "wx602bfd6b5414b967",
    "setting": {
      "urlCheck": true,
      "es6": true,
      "postcss": true,
      "minified": true,
      "checkSiteMap": false,
      "minifyWXSS": true,
      "minifyWXML": true,
      "uglifyFileName": true,
      "compileHotReLoad": false,
      "babelSetting": {
        "ignore": [],
        "disablePlugins": [],
        "outputPath": ""
      },
      "packNpmManually": true,
      "packNpmRelationList": []
    },
    "permission": {
      "scope.userLocation": {
        "desc": "你的位置信息将用于小程序位置接口的效果展示"
      }
    },
    "requiredPrivateInfos": ["getLocation", "chooseLocation"],
    "requiredBackgroundModes": ["location"],
    "optimization": {
      "subPackages": true
    },
    "lazyCodeLoading": "requiredComponents"
  },
  "mp-alipay": {
    "usingComponents": true
  },
  "mp-baidu": {
    "usingComponents": true
  },
  "h5": {
    "router": {
      "mode": "history",
      "base": "/ndcharge"
    },
    "sdkConfigs": {
      "maps": {
        "qqmap": {
          "key": "EAOBZ-AF7HL-W6DPS-E5R43-MACVV-PVBAF"
        },
        "tencent": {
          "key": "EAOBZ-AF7HL-W6DPS-E5R43-MACVV-PVBAF"
        },
        "amap": {
          "key": "c0192a8abaa0426520b9acdab28f0b40",
          "appkey_android": "8e8886aba2e5cce91ccc5cc3372d338d",
          "appkey_ios": "9be20324a19019f43d25035279865374"
        }
      }
    },
    "devServer": {
      "disableHostCheck": true,
      "proxy": {
        "/ast": {
          "target": "https://ndjtcs.evstyle.cn:6443/ast",
          "pathRewrite": {
            "^/ast": "/"
          },
          "changeOrigin": true
        },
        "/cst": {
          "target": "https://ndjtcs.evstyle.cn:6443/cst",
          "pathRewrite": {
            "^/cst": "/"
          },
          "changeOrigin": true
        },
        "/def": {
          "target": "https://ndjtcs.evstyle.cn:6443/def",
          "pathRewrite": {
            "^/def": "/"
          },
          "changeOrigin": true
        },
        "/pub": {
          "target": "https://ndjtcs.evstyle.cn:6443/pub",
          "pathRewrite": {
            "^/pub": "/"
          },
          "changeOrigin": true
        },
        "/busi": {
          "target": "https://ndjtcs.evstyle.cn:6443/busi",
          "changeOrigin": true,
          "pathRewrite": {
            "^/busi": "/"
          }
        },
        "/base": {
          "target": "https://ndjtcs.evstyle.cn:6443/base",
          "changeOrigin": true,
          "pathRewrite": {
            "^/base": "/"
          }
        },
        "/gateway": {
          "target": "https://ndjtcs.evstyle.cn:6443/gateway",
          "changeOrigin": true,
          "pathRewrite": {
            "^/gateway": "/"
          }
        },
        "/bil": {
          "target": "https://ndjtcs.evstyle.cn:6443/bil",
          "changeOrigin": true,
          "pathRewrite": {
            "^/bil": "/"
          }
        },
        "/ord": {
          "target": "https://ndjtcs.evstyle.cn:6443/ord",
          "pathRewrite": {
            "^/ord": "/"
          },
          "changeOrigin": true
        }
      }
    }
  }
}
