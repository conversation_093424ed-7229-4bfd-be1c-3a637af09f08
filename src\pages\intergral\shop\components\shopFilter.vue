<!--
@name: 积分商城顶部筛选
@description: 子组件
@time: 2025/1/2
-->
<template>
  <view>
    <view class="top-filter">
      <view v-for="(item, index) in optionNames" :key="index" class="top-filter-item" @click="handleClick(item)">
        {{ item.name }}
        <img :src="`${IMG_PATH}dropDownIcon.png`" alt="" />
        <uni-data-select
          :ref="`selectRef${index}`"
          v-model="item.value"
          :localdata="item.Option"
          @change="selectIt($event, item)"
          format="{text}"
          style="width: 100px; position: absolute"
          placeholder="-"
        >
        </uni-data-select>
        <!-- <img :src="`${IMG_PATH}dropDownIcon.png`" alt="" /> -->
      </view>
    </view>
    <uni-popup ref="popup" background-color="#fff">
      <view class="popup-box">
        <view class="popup-body">
          <view
            v-for="(it, i) in selectedOption"
            :key="i"
            :value="it.value"
            class="popup-body-item"
            @click="selectIt(it.value, clickOption)"
          >
            {{ it.text }}
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup';
import uniDataSelect from './uni-data-select';

export default {
  name: 'shopFilter',
  components: {
    uniPopup,
    uniDataSelect,
  },
  props: {
    optionNames: {
      type: Array,
      default: () => [],
    },
    options: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      selectedOption: [],
      clickOption: [],
    };
  },
  methods: {
    // 点击选择
    handleClick(item) {
      // #ifdef H5
      console.log(item);
      this.clickOption = item;
      this.selectedOption = item.Option;
      this.$refs.popup.open('bottom');
      // #endif
    },
    // 下拉选择
    selectIt(event, item) {
      console.log(event, item);
      const findData = item.Option?.find((q) => {
        return q.value == event;
      });
      this.$refs.popup.close();
      this.$emit('selectChaneg', findData);
    },
  },
  created() {},
};
</script>

<style scoped lang="scss">
.top-filter {
  display: flex;
  gap: 41rpx;
  grid-gap: 41rpx;
  margin: 41rpx 0 -17rpx 0;
  .top-filter-item {
    display: flex;
    gap: 8rpx;
    grid-gap: 8rpx;
    align-items: center;
    ::v-deep .uni-select__input-box {
      display: none;
    }
    img {
      width: 13rpx;
      height: 10rpx;
    }
  }
}
.popup-box {
  padding: 30rpx;
  .popup-top {
    display: flex;
    justify-content: space-between;
    font-size: 46rpx;
    .popup-top-config {
      color: #1e9cff;
    }
  }
  .popup-body {
    height: 36vh;
    overflow-y: scroll;
    text-align: center;
    font-size: 36rpx;
    .popup-body-item {
      padding: 30rpx 0;
    }
  }
}
</style>
