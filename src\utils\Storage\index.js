import { Cookies } from '../Cookies'
const DB_PREFIX = process.env.DBPREFIX || 'xdt_';
class localStore {
    constructor() {
        this.prefix = DB_PREFIX;
    }
    setItem (key, value, fn) {

        try {
            if (typeof value != 'string') {
                value = JSON.stringify(value);
            }
            // #ifdef H5
            Cookies.put(this.prefix + key, value)
            // #endif
            // #ifndef H5
            uni.setStorageSync(this.prefix + key, value)
            // #endif
            fn && fn()
        } catch (e) {
        }



    }
    getItem (key, fn) {
        if (!key) {
            throw new Error('没有找到key。');
            return;
        }
        if (typeof key === 'object') {
            throw new Error('key不能是一个对象。');
            return;
        }

        try {

            // #ifdef H5
            const value = Cookies.get(this.prefix + key);
            // #endif
            // #ifndef H5
            const value = uni.getStorageSync(this.prefix + key);
            // #endif
            if (value !== null) {
                try {
                    value = JSON.parse(value);
                } catch (e) {

                }
            }
            return value;
        } catch (e) {
            // error
        }
    }
    removeItem (key) {
        try {

            // #ifdef H5
            Cookies.remove(this.prefix + key);
            // #endif
            // #ifndef H5
            uni.removeStorageSync(this.prefix + key);
            // #endif
        } catch (e) {
            // error
        }
    }
    clear () {
        try {

            // #ifdef H5
            Cookies.clear();
            // #endif
            // #ifndef H5
            uni.clearStorageSync();
            // #endif
        } catch (e) {
            // error
        }
    }
}
export default new localStore();