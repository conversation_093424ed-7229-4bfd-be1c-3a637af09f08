/**
 * 加密工具统一封装
 * 避免多处导入crypto-js造成包体积增大
 */
import CryptoJS from 'crypto-js';

// 导出完整的CryptoJS对象
export default CryptoJS;

// 常用加密方法封装
export const MD5 = (text) => {
  return CryptoJS.MD5(text).toString();
};

export const SHA1 = (text) => {
  return CryptoJS.SHA1(text).toString();
};

export const SHA256 = (text) => {
  return CryptoJS.SHA256(text).toString();
};

export const Base64 = {
  encode: (text) => {
    return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(text));
  },
  decode: (text) => {
    return CryptoJS.enc.Utf8.stringify(CryptoJS.enc.Base64.parse(text));
  }
};

export const AES = {
  encrypt: (text, key, iv = '') => {
    const keyHex = CryptoJS.enc.Utf8.parse(key);
    const ivHex = iv ? CryptoJS.enc.Utf8.parse(iv) : null;
    const encrypted = CryptoJS.AES.encrypt(text, keyHex, {
      iv: ivHex,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.toString();
  },
  decrypt: (ciphertext, key, iv = '') => {
    const keyHex = CryptoJS.enc.Utf8.parse(key);
    const ivHex = iv ? CryptoJS.enc.Utf8.parse(iv) : null;
    const decrypted = CryptoJS.AES.decrypt(ciphertext, keyHex, {
      iv: ivHex,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
  }
};
