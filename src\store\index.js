import Vue from 'vue';
import Vuex from 'vuex';
import VuexPersistence from 'vuex-persist';
import Storage from '../utils/Storage/index';

let modules = {};
const context = require.context('./modules/', true, /\.js$/);

//引入所有modules
context.keys().map((key) => {
  modules = { ...modules, ...context(key) };
});

Vue.use(Vuex);

const state = {};
const mutations = {};
const getters = {};
const actions = {
  clear({ commit, dispatch }) {
    try {
      dispatch('login/clear', {}, { root: true });
      dispatch('common/clear', {}, { root: true });
    } catch (error) {}
  },
};

const vuexLocal = new VuexPersistence({
  storage: Storage,
  reducer: (state) => {
    let newState = {
      common: {
        waitTime: state.common.waitTime,
        saveLocationInfo: state.common.saveLocationInfo,
        lastLocation: state.common.lastLocation,
      },
      login: {
        mobile: state.login.mobile,
        token: state.login.token,
        refreshToken: state.login.refreshToken,
        openId: state.login.openId,
        h5AppId: state.login.h5AppId,
        userInfo: state.login.userInfo,
      },
      order: {
        chargeWaitTimeInfo: state.order.chargeWaitTimeInfo,
        chargeErrorTimeInfo: state.order.chargeErrorTimeInfo,
        carInfo: state.order.carInfo,
      },
      invoice: {
        historyList: state.invoice.historyList,
      },
    };
    // console.log(4444, state.common, newState.common)

    return newState;
  },
  // modules: ['user'], //only save user module
  // filter: (mutation) => mutation.type == 'user/SET_TOKEN' || mutation.type == 'user/REMOVE_TOKEN' || mutation.type == 'user/SET_LOGIN_USER'
});
// const vuexSession = new VuexPersistence({
//     storage: Storage,
//     reducer: (state) => {
//         let newState = { ...state };
//         delete newState.user
//         return newState;
//     },
//    })

const store = new Vuex.Store({
  state,
  mutations,
  getters,
  actions,
  modules,
  plugins: [vuexLocal.plugin],
});
// console.log(4444, store)
export default store;
