<template>
  <!-- pages/addCar.wxml -->
  <view>
    <view class="alls">
      <view class="addCar-msg" v-for="(item, index) in carList" :key="index">
        <view class="message">
          <view class="message-left">
            <image :src="item.modelImg || `${IMG_PATH}default-car.png`" />
          </view>
          <view class="message-right">
            <view class="message-name">{{ item.brandName }} {{ item.modelName }}</view>
            <view class="message-text">
              <text>{{ item.licenseNo }}</text>
            </view>
            <view v-if="item.contMileage && item.contMileage > 0">续航里程：{{ item.contMileage }}km</view>
            <view v-if="item.carRange && item.carRange > 0">续航里程：{{ item.carRange }}km</view>
            <view v-if="item.batCapacity">电池容量：{{ item.batCapacity }}kWh</view>
          </view>
        </view>

        <view class="addCar-delet">
          <view @tap="chack" :data-indexs="item.indexOfs" :data-id="item.prefId">
            <radio class="fr" :checked="item.checkeds"></radio>
            默认车辆
          </view>
          <view @tap="remove" :data-id="item.prefId">
            <image :src="`${IMG_PATH}56e1.png`" />
            删除
          </view>
        </view>
      </view>
    </view>
    <view class="add-button">
      <view @tap="add" class="add-button-bt styleColorB">添加车辆</view>
    </view>
  </view>
</template>

<script>
import { upPrefDefault, getPref, deletePref } from '@/services/index.js';
// pages/addCar.js
export default {
  data() {
    return {
      x2: false,
      carList: [],
      list: [],
      a: false,
      b: false,
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */ onLoad: function (options) {
    this.init();
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.init();
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {},
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {},
  methods: {
    add: function () {
      uni.navigateTo({
        url: '/subPackages/add/add',
      });
    },

    chack(e) {
      const prefId = e.currentTarget.dataset.id;
      if (!prefId) {
        uni.showToast({
          icon: 'error',
          title: '车辆ID无效',
          duration: 2000,
        });
        return;
      }

      this.upPrefDefault({ prefId });
    },

    async upPrefDefault(params) {
      let that = this;
      // 保存当前选中状态的索引
      const currentCheckedIndex = that.carList.findIndex((car) => car.checkeds);

      // 先更新UI状态
      that.carList.forEach((car, index) => {
        car.checkeds = false;
      });
      const newCheckedIndex = that.carList.findIndex((car) => car.prefId === params.prefId);
      if (newCheckedIndex !== -1) {
        that.carList[newCheckedIndex].checkeds = true;
      }
      console.log(params, 'params2');
      // 调用接口
      const [err, res] = await upPrefDefault(params);

      if (err) {
        // 接口失败，恢复原来的选中状态
        that.carList.forEach((car, index) => {
          car.checkeds = index === currentCheckedIndex;
        });

        uni.showToast({
          icon: 'error',
          title: '设置默认车辆失败',
          duration: 2000,
        });
        return;
      }

      if (!res) {
        // 接口返回失败，恢复原来的选中状态
        that.carList.forEach((car, index) => {
          car.checkeds = index === currentCheckedIndex;
        });

        uni.showToast({
          icon: 'error',
          title: '设置默认车辆失败',
          duration: 2000,
        });
        return;
      }

      // 接口成功，不需要额外操作，因为UI已经更新
      uni.showToast({
        icon: 'success',
        title: '设置成功',
        duration: 2000,
      });
    },

    remove: function (e) {
      var paramd = {
        prefId: e.currentTarget.dataset.id,
      };
      let that = this;
      that.deletePref(paramd);
      // network.requestLoading(
      //   baseUrl + '/wx/v0.2/deletePref',
      //   paramd,
      //   '正在加载数据',
      //   'POST',
      //   function (res) {
      //     that.init();
      //   },
      //   function () {
      //     uni.showToast({
      //       icon: 'loading',
      //       title: '加载数据失败',
      //     });
      //   }
      // );
    },
    async deletePref(params) {
      let that = this;
      const [err, res] = await deletePref(params);
      if (err) {
        return;
      }
      if (res) {
        that.init();
      }
    },

    init: function () {
      var paramd = {};
      let that = this;
      that.getPref(paramd);
    },
    async getPref(params) {
      let that = this;
      const [err, res] = await getPref(params);
      if (err) {
        return;
      }
      if (res) {
        if (res.carList.length > 0) {
          var cars = res.carList;
          for (var i = 0; i < res.carList.length; i++) {
            cars[i].checkeds = false;
            cars[i].indexOfs = i;
          }
          setTimeout(function () {
            cars[0].checkeds = true;
            that.setData({
              carList: cars,
              list: res.carList,
            });
          }, 200);
        } else {
          that.setData({
            carList: [],
            list: [],
          });
        }
      }
    },
  },
};
</script>
<style>
@import './addCar.css';
</style>
