<template>
  <div class="renew-popup-mask" @click="handleMaskClick">
    <div class="renew-popup" @click.stop :style="{ backgroundImage: `url(${IMG_PATH}member/popup-bg.png)` }">
      <!-- 关闭按钮 -->
      <div class="close-btn" @click="handleClose">
        <image class="close-icon" :src="`${IMG_PATH}member/close-icon.png`" mode="widthFix" />
      </div>

      <!-- 弹窗内容 -->
      <div class="popup-content">
        <div class="popup-title">管理自动续费</div>
        <div class="popup-subtitle" v-if="!isAutoRenewing">
          现在开通 低至<span class="price-highlight">0.29</span>元/天
        </div>

        <!-- 会员卡片 -->
        <div class="renew-member-card">
          <div class="renew-member-badge" :style="{ backgroundImage: `url(${IMG_PATH}${memberBadgeImage})` }">
            <image
              :class="['renew-badge-icon', memberType !== 0 ? 'renew-badge-icon-plus' : '']"
              :src="`${IMG_PATH}/${memberBadgeIcon}`"
              mode="widthFix"
            />
            <!-- <span class="renew-badge-text">{{ memberBadgeText }}</span> -->
          </div>
          <div class="renew-member-btn" @click="handleConfirm">{{ renewStatusText }}</div>
        </div>

        <!-- 说明文字 -->
        <div class="popup-desc-container">
          <scroll-view class="popup-desc" scroll-y="true" :show-scrollbar="true">
            <div class="popup-desc-content">
              {{ vipAutoFeeDesc || '-' }}
            </div>
          </scroll-view>
          <!-- 底部虚化遮罩 -->
        </div>

        <!-- 底部按钮 -->
        <div class="popup-btn" @click="handleConfirm">{{ confirmButtonText }}</div>
      </div>
    </div>

    <!-- 底部关闭按钮 - 移到弹窗外面 -->
    <div class="bottom-close-btn" @click="handleClose">
      <div class="close-icon-wrapper">
        <div class="close-line close-line-1"></div>
        <div class="close-line close-line-2"></div>
      </div>
    </div>

    <!-- 自定义协议确认弹窗 -->
    <AgreementModal :visible="showAgreementModal" @confirm="handleAgreementConfirm" @cancel="handleAgreementCancel" />
  </div>
</template>

<script>
import AgreementModal from './AgreementModal.vue';

export default {
  name: 'RenewPopup',
  components: {
    AgreementModal,
  },
  props: {
    // 会员类型：0-普通会员，1-PLUS会员
    memberType: {
      type: Number,
      default: 1,
    },
    // 是否已开启自动续费
    isAutoRenewing: {
      type: Boolean,
      default: false,
    },
    vipAutoFeeDesc: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      showAgreementModal: false,
    };
  },
  computed: {
    // 会员徽章背景图片
    memberBadgeImage() {
      return this.memberType === 0 ? 'member/normal-member-badge.png' : 'member/plus-member-badge.png';
    },
    // 会员徽章图标
    memberBadgeIcon() {
      return this.memberType === 0 ? 'normal-member.png' : 'plus-member.png';
    },
    // 会员徽章文字
    memberBadgeText() {
      return this.memberType === 0 ? 'PLUS会员' : '尊享版PLUS会员';
    },
    // 连续包月状态文字
    renewStatusText() {
      return this.isAutoRenewing ? '已开启连续包月' : '未开启连续包月';
    },
    // 底部按钮文字
    confirmButtonText() {
      return this.isAutoRenewing ? '关闭' : '点击开通';
    },
  },
  methods: {
    // 关闭弹窗
    handleClose() {
      this.$emit('close');
    },
    // 点击遮罩关闭
    handleMaskClick() {
      this.handleClose();
    },
    // 确认开通/关闭
    handleConfirm() {
      if (this.isAutoRenewing) {
        // 已开启自动续费，显示取消确认弹窗
        uni.showModal({
          title: '取消自动续费后，可能无法获得自动续费优惠',
          content: '',
          cancelText: '狠心取消',
          confirmText: '我再想想',
          success: (res) => {
            if (res.confirm) {
              // 用户点击了"我再想想"，不做任何操作
              return;
            } else if (res.cancel) {
              // 用户点击了"狠心取消"，确认关闭自动续费
              this.$emit('confirm', { action: 'cancel' });
            }
          },
        });
      } else {
        // 未开启自动续费，显示自定义协议确认弹窗
        this.showAgreementModal = true;
      }
    },
    // 处理协议确认弹窗的确认事件
    handleAgreementConfirm() {
      this.showAgreementModal = false;
      this.$emit('confirm', { action: 'open', agreed: true });
    },
    // 处理协议确认弹窗的取消事件
    handleAgreementCancel() {
      this.showAgreementModal = false;
      this.$emit('confirm', { action: 'open', agreed: false });
    },
  },
};
</script>

<style scoped lang="scss">
.renew-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.renew-popup {
  position: relative;
  width: 670rpx;
  height: 770rpx;
  background: #fed1a7;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 32rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  cursor: pointer;
}

.close-icon {
  width: 40rpx;
  height: 40rpx;
}

.popup-content {
  flex: 1;
  padding: 60rpx 40rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.popup-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  align-self: flex-start;
  line-height: 1.2;
}

.popup-subtitle {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 50rpx;
  align-self: flex-start;
  line-height: 1.3;
}

.price-highlight {
  color: #ff8a00;
  font-weight: bold;
  font-size: 36rpx;
}

.renew-member-card {
  position: relative;
  width: 100%;
  border-radius: 20rpx;
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.renew-member-badge {
  display: flex;
  align-items: center;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 20rpx 10rpx;
  border-radius: 15rpx;
  min-height: 80rpx;
}

.renew-badge-icon {
  width: 240rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

/* PLUS会员图标样式 */
.renew-badge-icon-plus {
  width: 299rpx;
  height: 50rpx;
}

.renew-member-btn {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 180rpx;
  height: 50rpx;
  border-radius: 25rpx;
  background: linear-gradient(147deg, #e7a888 26%, #ffe9dd 98%), #d5d9e2;
  color: #333;
  font-size: 20rpx;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
}

.popup-desc-container {
  position: relative;
  width: 100%;
  height: 190rpx;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
}

.popup-desc {
  height: 100%;
  width: 100%;
}

/* 自定义滚动条样式 */
.popup-desc ::-webkit-scrollbar {
  width: 6rpx;
}

.popup-desc ::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3rpx;
}

.popup-desc ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3rpx;
}

.popup-desc ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.popup-desc-content {
  font-size: 22rpx;
  color: #9e9e9e;
  font-family: PingFang SC;
  line-height: 1.6;
  text-align: justify;
  padding: 0 10rpx;
}

.popup-btn {
  position: absolute;
  left: 75rpx;
  bottom: 40rpx;
  width: 520rpx;
  height: 90rpx;
  border-radius: 60rpx;
  background: linear-gradient(180deg, #f3b359 0%, #ee6835 100%);
  color: #fff;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 底部关闭按钮 */
.bottom-close-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, calc(-50% + 450rpx));
  width: 70rpx;
  height: 70rpx;
  background: transparent;
  border: 4rpx solid #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10001;
}

.bottom-close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translate(-50%, calc(-50% + 450rpx)) scale(1.05);
}

.close-icon-wrapper {
  position: relative;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.close-line {
  position: absolute;
  width: 32rpx;
  height: 4rpx;
  background: #fff;
  border-radius: 2rpx;
  transition: all 0.3s ease;
}

.close-line-1 {
  transform: rotate(45deg);
}

.close-line-2 {
  transform: rotate(-45deg);
}

.bottom-close-btn:hover .close-line {
  background: #fff;
}
</style>
