.billinghistory {
  padding-bottom: 60rpx;
}
.billinghistory-lists {
  background-color: #fff;
  margin-top: 20rpx;
  padding: 30rpx 30rpx 20rpx;
}
.billinghistory-lists .list-main {
  position: relative;
  height: 88rpx;
  line-height: 88rpx;
  overflow: hidden;
  font-size: 28rpx;
  color: #333;
}

.billinghistory-lists .list-main:first-child:after {
  height: 0;
}
.list-main .list-content {
  color: #2a2a2a;
}
.list-main .list-right {
  color: #666;
}
.myorder-image {
  text-align: center;
  color: #b2b2b2;
  font-size: 32rpx;
  margin-top: 50%;
}
.myorder-image image {
  display: block;
  width: 70rpx;
  height: 86rpx;
  margin: 0 auto 20rpx auto;
}

.li-top {
  display: flex;
  align-item: center;
  justify-content: space-between;
}
.top-title {
  color: #333;
  font-family: 'PingFang SC';
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.top-time {
  margin-top: 8rpx;
  color: #666;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.top-amount {
  color: #333;
  font-family: 'PingFang SC';
  font-size: 48rpx;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.li-mid {
  display: flex;
  align-item: center;
  margin-top: 20rpx;
  color: #999;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #e7e9ec;
}
.li-bottom {
  margin-top: 20rpx;
  display: flex;
  align-item: center;
  justify-content: space-between;
}
.mid-mode {
  margin-right: 24rpx;
}
.bottom-desc {
  color: #333;
  font-family: 'PingFang SC';
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 54rpx;
}
.li-btns {
  display: flex;
  align-item: center;
  justify-center: flex-end;
}
.li-button {
  padding: 8rpx 20rpx;
  margin-left: 12rpx;
  border-radius: 6rpx;
  border: 1rpx solid #dbdddf;
  background: #fff;
  color: #333;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-align: center;
}
.li-remark {
  border-radius: 6rpx;
  border: 2rpx dashed #2196f3;
  border-spacing: 2rpx;
  background: #f0f8fe;
  color: #2196f3;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding: 8rpx 20rpx;
  margin-top: 12rpx;
}
.remark-orange {
  border-color: #ff7a00;
  background: #fff4eb;
  color: #ff7a00;
}
.success {
  color: #8bb70c;
}
.fail {
  color: #ff3333;
}
.examine {
  color: #ff7a00;
}
