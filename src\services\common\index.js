import { request as __request } from '../request/request';
import {
  ENVR,
  scanBaseurl,
  pubBaseurl,
  astBaseurl,
  cstBaseurl,
  defBaseurl,
  ordBaseurl,
  gatewayBaseurl,
  baseBaseurl,
  baseOpenBaseurl,
} from '../../config/global';

// 查询购买记录
export const register = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/register`,
      data: params,
    }
  );
};
// 获取验证码
export const getVerifyCode = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/send-msg`,
      data: params,
    }
  );
};
// 登录
export const loginInterface = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl + '/v0.3' : cstBaseurl + '/v0.4'}/login`,
      data: params,
    }
  );
};
// 注销
export const logOff = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : cstBaseurl}/v0.1/account-cancellation`,
      data: params,
    }
  );
};
// 获取token
export const refreshToken = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${scanBaseurl}/v0.1/swapSInfo`,
      data: params,
    }
  );
};
// 获取用户信息
export const getUserInfo = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/accounts`,
      data: params,
    }
  );
};
// 退出登录
export const logout = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl : cstBaseurl}/v0.1/signOut`,
      data: params,
    }
  );
};
// 根据code获取配置信息
export const getUserAgreement = async ({ code }, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${pubBaseurl}/v0.1/infos/${code}`,
      data: {},
    }
  );
};
// 自动登录
export const autoLogin = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${scanBaseurl}/v0.1/custinfo`,
      data: params,
    }
  );
};

// 微信缴费-h5页面
export const wxH5Pay = async (params, config = {}) => {
  return __request(
    { ...config, 'content-type': 'application/x-www-form-urlencoded' },
    {
      method: 'POST',
      url: `${ENVR == 'wx' ? scanBaseurl : baseBaseurl}/v0.1/account/balance`,
      data: params,
    }
  );
};
// 获取充电信息
export const getChargingInfo = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: ENVR == 'wx' ? 'POST' : 'GET',
      url: `${ENVR == 'wx' ? scanBaseurl : ordBaseurl}/v0.1/order-charge-setbacks`,
      data: params,
    }
  );
};
// 开始 01/结束 02 充电
export const changeChargingStatus = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR == 'wx' ? scanBaseurl : ordBaseurl}/v0.1/order-charge-controls`,
      data: params,
    }
  );
};

// 取消订单
export const cancelOrder = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      // url: `${scanBaseurl}/v0.1/cancel-order`,
      url: `${ENVR == 'wx' ? scanBaseurl + '/v0.1/cancel-order' : gatewayBaseurl + '/v0.1/orders'}`,
      data: params,
    }
  );
};
// 选择车牌
export const selectLicenseNo = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ENVR == 'wx' ? scanBaseurl : ordBaseurl}/v0.1/charging-change-info`,
      data: params,
    }
  );
};
// 城市查询
export const getCity = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${pubBaseurl}/v0.1/citys`,
      data: params,
    }
  );
};
// 修改用户信息
export const updateUserInfo = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${ENVR === 'wx' ? scanBaseurl + '/v0.1/custinfo-edit' : cstBaseurl + '/v0.1/custinfo'}`,
      data: params,
    }
  );
};
// 邀请人获取活动规则操作说明
export const queryActInProgress = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${pubBaseurl}/open/v0.2/queryActInProgress`,
      data: params,
    }
  );
};

// 余额查询
export const getBalanceCan = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ENVR === 'wx' ? scanBaseurl : defBaseurl}/v0.1/balance-can-return-record`,
      data: params,
    }
  );
};
// 优惠活动
export const getAccountsActs = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${defBaseurl}/open/v0.1/accounts/acts`,
      data: params,
    }
  );
};

// 扫码跳转后查询充电数据面板-根据gunId
export const getChargingInfoByGunId = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: ENVR === 'wx' ? 'POST' : 'GET',
      url: `${ENVR === 'wx' ? scanBaseurl : astBaseurl}/v0.1/guns/qrcode`,
      data: params,
    }
  );
};
// 获取订单信息
export const geGunInfo = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${
        ENVR === 'wx' ? scanBaseurl + '/v0.1/gun-info/' + (params.gunId || undefined) : baseBaseurl + '/v0.1/gun-info'
      }`,
      data: params,
    }
  );
};
// i宁德支付data加密接口
export const getIndData = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${defBaseurl}/v0.1/payEncryption`,
      data: params,
    }
  );
};
// i宁德身份认证获取签名接口
export const getSign = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${astBaseurl}/open/v0.1/sign`,
      data: params,
    }
  );
};
// 根据经纬度获取位置信息
export const getGeocoderLocation = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${baseOpenBaseurl}/v0.1/geocoder_v2`,
      data: params,
    }
  );
};
// i宁德身份认证获取token接口
export const getINDToken = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${astBaseurl}/open/v0.1/token`,
      data: params,
    }
  );
};
// i宁德身份认证获取token接口
export const getServiceprovision = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${ENVR == 'wx' ? scanBaseurl : baseBaseurl}/v0.2/infos`,
      data: params,
    }
  );
};
