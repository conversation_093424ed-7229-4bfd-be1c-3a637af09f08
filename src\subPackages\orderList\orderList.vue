<template>
  <view>
    <view>
      <view class="orderList" v-for="(item, index) in list" :key="index">
        <view class="tops">
          <view class="orderList_img">
            <image v-if="item.orderStatus == '03'" :src="`${IMG_PATH}<EMAIL>`"></image>
            <image v-else :src="`${IMG_PATH}<EMAIL>`"></image>
          </view>
          <view class="orderList_middle">
            <view class="address">{{ item.stationName }}</view>
            <view class="timed">开始时间: {{ item.applyTime }}</view>
          </view>
          <view v-if="item.orderStatus == '03'" class="orderList_type orage">
            {{ item.orderStatusName }}
          </view>
          <view v-else class="orderList_type">
            {{ item.orderStatusName }}
          </view>
        </view>

        <view class="down">
          <view>订单号: {{ item.orderNo }}</view>
          <button v-if="item.orderStatus == '03'" :data-msg="item.orderNo" @tap="Jump" class="orge">查看更多</button>
          <button v-else :data-msg="item.orderNo" @tap="Jump" class="blue">查看更多</button>
        </view>
      </view>

      <!-- <view class='orderList'>
  <view class='tops'>
    <view class='orderList_img'>
      <image src='../../image/<EMAIL>'></image>
    </view>
    <view class='orderList_middle'>
      <view class='address'>厦门软件园充电站厦门软件园充电站-1号桩厦门软件园充电站-1号桩-1号桩</view>
      <view class='timed'>开始时间: 2018-10-09 18:00</view>
    </view>
    <view class='orderList_type orage'>
      待启动
    </view>
    </view>
    <view class='down'>
    <view>订单号: 7362736273726372</view>
    <button class='.orge'>查看更多</button>
  </view>
  </view>  -->
    </view>
    <loading v-if="!hiddenLoading">正在加载</loading>
  </view>
</template>

<script>
import { getOrderList } from '@/services/index.js';
export default {
  data() {
    return {
      list: [],
      hiddenLoading: true,
    };
  },
  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    var that = this;
    that.setData({
      hiddenLoading: false,
    });
    const [, res] = await getOrderList({
      orderStatus: '01,02,03,04,05,08',
      pageNum: 50,
    });
    if (res) {
      that.setData({
        list: res.orderChargeList,
        hiddenLoading: true,
      });
    }
  },
  methods: {
    Jump(e) {
      console.log(e.target.dataset.msg);
      uni.navigateTo({
        url: '/subPackages/ordercharge/ordercharge?orderNo=' + e.target.dataset.msg,
      });
    },
  },
};
</script>
<style>
@import './orderList.css';
</style>
