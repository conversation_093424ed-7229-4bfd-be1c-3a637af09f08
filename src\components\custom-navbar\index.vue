<template>
  <section>
    <div class="foot-navbar">
      <div class="navbar-ul">
        <template v-for="(item, index) in navbarList">
          <div class="navbar-li" v-if="!item.isScan" :key="index" @click="toCallback(item)">
            <img class="navbar-li-icon" :src="item.navbar === currentNavbar ? item.checkedIcon : item.icon" />
            <div class="navbar-li-text">{{ item.text }}</div>
          </div>
          <div class="navbar-li-scan" :key="index" v-if="item.isScan" @click="toCallback(item)">
            <img class="navbar-scan" :src="item.icon" />
            <div class="navbar-scan-text">{{ item.text }}</div>
          </div>
        </template>
      </div>
      <receive v-if="show"></receive>
    </div>
    <scanCode v-model="showScan" @success="getScan" @err="err"></scanCode>
    <!-- <div style="height: calc(190rpx + env(safe-area-inset-bottom))"></div> -->
  </section>
</template>

<script>
import receive from './receive.vue';
import { mapState } from 'vuex';
import { subscribeMessage } from '@/utils/messageCode.js';
import { ENVR } from '@/config/global';
import scanCode from '../scanCode/index.vue';
export default {
  props: {
    currentNavbar: {
      type: String,
      default: 'indexPage',
    },
    show: {
      type: Boolean,
      default: () => false,
    },
  },
  components: { receive, scanCode },
  data() {
    return {
      showScan: false,
    };
  },
  computed: {
    ...mapState({
      userInfo: (state, getters) => getters['login/getUserInfo'],
    }),
    navbarList() {
      return [
        {
          icon: this.IMG_PATH_UI + 'navbar-index.png',
          text: '首页',
          checkedIcon: this.IMG_PATH_UI + 'navbar-index-checked.png',
          isScan: false,
          navbar: 'indexPage',
          methods: this.goINdex,
        },
        {
          icon: this.IMG_PATH_UI + 'navbar-station.png',
          text: '电站',
          checkedIcon: this.IMG_PATH_UI + 'navbar-station-checked.png',
          isScan: false,
          navbar: 'stationPage',
          methods: this.goStation,
        },
        {
          icon: this.IMG_PATH_UI + 'navbar-scan.png',
          text: '扫一扫',
          checkedIcon: 'navbar-scan.png',
          isScan: true,
          navbar: 'scanPage',
          methods: this.goScan,
        },
        // {
        //   icon: this.IMG_PATH + 'navbar-order.png',
        //   text: '我的订单',
        //   checkedIcon: this.IMG_PATH + 'navbar-order-checked.png',
        //   isScan: false,
        //   navbar: 'orderPage',
        //   methods: this.goOrder,
        // },
        {
          icon: this.IMG_PATH_UI + 'navbar-find.png',
          text: '发现',
          checkedIcon: this.IMG_PATH_UI + 'navbar-find-checked.png',
          isScan: false,
          navbar: 'discover',
          // methods: this.goDiscover,
        },
        {
          icon: this.IMG_PATH_UI + 'navbar-mine.png',
          text: '我的',
          checkedIcon: this.IMG_PATH_UI + 'navbar-mine-checked.png',
          isScan: false,
          navbar: 'minePage',
          methods: this.goMine,
        },
      ];
    },
  },
  created() {
    // const { isLoad } = app.globalData.mobileInfo;
    // if (!isLoad) {
    //   this.getMobileInfo();
    // }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    getScan(val) {
      console.log('成功', val);
      const message = val;
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '/pages/login/login',
        });
      } else {
        uni.navigateTo({
          url: '/subPackages/placeorder/placeorder?msg=' + message + '&titles=10',
        });
      }
    },
    err(err) {
      console.log(err, '失败');
      this.showScan = false;
    },
    // getMobileInfo() {
    //   const promise = new Promise((resolve, reject) => {
    //     uni.request({
    //       url: baseUrl + '/wx/v0.1/custinfo',
    //       data: {},
    //       method: 'GET',
    //       header: {
    //         'content-type': 'application/json',
    //         minitProgramToken: uni.getStorageSync('token') || '',
    //       },
    //       success: (res) => {
    //         const { ret } = res.data;
    //         if (ret === 200) {
    //           const { mobile } = res.data.data;
    //           app.globalData.mobileInfo = {
    //             isLoad: true,
    //             mobile: mobile || '未绑定',
    //           };
    //         }
    //       },
    //       complete: function () {
    //         resolve(app.globalData.mobileInfo);
    //       },
    //     });
    //   });
    //   return promise;
    // },
    goScan(item) {
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '/pages/login/login',
        });
      } else {
        ENVR !== 'wx' && this.scanNow();
        subscribeMessage(['充电开始通知', '余额不足提醒', '充电结算通知'], this.scanNow);
      }
    },
    scanNow() {
      // #ifdef H5
      this.showScan = true;
      // #endif

      // #ifdef MP-WEIXIN
      uni.scanCode({
        onlyFromCamera: true,
        success: (res) => {
          function GetUrlParam(paraName) {
            const url = res.result;
            const arrObj = url.split('?');
            if (arrObj.length > 1) {
              const arrPara = arrObj[1].split('&');
              var arr;
              for (var i = 0; i < arrPara.length; i++) {
                arr = arrPara[i].split('=');
                if (arr != null && arr[0] == paraName) {
                  return arr[1];
                }
              }
              return '';
            } else {
              return '';
            }
          }
          console.log(res, 'qrcode');
          const message = GetUrlParam('qrcode') || res.result;
          if (!this.userInfo?.mobile) {
            uni.navigateTo({
              url: '/pages/login/login',
            });
          } else {
            uni.navigateTo({
              url: '/subPackages/placeorder/placeorder?msg=' + message + '&titles=10',
            });
          }
        },
      });
      // #endif
    },
    toCallback(item) {
      switch (item.navbar) {
        case 'indexPage':
          if (this.currentNavbar !== 'indexPage') {
            uni.redirectTo({
              url: '/pages/home/<USER>',
            });
          }
          break;
        case 'discover':
          if (this.currentNavbar !== 'discover') {
            uni.redirectTo({
              url: '/pages/basic/discover/index',
            });
          }
          break;
        case 'stationPage':
          if (this.currentNavbar !== 'stationPage') {
            uni.redirectTo({
              url: '/pages/basic/mapStation/map',
            });
          }
          break;
        case 'scanPage':
          this.goScan(item);
          break;
        case 'orderPage':
          if (this.currentNavbar !== 'orderPage') {
            uni.redirectTo({
              url: '/pages/basic/myorder/myorder',
            });
          }
          break;
        case 'minePage':
          if (this.currentNavbar !== 'minePage') {
            uni.redirectTo({
              url: '/pages/personal/personal',
            });
          }
          break;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.foot-navbar {
  position: fixed;
  bottom: 0;
  left: 0;
  padding-bottom: calc(4rpx + env(safe-area-inset-bottom));
  background: #ffffff;
  width: 100vw;
  box-sizing: border-box;
  z-index: 10;
}
.navbar-bg {
  height: 190rpx;
  width: 100vw;
  position: absolute;
  top: -70rpx;
  left: 0;
}
.navbar-ul {
  display: flex;
  position: relative;
  z-index: 1;
  margin-top: 16rpx;
  padding: 0 24rpx;
  box-sing: border-box;
}
.navbar-li {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  height: 90rpx;
}
.navbar-li-scan {
  width: 160rpx;
  height: 90rpx;
}
.navbar-scan {
  width: 160rpx;
  height: 160rpx;
  margin-top: -84rpx;
}
.navbar-margin-left {
  margin-left: 20rpx;
}
.navbar-margin-right {
  margin-right: 20rpx;
}
.navbar-li-icon {
  width: 44rpx;
  height: 44rpx;
}
.navbar-li-text {
  margin-top: 16rpx;
  font-family: PingFang SC;
  font-size: 22rpx;
  font-weight: normal;
  line-height: 26rpx;
  text-align: center;
  color: #333333;
  width: 100%;
}
.navbar-scan-text {
  margin-top: -24rpx;
  font-family: PingFang SC;
  font-size: 22rpx;
  font-weight: normal;
  line-height: 26rpx;
  text-align: center;
  color: #333333;
  width: 100%;
}
</style>
