.add {
  min-height: 840rpx;
  overflow-y: auto;
}

.add-head {
  width: 100%;
  height: 120rpx;
  box-sizing: border-box;
  padding: 0 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  background: #fff;
}

.add-head .add-head-bull {
  width: 7rpx;
  height: 28rpx;
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 3rpx;
  margin-right: 12rpx;
}

.add-head .add-head-cars {
  display: flex;
  align-items: center;
}

.add-head .add-head-cars .icon-arrow_right {
  vertical-align: middle;
  display: flex;
  align-items: center;
  margin-top: 3rpx;
  margin-left: 17rpx;
}

.add-middle {
  display: flex;
  justify-content: space-around;
  box-sizing: border-box;
  padding: 0 30rpx;
  height: 102rpx;
  width: 100%;
  /* margin-bottom:69rpx; */
  background: #fff;
  padding-bottom: 20rpx;
}

.add-middle .viewInput {
  display: inline-block;
  width: 87rpx;
  height: 100%;
  background: #fff;
  color: #333;
  border: 1px solid #d4d4d4;
  border-radius: 8rpx;
  text-align: center;
  line-height: 87rpx;
}

.add-middle .dashed {
  border-style: dashed;
  border-color: #89e007;
}

.add-middle .actives {
  border: 1rpx solid #2196f3;
}

.add-middle .activeNo {
  border: none;
}

.add-button {
  width: 100%;
  height: 80rpx;
  box-sizing: border-box;
  padding: 0 30rpx;
}

.add-button .add-button-bt {
  width: 690rpx;
  height: 100%;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 28rpx;
}

.keyboard {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 452rpx;
  background-color: #cacfd7;
  text-align: center;
}

.keyboard .code {
  position: absolute;
  left: 6rpx;
  bottom: 11rpx;
  display: block;
  width: 10.8%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 5rpx;
  font-size: 30rpx;
  color: #000;
  text-align: center;
  background-color: rgba(165, 171, 181, 0.8);
  border-bottom: 2rpx solid rgba(0, 0, 0, 0.5);
}

.keyboard .delete-btn {
  position: absolute;
  right: 12.5%;
  bottom: 11rpx;
  display: block;
  width: 10.8%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 5rpx;
  font-size: 30rpx;
  font-style: normal;
  color: #000;
  text-align: center;
  background-color: rgba(165, 171, 181, 0.8);
  border-bottom: 2px solid rgba(0, 0, 0, 0.5);
}

.keyboard .delete {
  position: absolute;
  right: 6rpx;
  bottom: 11rpx;
  display: block;
  width: 10.8%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 5rpx;
  font-size: 30rpx;
  font-style: normal;
  color: #000;
  text-align: center;
  background-color: rgba(165, 171, 181, 0.8);
  background-repeat: no-repeat;
  background-size: 54.8% 40.5%;
  background-position: center center;
  border-bottom: 2px solid rgba(0, 0, 0, 0.5);
}

.keyboard .li {
  display: inline-block;
  width: 8.4%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 5rpx;
  font-size: 30rpx;
  margin: 10rpx 6rpx;
  color: #000;
  text-align: center;
  background-color: #fff;
  border-bottom: 2px solid rgba(0, 0, 0, 0.5);
}

.addImgs {
  height: 312rpx;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
}

.addImgs image {
  width: 500rpx;
  height: 171rpx;
}

.add-bt {
  margin-bottom: 16rpx;
  border-top: 2rpx solid #f4f4f4;
  font-size: 30rpx;
}

.add-bt-no-margin {
  border-top: 2rpx solid #f4f4f4;
  font-size: 30rpx;
}

.add-height {
  height: 88rpx;
}