<template>
  <view class="billingpersonal">
    <view class="billingpersonal-lists">
      <view class="list-main">
        <text class="list-main-hd">开票金额</text>
        <text class="list-main-bd red-color">¥{{ price }}</text>
      </view>
      <view class="list-main">
        <text class="list-main-hd">开票主体</text>
        <radio-group class="list-main-bd" @change="radioChange">
          <view class="radio" v-for="(item, index) in radioItems" :key="index">
            <radio :id="item.name" :value="item.name" :checked="item.checked"></radio>

            <text>{{ item.value }}</text>
          </view>
        </radio-group>
      </view>
      <view class="list-main">
        <text class="list-main-hd">发票类型</text>
        <radio-group class="list-main-bd" @change="radioTypeChange">
          <view class="radio" v-for="(item, index) in radioTypeItems" :key="index">
            <radio :id="item.name" :value="item.name" :checked="item.checked"></radio>

            <text>{{ item.value }}</text>
          </view>
        </radio-group>
      </view>
      <view class="list-main">
        <text class="list-main-hd">发票内容</text>
        <text class="list-main-bd gray-color">{{ invoiceTypeContent }}</text>

        <!-- <text class="list-main-bd gray-color">服务费</text> -->
      </view>
      <view class="list-main">
        <text class="list-main-hd"><text>*</text> 发票抬头</text>
        <label class="list-main-bd">
          <input
            type="text"
            @input="taitouFun"
            :value="taitou"
            placeholder="请输入发票抬头"
            placeholder-style="color:#999999"
          />
        </label>
        <image @tap="listShow" class="list-img" :src="`${IMG_PATH}list.png`"></image>
      </view>
      <view v-if="personal" class="list-main">
        <text class="list-main-hd"><text>*</text> 税号</text>
        <label class="list-main-bd">
          <input
            type="text"
            @input="shuihaoFun"
            :value="shuihao"
            placeholder="请输入纳税公司税号"
            placeholder-style="color:#999999"
          />
        </label>
      </view>
      <view v-if="personal" class="list-main">
        <text class="list-main-hd"><text v-if="invoiceType === '04'">*</text> 开户银行</text>
        <label class="list-main-bd">
          <input
            type="text"
            @input="bankCodeInp"
            :value="bankCode"
            placeholder="请输入开户银行"
            placeholder-style="color:#999999"
          />
        </label>
      </view>
      <view v-if="personal" class="list-main">
        <text class="list-main-hd"><text v-if="invoiceType === '04'">*</text> 银行账号</text>
        <label class="list-main-bd">
          <input
            type="text"
            @input="bankAccountInp"
            :value="bankAccount"
            placeholder="请输入银行账号"
            placeholder-style="color:#999999"
          />
        </label>
      </view>
    </view>
    <view class="billingpersonal-lists">
      <view class="list-main">
        <text class="list-main-hd"><text>*</text> 收件人</text>
        <label class="list-main-bd">
          <input
            type="text"
            @input="nameFun"
            :disabled="reopenType === 'reopen'"
            :value="name"
            placeholder="填写收件人姓名"
            placeholder-style="color:#999999"
          />
        </label>
      </view>
      <view class="list-main">
        <text class="list-main-hd"><text>*</text> 手机号</text>
        <label class="list-main-bd">
          <input
            type="number"
            @input="numberFun"
            :disabled="reopenType === 'reopen'"
            :value="number"
            placeholder="填写收件人手机号"
            placeholder-style="color:#999999"
          />
        </label>
      </view>
      <view class="list-main">
        <text class="list-main-hd"><text>*</text> 电子邮箱</text>
        <label class="list-main-bd">
          <input
            type="text"
            @input="addressFun"
            :disabled="reopenType === 'reopen'"
            :value="address"
            placeholder="填写收件人电子邮箱"
            placeholder-style="color:#999999"
          />
        </label>
      </view>
      <template v-if="typed == '02'">
        <view class="list-main">
          <text class="list-main-hd">备注</text>
          <label class="list-main-bd">
            <input
              type="text"
              @input="remarkFun"
              :disabled="reopenType === 'reopen'"
              :value="invoiceRemark"
              placeholder="填写备注"
              placeholder-style="color:#999999"
            />
          </label>
        </view>
      </template>
      <view class="bottom-bg">
        <button v-if="panduan" @tap="open" class="blue-bg" type="">提交</button>
        <button v-else class="blue-bg color-tj" type="">提交</button>
      </view>
    </view>
    <loading v-if="!hiddenLoading">请稍后...</loading>
    <modal-popup v-if="!listHidden" @closeModal="listConfirm" title="常用发票抬头">
      <view slot="footer">
        <view class="invoice-list">
          <radio-group @change="invoiceRadioChange">
            <template v-for="(item, index) in invoiceTitleList">
              <template v-if="typed === item.invoiceTitleType">
                <view class="invoice-li" :key="index">
                  <view class="li-content">
                    <view class="li-overflow">{{ item.invoiceTitle }}</view>
                    <text class="li-right" v-if="item.defaultFlag === '1'">{{
                      item.defaultFlag === '1' ? '默认' : ''
                    }}</text>
                  </view>

                  <radio :id="item.invoiceTitleId" :value="item.invoiceTitleId" :checked="item.checked"></radio>
                </view>
              </template>
            </template>
          </radio-group>
        </view>
        <view>
          <button class="blue-bg add-commonHeadings" @tap.native="addCommonHeadings">添加常用发票抬头</button>
        </view>
      </view>
    </modal-popup>
  </view>
</template>

<script>
import modalPopup from '../../components/modal-popup/index';
import { subscribeMessage } from '@/utils/messageCode.js';
import { ENVR } from '@/config/global';

import { setInvoiceAgain, setAccountInvoice, setAccountInvoiceRetry, getInvoiceTitleList } from '@/services/index.js';
export default {
  components: {
    modalPopup,
  },
  data() {
    return {
      hiddenLoading: true,
      price: '0',
      panduan: false,
      typed: '01',
      number: '',
      name: '',
      meh: [],
      time: [],
      taitou: '',
      shuihao: '',
      address: '',
      arr: [],
      zhuangt: true,
      token: '',
      invoiceRemark: '',

      radioItems: [
        {
          name: 'personal',
          value: '个人',
          checked: 'true',
        },
        {
          name: 'enterprise',
          value: '企业',
        },
      ],

      radioTypeItems: [
        {
          name: '03',
          value: '增值税普通发票',
          checked: 'true',
        },
      ],

      personal: false,
      listHidden: true,
      invoiceTitleList: [],
      bankCode: '',
      bankAccount: '',
      invoiceType: '03',
      fullPrice: '',
      reopenType: '',
      invoiceAppNo: '',
      isRefresh: false,
      invoiceTypeContent: '',
      invoiceTypeContentValue: '',
      // 订单类型 02 充电列表 10 会员列表  09 商品列表
      invoiceTypeContentItems: {
        '02': {
          label: '服务费',
          value: '02',
        },
        10: {
          label: '会员',
          value: '10',
        },
        '09': {
          label: '商品',
          value: '09',
        },
      },
      goodsType: '',
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log(options);
    const { msg = '', price, reopenType, type = '02', goodsType = '' } = options;
    const arr = msg.split(',');
    this.setData({
      token: uni.getStorageSync('token') || '',
      time: uni.getStorageSync('time') || [],
      fullPrice: uni.getStorageSync('fullPrice') || '',
      meh: uni.getStorageSync('meh') || [],
      arr,
      price,
      reopenType,
      invoiceTypeContent: this.invoiceTypeContentItems[type].label,
      invoiceTypeContentValue: type,
      goodsType,
    });

    if (reopenType === 'reopen') {
      uni.setNavigationBarTitle({
        title: '重开发票',
      });
    }
    if (reopenType) {
      const reopenObj = uni.getStorageSync('reopenObj') || {};
      const {
        invoiceTitle,
        invoiceTitleType,
        invoiceType,
        taxNum,
        recipients,
        phoneNo,
        email,
        invoiceAppNo,
        bankAccount,
        bankCode,
      } = reopenObj;
      this.setData({
        taitou: invoiceTitle,
        typed: invoiceTitleType,
        invoiceType,
        shuihao: taxNum,
        name: recipients,
        number: phoneNo,
        address: email,
        invoiceAppNo,
        invoiceRemark: '',
        bankAccount,
        bankCode,
      });
      this.radioChange({
        detail: {
          value: invoiceTitleType === '01' ? 'personal' : 'enterprise',
        },
      });
    }
    this.panDuan();
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.getInvoiceList();
  },
  methods: {
    getInvoiceList: function () {
      const that = this;
      // const { token } = this;
      that.getInvoiceTitleList();
      // uni.request({
      //   url: baseUrl + '/wx/v0.2/invoice-title-list',
      //   data: {},
      //   method: 'POST',
      //   header: {
      //     'content-type': 'application/json',
      //     minitProgramToken: token,
      //   },
      //   success: (res) => {
      //     that.setData({
      //       invoiceTitleList: res.data.invoiceTitleList.map((item) => {
      //         return {
      //           ...item,
      //           checked: false,
      //         };
      //       }),
      //     });
      //     console.log(that.invoiceTitleList, 'invoiceTitleList');
      //   },
      //   fail: (err) => {
      //     console.log(err, '请求出问题。');
      //   },
      // });
    },

    async getInvoiceTitleList() {
      const that = this;
      that.setData({
        hiddenLoading: false,
      });
      const [err, res] = await getInvoiceTitleList(
        {},
        // #ifdef H5
        {
          'content-type': 'application/x-www-form-urlencoded',
        }
        // #endif
      );
      that.setData({
        hiddenLoading: true,
      });
      if (err) {
        return;
      }
      if (res) {
        that.setData({
          invoiceTitleList: res.invoiceTitleList.map((item) => {
            return {
              ...item,
              checked: false,
            };
          }),
        });
      }
    },

    radioChange: function (e) {
      var checked = e.detail.value;
      var changed = {};
      var that = this;
      for (var i = 0; i < that.radioItems.length; i++) {
        if (checked.indexOf(that.radioItems[i].name) !== -1) {
          changed['radioItems[' + i + '].checked'] = true;
          that.setData(
            {
              personal: true,
              typed: '02',
              radioTypeItems: [
                {
                  name: '03',
                  value: '增值税普通发票',
                  checked: 'true',
                },
                {
                  name: '04',
                  value: '增值税专用发票',
                },
              ],
              taitou: '',
            },
            () => {
              if (that.reopenType) {
                that.radioTypeChange({
                  detail: {
                    value: that.invoiceType,
                  },
                });
              }
            }
          );
        } else {
          changed['radioItems[' + i + '].checked'] = false;
          that.setData({
            personal: false,
            typed: '01',
            taitou: '',
            radioTypeItems: [
              {
                name: '03',
                value: '增值税普通发票',
                checked: 'true',
              },
            ],
          });
        }
      }
      that.setData(changed);
      console.log(that.personal);
    },

    radioTypeChange: function (e) {
      const checked = e.detail.value;
      let { invoiceType, radioTypeItems } = this;
      radioTypeItems.forEach((item) => {
        if (item.name === checked) {
          item.checked = true;
          invoiceType = checked;
        } else {
          item.checked = false;
        }
      });
      this.setData({
        invoiceType,
        radioTypeItems,
      });
    },

    invoiceRadioChange: function (event) {
      const invoiceTitleId = event.detail.value;
      const invoiceList = this.invoiceTitleList;
      let invoiceObj = {};
      invoiceList.forEach((item) => {
        if (item.invoiceTitleId == invoiceTitleId) {
          item.checked = true;
          invoiceObj = item;
        } else {
          item.checked = false;
        }
      });
      console.log(invoiceObj, 'invoiceObj');
      const { bankAccount, bankName, invoiceTitle, taxNum } = invoiceObj;
      this.setData({
        invoiceTitleList: invoiceList,
        taitou: invoiceTitle,
        shuihao: taxNum,
        bankCode: bankName,
        bankAccount,
      });
      this.listConfirm();
    },

    panDuan: function () {
      const { zhuangt, number, name, taitou, address, invoiceType, bankAccount, bankCode } = this;
      let panduan = false;
      if (zhuangt) {
        if (number != '' && name != '' && taitou != '' && address != '') {
          if (invoiceType === '04') {
            if (bankAccount && bankCode) {
              panduan = true;
            } else {
              panduan = false;
            }
          } else {
            panduan = true;
          }
        } else {
          panduan = false;
        }
        this.setData({
          panduan,
        });
        var _self = this;
        setTimeout(function () {
          _self.panDuan();
        }, 1000);
      } else {
        console.log('no');
      }
    },

    nameFun: function (value) {
      console.log(value);
      this.setData({
        name: value.detail.value,
      });
    },

    addressFun: function (value) {
      console.log(value);
      this.setData({
        address: value.detail.value,
      });
    },
    remarkFun: function (value) {
      console.log(value);
      this.setData({
        invoiceRemark: value.detail.value,
      });
    },

    numberFun: function (value) {
      console.log(value);
      this.setData({
        number: value.detail.value,
      });
    },

    taitouFun: function (value) {
      console.log(value);
      this.setData({
        taitou: value.detail.value,
      });
    },

    shuihaoFun: function (value) {
      console.log(value);
      this.setData({
        shuihao: value.detail.value,
      });
    },

    bankCodeInp: function (event) {
      this.setData({
        bankCode: event.detail.value,
      });
    },

    bankAccountInp: function (event) {
      this.setData({
        bankAccount: event.detail.value,
      });
    },

    listShow: function () {
      this.setData({
        listHidden: false,
      });
    },

    listConfirm: function () {
      this.setData({
        listHidden: true,
      });
    },

    addCommonHeadings: function () {
      uni.navigateTo({
        url: '/subPackages/billingUsually/editCommonHeading/detail',
      });
      this.listConfirm();
    },
    // 订阅消息
    subscribeMessage: function (callback) {
      return subscribeMessage(['开票申请结果通知'], callback);
    },

    open: function () {
      const that = this;
      const {
        reopenType,
        name,
        address,
        invoiceRemark,
        taitou,
        number,
        shuihao,
        typed,
        invoiceAppNo,
        meh,
        bankAccount,
        bankCode,
        personal,
      } = this;
      // 名称正则:中文，大于位数2
      // const taitouRuler = /[\u4e00-\u9fa5]{3,}/;
      // if (!taitouRuler.test(taitou)) {
      //   uni.showToast({
      //     title: !personal ? '请填写真实姓名' : '请填写正确的企业名称',
      //     duration: 1200,
      //     icon: 'none',
      //   });
      //   return;
      // }
      // 税号正则：字母新增必须大写，位数18位
      const shuihaoRuler = /^[A-Z0-9]{18}$/;
      if (typed === '02' && !shuihaoRuler.test(shuihao)) {
        uni.showToast({
          title: '请填写正确税号',
          duration: 1200,
          icon: 'none',
        });
        return;
      }

      // 区分维信、H5
      if (ENVR === 'wx') {
        this.subscribeMessage(() => {
          if (reopenType === 'reopen') {
            that.setInvoiceAgain();
            // uni.request({
            //   url: baseUrl + '/wx/v0.1/account/invoiceAgain',
            //   data: {
            //     invoiceTitleType: typed,
            //     invoiceType: that.invoiceType,
            //     orderNo: invoiceAppNo,
            //     invoiceTitle: taitou,
            //     taxNum: shuihao,
            //     invoiceBank: bankCode,
            //     invoiceBankAccount: bankAccount,
            //   },
            //   method: 'POST',
            //   header: {
            //     'content-type': 'application/json',
            //     minitProgramToken: that.token,
            //   },
            //   success: (res) => {
            //     that.setData({
            //       hiddenLoading: true,
            //     });
            //     const { ret, msg } = res.data;
            //     if (ret == 200) {
            //       that.setData({
            //         zhuangt: false,
            //       });
            //       uni.showToast({
            //         title: '重开申请已提交',
            //         icon: 'success',
            //         duration: 1200,
            //       });
            //       setTimeout(function () {
            //         const pages = getCurrentPages();
            //         const prevPage = pages[pages.length - 2];
            //         prevPage.setData({
            //           isRefresh: true,
            //         });
            //         uni.navigateBack({
            //           delit: 1,
            //         });
            //       }, 1200);
            //     } else {
            //       uni.showToast({
            //         title: msg || ret,
            //         icon: 'none',
            //         duration: 1200,
            //       });
            //     }
            //   },
            //   fail: (err) => {
            //     that.setData({
            //       hiddenLoading: true,
            //     });
            //     uni.showToast({
            //       title: '重开失败',
            //       icon: 'none',
            //       duration: 1200,
            //     });
            //   },
            // });
          } else {
            var now = new Date();
            var year = now.getFullYear(); //年
            var month = now.getMonth() + 1; //月
            var day = now.getDate(); //日
            var hh = now.getHours(); //时
            var mm = now.getMinutes(); //分
            var ss = now.getSeconds(); //秒
            var clock = year + '-';
            if (month < 10) {
              clock += '0';
            }
            clock += month + '-';
            if (day < 10) {
              clock += '0';
            }
            clock += day + ' ';
            if (hh < 10) {
              clock += '0';
            }
            clock += hh + ':';
            if (mm < 10) {
              clock += '0';
            }
            clock += mm + ':';
            if (ss < 10) {
              clock += '0';
            }
            clock += ss;
            const list = that.arr;
            const timed = that.time;
            // console.log(price,'金钱总和')
            var arror = [];
            for (var i = 0; i < list.length; i++) {
              var appList = {
                orderNo: list[i],
                invoiceAmt: meh[i],
                successTime: timed[i],
                orderType: '02',
              };
              arror.push(appList);
            }
            const params = {
              appChannel: ENVR === 'wx' ? '07' : '01',
              invoiceTitle: taitou,
              invoiceTitleType: typed,
              invoiceMode: '02',
              invoiceType: that.invoiceType,
              invoiceMedia: '02',
              taxNum: shuihao,
              recipients: name,
              //收件人
              phoneNo: number,
              email: address,
              invoiceRemark: invoiceRemark,
              invoiceBank: bankCode,
              invoiceBankAccount: bankAccount,
              // orderType: list,
              appList: this.invoiceTypeContentValue !== '10' ? JSON.stringify(arror) : undefined,

              vipPurchaseList: this.invoiceTypeContentValue === '10' ? JSON.stringify(arror) : undefined,
              orderType: this.invoiceTypeContentValue, // 02 充电列表 10 会员列表  09 商品列表
              goodsType: this.invoiceTypeContentValue === '09' ? this.goodsType : undefined,
              // invoiceTAmt:arror[0]?.invoiceAmt ?? 0
            };
            console.log(params, 'params');
            // let url = '/wx/v0.1/account/invoice';
            if (reopenType === 'retry') {
              // url = '/wx/v0.1/account/invoiceRetry';
              params.orderNo = invoiceAppNo;
              params.invoiceAppNo = invoiceAppNo;

              this.setAccountInvoiceRetry(params);
            } else {
              that.setAccountInvoice(params);
            }
            // uni.request({
            //   url: baseUrl + url,
            //   data: params,
            //   method: 'POST',
            //   header: {
            //     'content-type': 'application/json',
            //     minitProgramToken: that.token,
            //   },
            //   success: (res) => {
            //     that.setData({
            //       hiddenLoading: true,
            //     });
            //     const { ret, msg } = res.data;
            //     if (ret == 200) {
            //       that.setData({
            //         zhuangt: false,
            //       });

            //       uni.showToast({
            //         title: '提交成功',
            //         icon: 'success',
            //         duration: 1200,
            //       });
            //       setTimeout(function () {
            //         if (reopenType === 'retry') {
            //           const pages = getCurrentPages();
            //           const prevPage = pages[pages.length - 2];
            //           prevPage.setData({
            //             isRefresh: true,
            //           });
            //           uni.navigateBack({
            //             delit: 1,
            //           });
            //         } else {
            //           uni.redirectTo({
            //             url: '/subPackages/invoiceopening/invoiceopening',
            //           });
            //         }
            //       }, 1200);
            //     } else {
            //       uni.showToast({
            //         title: msg || ret,
            //         icon: 'none',
            //         duration: 1200,
            //       });
            //     }
            //   },
            //   fail: (err) => {
            //     that.setData({
            //       hiddenLoading: true,
            //     });
            //     uni.showToast({
            //       title: '开票失败',
            //       icon: 'loading',
            //       duration: 1200,
            //     });
            //     console.log(err, '请求出问题。');
            //   },
            // });
          }
        });
      } else {
        if (reopenType === 'reopen') {
          that.setInvoiceAgain();
        } else {
          var now = new Date();
          var year = now.getFullYear(); //年
          var month = now.getMonth() + 1; //月
          var day = now.getDate(); //日
          var hh = now.getHours(); //时
          var mm = now.getMinutes(); //分
          var ss = now.getSeconds(); //秒
          var clock = year + '-';
          if (month < 10) {
            clock += '0';
          }
          clock += month + '-';
          if (day < 10) {
            clock += '0';
          }
          clock += day + ' ';
          if (hh < 10) {
            clock += '0';
          }
          clock += hh + ':';
          if (mm < 10) {
            clock += '0';
          }
          clock += mm + ':';
          if (ss < 10) {
            clock += '0';
          }
          clock += ss;
          const list = that.arr;
          const timed = that.time;
          // console.log(price,'金钱总和')
          var arror = [];
          for (var i = 0; i < list.length; i++) {
            var appList = {
              orderNo: list[i],
              invoiceAmt: meh[i],
              successTime: timed[i],
              orderType: this.invoiceTypeContentValue,
            };
            arror.push(appList);
          }
          const params = {
            appChannel: ENVR === 'wx' ? '07' : '01',
            invoiceTitle: taitou,
            invoiceTitleType: typed,
            invoiceMode: '02',
            invoiceType: that.invoiceType,
            invoiceMedia: '02',
            taxNum: shuihao,
            recipients: name,
            //收件人
            phoneNo: number,
            email: address,
            invoiceRemark: invoiceRemark,
            invoiceBank: bankCode,
            invoiceBankAccount: bankAccount,
            // orderType: list,
            appList: this.invoiceTypeContentValue === '02' ? JSON.stringify(arror) : undefined, // 订单

            vipPurchaseList: this.invoiceTypeContentValue === '10' ? JSON.stringify(arror) : undefined, // 会员
            integralPurchaseList: this.invoiceTypeContentValue === '09' ? JSON.stringify(arror) : undefined, // 商品

            orderType: this.invoiceTypeContentValue, // 02 充电列表 10 会员列表  09 商品列表
            goodsType: this.invoiceTypeContentValue === '09' ? this.goodsType : undefined,
            // invoiceTAmt:arror[0]?.invoiceAmt ?? 0
          };
          console.log(params, 'params');
          if (reopenType === 'retry') {
            params.orderNo = invoiceAppNo;
            params.invoiceAppNo = invoiceAppNo;
            this.setAccountInvoiceRetry(params);
          } else {
            that.setAccountInvoice(params);
          }
        }
      }
    },

    async setInvoiceAgain() {
      const that = this;
      const { reopenType, name, address, taitou, number, shuihao, typed, invoiceAppNo, meh, bankAccount, bankCode } =
        this;
      that.setData({
        hiddenLoading: false,
      });
      const [err, res] = await setInvoiceAgain(
        {
          invoiceTitleType: typed,
          invoiceType: that.invoiceType,
          orderNo: invoiceAppNo,
          invoiceTitle: taitou,
          taxNum: shuihao,
          invoiceBank: bankCode,
          invoiceBankAccount: bankAccount,
        },
        {
          // #ifdef H5
          'content-type': 'application/x-www-form-urlencoded',
          // #endif
        }
      );
      that.setData({
        hiddenLoading: true,
      });
      if (err) {
        uni.showToast({
          title: '重开失败',
          icon: 'none',
          duration: 1200,
        });
        return;
      }
      if (res) {
        const { ret, msg } = res;
        if (ret == 200) {
          that.setData({
            zhuangt: false,
          });
          uni.showToast({
            title: '重开申请已提交',
            icon: 'success',
            duration: 1200,
          });
          setTimeout(function () {
            const pages = getCurrentPages();
            const prevPage = pages[pages.length - 2];
            prevPage.setData({
              isRefresh: true,
            });
            uni.navigateBack({
              delit: 1,
            });
          }, 1200);
        } else {
          uni.showToast({
            title: msg || ret,
            icon: 'none',
            duration: 1200,
          });
        }
      }
    },
    async setAccountInvoice(params) {
      const that = this;
      const { reopenType, name, address, taitou, number, shuihao, typed, invoiceAppNo, meh, bankAccount, bankCode } =
        this;
      that.setData({
        hiddenLoading: false,
      });
      const [err, res] = await setAccountInvoice(params);
      that.setData({
        hiddenLoading: true,
      });
      if (err) {
        uni.showToast({
          title: err.msg || '开票失败',
          icon: 'loading',
          duration: 1200,
        });
        return;
      }
      if (res) {
        const { ret, msg } = res;
        if (ret == 200) {
          that.setData({
            zhuangt: false,
          });

          uni.showToast({
            title: '提交成功',
            icon: 'success',
            duration: 1200,
          });
          setTimeout(function () {
            if (reopenType === 'retry') {
              const pages = getCurrentPages();
              const prevPage = pages[pages.length - 2];
              prevPage.setData({
                isRefresh: true,
              });
              uni.navigateBack({
                delit: 1,
              });
            } else {
              uni.redirectTo({
                url: '/subPackages/invoiceopening/invoiceopening',
              });
            }
          }, 1200);
        } else {
          uni.showToast({
            title: msg || ret,
            icon: 'none',
            duration: 1200,
          });
        }
      }
    },
    async setAccountInvoiceRetry(params) {
      const that = this;
      const {
        reopenType,
        name,
        address,
        taitou,
        number,
        shuihao,
        typed,
        invoiceAppNo,
        meh,
        bankAccount,
        bankCode,
        invoiceRemark,
      } = this;
      that.setData({
        hiddenLoading: false,
      });
      const [err, res] = await setAccountInvoiceRetry(params, {
        'content-type': 'application/x-www-form-urlencoded',
      });
      that.setData({
        hiddenLoading: true,
      });
      if (err) {
        uni.showToast({
          title: err.msg || '开票失败',
          icon: 'loading',
          duration: 1200,
        });
        return;
      }
      if (res) {
        const { ret, msg } = res;
        if (ret == 200) {
          that.setData({
            zhuangt: false,
          });

          uni.showToast({
            title: '提交成功',
            icon: 'success',
            duration: 1200,
          });
          setTimeout(function () {
            if (reopenType === 'retry') {
              const pages = getCurrentPages();
              const prevPage = pages[pages.length - 2];
              prevPage.setData({
                isRefresh: true,
              });
              uni.navigateBack({
                delit: 1,
              });
            } else {
              uni.redirectTo({
                url: '/subPackages/invoiceopening/invoiceopening',
              });
            }
          }, 1200);
        } else {
          uni.showToast({
            title: msg || ret,
            icon: 'none',
            duration: 1200,
          });
        }
      }
    },
  },
};
</script>
<style>
@import './billingpersonal.css';
</style>
