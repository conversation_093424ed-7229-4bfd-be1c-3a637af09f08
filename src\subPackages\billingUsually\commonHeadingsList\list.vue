<template>
  <view>
    <view
      class="billinghistory-lists"
      v-for="(item, index) in invoiceTitleList"
      :key="index"
    >
      <view
        class="list-main"
        :data-obj="item"
        @tap="goDetail"
      >
        <text class="list-content">{{ item.invoiceTitle }}</text>

        <text class="list-right">{{ item.defaultFlag === '1' ? '默认' : '' }}</text>

        <view class="list-full"></view>

        <image class="list-icon" :src="`${IMG_PATH}right.png`" />
      </view>

      <view
        class="delete-btn"
        :data-obj="item"
        @tap.stop="delInvoice"
      >
        <text class="delete-text">删除</text>
      </view>
    </view>
    <view class="default-height"></view>
    <view v-if="invoiceTitleList.length === 0" class="myorder-image">
      <image :src="`${IMG_PATH}main-data-null.png`" mode="heightFix" />
      <text>当前暂无常用发票抬头</text>
    </view>
    <view class="bottom-bg">
      <button class="blue-bg" data-obj="" @tap.stop="goDetail($event, { obj: '' })">添加常用发票抬头</button>
    </view>
  </view>
</template>

<script>
import { deleteInvoiceTitle, getInvoiceTitleList } from '@/services';
export default {
  data() {
    return {
      token: '',
      invoiceTitleList: [],
      hist: true,
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function () {
    const that = this;
    that.setData({
      token: uni.getStorageSync('token'),
    });
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.getList();
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {},
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {},
  methods: {
    async getList() {
      const that = this;
      const { token } = this;
      uni.showLoading({
        title: '加载中',
      });
      const [err, res] = await getInvoiceTitleList(
        {},
        // #ifdef H5
        {
          'content-type': 'application/x-www-form-urlencoded',
        }
        // #endif
      );
      uni.hideLoading();
      if (res) {
        that.setData({
          invoiceTitleList: res.invoiceTitleList,
        });
      }
      // uni.request({
      //   url: baseUrl + '/wx/v0.2/invoice-title-list',
      //   data: {},
      //   method: 'POST',
      //   header: {
      //     'content-type': 'application/json',
      //     minitProgramToken: token,
      //   },
      //   success: (res) => {
      //     that.setData({
      //       invoiceTitleList: res.data.invoiceTitleList,
      //     });
      //     uni.hideLoading();
      //   },
      //   fail: (err) => {
      //     console.log(err, '请求出问题。');
      //     uni.hideLoading();
      //   },
      // });
    },

    async delInvoice(event) {
      console.log(event);
      const { invoiceTitleId, invoiceTitle } = event.currentTarget.dataset.obj;
      const that = this;
      const { token } = this;
      uni.showModal({
        title: '提示',
        content: `是否确定删除${invoiceTitle}抬头！`,
        success: async (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '删除中',
            });
            const [err, res] = await deleteInvoiceTitle({ invoiceTitleId });
            uni.hideLoading();
            if (res) {
              const { ret, msg } = res;
              if (ret === 200) {
                uni.showToast({
                  icon: 'success',
                  title: '删除成功',
                });
                that.getList();
              } else {
                uni.showToast({
                  title: msg || ret,
                  icon: 'none',
                  duration: 1200,
                });
              }
            }
            // uni.request({
            //   url: baseUrl + '/wx/v0.2/invoice-title-delete',
            //   data: {
            //     invoiceTitleId,
            //   },
            //   method: 'POST',
            //   header: {
            //     'content-type': 'application/json',
            //     minitProgramToken: token,
            //   },
            //   success: (res) => {
            //     uni.hideLoading();
            //     const { ret, msg } = res.data;
            //     if (ret === 200) {
            //       uni.showToast({
            //         icon: 'success',
            //         title: '删除成功',
            //       });
            //       that.getList();
            //     } else {
            //       uni.showToast({
            //         title: msg || ret,
            //         icon: 'none',
            //         duration: 1200,
            //       });
            //     }
            //   },
            //   fail: (err) => {
            //     console.log(err, '请求出问题。');
            //     uni.hideLoading();
            //   },
            // });
          } else if (res.cancel) {
            console.log('用户点击取消');
          }
        },
      });
    },

    goDetail: function (event, _dataset) {
      /* ---处理dataset begin--- */
      this.handleDataset(event, _dataset);
      /* ---处理dataset end--- */
      const { invoiceTitleId } = event?.currentTarget?.dataset?.obj;
      uni.navigateTo({
        url: `/subPackages/billingUsually/editCommonHeading/detail?invoiceTitleId=${invoiceTitleId || ''}`,
      });
    },
  },
};
</script>
<style>
@import './list.css';
</style>
