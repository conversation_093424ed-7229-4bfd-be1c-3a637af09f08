.flex-bow {
  position: fixed;
  width: 100%;
  height: 88rpx;
  box-sizing: border-box;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
}

.search-head {
  width: 100%;
  height: 88rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
  padding-left: 0;
}

.search-head input {
  width: 592rpx;
  height: 66rpx;
  border-radius: 8rpx;
  border: 2rpx solid #ccc;
  font-size: 28rpx;
  padding-left: 20rpx;
  padding-right: 20rpx;
  box-sizing: border-box;
  margin-left: 30rpx;
}

.search-head .icon {
  line-height: 88rpx;
  font-size: 53rpx;
  color: #666;
  padding-left: 15rpx;
}

.search-head .search-lefy {
  padding-left: 20rpx;
  font-size: 42rpx;
  padding-right: 5rpx;
  padding-bottom: 10rpx;
}

.search-middle {
  background: #fff;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.search-middle .search-add {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333;
  font-size: 26rpx;
  height: 88rpx;
  box-sizing: border-box;
  padding-right: 30rpx;
  margin-left: 30rpx;
  /* border-bottom: 1rpx solid #CBCBCB */
}

.search-middle .search-msg {
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #999;
}

.search-middle .search-push {
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 20rpx 30rpx 20rpx 30rpx;
}

.search-middle .search-push text {
  font-size: 26rpx;
  color: #666;
  margin-right: 30rpx;
  line-height: 41rpx;
}

.search-middle .add-head-bull {
  width: 7rpx;
  height: 28rpx;
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 3rpx;
  margin-right: 12rpx;
}

.search-list .list_li {
  height: 120rpx;
  margin-left: 30rpx;
  padding-right: 30rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
  color: #666;
  border-top: 1rpx solid #cbcbcb;
}

.search-list .list_li .list_li_lf {
  width: 50%;
  display: flex;
  flex-wrap: wrap;
  align-content: space-around;
  box-sizing: border-box;
  padding: 15rpx 0;
}

.search-list .list_li .list_li_lf view {
  width: 100%;
  font-size: 28rpx;
  color: #333;
}

.search-list .list_li .list_li_lf text {
  margin-left: 10rpx;
}

.search-list .list_li .list_li_lf .samlls {
  font-size: 26rpx;
  color: #666;
}

/* .search-list .list_li view{
  width: 50%;
  display: flex;
  align-items: center;
  height: 100%;
} */
.search-list .list_li .list_li_rt {
  width: 40%;
  line-height: 120rpx;
  text-align: right;
  font-size: 24rpx;
  color: #666;
}

/* pages/mapStation/map.wxss */

.fotters {
  width: 100%;
  height: 386rpx;
  position: absolute;
  bottom: 0;
  background: #fff;
  padding: 20rpx 30rpx 0 30rpx;
  box-sizing: border-box;
}

.fotters .fotters-block {
  display: inline-block;
  font-size: 30rpx;
}

.fotters-block-left {
  color: #e00a22;
  width: 550rpx;
  height: 120rpx;
  padding: 10rpx 0;
  overflow: hidden;
  /* height: 100rxpx */
}

.fotters-block-right {
  float: right;
  width: 140rpx;
  height: 120rpx;
  padding: 10rpx 0;
  text-align: center;
  color: #2196f3;
  /* margin-bottom:  */
}

.fotters-block-right .icon-guide1 {
  height: 60rpx;
  vertical-align: middle;
}

.fotters-block-right text {
  line-height: 60rpx;
}

.max-w {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.middle-size {
  font-size: 30rpx;
  color: #666;
}

.middle-size-other {
  font-size: 28rpx;
  color: #333;
}

.btn-bottom {
  width: 100%;
}

.btn-bottom view {
  display: inline-block;
  width: 50%;
  text-align: center;
  font-size: 30rpx;
  color: #2196f3;
  box-sizing: border-box;
  line-height: 55rpx;
}

.caidan {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 9vh;
  background: #fff;
  width: 100%;
  display: flex;
  justify-content: space-around;
  text-align: center;
  align-items: center;
  color: #939393;
}

.caidan .listBtn {
  width: 20%;
  font-size: 24rpx;
}

.caidan .on {
  color: #e00a22;
}

.caidan .scan {
  color: #fff;
  padding: 15rpx 0;
  width: 20%;
  border-radius: 100rpx;
  height: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.head-maps {
  width: 100%;
  height: 100rpx;
  position: absolute;
  top: 0;
  background: #e00a22;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.head-maps .head-maps-found {
  display: flex;
  width: 65%;
  background: #fff;
  height: 65rpx;
  align-items: center;
  border-radius: 100rpx;
  padding-left: 30rpx;
  box-sizing: border-box;
}

.head-maps .head-maps-found input {
  width: 80%;
  display: inline-block;
  height: 100%;
  font-size: 30rpx;
  padding-left: 10rpx;
}

.list-found {
  background: #eee;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding-top: 89rpx;
}

.list-found .station-list {
  width: 100%;
  /* height: 264rpx; */
  background: #fff;
  margin-bottom: 20rpx;
  box-sizing: border-box;
  padding: 20rpx 30rpx;
}

.page-hed {
  height: 88rpx;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  margin-bottom: 10rpx;
  background: #fff;
  color: #666;
  position: relative;
  top: 100rpx;
  z-index: 9898999;
  width: 100%;
}

.page-hed .line-right {
  border-right: 2rpx solid #e1e1e1;
  box-sizing: border-box;
}

.page-hed view {
  width: 50%;
  text-align: center;
  line-height: 70rpx;
  font-size: 28rpx;
  height: 70rpx;
}

.c-state2 {
  transform: rotate(0deg) scale(0.8) translate(75%, 0%);
  -webkit-transform: rotate(0deg) scale(0.8) translate(75%, 0%);
}

.page-hed .clcik {
  color: #e00a22;
}

.station-list .list-name {
  color: #333;
  font-size: 30rpx;
  line-height: 42rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 500;
}

.station-list .list-name .guideRight {
  display: flex;
  align-items: center;
  height: 100%;
  color: #2196f3;
  font-size: 24rpx;
}

.station-list .list-name .guideRight icon {
  margin-right: 14rpx;
  height: 100%;
  line-height: 42rpx;
  display: flex;
}

.station-list .list-list-status {
  display: flex;
  width: 100%;
}

.list-status .list-span {
  display: inline-block;
  width: 50%;
  text-align: left;
  color: #333;
  font-size: 30rpx;
  line-height: 60rpx;
}

.list-status view {
  font-size: 24rpx;
  color: #333;
}

.list-status view text {
  font-size: 36rpx;
  color: #2196f3;
  margin-right: 14rpx;
}

.list-number {
  float: right;
}

.list-number view {
  display: inline-block;
  color: #2196f3;
  justify-content: end;
  /* line-height: 60rpx; */
  font-size: 28rpx;
  text-align: left;
}

.list-number .Itemtype {
  text-align: right;
  line-height: 38rpx;
}

.list-number .Itemtype text {
  display: inline-block;
  width: 36rpx;
  height: 36rpx;
  line-height: 36rpx;
  text-align: center;
  background: #f44336;
  font-size: 20rpx;
  color: #fff;
  border-radius: 8rpx;
  margin-right: 12rpx;
}

.list-number .Itemtype .I {
  margin-left: 27rpx;
  background: #2196f3;
}

.list-address {
  color: #666;
  font-size: 28rpx;
  /* line-height: 60rpx; */
  width: 100%;
  /* overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis; */
}

.list-tagName {
  margin-top: 10rpx;
  padding-top: 10rpx;
  border-top: 1rpx solid rgba(241, 241, 241, 1);
}

.list-address .tagName {
  padding: 3rpx 6rpx;
  color: #2196f3;
  border-radius: 4rpx;
  font-size: 22rpx;
  border: 1px solid rgba(33, 150, 243, 1);
  margin-right: 24rpx;
}

.color-orange {
  color: #ffc927;
}

.color-gray {
  color: #d0d0d0;
}

.map-icon {
  display: inline-block;
  vertical-align: middle;
  margin-right: 10rpx;
}

.cityTabs {
  color: #fff;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cityTabs .addre_cty {
  display: inline-block;
  width: 110rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.station-ul {
  margin: 0 24rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}