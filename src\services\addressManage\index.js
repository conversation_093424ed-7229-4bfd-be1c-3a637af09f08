import { request as __request } from '../request/request';
// import { ENVR } from '../../config/global';
// const baseurl = ENVR === 'wx' ? '/scan/wx' : '/def/api';
import { ENVR, scanBaseurl, defBaseurl, baseBaseurl } from '../../config/global';
const baseurl = ENVR === 'wx' ? scanBaseurl : defBaseurl;
// 查询地址列表
export const getAddressList = (params, config = {}) => {
  return __request(
    { ...config },
    {
      url: `${ENVR === 'wx' ? scanBaseurl + '/v0.1/addr/search' : defBaseurl + '/v0.1/queryAddress'}`,
      method: 'POST',
      data: params,
    }
  );
};

// 编辑地址
export const editAddress = (params, config = {}) => {
  return __request(
    { ...config },
    {
      url: `${ENVR === 'wx' ? scanBaseurl + '/v0.1/addr/edit' : defBaseurl + '/v0.1/editArea'}`,
      method: 'POST',
      data: params,
    }
  );
};

// 查询省市区
export const getCountry = (params, config = {}) => {
  return __request(
    { ...config },
    {
      url: `${ENVR === 'wx' ? scanBaseurl + '/v0.1/province' : defBaseurl + '/v0.1/queryArea'}`,
      method: ENVR === 'wx' ? 'GET' : 'GET',
      data: params,
    }
  );
};
