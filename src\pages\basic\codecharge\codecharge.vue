<template>
  <!-- pages/codecharge/codecharge.wxml -->
  <view class="codecharge">
    <!-- <view class="code"> -->
    <!-- <text class="blue-color">扫码充电</text> -->
    <!-- <icon bindtap='scan' class="red-colors styleColorB" type="icon iconfont icon-scan" size="50"/>
  </view> -->
    <view class="charge">
      <text class="blue-color">输码充电</text>
      <view class="txt-btn">
        <input type="text" @input="scanIdNumber" placeholder="充电码充电" />
        <button v-if="scanId == ''">确认输入</button>
        <button v-else class="color-btn styleColorB" @tap="Jump">确认输入</button>
      </view>
      <text>输入桩上充电码下面序列号</text>
    </view>
  </view>
</template>

<script>
// pages/codecharge/codecharge.js
export default {
  data() {
    return {
      scanId: '',
    };
  },
  methods: {
    scanIdNumber(value) {
      console.log(value.detail.value);
      this.setData({
        scanId: value.detail.value,
      });
    },

    scan() {
      uni.scanCode({
        onlyFromCamera: true,
        success: (res) => {
          console.log(res.result, '0000000000000000000000000000000');
          // 2018614
          var message = res.result;
          var data = {
            gunId: res,
            types: 998,
            name: '扫码来的',
            // equipName:
            // url: '/subPackages/paymentorder/paymentorder?orderNo=' + orderNo + '&equipName=' + equipName + '&name=' + name
          };
          var msg = JSON.stringify(data);
          uni.navigateTo({
            url: '/subPackages/placeorder/placeorder?msg=' + message + '&titles=10',
          });
        },
      });
    },

    Jump() {
      if (this.scanId == '') {
        return;
      }
      var scanId = this.scanId;
      var data = {
        gunId: scanId,
        name: '扫码来的',
      };
      var msg = JSON.stringify(data);
      uni.navigateTo({
        url: '/subPackages/placeorder/placeorder?msg=' + scanId + '&titles=10',
      });
    },
  },
};
</script>
<style>
@import './codecharge.css';
</style>
