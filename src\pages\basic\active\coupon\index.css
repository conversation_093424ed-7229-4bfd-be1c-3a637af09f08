/* pages/active/coupon/index.wxss */
.act-image {
  display: flex;
  justify-content: center;
  min-height: 800rpx;
}
.act-image .image-item {
  width: 100%;
  height: auto;
  display: block;
}
.redeem {
  display: flex;
  justify-content: flex-end;
}
.redeem .redeem-button {
  margin: 30rpx 30rpx 0 0;
  border: 4rpx solid var(--theme);
  color: #1e9cff;
  background: #fff;
  width: 200rpx;
  height: 60rpx;
  border-radius: 16rpx;
  line-height: 60rpx;
  font-size: 30rpx;
}
.description .description-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
}
.description .description-content {
  font-size: 26rpx;
  color: #999999;
}
.description,
.footer {
  width: 690rpx;
  margin: 0 auto;
  margin-top: 30rpx;
}
.description {
  padding-bottom: 50rpx;
}
.footer {
  text-align: center;
}
.footer .footer-status {
  display: block;
  color: #999999;
  font-size: 50rpx;
  margin-bottom: 30rpx;
}
.footer .footer-button {
  padding-bottom: 50rpx;
}
