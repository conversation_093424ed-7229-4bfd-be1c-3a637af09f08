<template>
  <view>
    <!-- pages/standard.wxml -->
    <view class="standard" v-for="(item, index) in msg" :key="index">
      <view class="standard_head">
        <text></text>
        {{ item.startValue }} - {{ item.endValue }}
      </view>

      <view class="standard_list">
        <view class="list_info">充电费</view>
        <view @tap="handClickPop" :data-msg="item.chargePrice" class="list_price">{{ item.chargePrice }}</view>
      </view>

      <view class="standard_list">
        <view class="list_info">服务费</view>
        <view @tap="handClickPop" :data-msg="price" class="list_price">{{ price }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import { getStationDetail } from '@/services/index';
export default {
  data() {
    return {
      msg: [],
      price: '',
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    console.log(options);
    var stationId = options.stationId;
    var that = this;
    var params = {
      stationId: stationId,
    };
    const [, res] = await getStationDetail(params);
    if (res) {
      that.setData({
        msg: res.defAmt.chargeItemList,
        price: res.defAmt.serviceAmt,
      });
    } else {
      console.log('失败');
      uni.showToast({
        icon: 'loading',
        title: '加载数据失败',
      });
    }
  },
  methods: {
    handClickPop(item) {
      console.log('item', item.currentTarget.dataset);
      if (item && item.currentTarget && item.currentTarget.dataset) {
        var msg = item.currentTarget.dataset.msg.replace(/;/g, '\r\n');
        uni.showModal({
          title: '提示',
          showCancel: false,
          content: msg || '---',
          success(res) {
            if (res.confirm) {
              console.log('用户点击确定');
            } else if (res.cancel) {
              console.log('用户点击取消');
            }
          },
        });
      }
    },
  },
};
</script>
<style>
@import './standard.css';
</style>
