<!--
@name: 积分任务列表
@description: 积分任务列表
@time: 2024/9/2
-->
<template>
  <!--      积分任务-->
  <view class="task-ul">
    <view class="task-li" v-if="bindCar === 0">
      <view class="task-title">{{ point.isCoupon ? '完善信息送优惠券' : '完善信息送积分' }}</view>
      <view class="task-desc">+{{ point.isCoupon ? point.couponName : `${point.improve || 0}积分` }}</view>
      <view class="task-btn" @click="jumpToPage('info')"
        >去完善 <img class="task-btn-right" :src="`${IMG_PATH}member/task-right.png`" />
      </view>
    </view>
    <view class="task-li">
      <view class="task-title">签到得积分</view>
      <view class="task-desc">+{{ point.signIn || 0 }}积分</view>
      <view
        :class="[
          'task-btn',
          {
            'task-btn-opacity': checkIn > 0,
          },
        ]"
        @click="jumpToPage('sign')"
        >签到<img class="task-btn-right" :src="`${IMG_PATH}member/task-right.png`" />
      </view>
    </view>
    <view class="task-li" @click="jfCallback">
      <view class="task-title">充电送积分</view>
      <view class="task-desc">规则说明</view>
      <view class="task-btn" @click.stop="jumpToPage('charge')"
        >去充电<img class="task-btn-right" :src="`${IMG_PATH}member/task-right.png`" />
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import { integralRule, refreshTaskStatus, signIntegeral } from '@/services/index.js';
export default {
  name: 'TaskList',
  data() {
    return {
      checkIn: 0,
      bindCar: 0,
      point: {
        signIn: 0,
        improve: 0,
      },
    };
  },
  computed: {
    ...mapState({
      userInfo: (state, getters) => getters['login/getUserInfo'],
    }),
  },
  mounted() {
    this.refreshStatus();
    this.getPoints();
  },
  methods: {
    async getPoints() {
      const [, res] = await integralRule();
      if (res && res.list) {
        this.point = res.list.reduce(
          (cur, nex) => {
            // if (nex.eventType == '05') cur.signIn = nex.integralNo;
            if (nex.eventType == '02') {
              cur.improve = nex.integralNo;
              cur.isCoupon = nex.isCoupon;
              cur.couponName = nex.couponName;
            }
            return cur;
          },
          { signIn: 0, improve: 0 }
        );
        this.point.signIn = res.signIntegeral;
      }
    },
    // 积分任务跳转
    jumpToPage(type) {
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '/pages/login/login',
        });
      } else {
        switch (type) {
          // 完善信息
          case 'info':
            uni.navigateTo({
              url: '/subPackages/addCar/addCar',
            });
            break;
          // 签到
          case 'sign':
            console.log('去签到');
            uni.navigateTo({
              url: '/subPackages/signin/index',
            });
            break;
          // 充电
          case 'charge':
            uni.navigateTo({
              url: '/pages/basic/mapStation/map',
            });
            break;
          default:
            break;
        }
      }
    },
    // 刷新任务状态
    async refreshStatus() {
      const [, res] = await refreshTaskStatus();
      if (res && res.ret == 200) {
        this.checkIn = Number(res.checkIn);
        this.bindCar = Number(res.bindCar);
      }
    },
    // 获取积分配置
    getPointsConfig() {},
    jfCallback() {
      uni.navigateTo({
        url: `/pages/setting/agreement/index?&code=0501&title=${'《规则说明》'}`,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.task-ul {
  display: flex;
  padding: 0 24rpx 16rpx 24rpx;
  gap: 16rpx;
  grid-gap: 16rpx;
  box-sizing: border-box;
}
.task-li {
  flex: 1;
  box-sizing: border-box;
  border-radius: 16rpx;
  background: white;
  padding: 12rpx 20rpx;
  border-radius: 16rpx;
  background: linear-gradient(180deg, #ffe9da 0%, #ffcdb3 100%);
  max-width: 37%;
}
.task-title {
  font-family: PingFang SC;
  font-size: 26rpx;
  font-weight: normal;
  line-height: 30rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  color: #666666;
}
.task-desc {
  margin-top: 12rpx;
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: normal;
  line-height: 28rpx;
  color: #f46f00;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}
.task-btn {
  margin-top: 36rpx;
  padding: 7rpx 4rpx 7rpx 12rpx;
  border-radius: 80rpx;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  // background: #2da1da;
  background: linear-gradient(180deg, #4d4845 0%, #282934 100%);
  font-family: PingFang SC;
  font-size: 20rpx;
  font-weight: normal;
  line-height: 22rpx;
  color: #ffffff;
}
.task-btn-right {
  width: 20rpx;
  height: 20rpx;
}
.task-btn-opacity {
  opacity: 0.5;
}
</style>
