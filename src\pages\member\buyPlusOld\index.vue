<!--
@name: 购买plus会员
@description: 购买plus会员页面
@time: 2024/8/9
-->
<template>
  <view class="buy-plus" :style="{ backgroundImage: `url(${IMG_PATH}member/buy-plus-bg.png)` }">
    <!--        用户信息-->
    <view class="user-info">
      <!--            头像-->
      <img class="user-info-profile" :src="userInfo.imgUrl || `${IMG_PATH}defaultAvatar.png`" />
      <view class="user-info-content">
        <view class="name-mobile">
          <text class="name">{{ userInfo.custNickname || '未设置昵称' }}</text>
          <text class="mobile" v-if="memberInfo.vipFlag === '1'">({{ userInfo.mobile || '' }})</text>
          <img
            class="user-vip-rank"
            :src="`${IMG_PATH}member/rank-${rankBackGround}.png`"
            v-if="memberInfo.vipFlag === '0' && rankBackGround"
          />
          <img class="user-vip-rank" :src="`${IMG_PATH}member/rank-plus.png`" v-if="memberInfo.vipFlag === '1'" />
        </view>
        <text class="mobile" v-if="memberInfo.vipFlag === '0'">{{ userInfo.mobile || '' }}</text>
        <text class="time" v-if="memberInfo.vipFlag === '1'">会员有效期至：{{ memberInfo.vipExpireTime }}</text>
      </view>
    </view>
    <!--        套餐、权益-->
    <view class="buy-plus-content">
      <!--            套餐列表-->
      <MembershipPackage :member-info="memberInfo" />
      <!--            权益列表-->
      <view class="equity-list">
        <EquityList :member-info="memberInfo" />
      </view>
    </view>
  </view>
</template>

<script>
import MembershipPackage from './components/MembershipPackage';
import EquityList from '../components/EquityList';
import { getMemberInfo } from '@/services/index.js';
import { mapState } from 'vuex';
import AppButton from '@/components/AppButton/index';

export default {
  name: 'index',
  components: {
    MembershipPackage,
    EquityList,
    AppButton,
  },
  data() {
    return {
      detail: {},
      map: {
        1: 'qt',
        2: 'by',
        3: 'hj',
        4: 'bj',
      },
      memberInfo: {},
    };
  },
  computed: {
    ...mapState({
      userInfo: (state, getters) => getters['login/getUserInfo'],
    }),
    rankBackGround() {
      return this.memberInfo && this.memberInfo.rankNo ? this.map[this.memberInfo.rankNo] : '';
    },
  },
  onLoad() {
    this.getUserInfo();
    this.getMemberInfo();
  },
  methods: {
    // 获取用户信息
    getUserInfo() {
      this.$store.dispatch('login/getUserInfoCallback');
    },
    // 获取会员信息
    async getMemberInfo() {
      const params = {
        plusEquity: '1',
      };
      const [err, res] = await getMemberInfo(params);
      if (res) {
        this.memberInfo = res;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.buy-plus {
  height: 100%;
  background-size: contain;
  background-position: center top -88px;
  .user-info {
    display: flex;
    align-items: center;
    padding: 38rpx 40rpx 52rpx 40rpx;
    &-profile {
      width: 100rpx;
      height: 100rpx;
      border-radius: 100rpx;
    }
    &-content {
      display: flex;
      flex-direction: column;
      margin-left: 16rpx;
      .name-mobile {
        display: flex;
        margin-bottom: 8rpx;
        .user-vip-rank {
          margin-left: 8rpx;
          background-size: cover;
          width: 99rpx;
          height: 42rpx;
        }
        .name {
          font-size: 36rpx;
          font-weight: bold;
          line-height: 43rpx;
          color: #ffffff;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          max-width: 200rpx;
        }
        .mobile {
          font-size: 26rpx;
          line-height: 40rpx;
          color: #ffffff;
          margin-left: 8rpx;
        }
      }
      .mobile,
      .time {
        font-size: 22rpx;
        line-height: 26rpx;
        color: #ffffff;
      }
      .time {
        margin-top: 8rpx;
      }
    }
  }
  .buy-plus-content {
    min-height: calc(100% - 190rpx);
    background: white;
    border-radius: 24rpx 24rpx 0 0;
    padding: 40rpx 24rpx 0 24rpx;
    .equity-list {
      padding: 40rpx 0 106rpx 0;
    }
  }
}
</style>
