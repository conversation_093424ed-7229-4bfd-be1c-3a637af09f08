<template>
  <view class="">
    <!-- #ifdef MP-WEIXIN -->
    <view class="banner" :style="'padding-top:' + s_top + 'rpx;line-height:' + s_height + 'rpx'">
      <view class="banner-title">发现</view>
    </view>
    <!-- #endif -->
    <div class="card-wrap">
      <div class="card card1" @click="toCallback('active')">
        <div class="card-title">平台活动</div>
        <div class="card-btn" style="color: #1fc4b0">去参与</div>
      </div>
      <div class="card-item">
        <div class="card card-item-box bg-video" @click="toCallback('instructVideo')">
          <div class="card-title">指导视频</div>
          <div class="card-btn" style="color: #339efe">去观看</div>
        </div>
        <div class="card card-item-box bg-shopping" @click="toCallback('intergral')">
          <div class="card-title">积分商城</div>
          <div class="card-btn" style="color: #f9b055">去查看</div>
        </div>
      </div>
      <div class="card card2" @click="toCallback('gonggao')">
        <div class="title">系统公告</div>
        <div class="tip">System announcement entrance</div>
      </div>
    </div>
    <custom-nav-bar currentNavbar="discover"></custom-nav-bar>
    <image class="mine-bg" :src="`${IMG_PATH_UI}bg.png`"></image>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import customNavBar from '../../../components/custom-navbar/index';
export default {
  props: {},
  components: {
    customNavBar,
  },
  data() {
    return {
      s_top: '',
      s_height: '',
      // phone: '',
    };
  },
  computed: {
    ...mapState({
      userInfo: (state, getters) => getters['login/getUserInfo'],
    }),
  },
  watch: {},
  created() {},
  mounted() {
    // this.getPhone();
    // #ifdef MP
    this.initTopImg();
    // #endif
  },
  methods: {
    // // 获取手机号
    // getPhone() {
    //   var that = this;
    //   var params = {};
    //   var token = uni.getStorageSync('token') || '';
    //   uni.request({
    //     url: baseDate() + '/wx/v0.1/custinfo',
    //     data: params,
    //     method: 'GET',
    //     header: {
    //       'content-type': 'application/json',
    //       minitProgramToken: token,
    //     },
    //     success: (res) => {
    //       const { data, ret } = res.data;
    //       let phone = '未绑定';
    //       if (ret === 200 && data.mobile) {
    //         phone = data.mobile;
    //       }
    //       that.setData({
    //         phone,
    //       });
    //     },
    //   });
    // },
    initTopImg() {
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      this.setData({
        s_top: menuButtonInfo?.top * 2,
        s_height: menuButtonInfo?.height * 2,
      });
    },
    toCallback(type) {
      if (!this.userInfo?.mobile) {
        uni.navigateTo({
          url: '/pages/login/login',
        });
      } else {
        switch (type) {
          case 'gonggao':
            uni.navigateTo({
              url: '/subPackages/envelope/index',
            });
            break;
          case 'intergral':
            uni.navigateTo({
              url: '/pages/intergral/shop/index',
            });
            break;
          case 'active':
            console.log('212');
            uni.navigateTo({
              url: '/subPackages/envelopeActive/index',
            });
            break;
          // 指导视频
          case 'instructVideo':
            uni.navigateTo({
              url: '/pages/basic/instructVideo/index',
            });
            break;
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
page {
  background: #f6f7f9;
}
.banner {
  padding: 20rpx 20rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  z-index: 2;
  box-sizing: border-box;
}

.banner-title {
  font-family: 'PingFang SC';
  font-size: 36rpx;
  font-style: normal;
  font-weight: 500;
  flex: 1;
  text-indent: 4rpx;
  color: #000000;
  text-align: center;
  font-weight: 700;
}
.mine-bg {
  position: fixed;
  top: 0;
  left: 0;
  top: 0;
  height: 100vh;
  width: 100%;
  z-index: -1;
}
.card-wrap {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding-top: 140rpx;
  justify-content: space-between;
  gap: 15rpx;
  grid-gap: 15rpx;
  padding: 20rpx;
  .card-title {
    font-family: PingFang SC;
    font-size: 36rpx;
    font-weight: normal;
    letter-spacing: 0px;
    color: #ffffff;
    position: absolute;
    left: 15px;
    top: 24rpx;
  }
  .card-btn {
    position: absolute;
    left: 15px;
    bottom: 24rpx;
    font-size: 26rpx;
    line-height: 40rpx;
    text-align: center;
    width: 106rpx;
    height: 40rpx;
    border-radius: 10rpx;
    opacity: 1;
    background: #ffffff;
    box-shadow: inset 0px 0px 4px 0px rgba(0, 0, 0, 0.06);
  }
  .card-item {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 345rpx;
    height: 436rpx;
    .card-item-box {
      width: 100%;
      height: 215rpx;
      background-size: 100% 100%;
    }
    .bg-video {
      background-image: url('https://ndjtcs.evstyle.cn:6443/nd-front/image/bg-video.png');
    }
    .bg-shopping {
      background-image: url('https://ndjtcs.evstyle.cn:6443/nd-front/image/bg-shopping.png');
    }
  }
  .card1 {
    width: 345rpx;
    height: 436rpx;
    background-image: url('https://ndjtcs.evstyle.cn:6443/nd-front/image/bg-activity.png');
    background-size: 100% 100%;
  }
  .card2 {
    width: 100%;
    height: 206rpx;
    background-size: 100% 100%;
    padding: 0;
    background-image: url('https://ndjtcs.evstyle.cn:6443/nd-front/image/bg-notice.png');
    .title {
      font-family: HuXiaoBo-NanShen;
      font-size: 28px;
      font-weight: normal;
      line-height: 32.76px;
      letter-spacing: 4rpx;
      color: #ffffff;
      position: absolute;
      top: 64rpx;
      left: 136rpx;
    }
    .tip {
      font-family: Alibaba PuHuiTi;
      font-size: 16rpx;
      font-weight: normal;
      color: #ffffff;
      bottom: 52rpx;
      position: absolute;
      left: 138rpx;
    }
  }
  .card {
    position: relative;
  }
}
</style>
