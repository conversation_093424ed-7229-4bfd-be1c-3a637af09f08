<template>
  <view>
    <view class="searchLetter touchClass">
      <view
        :style="'height:' + itemH + 'px'"
        :data-letter="item.name"
        @touchstart.stop.prevent="searchStart"
        @touchmove.stop.prevent="searchMove"
        @touchend.stop.prevent="searchEnd"
        v-for="(item, index) in searchLetter"
        :key="index"
      >
        {{ item.name }}
      </view>
    </view>

    <block v-if="isShowLetter">
      <view class="showSlectedLetter">
        {{ showLetter }}
      </view>
    </block>
    <!-- 一级品牌 -->
    <scroll-view :scroll-y="true" :style="'height:' + winHeight + 'px'" @scroll="bindScroll" :scroll-top="scrollTop">
      <view class="selection" v-for="(item, index) in cityList" :key="item.shou_zm">
        <view :data-msg="item" class="item_letter">{{ item.shou_zm }}</view>

        <view
          @tap="jump"
          class="item_city"
          :data-city="ct.brandName"
          v-for="(ct, index1) in item.cityInfo"
          :key="ct.id"
        >
          <label class="car_name">{{ ct.brandName }}</label>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getCity, getGeocoderLocation } from '@/services/index';
export default {
  data() {
    return {
      cityCup: [],
      searchLetter: [],
      showLetter: '',
      winHeight: 0,
      tHeight: 0,
      bHeight: 0,
      msg: [],
      startPageY: 0,
      cityList: [],
      isShowLetter: false,
      scrollTop: 0,
      three_none: 'none',
      two_none: 'none',
      car_series: [],
      car_ser: null,

      //城市检索的首字母
      searchLetter: [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'J',
        'K',
        'L',
        'M',
        'N',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'W',
        'X',
        'Y',
        'Z',
      ],

      // 三级
      car_vend_ser: ['阿波罗', '宝马'],

      cityObj: [],

      shuju: {
        car_name: [],
      },

      car_pinpai: {
        pinpai: null,
      },

      city: '',
      currentTab: '',

      ct: {
        brandName: '',
        id: '',
      },
    };
  },
  mixins: [],
  props: {
    paramDate: String, // 简化的定义方式
  },
  // 私有数据，可用于模版渲染
  shuju: {
    car_name: [],
  },
  car_pinpai: {
    pinpai: null,
  },
  mounted() {
    // 处理小程序 attached 生命周期
    this.attached();
    // 处理小程序 ready 生命周期
    this.$nextTick(() => this.ready());
  },
  moved() {},
  destroyed() {},
  methods: {
    handlePageShow() {
      this.oppen();
    },

    // 此处attached的声明会被lifetimes字段中的声明覆盖
    // ready () {
    //   var that = this;
    //   uni.request({
    //     url: anthodsDate + '/api/v0.1/citys?cityName=',
    //     data: {},
    //     method: 'GET',

    //     header: {
    //       'content-type': 'application/json',
    //     },

    //     success: (res) => {
    //       that.setData({
    //         msg: res.data.queryCityList,
    //       });
    //       that.cityObj = res.data.queryCityList;
    //       uni.setStorageSync('key', that.cityObj);
    //       that.shuju.car_name = that.cityObj;
    //       var searchLetter = that.searchLetter;
    //       var cityList = that.cityListFun();
    //       var sysInfo = uni.getSystemInfoSync();
    //       var winHeight = sysInfo.windowHeight;

    //       //添加要匹配的字母范围值
    //       //1、更加屏幕高度设置子元素的高度
    //       var itemH = winHeight / searchLetter.length;
    //       var tempObj = [];
    //       for (var i = 0; i < searchLetter.length; i++) {
    //         var temp = {};
    //         temp.name = searchLetter[i];
    //         temp.tHeight = i * itemH;
    //         temp.bHeight = (i + 1) * itemH;
    //         tempObj.push(temp);
    //       }
    //       that.setData({
    //         winHeight: winHeight,
    //         itemH: itemH,
    //         searchLetter: tempObj,
    //         cityCup: res.data.queryCityList,
    //         cityList: cityList,
    //       });
    //     },

    //     fail: (err) => {},
    //   });
    // },
    async ready() {
      var that = this;
      const [, res] = await getCity({ cityName: '' });
      console.log(res, 'res');
      if (res) {
        that.setData({
          msg: res.queryCityList,
        });
        that.cityObj = res.queryCityList;
        uni.setStorageSync('key', that.cityObj);
        that.shuju.car_name = that.cityObj;
        var searchLetter = that.searchLetter;
        var cityList = that.cityListFun();
        var sysInfo = uni.getSystemInfoSync();
        var winHeight = sysInfo.windowHeight;

        //添加要匹配的字母范围值
        //1、更加屏幕高度设置子元素的高度
        var itemH = winHeight / searchLetter.length;
        var tempObj = [];
        for (var i = 0; i < searchLetter.length; i++) {
          var temp = {};
          temp.name = searchLetter[i];
          temp.tHeight = i * itemH;
          temp.bHeight = (i + 1) * itemH;
          tempObj.push(temp);
        }
        that.setData({
          winHeight: winHeight,
          itemH: itemH,
          searchLetter: tempObj,
          cityCup: res.queryCityList,
          cityList: cityList,
        });
      }
    },

    // 生命周期函数，可以为函数，或一个在methods段中定义的方法名
    attached() {},

    // 生命周期函数，可以为函数，或一个在methods段中定义的方法名
    attached() {},

    oppen() {},

    async jump(e) {
      var that = this;
      const [, res] = await getGeocoderLocation({ city: e.currentTarget.dataset.city });
      if (res) {
        that.$emit('mycity', {
          detail: {
            address: e.currentTarget.dataset.city,
            lon: res.data.result.location.lng,
            lat: res.data.result.location.lat,
          },
        });
      } else {
        that.setData({
          city: '定位失败',
        });
      }
    },

    searchStart(e) {
      var showLetter = e.currentTarget.dataset.letter;
      var pageY = e.touches[0].pageY;
      this.setScrollTop(this, showLetter);
      this.nowLetter(pageY, this);
      this.setData({
        showLetter: showLetter,
        startPageY: pageY,
        isShowLetter: true,
      });
    },

    searchMove(e) {
      var pageY = e.touches[0].pageY;
      var startPageY = this.startPageY;
      var tHeight = this.tHeight;
      var bHeight = this.bHeight;
      if (startPageY - pageY > 0) {
        //向上移动
        if (pageY < tHeight) {
          // showLetter=this.mateLetter(pageY,this);
          this.nowLetter(pageY, this);
        }
      } else {
        //向下移动
        if (pageY > bHeight) {
          // showLetter=this.mateLetter(pageY,this);
          this.nowLetter(pageY, this);
        }
      }
    },

    searchEnd(e) {
      // var showLetter=e.currentTarget.dataset.letter;
      var that = this;
      setTimeout(function () {
        that.setData({
          isShowLetter: false,
        });
      }, 1000);
    },

    nowLetter(pageY, that) {
      //当前选中的信息
      var letterData = this.searchLetter;
      var bHeight = 0;
      var tHeight = 0;
      var showLetter = '';
      for (var i = 0; i < letterData.length; i++) {
        if (letterData[i].tHeight <= pageY && pageY <= letterData[i].bHeight) {
          bHeight = letterData[i].bHeight;
          tHeight = letterData[i].tHeight;
          showLetter = letterData[i].name;
          break;
        }
      }
      this.setScrollTop(that, showLetter);
      that.setData({
        bHeight: bHeight,
        tHeight: tHeight,
        showLetter: showLetter,
        startPageY: pageY,
      });
    },

    bindScroll(e) {},

    setScrollTop(that, showLetter) {
      var scrollTop = 0;
      var cityList = that.data.cityList;
      var cityCount = 0;
      var initialCount = 0;
      for (var i = 0; i < cityList.length; i++) {
        if (showLetter == cityList[i].shou_zm) {
          scrollTop = initialCount * 30 + cityCount * 41;
          break;
        } else {
          initialCount++;
          cityCount += cityList[i].cityInfo.length;
        }
      }
      that.setData({
        scrollTop: scrollTop,
      });
    },

    btn_sel_name(e) {
      var that = this;
      var sel_name = e.currentTarget.dataset.item;
      var model = that.car_pinpai.pinpai.concat(that.car_ser, sel_name);
      uni.redirectTo({
        url: '../daiban/daiban?model=' + model,
      });
    },

    cityListFun() {
      var that = this;
      var searchLetter = that.searchLetter;
      var tempObj = [];
      searchLetter.forEach((shou_zm) => {
        var cityInfo = [];
        that.shuju.car_name.forEach((item) => {
          if (shou_zm == item.firstRegionName.charAt(0)) {
            cityInfo.push({
              brandName: item.areaName,
              brandImg: item.brandImg,
              brandId: item.areaCode,
            });
          }
        });
        tempObj.push({
          shou_zm,
          cityInfo,
        });
      });
      return tempObj;
    },

    searchLetterFun() {
      return searchLetter;
    },

    opop(e) {},

    swichNav(e) {
      var that = this;
      if (this.currentTab === e.target.dataset.current) {
        return false;
      } else {
        that.setData({
          currentTab: e.target.dataset.current,
        });
      }
    },

    myPrivateMethodFun() {},
    propertyChangeFun(newVal, oldVal) {},

    goDetail(e) {
      let infoId = e.currentTarget.dataset.id;
      let title = e.currentTarget.dataset.title;
      uni.navigateTo({
        url: '/pages/orderDetail/orderDetail?infoId=' + infoId + '&title=' + title,
      });
    },

    getProgram(e) {},
  },
  created() {},
};
</script>
<style>
@import './city.css';
</style>
