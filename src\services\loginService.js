import { Ajax } from '../utils/Ajax/index';
import { MD5 } from '../utils/crypto';
import TripleDES from '../utils/TripleDES/index';
import { BASE_URL, ORG_CODE, LOGIN_CHANNEL } from '../config/global';

/**
 * loginChannel h5渠道号
 */
export const initCodeApi = async (params) => {
  return Ajax({
    url: '/api/open/v0.1/initCode',
    method: 'GET',
    data: {
      loginChannel: LOGIN_CHANNEL,
    },
  });
};

/**
 * mobile	必填	手机号码
oaccNo	选填	第三方登录账号
loginType	必填	登录类型02短信03第三方
loginChannel	必填	登录渠道01
verifyCode	必填	验证码
*/
export const loginApi = async (params) => {
  return Ajax({
    url: '/cst/api/v0.4/login',
    method: 'POST',
    data: params,
  });
};

// export const WXloginApi = async (params) => {
//     return Ajax({
//         url: '/api/open/v0.1/login',
//         method: "POST",
//         data: {
//             loginChannel: '07',
//             otherType: '05',
//             orgCode: ORG_CODE,
//             encryptedData: params.encryptedData,
//             iv: params.iv
//         }
//     })
// }

let getNewToken = null;
export const checkRefreshTokenApi = async (params) => {
  console.log('刷新的入口：', params);
  if (getNewToken) {
    return getNewToken;
  }
  getNewToken = new Promise(async (resolve, reject) => {
    const durl = /^http(s?):\/\/([^\/]+)/i;
    const domain = BASE_URL.match(durl);

    let a = Math.floor(Math.random() * 10);
    let b = Math.floor(Math.random() * 10);
    let c = Math.floor(Math.random() * 10);
    let time = new Date().getTime();

    const endTime = `${time}${a}${b}${c}`;
    let appString = `${a}token${b}${params.refreshToken}${c}${endTime}`;
    console.log('刷新appString', appString);
    const md5result = MD5(appString).toString();
    const sign = TripleDES.encrypt(md5result);
    console.log('刷新sign', sign);
    // console.log(333, domain[0])
    try {
      const info = await Ajax({
        url: '/cst/api/v0.2/tokens',
        method: 'POST',
        data: {
          //   token: params.token,
          refreshToken: params.refreshToken,
          //   orgCode: ORG_CODE,
          sign: sign,
          timestamp: endTime,
        },
      });
      console.log('刷新结果：', info);
      resolve(info);
    } catch (error) {
      reject(error);
    } finally {
      getNewToken = null;
    }
  });
  return getNewToken;
};

/**
 * mobile	必填	用户手机号
sign	必填	签名
timestamp	必填	时间戳
verifyType	必填	短信类型
*/
export const getSMSApi = async (mobile) => {
  let a = Math.floor(Math.random() * 10);
  let b = Math.floor(Math.random() * 10);
  let c = Math.floor(Math.random() * 10);
  let time = new Date().getTime();

  const endTime = `${time}${a}${b}${c}`;
  let appString = `${a}mobile${b}${mobile}${c}${endTime}`;

  const md5result = MD5(appString).toString();
  const sign = TripleDES.encrypt(md5result);
  return Ajax({
    url: '/api/open/v0.1/get-sms',
    method: 'POST',
    data: {
      mobile: mobile,
      sign: sign,
      timestamp: endTime,
      verifyType: '01',
    },
  });
};
