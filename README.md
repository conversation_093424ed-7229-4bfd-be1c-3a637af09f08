# h5-project

## Project setup

```
yarn install
```

### Compiles and hot-reloads for development

```
yarn run serve
```

### Compiles and minifies for production

```
yarn run build
```

### Run your tests

```
yarn run test
```

### Lints and fixes files

```
yarn run lint
```

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).

### 运行至小程序开发工具 npm run dev:mp-weixin

### Structural catalogue 结构目录

home.vue 首页
station.vue 站点详情
set.vue 设置
settle.vue 充电结算
transaction.vue 下单
charge.vue 充电
list.vue 站点列表

### 运行 H5

```
npm run dev:h5
```

### 编译 H5

```
npm run build:h5
```

### 采用 uni-app

```
采用vue的生命周期/小程序的语法
具体语法见官网文档
https://uniapp.dcloud.io/component/README
```

宁德 H5 工程
