<template>
  <div class="wrap">
    <!-- #ifdef MP-WEIXIN -->
    <div class="nav-bar" :style="'padding-top:' + s_top + 'rpx;line-height:' + s_height + 'rpx'">
      <image src="https://ndjtcs.evstyle.cn:6443/nd-front/image/leftArrow.png" mode="scaleToFill" @click="back" />
      <div class="text" @click="back">节能勋章</div>
    </div>
    <!-- #endif -->
    <div class="card">
      <div class="jb-wrap">
        <image :src="iconTrophy" mode="scaleToFill" />
      </div>
      <div class="text-wrap">
        <div class="text-item" v-for="(item, index) in list" :key="index">
          <div class="icon">
            <image :src="item.src" mode="scaleToFill" />
          </div>
          <div class="text">
            <div class="num">{{ item.num }}</div>
            <div class="unit">{{ item.unit }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCutInfo } from '@/services/index.js';
export default {
  data() {
    return {
      s_top: '',
      s_height: '',
      info: {
        chcPq: '-',
        carbonTotal: '-',
        gasolineAmount: '-',
      },
      iconTrophy: 'https://ndjtcs.evstyle.cn:6443/nd-front/image/icon-trophy.png',
    };
  },
  mounted() {
    // #ifdef MP
    this.initTopImg();
    // #endif
    this.initData();
  },
  computed: {
    bgImg() {
      return this.IMG_PATH + 'medal-jb.png';
    },
    list() {
      return [
        {
          unit: '累计充电量（KWh）',
          num: this.info.chcPq,
          src: 'https://ndjtcs.evstyle.cn:6443/nd-front/image/icon-charge.png',
        },
        {
          unit: '减少CO2排放量(kg)',
          num: this.info.carbonTotal,
          src: 'https://ndjtcs.evstyle.cn:6443/nd-front/image/icon-co2.png',
        },
        {
          unit: '节省燃油使用量(L汽油)',
          num: this.info.gasolineAmount,
          src: 'https://ndjtcs.evstyle.cn:6443/nd-front/image/icon-gas-station.png',
        },
      ];
    },
  },
  methods: {
    initTopImg() {
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      console.log(menuButtonInfo, 'menuButtonInfo');
      this.setData({
        s_top: menuButtonInfo?.top * 2,
        s_height: menuButtonInfo?.height * 2,
      });
    },
    back() {
      console.log('chufa1');
      uni.navigateBack({ delta: -1 });
    },
    async initData() {
      const _this = this;
      const [, res] = await getCutInfo();
      if (res && res.ret == '200') {
        _this.info = res;
      } else {
        _this.info = {};
      }
    },
  },
};
</script>

<style scoped lang="scss">
.wrap {
  width: 100vw;
  height: 100vh;
  overflow: auto;
  background: linear-gradient(180deg, #b9def2 18rpx, rgba(185, 222, 242, 0) 342rpx);
  display: flex;
  flex-direction: column;
  image {
    width: 80%;
    height: 660rpx;
  }
  .nav-bar {
    width: 100%;
    height: 120rpx;
    display: flex;
    padding-top: 25rpx;
    padding-left: 30rpx;
    align-items: center;
    .text {
      margin-left: 30%;
      font-size: 36rpx;
      font-weight: 500;
      line-height: 40px;
      letter-spacing: 0px;
      font-weight: 600;
      color: #000000;
    }
    image {
      width: 30rpx;
      height: 45rpx;
      margin-right: 15rpx;
    }
  }
  .card {
    margin: 40rpx auto;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.16) 0%, #ffffff 100%);
    box-sizing: border-box;
    border: 2px solid;
    // border-image: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%) 2;
    border-color: #fff;
    backdrop-filter: blur(10px);
    width: 702rpx;
    max-height: 1100rpx;
    flex: 1;
    border-radius: 24rpx;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .jb-wrap {
      width: 100%;
      height: 400rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      image {
        width: 370rpx;
        height: 370rpx;
      }
    }
    .text-wrap {
      width: 100%;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      .text-item {
        margin-bottom: 30rpx;
        width: 90%;
        height: 150rpx;
        display: flex;
        flex-direction: row;
        padding: 0 20rpx;
        background: rgba(118, 203, 239, 0.1);
        border-radius: 20rpx;
        overflow: hidden;
        .text {
          padding: 0 15rpx 0 30rpx;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: center;
          .num {
            font-size: 48rpx;
            font-weight: bold;
            line-height: normal;
            letter-spacing: 0px;
            font-variation-settings: 'opsz' auto;
            font-feature-settings: 'kern' on;
            color: #333333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .unit {
            font-size: 26rpx;
            font-weight: normal;
            line-height: normal;
            text-align: center;
            letter-spacing: 0em;
            font-variation-settings: 'opsz' auto;
            color: rgba(0, 0, 0, 0.7);
            z-index: 1;
          }
        }
        .icon {
          width: 100rpx;
          height: 100%;
          display: flex;
          display: flex;
          justify-content: center;
          align-items: center;
          image {
            width: 88rpx;
            height: 88rpx;
          }
        }
      }
    }
  }
}
</style>
