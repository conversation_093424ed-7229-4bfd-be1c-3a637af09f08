.search-head {
  width: 100%;
  height: 88rpx;
  background: #fff;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
  padding-left: 0;
}
.search-head input {
  width: 592rpx;
  height: 66rpx;
  border-radius: 8rpx;
  border: 1rpx solid #ccc;
  font-size: 28rpx;
  padding-left: 20rpx;
  padding-right: 20rpx;
  box-sizing: border-box;
}
.search-head .icon {
  line-height: 88rpx;
  font-size: 53rpx;
  color: #666;
  padding-left: 15rpx;
}
.search-head .search-lefy {
  padding-left: 20rpx;
  font-size: 42rpx;
  padding-right: 5rpx;
  padding-bottom: 10rpx;
}
.search-middle {
  background: #fff;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}
.search-middle .search-add {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333;
  font-size: 26rpx;
  height: 88rpx;
  box-sizing: border-box;
  padding-right: 30rpx;
  margin-left: 30rpx;
  /* border-bottom: 1rpx solid #CBCBCB */
}
.search-middle .search-msg {
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #999;
}
.search-middle .search-push {
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 20rpx 30rpx 20rpx 30rpx;
}
.search-middle .search-push text {
  font-size: 26rpx;
  color: #666;
  margin-right: 30rpx;
  line-height: 41rpx;
}
.search-middle .add-head-bull {
  width: 7rpx;
  height: 28rpx;
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 3rpx;
  margin-right: 12rpx;
}
.search-list .list_li {
  height: 120rpx;
  margin-left: 30rpx;
  padding-right: 30rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
  color: #666;
  border-top: 1rpx solid #cbcbcb;
}
.search-list .list_li .list_li_lf {
  width: 50%;
  display: flex;
  flex-wrap: wrap;
  align-content: space-around;
  box-sizing: border-box;
  padding: 15rpx 0;
}
.search-list .list_li .list_li_lf view {
  width: 100%;
  font-size: 28rpx;
  color: #333;
}
.search-list .list_li .list_li_lf text {
  margin-left: 10rpx;
}
.search-list .list_li .list_li_lf .samlls {
  font-size: 26rpx;
  color: #666;
}
/* .search-list .list_li view{
  width: 50%;
  display: flex;
  align-items: center;
  height: 100%;
} */
.search-list .list_li .list_li_rt {
  width: 40%;
  line-height: 120rpx;
  text-align: right;
  font-size: 24rpx;
  color: #666;
}
.shCar {
  width: 100%;
  background: #fff;
}
.shCar .shCar_list {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 10rpx 30rpx;
  font-size: 30rpx;
}
.shCar .shCar_list icon {
  font-size: 55rpx;
}
.shCar .shCar_list .icon-checked {
  color: #2196f3;
}
