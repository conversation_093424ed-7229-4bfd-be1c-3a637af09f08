<template>
  <div class="wrap">
    <!-- #ifdef MP-WEIXIN -->
    <view
      class="banner"
      :style="'padding-top:' + s_top + 'rpx;height:' + s_height + 'rpx;line-height:' + s_height + 'rpx'"
    >
      <image class="banner-img" @tap="goBack" mode="widthFix" :src="`${IMG_PATH}back.png`"></image>
      <view class="banner-title">购买PLUS会员</view>
    </view>
    <!-- #endif -->

    <scroll-view
      scroll-y="true"
      refresher-enabled="true"
      :refresher-triggered="triggered"
      :refresher-threshold="100"
      refresher-background="rgba(0,0,0,0)"
      :show-scrollbar="false"
      @refresherrefresh="onRefresh"
      @refresherrestore="onRestore"
      class="content"
    >
      <div class="user-card" :style="{ backgroundImage: `url(${IMG_PATH}member/user-card-bg.png)` }">
        <div class="user-logo">
          <image :src="userInfo.imgUrl || `${IMG_PATH}defaultAvatar.png`" />
        </div>
        <div class="user-info">
          <div class="phone">{{ desensitization(userInfo.mobile || '-') }}</div>
          <div class="time" v-if="isMember">{{ memberInfo.vipExpireTime || '-' }}到期</div>
          <div class="vip-level" v-if="isMember">
            <div class="bg-text" @click="toAccumulate">
              累计节省<span>{{ saveMoney }}</span
              >元
            </div>
          </div>
        </div>
        <div class="user-btn">
          <div class="btn" @click="toPay">
            {{ btnText }}
            <div
              class="btn-tip"
              v-if="isMember && showOverIcon"
              :style="{ backgroundImage: `url(${IMG_PATH}member/become-due-bg.png)` }"
            ></div>
          </div>
          <div class="record-btn" @click="toOpenRecord">
            开通记录
            <image class="banner-img" :src="`${IMG_PATH}member/record-back.png`"></image>
          </div>
        </div>
      </div>
      <div class="box">
        <div class="title-bg">
          <image class="title-bg" :src="`${IMG_PATH}member/interests-text.png`" mode="scaleToFill" />
        </div>
        <div class="member-type-tab">
          <div
            class="member-type-btn"
            :class="{ active: memberType === 0 }"
            @click="changeMemeberType(0)"
            :style="{ backgroundImage: `url(${IMG_PATH}/normal-member.png)` }"
          ></div>
          <div
            class="member-type-btn"
            :class="{ active: memberType === 1 }"
            @click="changeMemeberType(1)"
            :style="{ backgroundImage: `url(${IMG_PATH}/plus-member.png)` }"
          ></div>
        </div>
        <div class="member-card" :style="{ backgroundImage: `url(${IMG_PATH}member/member-card-bg.png)` }">
          <div class="top-member" :style="{ backgroundImage: `url(${IMG_PATH}member/money-card-bg.png)` }">
            <div class="title">
              充电享会员价月省 <span>{{ maxAmt }}</span> 元
              <div class="query-icon">
                ?
                <div class="query-icon-popup">最高月省按照每个月充电{{ vipConfig.monthDegreeConfig || '' }}度计算</div>
              </div>
            </div>

            <div class="bg-text">
              单单省钱
              <template v-if="maxCoupon && maxCoupon != 0"> , 单次最高抵扣{{ maxCoupon }}元 </template>
            </div>
          </div>

          <div class="member-card-wrap">
            <div
              class="member-card-item"
              v-for="(item, index) in memberList"
              :key="index"
              v-show="item.show"
              @click="toInviolable(item)"
              :style="{ backgroundImage: `url(${IMG_PATH}member/member-gift-bg.png)` }"
            >
              <div class="title">
                <image :src="item.icon" mode="widthFix" />
                {{ item.title || '-' }}
              </div>
              <div class="text">
                <div class="text-item" v-for="(int, ind) in item.text" :key="ind">{{ int }}</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 省钱攻略 -->
        <template v-if="memberInfo.vipFlag == '0'">
          <div class="title-bg">
            <image class="title-bg" :src="`${IMG_PATH}member/save-money-text.png`" mode="scaleToFill" />
          </div>
          <div class="table-wrap">
            <div class="table-bg"></div>
            <div class="table-box">
              <div class="th-wrap">
                <div class="th-item">充电度数(度)</div>
                <div class="th-item">充电频次(次)</div>
                <div class="th-item">用户可省(元)</div>
              </div>
              <div class="th-wrap" v-for="(item, index) in tableData" :key="index">
                <div class="th-item num">{{ item.chargeDegree || '-' }}</div>
                <div class="th-item num">{{ item.chargeFrequency || '-' }}</div>
                <div class="th-item num">{{ item.saveMoney || '-' }}1</div>
              </div>
            </div>
          </div>
        </template>
        <!-- 会员帮助/说明 -->
        <template v-if="memberInfo.vipFlag == '1'">
          <div class="title-bg">
            <image class="title-bg" :src="`${IMG_PATH}member/help-text.png`" mode="scaleToFill" />
          </div>

          <div class="table-wrap">
            <div class="table-bg"></div>
            <div class="table-card-item" @click="tabCallback(item)" v-for="(item, index) in tableCardList" :key="index">
              <image :src="item.icon" class="icon" mode="widthFix" />
              {{ item.title }}
              <image class="right-icon" :src="`${IMG_PATH}member/member-icon/icon-20.png`" mode="widthFix" />
            </div>
          </div>
        </template>
        <image class="tip-bg" :src="`${IMG_PATH}member/tip-text.png`" mode="widthFix" />
        <div class="tip-text-wrap">
          <div class="title">
            当前会员版本月限定优惠次数{{ saveInfo.limitNum || 0 }}次，会员场站单次充电最大额度{{
              saveInfo.limitAmt || 0
            }}元。具体省钱金额根据电站、时段、电量等因素酌情增减。
          </div>
          <div class="tip">公式：省钱金额=折扣省钱金额+整单直减券+充电满减券-包月费用</div>
        </div>
        <div class="tip-text-wrap" v-if="selectVipType == '04' || selectVipType == '08'" style="margin-top: 20rpx">
          <div class="title">
            {{ vipAutoFeeDesc || '-' }}
          </div>
        </div>
      </div>
    </scroll-view>
    <div class="bottom-wrap">
      <scroll-view class="money-wrap" :scroll-x="true" :show-scrollbar="false" v-if="!isAutoRenewing">
        <view class="money-wrap-box">
          <view
            class="money-item"
            v-for="(item, index) in filteredMenuList"
            :key="index"
            @click="choosePrice(item)"
            :style="{
              backgroundImage: `url(${IMG_PATH}member/${
                selectVipPage.vipId == item.vipId ? 'deposit-money-active-bg' : 'deposit-money-bg'
              }.png)`,
            }"
          >
            <div class="title">
              {{ getVipTypeLabel(item.vipType) || item.vipTypeName || '--' }}
            </div>
            <div class="money">
              <template v-if="isNewUserPrice && (item.vipType === '04' || item.vipType === '08')">
                <span class="new-price">￥ {{ item.vipPriceNew }}</span>
                <span class="old-price">￥ {{ item.vipPriceOld }}</span>
              </template>
              <template v-else-if="isNewUserPrice">
                <span class="new-price">￥ {{ item.vipPriceNew }}</span>
              </template>
              <template v-else> ￥ {{ item.vipPriceOld }} </template>
            </div>
            <div class="description">{{ item.vipDesc || '' }}</div>
            <div
              class="new-icon"
              v-if="isNewUserPrice"
              :style="{ backgroundImage: `url(${IMG_PATH}member/customers-bg.png)` }"
            ></div>
          </view>
        </view>
      </scroll-view>
      <div class="btn" :class="{ 'btn-disabled': isAutoRenewing }" @click="chooseChannel">
        <template v-if="isAutoRenewing">自动续费中</template>
        <template v-else-if="isMember">立即续费</template>
        <template v-else>
          <div class="btn-content">
            <div class="btn-price-section">
              <template v-if="isNewUserPrice && (selectVipPage.vipType === '04' || selectVipPage.vipType === '08')">
                <span class="btn-new-price">￥ {{ selectVipPage.vipPriceNew || '' }}</span>
                <span class="btn-old-price">￥ {{ selectVipPage.vipPriceOld || '' }}</span>
              </template>
              <template v-else-if="isNewUserPrice">
                <span class="btn-new-price">￥ {{ selectVipPage.vipPriceNew || '' }}</span>
              </template>
              <template v-else>
                <span class="btn-new-price">￥ {{ selectVipPage.price || '' }}</span>
              </template>
            </div>
            <div class="btn-text-section">
              <span class="btn-action-text">立即开通</span>
              <span v-if="saveMoney > 0" class="btn-save-text">(已省{{ saveMoney || 0 }}元)</span>
            </div>
          </div>
        </template>
      </div>
      +
      <div class="agreement-tip" v-if="!isAutoRenewing">
        开通前请先阅读
        <span class="agreement" @click="openProtocol('member')">《宁德会员服务协议》</span>
        <span class="agreement" @click="openProtocol('renew')">《自动续费服务协议》</span>
      </div>
    </div>
    <Pay ref="Pay" :selectVipPage="selectVipPage" :memberInfo="memberInfo" @reset="loadData"> </Pay>

    <!-- 续费管理弹窗组件 -->
    <RenewPopup
      v-if="showRenewPopup"
      :memberType="memberType"
      :isAutoRenewing="isAutoRenewing"
      @close="hideRenewDialog"
      @confirm="openAutoRenew"
      :vipAutoFeeDesc="vipAutoFeeDesc"
    />

    <!-- 立即续费协议确认弹窗 -->
    <AgreementModal
      :visible="showRenewAgreementModal"
      @confirm="handleRenewAgreementConfirm"
      @cancel="handleRenewAgreementCancel"
    />
  </div>
</template>

<script>
import Pay from '@/components/toPay/index.vue';
import RenewPopup from './RenewPopup/index.vue';
import AgreementModal from './RenewPopup/AgreementModal.vue';
import { mapState } from 'vuex';
import { desensitization } from '@/utils/util.js';
import { makePhoneCall } from '@/utils/index.js';
import { callNativeAutoRenew } from '@/utils/bridge/index.js';
import {
  getMemberInfo,
  getConfigDesc,
  getConfigOption,
  getConfig,
  saveMoneyStrategy,
  getUserSaveRecord,
  recharge,
  getIndData,
} from '@/services/index.js';
import moment from 'moment';

// 枚举常量
const MEMBER_TYPE = {
  PLUS: { label: 'PLUS会员', value: 0 },
  PREMIUM: { label: '尊享版PLUS会员', value: 1 },
};

const VIP_TYPE_CONFIG = {
  '01': { label: 'PLUS会员月卡', shortLabel: '月卡', memberType: MEMBER_TYPE.PLUS.value },
  '02': { label: 'PLUS会员季卡', shortLabel: '季卡', memberType: MEMBER_TYPE.PLUS.value },
  '03': { label: 'PLUS会员年卡', shortLabel: '年卡', memberType: MEMBER_TYPE.PLUS.value },
  '04': { label: 'PLUS会员连续包月', shortLabel: '连续包月', memberType: MEMBER_TYPE.PLUS.value },
  '05': { label: '尊享版PLUS会员月卡', shortLabel: '月卡(尊享版)', memberType: MEMBER_TYPE.PREMIUM.value },
  '06': { label: '尊享版PLUS会员季卡', shortLabel: '季卡(尊享版)', memberType: MEMBER_TYPE.PREMIUM.value },
  '07': { label: '尊享版PLUS会员年卡', shortLabel: '年卡(尊享版)', memberType: MEMBER_TYPE.PREMIUM.value },
  '08': { label: '尊享版PLUS会员连续包月', shortLabel: '连续包月(尊享版)', memberType: MEMBER_TYPE.PREMIUM.value },
};

const CHANNEL_MAP = {
  1: { label: '支付宝', value: '1302' },
  2: { label: '微信支付', value: '1303' },
  3: { label: '银联支付', value: '1301' },
};

const VIP_FLAG = {
  NON_MEMBER: { label: '非会员', value: '0' },
  MEMBER: { label: '会员', value: '1' },
};

const PRICE_TYPE = {
  OLD: { label: '老用户价格', value: 'old' },
  NEW: { label: '新用户价格', value: 'new' },
};

const AUTO_RENEW_STATUS = {
  DISABLED: { label: '未开启自动续费', value: '0' },
  ENABLED: { label: '已开启自动续费', value: '1' },
};
export default {
  props: {},
  components: { Pay, RenewPopup, AgreementModal },
  data() {
    return {
      s_top: 0,
      s_height: 0,
      tableData: [],
      saveInfo: {},
      memberInfo: {},
      serviceType: 'member',
      content: '',
      menuList: [],
      selectVipId: '',
      expireWarnDay: '',
      // 卡片配置
      benefitList: [],
      onlyMaxDeduction: '',
      telephone: '',
      vipConfig: {},
      maxAmt: 0,
      // 优惠券
      cashCoupon: [],
      // 折扣券
      discountCoupon: [],
      saveMoney: 0,
      maxCoupon: 0,
      triggered: 'restore',
      freshing: false,
      workUrls: [],
      fileList1: [],
      memberType: 0,
      selectVipType: '',
      // 续费管理弹窗控制
      showRenewPopup: false,
      vipAutoFeeDesc: '',
      // 立即续费协议确认弹窗控制
      showRenewAgreementModal: false,
      // 自动续费状态跟踪
      isWaitingAutoRenewResult: false, // 是否正在等待自动续费结果
      autoRenewCheckTime: null, // 记录跳转云闪付的时间
    };
  },

  async onLoad(options) {
    // 初始化配置数据
    await this.initConfig();

    // i宁德支付成功回调
    if (options?.payRes === 'success') {
      uni.showModal({
        title: '开通成功',
        showCancel: false,
        content: '欢迎您成为尊贵的VIP会员',
        confirmText: '我知道了',
      });
      this.$store.dispatch('login/getUserInfoCallback');
    }

    // 加载页面数据
    this.loadData();
  },
  computed: {
    ...mapState({
      userInfoOBJ: (state, getters) => getters['login/getUserInfo'],
    }),
    userInfo() {
      return {
        imgUrl: '',
        ...(this.userInfoOBJ ? this.userInfoOBJ : {}),
      };
    },
    // 是否为新用户价格
    isNewUserPrice() {
      return this.memberInfo.priceType === PRICE_TYPE.NEW.value;
    },
    // 是否为会员
    isMember() {
      return this.memberInfo.vipFlag === VIP_FLAG.MEMBER.value;
    },
    // 是否为自动续费中
    isAutoRenewing() {
      return this.isMember && this.memberInfo.ifOpenAuto === AUTO_RENEW_STATUS.ENABLED.value;
    },
    // 当前会员类型配置
    currentMemberTypeConfig() {
      return Object.values(MEMBER_TYPE).find((type) => type.value === this.memberType) || MEMBER_TYPE.PLUS;
    },
    // 获取当前会员类型对应的VIP类型列表
    currentVipTypes() {
      return Object.keys(VIP_TYPE_CONFIG).filter((vipType) => VIP_TYPE_CONFIG[vipType].memberType === this.memberType);
    },
    memberList() {
      return [
        {
          title: '现金券',
          icon: this.IMG_PATH + 'member/member-icon/icon-1.png',
          text: this.cashCoupon
            .sort((a, b) => b.cpnAmt - a.cpnAmt)
            .map((q) => {
              return q.cpnName + '*' + q.cpnNum;
            })
            .slice(0, 2),
          show: true,
        },
        {
          title: '折扣券',
          icon: this.IMG_PATH + 'member/member-icon/icon-2.png',
          text: this.discountCoupon
            .sort((a, b) => a.cpnAmt - b.cpnAmt)
            .map((q) => {
              return q.cpnName + '*' + q.cpnNum;
            })
            .slice(0, 2),
          show: true,
        },
        {
          title: '开卡礼',
          icon: this.IMG_PATH + 'member/member-icon/icon-8.png',
          text: this.vipConfig?.welcomeGift
            ? JSON.parse(this.vipConfig?.welcomeGift)
                .sort((a, b) => b.amt - a.amt)
                .map((q) => {
                  return q.couponName + '*' + q.couponNum;
                })
                .slice(0, 2)
            : [],
          show: true,
        },
        {
          title: '积分兑换',
          icon: this.IMG_PATH + 'member/member-icon/icon-3.png',
          text: [this.vipConfig?.isExchangeExplain || '-'],
          show: this.vipConfig?.isExchange == '1',
        },
        {
          title: '双倍积分',
          icon: this.IMG_PATH + 'member/member-icon/icon-4.png',
          text: [this.vipConfig?.isDoubleExplain || '-'],
          show: this.vipConfig?.isDouble == '1',
        },
        {
          title: '专属客服',
          icon: this.IMG_PATH + 'member/member-icon/icon-5.png',
          text: [this.vipConfig?.isExclusiveCustomerExplain || '-'],
          show: this.vipConfig?.isExclusiveCustomer == '1',
        },
        {
          title: '升级礼包',
          icon: this.IMG_PATH + 'member/member-icon/icon-6.png',
          text: [this.vipConfig?.isUpgradePackageExplain || '-'],
          show: this.vipConfig?.isUpgradePackage == '1',
        },
        {
          title: '会员场站',
          icon: this.IMG_PATH + 'member/member-icon/icon-7.png',
          text: [this.vipConfig?.isMemberStationExplain || '-'],
          show: this.vipConfig?.isMemberStation == '1',
        },
        {
          title: '会员折上折',
          icon: this.IMG_PATH + 'member/member-icon/icon-9.png',
          text: [this.vipConfig?.isMemberDayAdditionalDiscountExplain || '-'],
          show: this.vipConfig?.isMemberDayAdditionalDiscount == '1',
        },
      ];
    },
    tableCardList() {
      const allItems = [
        {
          icon: this.IMG_PATH + 'member/member-icon/icon-19.png',
          title: '省钱攻略',
          path: `/pages/member/strategyGuide/index?type=${this.memberType}`,
        },
        {
          icon: this.IMG_PATH + 'member/member-icon/icon-18.png',
          title: '意见反馈',
          path: '/subPackages/feedback/index?typeName=会员&typeId=4',
        },
        {
          icon: this.IMG_PATH + 'member/member-icon/icon-17.png',
          title: 'PLUS会员协议',
          path: '/pages/setting/agreement/index',
          params: {
            code: '0412',
            title: '《PLUS会员协议》',
          },
        },
        {
          icon: this.IMG_PATH + 'member/member-icon/icon-14.png',
          title: '规则说明',
          path: '/pages/setting/agreement/index',
          params: {
            code: '0209',
            title: '《规则说明》',
          },
        },
        {
          icon: this.IMG_PATH + 'member/member-icon/icon-13.png',
          title: '续费管理',
          type: 'renew',
        },
        {
          icon: this.IMG_PATH + 'member/member-icon/icon-12.png',
          title: '开通记录',
          path: '/pages/member/openRecord/index',
        },
        {
          icon: this.IMG_PATH + 'member/member-icon/icon-11.png',
          title: '常见问题',
          path: '/subPackages/commonProblem/index' + `?type=4`,
        },
        {
          icon: this.IMG_PATH + 'member/member-icon/icon-10.png',
          title: '联系客服',
          type: 'toPhone',
        },
      ];

      // 如果不显示续费管理，过滤掉续费管理项
      if (!this.showRenewManagement) {
        return allItems.filter((item) => item.type !== 'renew');
      }

      return allItems;
    },
    btnText() {
      return this.isMember ? '立即续费' : '立即开通';
    },
    // 过滤后的套餐列表（i宁德不显示连续包月）
    filteredMenuList() {
      const appName = uni.getStorageSync('app') || '';
      if (appName === 'i宁德') {
        // i宁德不显示vipType为04和08的连续包月套餐
        return this.menuList.filter((item) => item.vipType !== '04' && item.vipType !== '08');
      }
      return this.menuList;
    },
    selectVipPage() {
      const findData = this.filteredMenuList.find((q) => q.vipId == this.selectVipId);
      if (!findData) {
        return {};
      }
      return {
        ...findData,
        price: this.isNewUserPrice ? findData.vipPriceNew : findData.vipPriceOld,
      };
    },
    showOverIcon() {
      return (
        this.isMember &&
        this.memberInfo.vipExpireTime &&
        this.isWithinDays(this.memberInfo.vipExpireTime, this.expireWarnDay) <= this.expireWarnDay
      );
    },
    // 是否显示续费管理功能（i宁德不显示）
    showRenewManagement() {
      const appName = uni.getStorageSync('app') || '';
      return appName !== 'i宁德';
    },
  },
  watch: {},

  mounted() {
    // #ifdef MP-WEIXIN
    // 监听从其他小程序返回的事件
    this.appShowHandler = (options) => {
      console.log('App Show:', options);
      // 检查是否从云闪付小程序返回
      if (options.scene === 1038 && options.referrerInfo) {
        this.handleAutoRenewCallback(options.referrerInfo.extraData);
      }
    };
    uni.onAppShow(this.appShowHandler);
    // #endif
  },
  beforeDestroy() {
    // #ifdef MP-WEIXIN
    // 移除监听器
    if (this.appShowHandler) {
      uni.offAppShow(this.appShowHandler);
    }
    // #endif
  },
  onShow() {
    // #ifdef MP
    // 小程序环境下初始化顶部图片
    this.initTopImg();
    // #endif

    // #ifdef MP-WEIXIN
    // 微信小程序检查是否需要查询自动续费结果
    this.checkAutoRenewResult();
    // #endif
  },
  methods: {
    // 初始化配置数据
    async initConfig() {
      try {
        // 单次最高抵扣
        this.onlyMaxDeduction = await this.getConfigOption('onlyMaxDeduction');
        // 客服电话
        this.telephone = await this.getConfigOption('customerTelephone');
        // 自动续费说明
        this.vipAutoFeeDesc = await this.getConfigOption('vipAutoFeeDesc');
      } catch (error) {
        console.error('初始化配置失败:', error);
      }
    },

    // 显示续费管理弹窗
    showRenewDialog() {
      this.showRenewPopup = true;
    },
    // 隐藏续费管理弹窗
    hideRenewDialog() {
      this.showRenewPopup = false;
    },
    // 开通/取消自动续费
    async openAutoRenew(data) {
      console.log('自动续费操作:', data);

      if (data.action === 'open') {
        // 开通自动续费
        if (data.agreed) {
          await this.handleAutoRenewOpen();
        } else {
          console.log('用户取消开通自动续费');
        }
      } else if (data.action === 'cancel') {
        // 取消自动续费
        await this.handleAutoRenewCancel();
      }

      this.hideRenewDialog();
    },

    // 处理开通自动续费
    async handleAutoRenewOpen() {
      try {
        uni.showLoading({
          title: '开通中...',
          mask: true,
        });

        const params = {
          reqType: '02', // 开通自动续费
          vipType: this.selectVipPage.vipType || '01',
          autoRenewFlag: '1', // 开启自动续费
        };

        // #ifdef MP-WEIXIN
        // 微信小程序逻辑
        params.appType = '04';
        params.transactionChannel = '1303';

        // 获取微信登录code
        uni.login({
          success: async (loginRes) => {
            params.extend = loginRes.code;
            await this.callAutoRenewAPI(params, 'open');
          },
          fail: (err) => {
            console.error('微信登录失败:', err);
            uni.hideLoading();
            uni.showToast({
              title: '登录失败，请重试',
              icon: 'none',
            });
          },
        });
        // #endif

        // #ifdef H5
        // H5逻辑
        params.appType = '04';
        params.transactionChannel = '12';
        await this.callAutoRenewAPI(params, 'open');
        // #endif
      } catch (error) {
        console.error('开通自动续费失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '开通失败，请重试',
          icon: 'none',
        });
      }
    },

    // 处理取消自动续费
    async handleAutoRenewCancel() {
      try {
        uni.showLoading({
          title: '取消中...',
          mask: true,
        });

        const params = {
          reqType: '03', // 取消自动续费
          vipType: this.selectVipPage.vipType || '01',
          autoRenewFlag: '0', // 关闭自动续费
        };

        // #ifdef MP-WEIXIN
        // 微信小程序逻辑
        params.appType = '04';
        params.transactionChannel = '1303';

        uni.login({
          success: async (loginRes) => {
            params.extend = loginRes.code;
            await this.callAutoRenewAPI(params, 'cancel');
          },
          fail: (err) => {
            console.error('微信登录失败:', err);
            uni.hideLoading();
            uni.showToast({
              title: '登录失败，请重试',
              icon: 'none',
            });
          },
        });
        // #endif

        // #ifdef H5
        // H5逻辑
        params.appType = '04';
        params.transactionChannel = '12';
        await this.callAutoRenewAPI(params, 'cancel');
        // #endif
      } catch (error) {
        console.error('取消自动续费失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '取消失败，请重试',
          icon: 'none',
        });
      }
    },

    // 调用自动续费API
    async callAutoRenewAPI(params, action) {
      try {
        // #ifdef MP-WEIXIN
        if (action === 'open') {
          // 微信小程序开通自动续费，需要跳转云闪付
          await this.handleWxAutoRenewOpen(params);
        } else {
          // 取消自动续费，直接调用API
          await this.handleAutoRenewCancel(params);
        }
        // #endif

        // #ifdef H5
        if (action === 'open') {
          // H5端开通自动续费，需要通过桥接调用原生
          await this.handleH5AutoRenewOpen(params);
        } else {
          // 取消自动续费，直接调用API
          await this.handleAutoRenewCancel(params);
        }
        // #endif
      } catch (error) {
        console.error('API调用失败:', error);
        uni.hideLoading();
        const actionText = action === 'open' ? '开通' : '取消';
        uni.showToast({
          title: `${actionText}失败，请重试`,
          icon: 'none',
        });
      }
    },

    // 微信小程序开通自动续费
    async handleWxAutoRenewOpen(params) {
      try {
        // 1. 调用接口获取云闪付签约信息
        const [, tokenRes] = await this.getAutoRenewToken(params);

        if (!tokenRes || tokenRes.ret !== 200) {
          uni.hideLoading();
          uni.showToast({
            title: tokenRes?.msg || '获取签约信息失败',
            icon: 'none',
          });
          return;
        }

        const { appId, token, signUrl } = tokenRes.data || {};

        if (!appId || !token) {
          uni.hideLoading();
          uni.showToast({
            title: '签约参数不完整',
            icon: 'none',
          });
          return;
        }

        uni.hideLoading();

        // 记录跳转状态和时间
        this.isWaitingAutoRenewResult = true;
        this.autoRenewCheckTime = Date.now();

        // 将状态保存到本地存储
        uni.setStorageSync('autoRenewWaiting', {
          isWaiting: true,
          checkTime: this.autoRenewCheckTime,
          token: token,
        });

        // 2. 跳转到云闪付小程序
        uni.navigateToMiniProgram({
          appId: appId, // 云闪付小程序appId
          path: signUrl || '', // 云闪付签约页面路径
          extraData: {
            token: token,
            returnUrl: 'pages/member/buyPlus/index', // 回调页面
          },
          success: (res) => {
            console.log('跳转云闪付成功:', res);
          },
          fail: (err) => {
            console.error('跳转云闪付失败:', err);
            // 跳转失败时清除等待状态
            this.clearAutoRenewWaitingState();
            uni.showToast({
              title: '跳转云闪付失败',
              icon: 'none',
            });
          },
        });
      } catch (error) {
        console.error('微信开通自动续费失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '开通失败，请重试',
          icon: 'none',
        });
      }
    },

    // 取消自动续费（通用方法）
    async handleAutoRenewCancel(params) {
      try {
        const [, res] = await recharge(params);
        uni.hideLoading();

        if (res && res.ret === 200) {
          uni.showToast({
            title: '取消成功',
            icon: 'success',
          });

          setTimeout(() => {
            this.loadData();
          }, 1500);
        } else {
          uni.showToast({
            title: res?.msg || '取消失败，请重试',
            icon: 'none',
          });
        }
      } catch (error) {
        console.error('取消自动续费失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '取消失败，请重试',
          icon: 'none',
        });
      }
    },

    // 获取自动续费token
    async getAutoRenewToken(params) {
      // 调用获取云闪付签约token的接口
      // 这里需要根据实际的API接口调用
      return await recharge({
        ...params,
        reqType: '04', // 获取签约token的请求类型
      });
    },

    // 处理云闪付自动续费回调
    handleAutoRenewCallback(extraData) {
      console.log('云闪付回调数据:', extraData);

      if (!extraData) {
        return;
      }

      const { status, message, signResult } = extraData;

      if (status === 'success' || signResult === '00') {
        // 签约成功
        this.showAutoRenewSuccessModal();
        // 刷新页面数据
        setTimeout(() => {
          this.loadData();
        }, 2000);
      } else if (status === 'fail' || signResult === '01') {
        // 签约失败
        this.clearAutoRenewWaitingState();
        uni.showToast({
          title: message || '签约失败，请重试',
          icon: 'none',
          duration: 2000,
        });
      } else if (status === 'cancel') {
        // 用户取消签约
        this.clearAutoRenewWaitingState();
        uni.showToast({
          title: '已取消签约',
          icon: 'none',
          duration: 1500,
        });
      }
    },

    // 显示自动续费签约成功弹窗
    showAutoRenewSuccessModal() {
      // 清除等待状态
      this.clearAutoRenewWaitingState();

      uni.showModal({
        title: '签约完成',
        content: '恭喜您！自动续费签约已完成，到期将自动为您续费。',
        showCancel: false,
        confirmText: '我知道了',
        confirmColor: '#007aff',
        success: (res) => {
          if (res.confirm) {
            console.log('用户确认签约完成');
          }
        },
      });
    },

    // 检查自动续费结果
    async checkAutoRenewResult() {
      const waitingState = uni.getStorageSync('autoRenewWaiting');

      if (!waitingState || !waitingState.isWaiting) {
        return;
      }

      // 检查是否超过了合理的等待时间（比如10分钟）
      const currentTime = Date.now();
      const waitingTime = currentTime - waitingState.checkTime;
      const maxWaitingTime = 10 * 60 * 1000; // 10分钟

      if (waitingTime > maxWaitingTime) {
        // 超时，清除等待状态
        this.clearAutoRenewWaitingState();
        return;
      }

      // 在合理时间内，主动查询签约结果
      try {
        uni.showLoading({
          title: '检查签约状态...',
          mask: true,
        });

        const result = await this.queryAutoRenewStatus(waitingState.token);

        uni.hideLoading();

        if (result && result.ret === 200) {
          const { signStatus, signResult } = result.data || {};

          if (signStatus === 'success' || signResult === '00') {
            // 签约成功
            this.showAutoRenewSuccessModal();
            setTimeout(() => {
              this.loadData();
            }, 1000);
          } else if (signStatus === 'fail' || signResult === '01') {
            // 签约失败
            this.clearAutoRenewWaitingState();
            uni.showToast({
              title: '签约失败',
              icon: 'none',
            });
          }
          // 如果状态是处理中，保持等待状态不变
        } else {
          // 查询失败，清除等待状态
          this.clearAutoRenewWaitingState();
        }
      } catch (error) {
        console.error('查询签约状态失败:', error);
        uni.hideLoading();
        // 查询出错，清除等待状态
        this.clearAutoRenewWaitingState();
      }
    },

    // 查询自动续费签约状态
    async queryAutoRenewStatus(token) {
      // 调用查询签约状态的接口
      return await recharge({
        reqType: '05', // 查询签约状态的请求类型
        token: token,
        appType: '04',
        transactionChannel: '1303',
      });
    },

    // 清除自动续费等待状态
    clearAutoRenewWaitingState() {
      this.isWaitingAutoRenewResult = false;
      this.autoRenewCheckTime = null;
      uni.removeStorageSync('autoRenewWaiting');
    },

    // H5端开通自动续费
    async handleH5AutoRenewOpen(params) {
      try {
        // 1. 调用接口获取云闪付签约信息
        const [, tokenRes] = await this.getAutoRenewToken(params);

        if (!tokenRes || tokenRes.ret !== 200) {
          uni.hideLoading();
          uni.showToast({
            title: tokenRes?.msg || '获取签约信息失败',
            icon: 'none',
          });
          return;
        }

        const { token, signUrl } = tokenRes.data || {};

        if (!token) {
          uni.hideLoading();
          uni.showToast({
            title: '签约参数不完整',
            icon: 'none',
          });
          return;
        }

        uni.hideLoading();

        // 2. 通过桥接方法调用原生拉起云闪付
        const bridgeResult = await callNativeAutoRenew({
          token: token,
          signUrl: signUrl,
        });

        if (bridgeResult && bridgeResult.success) {
          // 桥接调用成功，显示询问弹窗
          this.showH5AutoRenewConfirmModal();
        } else {
          uni.showToast({
            title: '调用原生签约失败',
            icon: 'none',
          });
        }
      } catch (error) {
        console.error('H5开通自动续费失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '开通失败，请重试',
          icon: 'none',
        });
      }
    },

    // 显示H5自动续费确认弹窗
    showH5AutoRenewConfirmModal() {
      uni.showModal({
        title: '签约确认',
        content: '请确认您是否已完成云闪付自动续费签约？',
        cancelText: '未完成',
        confirmText: '已完成',
        success: async (res) => {
          if (res.confirm) {
            // 用户确认已完成签约，重新获取会员信息验证
            await this.verifyH5AutoRenewResult();
          } else {
            // 用户未完成签约
            uni.showToast({
              title: '签约未完成',
              icon: 'none',
            });
          }
        },
      });
    },

    // 验证H5自动续费结果
    async verifyH5AutoRenewResult() {
      try {
        uni.showLoading({
          title: '验证签约状态...',
          mask: true,
        });

        // 重新获取会员信息
        await this.getMemberInfo();

        uni.hideLoading();

        // 检查自动续费状态是否已开启
        if (this.isAutoRenewing) {
          // 签约成功
          uni.showModal({
            title: '签约成功',
            content: '恭喜您！自动续费签约已完成，到期将自动为您续费。',
            showCancel: false,
            confirmText: '我知道了',
            confirmColor: '#007aff',
          });

          // 刷新页面数据
          setTimeout(() => {
            this.loadData();
          }, 1000);
        } else {
          // 签约可能未成功或还在处理中
          uni.showModal({
            title: '签约状态确认',
            content: '暂未检测到签约成功，可能还在处理中。是否需要重新尝试？',
            cancelText: '稍后再试',
            confirmText: '重新尝试',
            success: (modalRes) => {
              if (modalRes.confirm) {
                // 用户选择重新尝试，重新触发开通流程
                this.openAutoRenew({ action: 'open', agreed: true });
              }
            },
          });
        }
      } catch (error) {
        console.error('验证H5自动续费结果失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '验证失败，请重试',
          icon: 'none',
        });
      }
    },

    // 处理立即续费协议确认
    handleRenewAgreementConfirm() {
      this.showRenewAgreementModal = false;
      // 确认后执行开通自动续费逻辑
      this.proceedWithAutoRenew();
    },

    // 处理立即续费协议取消
    handleRenewAgreementCancel() {
      this.showRenewAgreementModal = false;
    },

    // 执行开通自动续费逻辑
    async proceedWithAutoRenew() {
      try {
        uni.showLoading({
          title: '开通中...',
          mask: true,
        });

        // 构建开通自动续费的参数
        const params = {
          vipId: this.selectVipPage.vipId,
          vipType: this.selectVipPage.vipType,
          price: this.selectVipPage.price,
          autoRenewFlag: '1', // 开启自动续费
        };

        // #ifdef MP-WEIXIN
        // 微信小程序逻辑
        params.appType = '04';
        params.transactionChannel = '1303';
        // #endif

        // #ifdef H5
        // H5逻辑
        params.appType = '03';
        params.transactionChannel = '1302';
        // #endif

        // 调用开通自动续费API
        await this.callAutoRenewAPI(params, 'open');
      } catch (error) {
        console.error('开通自动续费失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '开通失败，请重试',
          icon: 'none',
        });
      }
    },

    // 执行续费逻辑（非连续包月套餐使用）
    proceedWithRenewal() {
      const appName = uni.getStorageSync('app') || '';
      if (appName == 'i宁德') {
        this.recharge();
        return;
      }
      this.$refs.Pay && this.$refs.Pay.chooseChannel();
    },
    async onRefresh() {
      if (this.freshing) return;
      this.freshing = true;
      try {
        await this.loadData();
      } finally {
        this.freshing = false;
        this.triggered = false;
      }
    },

    onRestore() {
      this.triggered = 'restore'; // 需要重置
    },
    changeMemeberType(val) {
      this.memberType = val;
      this.loadData();
    },
    // 去累计节省页面
    toAccumulate() {
      uni.navigateTo({
        url: '/pages/member/saveRecords/index',
      });
    },
    async getUserSaveRecord() {
      const [, res] = await getUserSaveRecord({});
      if (res) {
        this.saveMoney = res?.saveMoney || 0;
      }
    },
    // 脱敏
    desensitization,
    // 获取VIP类型标签
    getVipTypeLabel(vipType) {
      return VIP_TYPE_CONFIG[vipType]?.shortLabel || '';
    },
    // 获取渠道值
    getChannelValue(channelKey) {
      return CHANNEL_MAP[channelKey]?.value || '';
    },
    // 检查VIP类型是否属于当前会员类型
    isVipTypeForCurrentMember(vipType) {
      return VIP_TYPE_CONFIG[vipType]?.memberType === this.memberType;
    },
    isWithinDays(targetDate) {
      // 当前时间和目标时间
      const currentTime = moment(new Date());
      const targetTime = moment(targetDate);

      // 计算时间间隔
      const duration = moment.duration(targetTime.diff(currentTime));

      // 计算相差的天数（包括小数位）
      return duration.asDays();
    },
    initTopImg() {
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      this.s_top = menuButtonInfo?.top * 2 + 20;
      this.s_height = menuButtonInfo?.height * 2;
    },
    loadData() {
      // 用户信息
      this.getUserInfo();
      // 会员信息
      this.getMemberInfo();
      // 套餐配置
      this.getMenuList();
      // 省钱攻略
      this.saveMoneyStrategy();
      // 会员权益配置
      this.getConfigDesc();

      this.getUserSaveRecord();
    },
    goBack() {
      uni.navigateBack({
        delta: 1,
      });
    },
    // 获取用户信息
    getUserInfo() {
      this.$store.dispatch('login/getUserInfoCallback');
    },
    // 获取会员信息
    async getMemberInfo() {
      const params = {
        plusEquity: '1',
      };
      const [, res] = await getMemberInfo(params);
      if (res) {
        this.memberInfo = res;
      }
    },
    // 打开协议弹框
    openProtocol() {
      uni.navigateTo({
        url: `/pages/setting/agreement/index?&code=0412&title=${'《宁德会员服务协议》'}`,
      });
    },
    // 获取会员权益配置
    async getConfigDesc() {
      const [, res] = await getConfigDesc({
        vipLevel: this.memberType == 0 ? '5' : '6',
      });
      if (res) {
        this.vipConfig = { ...res.vipConfig };
        this.maxAmt = res.maxAmt || 0;
        this.cashCoupon = res.cashCoupon || [];
        this.discountCoupon = res.discountCoupon || [];
        this.maxCoupon = res.maxCoupon || 0;
      }
    },
    // 查询套餐
    async getMenuList() {
      const [, res] = await getConfig();
      if (res) {
        this.menuList = res.vipPayConfigs.sort((a, b) => {
          // 将 vipType 为 '04' 的排在最前面
          if (a.vipType === '04' && b.vipType !== '04') {
            return -1; // a 排在 b 前
          } else if (a.vipType !== '04' && b.vipType === '04') {
            return 1; // b 排在 a 前
          } else {
            return 0; // 如果都是 '04' 或都不是，则顺序不变
          }
        });
        // 获取对应plus、尊享plus会员的套餐
        const menuListVipType = this.menuList.filter((item) => this.isVipTypeForCurrentMember(item?.vipType));

        if (menuListVipType.length > 0) {
          this.menuList = menuListVipType;

          // 根据应用环境选择默认套餐
          const appName = uni.getStorageSync('app') || '';
          let defaultItem;

          if (appName === 'i宁德') {
            // i宁德环境下，选择第一个非连续包月套餐
            defaultItem =
              menuListVipType.find((item) => item.vipType !== '04' && item.vipType !== '08') || menuListVipType[0];
          } else {
            // 其他环境下，选择第一个套餐
            defaultItem = menuListVipType[0];
          }

          this.selectVipId = defaultItem?.vipId;
          this.selectVipType = defaultItem?.vipType;
        }
      }
    },
    choosePrice(item) {
      this.selectVipId = item.vipId;
      this.selectVipType = item.vipType;
    },
    // 会员权益卡片点击
    toInviolable(item) {
      uni.navigateTo({
        url: `/pages/member/inviolable/index?title=${item.title}&type=${this.memberType}`,
      });
    },
    // 获取系统参数
    async getConfigOption(code) {
      const [, res] = await getConfigOption({ paraCode: code });
      if (res && res.list && Array.isArray(res.list) && res.list.length > 0) {
        return res.list[0]?.paramValue;
      }
      return '';
    },
    // 获取省钱攻略
    async saveMoneyStrategy() {
      const [, res] = await saveMoneyStrategy({
        vipLevel: this.memberType == 0 ? '5' : '6',
      });
      if (res) {
        this.tableData = res.saveMoneyStrategy.slice(0, 5);
        const { vipDisAmt, disAmt, cashAmt, monthVipPrice } = res;
        const vipDiscount = parseFloat(vipDisAmt) || 0;
        const discount = parseFloat(disAmt) || 0;
        const cashAmount = parseFloat(cashAmt) || 0;
        const monthlyVipPrice = parseFloat(monthVipPrice) || 0;
        // 计算结果
        const result = vipDiscount + discount + cashAmount - monthlyVipPrice;
        this.saveInfo = {
          ...res,
          totalNum: result,
        };
      }
    },
    // 开通记录
    toOpenRecord() {
      uni.navigateTo({
        url: '/pages/member/openRecord/index',
      });
    },
    // 说明点击
    tabCallback(item) {
      if (item.type == 'renew') {
        this.showRenewPopup = true;
        return;
      }
      if (item.type == 'toPhone') {
        makePhoneCall(this.telephone);
        return;
      }
      let query = '';
      if (item.params) {
        query = `?${Object.entries(item.params).reduce((cur, nex, index) => {
          if (index == Object.entries(item.params).length - 1) {
            cur += `${nex[0]}=${nex[1]}`;
          } else {
            cur += `${nex[0]}=${nex[1]}&`;
          }
          return cur;
        }, '')}`;
      }
      if (item.path) {
        uni.navigateTo({
          url: item.path + query,
        });
      }
    },
    // 立即续费 立即开通
    toPay() {
      this.selectVipId = this.filteredMenuList.find((q) => q.vipId == '1')?.vipId;
      this.selectVipType = this.filteredMenuList.find((q) => q.vipId == '1')?.vipType;
      this.chooseChannel();
    },
    chooseChannel() {
      // 如果正在自动续费中，不执行任何操作
      if (this.isAutoRenewing) {
        return;
      }

      console.log(this.selectVipPage, 'page');

      // 如果选择的是连续包月套餐(04或08)，弹出协议确认弹窗
      // 用户确认后会走开通自动续费的逻辑 callAutoRenewAPI
      if (this.selectVipPage.vipType == '04' || this.selectVipPage.vipType == '08') {
        this.showRenewAgreementModal = true;
        return;
      }
      // i宁德直接跳转
      const appName = uni.getStorageSync('app') || '';
      if (appName == 'i宁德') {
        this.recharge();
        return;
      }
      this.$refs.Pay && this.$refs.Pay.chooseChannel();
    },
    async getIndData(params) {
      const param = {
        userNo: uni.getStorageSync('toonno') ?? '', // toonno用户详情获取app.vue
        orderId: params?.recordId,
        amount: params?.price,
        goodsName: params?.goodsName,
        goodsId: params?.goodsId,
        notifyUrl: 'https://ndjtcs.evstyle.cn:6443/base/api/iningde-notify', // 支付成功异步回调地址
        memo: '', // 备注
        redirectUrl: '/pages/member/buyPlus/index?payRes=success', // 支付成功页面返回,显示支付结果提示的paySuccess()
      };

      const [err, res] = await getIndData(param);
      if (err) {
        uni.showModal({
          title: '开通失败',
          showCancel: false,
          content: msg,
          confirmText: '确定',
        });
        return;
      }
      return res;
    },
    // 查询支付参数
    async recharge() {
      const params = {
        reqType: '01',
        appType: '04',
        payAmount: this.selectVipPage.price,
        vipType: this.selectVipPage.vipType,
        transactionChannel: '12',
        redirectUrl: 'https://ndjtcs.evstyle.cn:6443/ndcharge/pages/member/buyPlus/index',
      };
      const [, res] = await recharge(params);
      if (res && res?.orderResultINingDe) {
        window.location.href = res.orderResultINingDe;
        // const data =  this.getIndData(res)
        // window.location.href = `https://pay.evstyle.cn/ningde-checkout-h5/m/#/pay-tripartite?parentMerchantNo=P0000000069&merchantNo=91350900MA2YQWT48C&data=${data}`;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.wrap {
  background: #4a454c;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  // padding-bottom: 350rpx;
  .bg-text {
    background: #eab193;
    border-radius: 100rpx;
    padding: 8rpx 32rpx;
    display: inline-block;
    span {
      font-size: 31rpx;
      font-weight: 600;
    }
  }
  .content {
    flex: 1;
    background: linear-gradient(166deg, #504a51 -2%, #1e1f28 30%, #1e1f28 100%);
    overflow-y: auto;
    width: 100%;
    margin-top: 40rpx;
    display: flex;
    flex-direction: column;
    .user-card {
      width: 90%;
      height: 247rpx;
      box-sizing: border-box;
      margin: 0 auto;
      background-size: 100% 100%;
      padding: 60rpx 24rpx 10rpx 24rpx;
      display: flex;
      flex-direction: row;
      .user-logo {
        width: 77rpx;
        height: 77rpx;
        border-radius: 50%;
        image {
          width: 100%;
          height: 100%;
        }
      }
      .user-info {
        flex: 1;
        padding: 0 21rpx 10rpx 30rpx;
        display: flex;
        flex-direction: column;
        gap: 20rpx;
        grid-gap: 20rpx;
        .phone {
          font-family: PingFang SC;
          font-size: 32rpx;
          color: #333333;
          font-weight: 700;
        }
        .time {
          font-family: PingFang SC;
          font-size: 24rpx;
          color: #9c580a;
        }
        .vip-level {
          max-width: 400rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          opacity: 1;
          font-size: 24rpx;
          color: #333333;
          margin-left: -10rpx;
        }
      }
      .user-btn {
        width: 175rpx;
        .btn {
          border-radius: 192px;
          background: linear-gradient(
              126deg,
              rgba(255, 255, 255, 0) -2%,
              rgba(255, 255, 255, 0.19) 11%,
              rgba(10, 9, 9, 0) 60%
            ),
            linear-gradient(109deg, #5e5a54 0%, #1e1f28 94%);
          box-shadow: inset 0px 1px 0px 0px #fff9ea;
          height: 60rpx;
          padding: 12rpx 22rpx;
          font-size: 24rpx;
          line-height: 36px;
          color: #ffe3c1;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          .btn-tip {
            width: 104rpx;
            height: 48rpx;
            position: absolute;
            left: -45rpx;
            top: -20rpx;
            background-size: 100% auto;
            background-repeat: no-repeat;
          }
        }
        .record-btn {
          font-size: 24rpx;
          font-weight: 600;
          color: #25252c;
          margin-top: 50rpx;
          text-align: right;
          padding-right: 25rpx;
          image {
            margin-left: 5rpx;
            width: 14rpx;
            height: 16rpx;
          }
        }
      }
    }
    .box {
      width: 100%;
      border: 1px solid #e7ab8c;
      background: #282934;
      border-radius: 30rpx;
      padding: 40rpx 24rpx;
      box-sizing: border-box;
      padding-bottom: 400rpx;
      .title-bg {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15rpx;
        margin-top: 10rpx;
        image {
          width: 301rpx;
          height: 43rpx;
        }
      }
      .member-type-tab {
        margin-top: 32rpx;
        margin-bottom: 16rpx;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
        box-sizing: border-box;
        .member-type-btn {
          width: 342.5rpx;
          height: 100rpx;
          /* 自动布局 */
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          border-radius: 18rpx;
          background-position: center;
          background-repeat: no-repeat;
          opacity: 1;
          background-color: rgba(255, 233, 221, 0.1);
          box-sizing: border-box;
        }
        .member-type-btn:nth-child(1) {
          background-size: 181rpx 47rpx;
        }
        .member-type-btn:nth-child(2) {
          background-size: 301rpx 77rpx;
        }
        .member-type-btn.active {
          background-color: rgba(253, 229, 216, 0.26);
          border: 2rpx solid #ca8b6a;
        }
      }
      .member-card {
        width: 100%;
        min-height: 200rpx;
        padding: 37rpx 27rpx;
        // background: #e8ac8d;
        box-sizing: border-box;
        border: 2px solid;
        background-size: 100% 100%;
        // border-image: linear-gradient(180deg, #ffffff 0%, rgba(192, 147, 128, 0.522) 47%, rgba(192, 147, 128, 0) 100%) 2;
        border-radius: 24rpx;
        .top-member {
          // width: 100%;
          // width: 647rpx;
          height: 153.59rpx;
          background-size: 100% 100%;
          padding: 24rpx;
          box-sizing: border-box;

          .query-icon {
            background: #3d3d3d;
            width: 40rpx;
            height: 40rpx;
            text-align: center;
            line-height: 40rpx;
            font-size: 30rpx;
            margin-left: 10rpx;
            color: #fff;
            border-radius: 50%;
            position: relative;
          }
          .query-icon-popup {
            z-index: 1;
            position: absolute;
            display: none;
            top: -85rpx;
            right: -100rpx;
            background-color: #333;
            border-radius: 8px;
            color: #fff;
            font-size: 12px;
            text-align: left;
            line-height: 16px;
            padding: 12px;
            white-space: nowrap;
          }
          .query-icon:hover .query-icon-popup {
            display: block;
          }
          .bg-text {
            font-size: 24rpx;
          }
          .title {
            font-size: 32rpx;
            font-weight: 700;
            color: #333333;
            margin-bottom: 10rpx;
            display: flex;
            flex-direction: row;
            align-items: baseline;
            span {
              font-size: 50rpx;
              font-weight: 800;
            }
          }
        }
        .member-card-wrap {
          margin-top: 24rpx;
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          box-sizing: border-box;
          gap: 20rpx;
          grid-gap: 20rpx;
          .member-card-item {
            background-size: 100% 100%;
            padding: 24rpx;
            // width: 308rpx !important;
            width: calc(50% - 15rpx) !important;
            height: 145rpx;
            box-sizing: border-box;
            .title {
              display: flex;
              font-size: 32rpx;
              font-weight: 800;
              color: #333333;
              margin-bottom: 15rpx;
              align-items: center;
              image {
                margin-right: 10rpx;
                width: 42.8rpx;
              }
            }
            .text {
              display: flex;
              flex-direction: column;
              width: 100%;
              padding-left: 20rpx;
              .text-item {
                width: 100%;
                margin-bottom: 8rpx;
                font-size: 22rpx;
                color: #333333;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }
        }
      }
      .table-wrap {
        border-radius: 24rpx;
        opacity: 1;
        background: #363749;
        box-sizing: border-box;
        width: 651rpx;
        padding: 40rpx 27rpx;
        margin: 0 auto;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 20rpx;
        grid-gap: 20rpx;
        margin-bottom: 10rpx;
        position: relative;
        overflow: hidden;
        .table-bg {
          position: absolute; /* 绝对定位 */
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border: 2px solid;
          z-index: 0;
          border-image: linear-gradient(
              270deg,
              rgba(241, 209, 190, 0) 0%,
              rgba(241, 209, 190, 0.16) 30%,
              #f0cebc 48%,
              rgba(235, 186, 160, 0.2) 70%,
              rgba(235, 186, 160, 0) 100%
            )
            3;
        }
        .table-box {
          border: 1px solid #e8ac8d;
          border-radius: 24rpx;
          width: 100%;
          .th-wrap {
            width: 100%;
            box-sizing: border-box;
            display: flex;
            border-bottom: 1px solid #e8ac8d;
            .th-item {
              flex: 1;
              max-width: 33%;
              display: flex;
              justify-content: center;
              align-items: center;
              font-size: 28rpx;
              color: #e8ac8d;
              padding: 18rpx 10rpx;
              border-right: 1px solid #e8ac8d;
              overflow: hidden;
              width: 33%;
              &:last-child {
                border-right: none;
              }
              &.num {
                color: #fff;
              }
            }
            &:last-child {
              border-bottom: none;
            }
          }
        }
        .table-card-item {
          padding: 22rpx 10rpx 22rpx 16rpx;
          border-radius: 18rpx;
          background: rgba(232, 172, 141, 0.2);
          box-sizing: border-box;
          border: 1px solid #e8ac8d;
          flex-basis: 280rpx;
          height: 86.8rpx;
          display: flex;
          align-items: center;
          box-sizing: border-box;
          font-size: 24rpx;
          color: #f2c3aa;
          z-index: 2;
          .icon {
            width: 42.8rpx;
            height: 42.8rpx;
            margin-right: 10rpx;
          }
          .right-icon {
            margin-left: auto;
            width: 32rpx;
            height: 32rpx;
          }
        }
      }
      .tip-bg {
        width: 100%;
        width: 679rpx;
        height: 43rpx;
        margin: 15rpx;
      }
      .tip-text-wrap {
        border-radius: 24rpx;
        opacity: 1;
        background: #363749;
        padding: 40rpx 27rpx;
        .title {
          white-space: wrap;
          font-size: 24rpx;
          font-weight: 500;
          text-align: center;
          color: #ffffff;
          margin-bottom: 15rpx;
          line-height: 30rpx;
        }
        .tip {
          font-size: 20rpx;
          text-align: center;
          color: rgba(255, 255, 255, 0.81);
        }
      }
      .text-tip {
        font-size: 24rpx;
        color: #ffffff;
        line-height: 30rpx;
      }
    }
  }
  .bottom-wrap {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 3;
    // border-radius: 14px 14px 0px 0px;
    opacity: 1;
    background: #171420;
    width: 100vw;
    padding: 24rpx 10rpx 40rpx 10rpx;
    box-sizing: border-box;
    min-height: 240rpx;
    // position: relative;
    .money-wrap {
      // white-space: nowrap;
      width: 100%;
      // padding: 0 24rpx;
      // display: flex;
      // flex-wrap: nowrap;
      // flex-direction: row;
      // justify-content: space-around;
      // overflow-x: auto;
      // gap: 20rpx;
      .money-wrap-box {
        display: flex;
        flex-wrap: nowrap;
        padding: 0 24rpx;
      }
      .money-item {
        margin-right: 10rpx;
        background-size: 100% 100%;
        width: 33%;
        // width: 220.67rpx;
        display: inline-block;
        height: 150rpx;
        padding: 22rpx 10rpx 22rpx 16rpx;
        position: relative;
        // white-space: wrap;
        .title {
          width: 220rpx;
          word-wrap: break-word;

          // overflow: hidden;
          font-size: 26rpx;
          font-weight: 800;
          color: #ffffff;
          margin-bottom: 15rpx;
        }
        .money {
          font-size: 34rpx;
          font-weight: bold;
          color: #e8ac8d;
          display: flex;
          align-items: baseline;
          gap: 4rpx;

          .new-price {
            font-size: 34rpx;
            font-weight: bold;
            color: #e8ac8d;
          }

          .old-price {
            font-size: 20rpx;
            font-weight: normal;
            color: #999999;
            text-decoration: line-through;
            opacity: 0.7;
            margin-left: 4rpx;
            white-space: nowrap;
            flex-shrink: 0;
          }
        }
        .description {
          font-size: 20rpx;
          font-weight: normal;
          color: rgba(255, 255, 255, 0.8);
          margin-top: 8rpx;
          line-height: 1.2;
        }
        .new-icon {
          width: 102rpx;
          height: 36rpx;
          background-size: 100% auto;
          position: absolute;
          top: 0;
          right: 0;
        }
      }
    }
    .btn {
      margin: 20rpx auto;
      border-radius: 16rpx;
      opacity: 1;
      // background: #3d2f30;
      background: #d89d70;
      width: 702rpx;
      height: 100rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36rpx;
      color: #ffffff;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      padding: 0 20rpx;

      .btn-content {
        display: flex;
        align-items: baseline;
        justify-content: center;
        gap: 12rpx;
        width: 100%;
      }

      .btn-price-section {
        display: flex;
        align-items: baseline;
        gap: 6rpx;

        .btn-new-price {
          font-size: 36rpx;
          font-weight: 600;
          color: #ffffff;
        }

        .btn-old-price {
          font-size: 24rpx;
          font-weight: normal;
          color: rgba(255, 255, 255, 0.7);
          text-decoration: line-through;
          line-height: 1;
        }
      }

      .btn-text-section {
        display: flex;
        align-items: baseline;
        gap: 8rpx;

        .btn-action-text {
          font-size: 36rpx;
          font-weight: 600;
          color: #ffffff;
        }

        .btn-save-text {
          font-size: 24rpx;
          font-weight: normal;
          color: rgba(255, 255, 255, 0.9);
        }
      }

      &.active {
        background: #d89d70;
      }

      &.btn-disabled {
        background: rgba(216, 157, 112, 0.2);
        cursor: not-allowed;
        // opacity: 0.2;
        color: #ffffff;

        &:hover {
          background: #d89d70;
        }
      }
    }
    .agreement-tip {
      font-family: PingFang SC;
      font-size: 24rpx;
      color: #999999;
      text-align: center;
      margin-bottom: 10rpx;
      span {
        color: #d89d70;
      }
    }
  }
}
.banner {
  padding: 20rpx 20rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  z-index: 2;
  box-sizing: border-box;
  color: #fff;
  .banner-title {
    font-family: 'PingFang SC';
    font-size: 36rpx;
    font-style: normal;
    font-weight: 500;
    flex: 1;
    text-indent: 4rpx;
    color: #000000;
    font-weight: 700;
    text-align: left;
    color: #fff;
  }
  .banner-img {
    width: 48rpx;
    height: 48rpx;
    margin-right: 20rpx;
  }
}
</style>
