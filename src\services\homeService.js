import { Ajax } from '../utils/Ajax/index';
import { ORG_CODE } from '../config/global';
/** 
 * 	lon	经度	varchar
	lat	维度	varchar
*/

export const getHomeStationApi = async (params) => {
    return Ajax({
        url: '/api/open/v0.1/home-station',
        method: "GET",
        hideLoad: true,
        data: {
            orgCode: ORG_CODE,
            ...params
        }
    })
}
/** 
 * 	mobile 手机号
*/
export const getHomeOrderApi = async (mobile) => {
    return Ajax({
        url: '/api/v0.1/home-order',
        method: "GET",
        hideLoad: true,
        data: {
            mobile: mobile
        }
    })
}
export const getHomeLoadingApi = async (params) => {
    return Ajax({
        url: '/api/open/v0.1/home-page-loading',
        method: "GET",
        data: params
    })
}


/**
 * 邀请送领取优惠券
 * inviteCustId	必填	邀请人ID
 */
export const getinviteActCpnApi = async (inviteCustId) => {
    return Ajax({
        url: '/api/v0.1/getinviteActCpn',
        method: "GET",
        hideLoad: true,
        data: {
            inviteCustId: inviteCustId
        }
    })
}


