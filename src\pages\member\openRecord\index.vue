<template>
  <div class="wrap" :style="{ backgroundImage: `url(${IMG_PATH_UI}bg.png)` }">
    <!-- #ifdef MP-WEIXIN -->
    <view
      class="banner"
      :style="'padding-top:' + s_top + 'rpx;height:' + s_height + 'rpx;line-height:' + s_height + 'rpx'"
    >
      <!-- <image class="banner-img" @tap="goBack" mode="widthFix" :src="`${IMG_PATH}back.png`"></image> -->
      <view class="banner-title"
        ><icon type="icon iconfont icon-arrow_left" class="mr15" @click="goBack" size="18" /> 开通记录</view
      >
    </view>
    <!-- #endif -->
    <div class="content">
      <scroll-view
        v-if="tableData.length > 0"
        class="list-found"
        scroll-y="true"
        refresher-enabled="true"
        :refresher-triggered="triggered"
        :refresher-threshold="100"
        @scrolltolower="toLowFun"
        :lower-threshold="50"
        refresher-background="rgba(0,0,0,0)"
        :show-scrollbar="false"
        @refresherrefresh="onRefresh"
        @refresherrestore="onRestore"
      >
        <div class="station-item" v-for="(item, index) in tableData" :key="index">
          <div class="code">订单ID: {{ item.recordId || '-' }}</div>
          <view class="station-li">
            <div class="title">{{ item.vipTypeName || '-' }}</div>
            <div class="li-content">
              <div class="label">购买时间</div>
              <div class="time">{{ item.payTime || '-' }}</div>
              <div class="label">生效时间</div>
              <div class="time">{{ item.effectTime || '-' }}</div>
              <div class="label">到期时间</div>
              <div class="time">{{ item.expireTime || '-' }}</div>
              <div class="money">
                实付:
                <template v-if="(item.integral || item.integral === 0) && (item.amount || item.amount === 0)"
                  >{{ item.integral }}积分 + {{ item.amount }}元</template
                >
                <template v-else-if="item.amount || item.amount === 0">{{ item.amount }}元</template>
                <template v-else-if="item.integral || item.integral === 0">{{ item.integral }}积分</template>
              </div>
            </div>
          </view>
        </div>
      </scroll-view>
      <AEmpty v-else></AEmpty>
    </div>
  </div>
</template>

<script>
import { getRecord } from '@/services/index.js';
import AEmpty from '@/components/AEmpty';
export default {
  props: {},
  components: { AEmpty },
  data() {
    return {
      s_top: 0,
      s_height: 0,
      tableData: [],
      tablePage: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      triggered: 'restore',
      freshing: false,
      memberInfo: {},
      loading: false,
    };
  },
  onLoad() {},
  onShow() {
    // #ifdef MP
    this.initTopImg();
    // #endif
  },
  computed: {
    topFixed2() {
      return 200 + this.s_top + 'rpx';
    },
  },
  watch: {},
  created() {
    this.loadData();
  },
  mounted() {},
  methods: {
    goBack() {
      // #ifdef H5
      this.$router.go(-1);
      // #endif
      // #ifdef MP-WEIXIN
      uni.navigateBack({
        delta: -1,
      });
      // #endif
    },
    initTopImg() {
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      this.setData({
        s_top: menuButtonInfo?.top * 2 + 20,
        s_height: menuButtonInfo?.height * 2,
      });
    },
    toLowFun() {
      console.log('触底');
      if (this.tablePage.total <= this.tableData.length || this.freshing || this.loading) return;
      this.tablePage.current += 1;
      this.loadData();
    },
    async loadData() {
      const params = {
        totalNum: this.tablePage.pageSize,
        pageNum: this.tablePage.current,
      };
      this.loading = true;
      const [err, res] = await getRecord(params);
      this.loading = false;
      if (res) {
        res.vipPayRecords.forEach((item) => {
          if (!this.tableData.find((q) => q.recordId === item.recordId)) {
            this.tableData.push({
              ...item,
            });
          }
        });
        this.tablePage.total = res.count;
      }
    },
    onRefresh() {
      if (this.freshing) return;
      this.freshing = true;
      this.tablePage = {
        current: 1,
        pageSize: 20,
        total: 0,
      };
      this.loadData();
      this.freshing = false;
      this.triggered = false;
    },

    onRestore() {
      this.triggered = 'restore'; // 需要重置
    },
  },
};
</script>

<style scoped lang="scss">
.wrap {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  background-size: 100% 100%;
  .banner {
    padding: 20rpx 20rpx 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
    z-index: 2;
    box-sizing: border-box;
    color: #000000;
    .banner-title {
      font-family: 'PingFang SC';
      font-size: 36rpx;
      font-style: normal;
      font-weight: 500;
      flex: 1;
      text-indent: 4rpx;
      color: #000000;
      font-weight: 700;
      text-align: left;
      color: #000000;
      display: flex;
      align-items: center;
      line-height: 0;
    }
    .banner-img {
      width: 48rpx;
      height: 48rpx;
      margin-right: 20rpx;
    }
    .mr15 {
      margin-right: 15rpx;
    }
  }
  .info {
    padding: 50rpx 24rpx 24rpx 24rpx;
    display: flex;
    width: 100%;
    align-items: center;
    .btn {
      margin-right: 15rpx;
      border-radius: 19rpx;
      background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0) -3%,
          rgba(255, 255, 255, 0.19) 11%,
          rgba(10, 9, 9, 0) 65%
        ),
        linear-gradient(115deg, #5e5a54 0%, #1e1f28 93%);
      box-shadow: inset 0px 1px 0px 0px #fff9ea;
      font-size: 24rpx;
      padding: 12rpx 16rpx;
      color: #ffe3c1;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 110rpx;
    }
    .code {
      max-width: 500rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .content {
    width: 100%;
    flex: 1;
    padding: 40rpx 24rpx;
    font-size: 32rpx;
    color: #333333;
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    grid-gap: 24rpx;
    overflow: hidden;
    .list-found {
      width: 100%;
      height: 100%;
      padding-bottom: 40rpx;
      .station-item {
        .code {
          font-size: 30rpx;
          line-height: 50rpx;
          padding-left: 20rpx;
          font-weight: 700;
        }
        .station-li {
          background: #fff;
          padding: 24rpx;
          margin-bottom: 15rpx;
          border-radius: 16rpx;
          width: 100%;
          margin-bottom: 15rpx;
          border: 1px solid rgba(0, 0, 0, 0.05);
          .title {
            font-size: 32rpx;
            color: #333333;
            line-height: 50rpx;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            font-weight: 600;
          }
          .li-content {
            width: 100%;
            padding: 24rpx 0;
            display: flex;
            font-size: 28rpx;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: space-between;
            box-sizing: border-box;
            gap: 16rpx;
            grid-gap: 16rpx;
            .label {
              width: 200rpx;
              color: #999999;
              text-align: left;
            }
            .time {
              width: 400rpx;
              color: #333333;
              text-align: right;
            }
            .money {
              width: 100%;
              font-size: 32rpx;
              color: #ff8301;
              text-align: right;
              margin-top: 20rpx;
            }
          }
        }
      }
    }
  }
}
</style>
