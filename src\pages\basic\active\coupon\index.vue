<template>
  <view>
    <!-- 海报 -->
    <view class="act-image">
      <image class="image-item" :src="imageUrl" mode="widthFix" />
    </view>
    <!-- 领取按钮 -->
    <view class="redeem" v-if="detailInfo.cpnList.length !== 0">
      <button class="redeem-button" @click="allPick">一键领取</button>
    </view>
    <!-- 优惠券 -->
    <view @click="redeemCoupon" :data-cpnId="item.cpnId" v-for="(item, index) in detailInfo.cpnList" :key="index">
      <CouponCard :cardInfo="item" />
    </view>
    <!-- 活动说明 -->
    <view class="description">
      <text class="description-title">活动说明：</text>
      <text class="description-content">{{ detailInfo.act.actMark }}</text>
    </view>
    <!-- 状态提示 -->
    <view class="footer" v-if="detailInfo.cpnList.length === 0">
      <!-- <text class="footer-status">
          statusName
        </text> -->
      <text class="footer-status" v-if="detailInfo.act.actState === '3' || detailInfo.act.actState === '4'">
        活动已结束！
      </text>
      <text class="footer-status" v-if="detailInfo.act.actState === '2'"> 优惠券已领完！ </text>
      <view class="footer-button">
        <main-btn ghost bind:click="jumpToPage"> 去领券中心 </main-btn>
      </view>
    </view>
  </view>
</template>

<script>
import { baseDate } from '@/utils/base.js';
import { getCoupon, getCouponReceive, getCouponReceiveAll } from '@/services/index';
import CouponCard from '@/components/CouponCard/index';
import { mapState } from 'vuex';
// pages/active/coupon/index.js
export default {
  components: { CouponCard },
  data() {
    return {
      actId: '',
      detailInfo: {},
      imageUrl: '',
    };
  },
  computed: {
    ...mapState({
      userInfo: (state, getters) => getters['login/getUserInfo'],
    }),
    // 状态文字提示
    statusName(data) {
      let str = '';
      if (data?.detailInfo?.act?.actState === '3' || data?.detailInfo?.act?.actState === '4') {
        str = '活动已结束';
      } else if (data?.detailInfo?.act?.actState === '2' && data?.detailInfo?.cpnList?.length === 0) {
        str = '优惠券已领完，下回早点来！';
      } else {
        str = '';
      }
      return str;
    },
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.initData();
  },
  onLoad(options) {
    // 跳转连接处理，二维码跳转取q中的值，连接分享跳转取id中的值
    if (options?.q) {
      const arr = decodeURIComponent(options?.q).split('id=');
      this.actId = arr[1];
    } else if (options?.id) {
      this.actId = options.id;
    } else {
      // do nothing
    }
  },
  methods: {
    // 查询优惠券
    async getDetailInfo() {
      var param = {
        prodBusiType: '02',
        actId: this.actId,
      };
      const [, res] = await getCoupon(param);
      if (res && res.ret == 200) {
        const url = `${baseDate()}/pub/api/v0.1/attachs/${res.fileId}`.replace('/scan', '');
        this.detailInfo = res;
        this.imageUrl = url;
        _self.setData({
          detailInfo: res.data,
          imageUrl: url,
        });
      }
    },
    // 领取优惠券
    async redeemCoupon(e) {
      var _self = this;
      var param = {
        mobile: '',
        getChannel: '02',
        cpnId: e.currentTarget.dataset.cpnid,
      };
      const [, res] = await getCouponReceive(param);
      if (res) {
        uni.showToast({
          title: '领取成功',
          icon: 'success',
          duration: 1200,
        });
        // 刷新页面详情接口
        this.getDetailInfo();
      }
    },
    // 一键领取
    async allPick() {
      const arr = this['__data__'].detailInfo.cpnList.map((item) => {
        return item.cpnId;
      });
      const cpnIds = arr.join(',');
      var _self = this;
      var param = {
        mobile: '',
        getChannel: '02',
        cpnId: cpnIds,
      };
      const [, res] = await getCouponReceiveAll(param);
      if (res) {
        uni.showToast({
          title: '领取成功',
          icon: 'success',
          duration: 1200,
        });
        // 刷新页面详情接口
        this.getDetailInfo();
      }
    },
    // 跳转到领券中心
    jumpToPage() {
      wx.navigateTo({
        url: '/pages/gocup/gocup',
      });
    },
    // 初始化数据
    initData() {
      // 登录校验不通过跳转登录页面，没有活动id页无法跳转页面
      // if (this.userInfo?.mobile && this.actId) {
      // 二维码携带活动id则跳转活动页，否则跳转到首页
      this.getDetailInfo();
      // } else {
      //   uni.navigateTo({
      //     url: '/pages/login/login',
      //   });
      // }
    },
  },
};
</script>
<style>
@import './index.css';
</style>
