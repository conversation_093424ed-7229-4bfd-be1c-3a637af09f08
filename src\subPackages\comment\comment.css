.login-header {
  height: 180rpx;
  background: #de3031;
  width: 100%;
  color: #fff;
  box-sizing: border-box;
  display: flex;
}
.login-header .login-img {
  width: 180rpx;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.login-header .login-img image {
  width: 120rpx;
  height: 120rpx;
}
.login-header .login-header-message {
  width: 570rpx;
  padding: 30rpx 30rpx 30rpx 0;
  box-sizing: border-box;
  position: relative;
}
.login-header-message .login-message1 text {
  float: right;
}
.login-header-message .login-message2,
.login-header-message .login-message3 {
  font-size: 24rpx;
  line-height: 40rpx;
}
.login-header-message .login-message2 image {
  width: 16rpx;
  height: 20rpx;
  margin-right: 10rpx;
}
.login-header-message .login-message2 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 80%;
}
.login-header-message .login-message3 image {
  width: 16rpx;
  height: 16rpx;
  margin-right: 10rpx;
}
.login-header-message #login-text {
  width: 50rpx;
  height: 50rpx;
  position: absolute;
  right: 30rpx;
  bottom: 30rpx;
  /* background: blue */
}
.login-header-message #login-text image {
  width: 100%;
  height: 100%;
}
.login-middle {
  height: 430rpx;
  width: 100%;
  box-sizing: border-box;
  padding: 30rpx;
  font-size: 30rpx;
  display: flex;
  flex-wrap: wrap;
  align-content: space-between;
  background: #fff;
}
.login-middle view {
  width: 750rpx;
}
.login-middle text {
  width: 150rpx;
  display: inline-block;
  color: #7f7f7f;
}
.login-nav {
  background: #fff;
  margin-top: 30rpx;
  height: 88rpx;
  width: 100%;
  padding: 0 30rpx;
  line-height: 88rpx;
  font-size: 36rpx;
  margin-bottom: 1rpx;
  box-sizing: border-box;
}
.login-navMsg {
  background: #fff;
  height: 150rpx;
  width: 100%;
  font-size: 30rpx;
  padding: 30rpx;
  box-sizing: border-box;
  display: flex;
  align-content: space-around;
  flex-wrap: wrap;
  border-bottom: 1rpx solid #eef2f6;
}
.login-navMsg view {
  width: 100%;
}
.login-navMsg view text {
  color: #7f7f7f;
  margin-right: 30rpx;
}
.login-button {
  background: #eef2f6;
  height: 343rpx;
  padding: 30rpx;
  box-sizing: border-box;
}
.login-button button {
  background: #de3031;
  color: #fff;
  font-size: 30rpx;
}
.comt-evaluate {
  height: 267rpx;
  width: 100%;
  box-sizing: border-box;
  color: #666;
  font-size: 30rpx;
  padding: 30rpx;
  display: flex;
  flex-wrap: wrap;
  align-content: space-between;
}
.comt-evaluate .comt-list {
  width: 100%;
  display: flex;
  align-items: center;
}
.comt-evaluate .comt-list text {
  width: 180rpx;
  display: inline-block;
}
.comt-evaluate .comt-img {
  width: 32rpx;
  height: 32rpx;
  margin-right: 33rpx;
  display: inline-block;
}
.comt-evaluate .comt-list .comt-img image {
  width: 100%;
  height: 100%;
}
.section textarea {
  height: 240rpx;
  width: 100%;
  box-sizing: border-box;
  background: #f8f8f8;
  font-size: 28rpx;
  padding: 30rpx;
}
.upload-img {
  width: 100%;
  height: 82rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
  color: #8f8f8f;
  background: #eef2f6;
  line-height: 82rpx;
  font-size: 28rpx;
  margin-top: 30rpx;
}
.upload-pic {
  height: 220rpx;
  width: 100%;
  box-sizing: border-box;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
}
.upload-pic view {
  width: 160rpx;
  height: 160rpx;
  margin-right: 30rpx;
}
.upload-pic view image {
  width: 100%;
  height: 100%;
}
.comt-footer {
  width: 100%;
  height: 180rpx;
  box-sizing: border-box;
  background: #eef2f6;
  font-size: 24rpx;
  color: #b2b2b2;
  padding: 0 30rpx;
}
.comt-footer text {
  height: 46rpx;
  line-height: 46rpx;
  box-sizing: border-box;
}
.comt-footer button {
  font-size: 30rpx;
  color: #fff;
  background: #2196f3;
  margin-top: 20rpx;
  height: 88rpx;
  line-height: 88rpx;
}
