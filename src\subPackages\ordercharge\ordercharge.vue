<template>
  <view>
    <view class="orderdetails">
      <view class="orderdetails-main">
        <view class="main-no">
          {{ msg.orderNo || '' }}
          <text v-if="msg.orderStatus == '06'" class="fr">{{ msg.orderTBal || '' }}</text>
          <text v-if="msg.orderStatus == '07'" class="fr">{{ msg.orderTBal || '' }}</text>
          <text v-if="msg.orderStatus == '08'" class="fr">{{ msg.orderTBal || '' }}</text>
          <text v-if="msg.orderStatus == '09'" class="fr">{{ msg.orderTBal || '' }}</text>
          <text v-else class="fr">{{ msg.prepayTbal || '' }}</text>
        </view>
        <view class="main-address">{{ chargeOrderList.stationAddr || '' }}</view>
        <view class="main-time">{{ msg.applyTime || '' }}</view>
        <icon @tap="distanceMap" class="icon-guide blue-color" type="icon iconfont icon-guide1" size="22" />
      </view>
      <view class="orderdetails-lists">
        <view class="list-main">
          状态
          <text class="main-content fr">{{ msg.orderStatusName }}</text>
        </view>
        <view class="list-main">
          站名
          <text class="main-content fr">{{ chargeOrderList.stationName }}</text>
        </view>
        <view class="list-main">
          桩名
          <!-- <text class="main-content fr">{{ chargeOrderList.pileName}} + {{ chargeOrderList.equipName}}</text> -->
          <text class="main-content fr">{{ chargeOrderList.pileName }}</text>
        </view>
        <view class="list-main">
          枪名
          <!-- <text class="main-content fr">{{ chargeOrderList.pileName}} + {{ chargeOrderList.equipName}}</text> -->
          <text class="main-content fr">{{ chargeOrderList.equipName }}</text>
        </view>
        <view v-if="chargeOrderList.subType == '01'" class="list-main">
          类型
          <text class="main-content fr">慢充</text>
        </view>
        <view v-if="chargeOrderList.subType == '02'" class="list-main">
          类型
          <text class="main-content fr">快充</text>
        </view>

        <view class="list-main">
          充电时长
          <text class="main-content fr">{{ chargeOrderList.chargeTimes || '0' }}分钟</text>
        </view>
        <!-- <view class="list-main">已充电量
      <text class="main-content fr">{{chargeOrderList.chargePq||'0'}}kWh</text>
    </view>
    <view class="list-main">预计充电时长
      <text class="main-content fr">{{chargeOrderList.chargeTimes||'0'}}分钟</text>
    </view> -->
        <view class="list-main">
          总充电量
          <text class="main-content fr">{{ chargeOrderList.chargePq || '0' }}kWh</text>
        </view>
      </view>
      <view class="orderdetails-lists">
        <!-- <view class="list-main">预收费用
      <text class="main-content fr">{{msg.prepayBal}}元</text>
    </view> -->
        <!-- <view wx:if='{{msg.orderStatus=="07"||msg.orderStatus=="09"}}' class="list-main">订单金额
      <text class="main-content fr">{{msg.orderTBal||0}}元</text>
    </view> -->
        <view v-for="(item, index) in payList" :key="index" class="collapsible-panel">
          <!-- 主标题行 - 点击切换折叠状态 -->
          <view class="list-main panel-header" @click="togglePanel(index)">
            <span style="font-weight: 700; font-size: 30rpx">{{ item.payTypeName }}</span>

            <!-- 折叠/展开图标 -->
            <view class="toggle-icon">
              <image
                :src="`${IMG_PATH}${item.isExpanded ? 'icon-close-row.png' : 'icon-open-row.png'}`"
                mode="aspectFit"
                class="toggle-icon-img"
              ></image>
            </view>
          </view>

          <!-- 折叠内容区域 -->
          <view class="panel-content" v-if="item.isExpanded">
            <view class="list-main sub-item" v-for="(int, i) in item.preferentialList" :key="i">
              <span class="sub-item-name">{{ int.payTypeName }}</span>
              <text class="main-content fr">{{ int.payAmt }}元</text>
            </view>
          </view>
        </view>

        <!-- <view wx:if="{{prcChargeDetList[0].pricingAmt}}" class="list-main">充电费
      <text class="main-content fr">{{prcChargeDetList[0].pricingAmt||0}}元</text>
    </view>
     <view wx:if="{{prcChargeDetList[0].pricingAmt}}" class="list-main">服务费
      <text class="main-content fr">{{prcChargeDetList[1].pricingAmt||0}}元</text>
    </view> -->
        <!-- <view class="list-main">服务费
      <text class="main-content fr">{{prcChargeDetList.rtrcModeName}}元</text>
    </view> -->
      </view>
      <view v-if="msg.orderStatus == '06'" class="orderdetails-lists">
        <view class="list-main">我的点评</view>
        <view class="list-main">
          <text>综合评分</text>
          <view v-if="orderEvaLis.evaScore == '2.0'" style="display: inline-block" class="station-opac-img">
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
          </view>
          <view v-if="orderEvaLis.evaScore == '4.0'" style="display: inline-block" class="station-opac-img">
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
          </view>
          <view v-if="orderEvaLis.evaScore == '6.0'" style="display: inline-block" class="station-opac-img">
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
          </view>
          <view v-if="orderEvaLis.evaScore == '8.0'" style="display: inline-block" class="station-opac-img">
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
          </view>
          <view v-if="orderEvaLis.evaScore == '10.0'" style="display: inline-block" class="station-opac-img">
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
            <image :src="`${IMG_PATH}<EMAIL>`"></image>
          </view>
        </view>
        <view class="evaRemark">
          {{ orderEvaLis.evaRemark || '' }}
        </view>
        <view class="evaImg">
          <view v-for="(item, index) in orderEvaLis.oderEvaPicList" :key="index">
            <image :src="item.evaPic"></image>
          </view>
        </view>
      </view>

      <view v-if="msg.orderStatus == '04'" class="ordercharge-btn">
        <button @tap="charge" class="btn blue-bg fr pingjia">查看进度</button>
      </view>
      <view class="ordercharge-btns">
        <view v-if="msg.orderStatus == '03'" class="ordercharge-btn">
          <button @tap="cancelOrder" data-name="02" class="btn orange-bg">取消订单</button>
          <button @tap="charge" class="btn blue-bg fr">充电</button>
        </view>
        <view v-if="msg.orderStatus == '02'" class="ordercharge-btn">
          <button @tap="cancelOrder" data-name="02" class="btn orange-bg">取消订单</button>
          <button @tap="pay" class="btn blue-bg fr">支付</button>
        </view>
        <view v-if="msg.orderStatus == '07'" class="ordercharge-btn">
          <button @tap="pingjia" class="btn blue-bg fr pingjia">我要评价</button>
        </view>
      </view>
      <!-- <modal
        v-if="!hidden"
        :title="tit"
        :confirm-text="zuo"
        :cancel-text="you"
        @cancel="cancel"
        @confirm="confirm"
        :no-cancel="nocancel"
      ></modal> -->
      <uni-popup ref="popup">
        <view class="popup-box">
          <view class="popup-title">{{ tit }}</view>
          <view class="btns">
            <view @click="cancel" class="cancel-btn">{{ you }}</view>
            <view @click="confirm" class="confirm-btn">{{ zuo }}</view>
          </view>
        </view>
      </uni-popup>
    </view>
    <loading v-if="!hiddenLoading">正在加载</loading>
    <loading v-if="!quxiao">{{ Otanl }}</loading>
  </view>
</template>

<script>
import { getChargeOrderDtl, changeChargingStatus, getEvaDtl, cancelOrder } from '@/services/index.js';
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup';
import uniPopupDialog from '@/components/uni-ui/uni-popup-dialog/uni-popup-dialog';
import { openLocation } from '@/utils/index.js';
import { keyFor } from 'core-js/fn/symbol';
export default {
  components: { uniPopup, uniPopupDialog },
  data() {
    return {
      quxiao: true,
      tit: '确定要取消订单吗',
      zuo: '确定',
      you: '考虑下',
      prcChargeDetList: [],
      token: '',

      msg: {
        orderStatus: '',
        orderTBal: '',
        prepayTbal: '',
        applyTime: '',
        orderStatusName: '',
      },

      chargeOrderList: {
        stationAddr: '',
        stationName: '',
        pileName: '',
        equipName: '',
        subType: '',
        chargeTimes: '',
        chargePq: '',
      },

      hidden: true,
      orderNo: '',
      orderEvaLis: [],
      hiddenLoading: false,
      payList: [],
      Otanl: '取消失败',
      statusN: '',
      nocancel: '',
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    console.log(options, 'options');
    var that = this;
    // _self.setData({
    //   hiddenLoading: true
    // });'
    this.orderNo = options.orderNo;
    const [, res] = await getChargeOrderDtl({
      orderNo: options.orderNo,
    });
    if (res) {
      console.log(res.payList, 'pay');
      // 处理payList，为每个项添加isExpanded属性
      const processedPayList = res.payList
        ? res.payList.map((item) => ({
            ...item,
            isExpanded: true, // 默认折叠状态
          }))
        : [];

      that.setData({
        msg: res,
        payList: processedPayList,
        chargeOrderList: res.chargeOrderList[0],
        hiddenLoading: true,
        prcChargeDetList: res.prcChargeDetList,
      });
      console.log(res, 'res');
      if (res.orderStatus == '06') {
        const [, result] = await getEvaDtl({
          orderNo: that.orderNo,
          evaUserType: '02',
          pageNum: 1,
          // evaObjectId: res.orderId,
          // stationId: res.stationId,
          totalNum: 99,
        });
        if (result) {
          that.setData({
            orderEvaLis: result.orderEvaList[0],
          });
        }
      }
    }
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    uni.setStorageSync('startd', 0);
  },
  methods: {
    // 切换折叠面板的展开/折叠状态
    togglePanel(index) {
      if (!this.payList || !this.payList[index]) return;

      // 创建一个新的数组，避免直接修改原数组
      const updatedPayList = [...this.payList];
      updatedPayList[index].isExpanded = !updatedPayList[index].isExpanded;

      this.setData({
        payList: updatedPayList,
      });
    },

    cancelOrder(e) {
      console.log(e, '点击了取消订单的按钮');
      var that = this;
      if (e.currentTarget.dataset.name == '01') {
        console.log('充电的取消订单');
        that.setData({
          statusN: '01',
          tit: '确定停止吗',
        });
      } else if (e.currentTarget.dataset.name == '02') {
        console.log('支付的取消订单');
        that.setData({
          statusN: '02',
          tit: '确定要取消订单吗',
        });
      }
      // that.setData({
      //   hidden: false,
      // });
      that.$refs.popup.open();
    },

    cancel() {
      this.$refs.popup.close();
      // this.setData({
      //   hidden: true, //关闭弹框
      // });
      console.log('不取消');
    },

    async confirm() {
      var that = this;
      console.log('确定取消');
      that.$refs.popup.close();
      // that.setData({
      //   hidden: true, //关闭弹框
      // });
      var _s = that.statusN;
      console.log(_s, '当前取消的状态是----');
      if (_s == '01') {
        var that = this;
        var token = that.token;
        that.setData({
          tit: '确定要结束充电吗？',
          zuo: '确定',
          you: '考虑下',
        });
        const [, res] = await changeChargingStatus(
          {
            controlType: '02',
            orderNo: that.orderNo,
          },
          {
            // #ifdef H5
            'content-type': 'application/x-www-form-urlencoded',
            // #endif
          }
        );
        if (res) {
          if (res.ret == 200) {
            uni.showToast({
              title: '结束成功',
              icon: 'success',
              duration: 1200,
            });
            setTimeout(function () {
              uni.redirectTo({
                url: '/subPackages/myorder/myorder',
              });
            }, 1200);
          } else {
            that.$refs.popup.close();
            // that.setData({
            //   hidden: true,
            // });
            uni.showToast({
              title: '操作失败',
              icon: 'loading',
              duration: 1200,
            });
          }
        }
        // that.setData({
        //   hidden: true,
        // });
        that.$refs.popup.close();
      } else if (_s == '02') {
        var that = this;
        var token = that.token;
        that.setData({
          tit: '确定要取消订单吗？',
          zuo: '确定',
          you: '考虑下',
        });
        const [, res] = await cancelOrder({
          orderNo: that.orderNo,
        });
        if (res) {
          if (res.ret == 200) {
            uni.showToast({
              title: '取消成功',
              icon: 'success',
              duration: 1200,
            });
            setTimeout(function () {
              uni.redirectTo({
                url: '/subPackages/myorder/myorder',
              });
            }, 1200);
          } else {
            that.$refs.popup.close();
            that.setData({
              // hidden: true,
              quxiao: false,
              Otanl: res.msg,
            });
            setTimeout(function () {
              that.setData({
                quxiao: true,
              });
            }, 2000);
          }
        }
      }
    },

    charge() {
      console.log(this.orderNo, '这是跳到充电页面的传参');
      var name = this.chargeOrderList.pileName + ' ' + this.chargeOrderList.equipName;
      uni.setStorageSync('nameCup', name);
      uni.redirectTo({
        url: '/pages/basic/charge/charge?orderNo=' + this.orderNo,
      });
    },

    pay() {
      var that = this;
      var price = that.chargeOrderList.rtrcModeName;
      uni.setStorage({
        key: 'price',
        data: price,
      });
      var orderNo = that.orderNo;
      uni.navigateTo({
        url: '/subPackages/paymentorder/paymentorder?orderNo=' + that.orderNo,
      });
    },

    pingjia() {
      console.log('评价');
      var that = this;
      var orderNo = that.orderNo;
      uni.redirectTo({
        url: '/subPackages/comment/comment?orderNo=' + that.orderNo,
      });
    },

    distanceMap() {
      var that = this;
      console.log(that.chargeOrderList, 'INFO');
      openLocation({
        latitude: parseFloat(that.chargeOrderList.lat),
        longitude: parseFloat(that.chargeOrderList.lon),
        address: that.chargeOrderList.stationAddr,
        success(e) {
          console.log(e, '调起成功');
        },
      });
    },
  },
};
</script>
<style>
@import './ordercharge.css';
</style>
<style lang="scss" scoped>
/* 折叠面板样式 */
.collapsible-panel {
  margin-bottom: 10rpx;

  .panel-header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .toggle-icon {
      position: absolute;
      right: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      transition: all 0.3s;

      .toggle-icon-img {
        width: 32rpx;
        height: 32rpx;
        display: block;
      }
    }
  }

  .panel-content {
    animation: slideDown 0.3s ease-out;
    overflow: hidden;

    .sub-item {
      padding-left: 30rpx;
      font-size: 28rpx;
      color: #666;
      border-top: 1px solid #f5f5f5;

      .sub-item-name {
        color: #333;
      }
    }
  }
}

@keyframes slideDown {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 500rpx;
    opacity: 1;
  }
}

.popup-box {
  background: #fff;
  border-radius: 24rpx;
  width: 80vw;
  .popup-title {
    font-size: 36rpx;
    width: 100%;
    text-align: center;
    margin: 24rpx 0;
    padding: 24rpx 0;
  }
  .btns {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 24rpx;
    border-top: 1rpx solid #cbcbcb;
    .ok-btn {
      font-size: 32rpx;
      font-weight: bold;
      color: rgb(84, 179, 255);
      width: 100%;
      box-sizing: border-box;
      padding: 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-left: 1rpx solid #cbcbcb;
    }
    .cancel-btn {
      font-size: 32rpx;
      font-weight: bold;
      width: 49%;
      box-sizing: border-box;
      padding: 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-right: 1rpx solid #cbcbcb;
    }
    .confirm-btn {
      font-size: 32rpx;
      font-weight: bold;
      color: rgb(84, 179, 255);
      width: 49%;
      box-sizing: border-box;
      padding: 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-left: 1rpx solid #cbcbcb;
    }
  }
}
</style>
