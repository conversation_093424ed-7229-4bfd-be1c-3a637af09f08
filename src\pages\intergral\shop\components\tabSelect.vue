<!--
@name: 蓝底tab选择器
@description: 描述
@time: 2025/1/3
-->
<template>
  <scroll-view :scroll-x="true" :show-scrollbar="false">
    <view class="tabs">
      <view
        v-for="(item, id) in options"
        :key="id"
        :class="selectedId == id ? 'is-active-box' : 'tabs-item'"
        @click="handleClick(item, id)"
      >
        <text :class="{ 'is-active': selectedId === id }">{{ item.name }}</text>
        <!-- <view :class="{ line: selectedId === id }"></view> -->
      </view>
    </view>
  </scroll-view>
</template>

<script>
export default {
  name: 'index',
  props: {
    options: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      selectedId: 0,
    };
  },
  methods: {
    // 切换tab
    handleClick(item, id) {
      this.selectedId = id;
      this.$emit('change', item);
    },
  },
};
</script>

<style scoped lang="scss">
.tabs {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  overflow: scroll;
  white-space: nowrap;
  gap: 12rpx;
  grid-gap: 12rpx;
  &-item {
    display: flex;
    flex-direction: column;
    font-size: 28rpx;
    line-height: 42rpx;
    color: #1e9cff;
    align-items: center;
    padding: 10rpx 20rpx;
    background: rgba(30, 156, 255, 0.1);
    border-radius: 42rpx;
    .line {
      margin-top: 12rpx;
      height: 6rpx;
      width: 37rpx;
      background: #1e9cff;
      border-radius: 2rpx;
    }
  }
  .is-active-box {
    display: flex;
    flex-direction: column;
    font-size: 28rpx;
    line-height: 42rpx;
    color: #666666;
    align-items: center;
    padding: 10rpx 20rpx;
    background: #1e9cff;
    border-radius: 42rpx;
    .is-active {
      font-size: 28rpx;
      line-height: 42rpx;
      color: #fff;
    }
  }
}
</style>
