.application {
  width: 100%;
  min-height: 100vh;
  background: rgba(238, 242, 246, 1);
  padding-bottom: 200rpx;
}
.application_head {
  width: 100%;
  height: 338rpx;
  background: #fff;
  display: flex;
  align-content: space-between;
  flex-wrap: wrap;
  justify-content: center;
  box-sizing: border-box;
  padding: 50rpx 0;
  margin-bottom: 16rpx;
}

.application_head image {
  width: 116rpx;
  height: 116rpx;
}

.application_head .head_title {
  width: 100%;
  font-size: 30rpx;
  color: rgba(102, 102, 102, 1);
  font-weight: 400;
  text-align: center;
}
.application_head .head_price {
  width: 100%;
  font-size: 40rpx;
  text-align: center;
  color: #2196f3;
  font-weight: 500;
}

.application_head .head_price .head_count {
  font-size: 26rpx;
  color: rgb(102, 102, 102);
}

.application_head .head_price text {
  margin-right: 11rpx;
}
.application_head .head_name {
  width: 100%;
  text-align: center;
  font-size: 36rpx;
  color: rgba(51, 51, 51, 1);
  font-weight: 500;
}

.application_head .head_msg {
  width: 565rpx;
  text-align: center;
  line-height: 40rpx;
  font-weight: 400;
  font-size: 26rpx;
  color: rgba(153, 153, 153, 1);
}

.application .application_height {
  height: 288rpx;
}

.application .application_hight {
  height: 388rpx;
}
.application .application_overflow {
  width: 100%;
  height: 954rpx;
  overflow: auto;
}
.application .application_overflow .application_record {
  width: 100%;
  height: 139rpx;
  background: #fff;
  box-sizing: border-box;
  padding: 0 27rpx;
  display: flex;
  align-items: center;
}

.application .application_overflow .application_record image {
  width: 74rpx;
  height: 74rpx;
  margin-right: 31rpx;
}

.application .application_overflow .application_record .record_meeage {
  flex: 1;
  height: 100%;
  border-bottom: 1rpx solid rgba(198, 200, 201, 0.9);
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  align-content: space-between;
  padding: 22rpx 0;
}

.application .application_overflow .application_record .record_meeage .record_title {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.application .application_overflow .application_record .record_meeage .record_title .title {
  font-size: 34rpx;
}

.application .application_overflow .application_record .record_meeage .record_title .price {
  font-size: 36rpx;
}

.application .application_overflow .application_record .record_meeage .record_price {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
}

.application .application_flex {
  margin-top: 30rpx;
  width: 100%;
}

.application .application_flex .flex_btn {
  width: 690rpx;
  height: 88rpx;
  background: #2196f3;
  border-radius: 4rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
  color: rgba(255, 255, 255, 1);
  font-weight: 400;
  margin: 0 auto;
}

.application .application_flex .flex_btn_not {
  opacity: 0.4;
}

.application .application_flex .flex_title {
  width: 100%;
  height: 34rpx;
  font-size: 26rpx;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  justify-content: center;
}

.application .application_flex .flex_title text {
  color: #2196f3;
}

.application .application_flex .flex_title .flex_title_line {
  color: rgba(153, 153, 153, 1);
  margin: 0 40rpx;
}

.application .application_position {
  position: fixed;
  z-index: 1;
  bottom: 14rpx;
}
