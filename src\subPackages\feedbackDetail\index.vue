<template>
  <view class="mine-page">
    <view class="justify-between align-center statusline">
      <text>{{ form.title }}</text>
      <text :class="form.state === '01' ? 'red' : 'blue'">{{ form.state === '01' ? '待解决' : '已解决' }}</text>
    </view>
    <view class="log right">
      <view class="content">
        {{ form.content }}
        <template v-if="form.faqImageList.length > 0">
          <image
            v-for="item in form.faqImageList"
            :key="item.imageUrl"
            class="content-img"
            mode="aspectFit"
            :src="item.imageUrl"
          ></image>
        </template>
      </view>
      <image :src="`${IMG_PATH}logo.png`" class="avatar" />
    </view>
    <block v-for="(item, index) in form.replyList" :key="index">
      <view class="log left" v-if="item.custType == '02'">
        <image :src="`${IMG_PATH}logo.png`" class="avatar" />

        <view class="content">
          {{ item.content }}
          <template v-if="item.replyImageList.length > 0">
            <image
              v-for="it in item.replyImageList"
              :key="it.imageUrl"
              class="content-img"
              mode="aspectFit"
              :src="it.imageUrl"
            ></image>
          </template>
        </view>
      </view>

      <view class="log right" v-if="item.custType == '01'">
        <view class="content">
          {{ item.content }}
          <template v-if="item.replyImageList.length > 0">
            <image
              v-for="it in item.replyImageList"
              :key="it.imageUrl"
              class="content-img"
              mode="aspectFit"
              :src="it.imageUrl"
            ></image>
          </template>
        </view>

        <image :src="`${IMG_PATH}logo.png`" class="avatar" />
      </view>
    </block>
    <view class="nodata" v-if="!form.content">
      <image :src="`${IMG_PATH}nodata.png`" mode="widthFix" />
      <text>暂无数据</text>
    </view>
    <view style="height: 200rpx"></view>

    <view class="safe_foot flex-row" v-if="form.state === '01'">
      <main-btn ghost @click="reply" class="item">追问</main-btn>
      <main-btn @click="resolve" class="item">已解决</main-btn>
    </view>
  </view>
</template>

<script>
import mainBtn from '@/components/main-btn';

import { getFaqReply, offFaqReply, submitPubFaq } from '@/services/index.js';
const fieldNameMap = {
  feedbackType: '问题分类',
  title: '问题标题',
  content: '问题详情',
};
export default {
  components: {
    mainBtn,
  },
  data() {
    return {
      faqId: null,
      typeName: null,
      form: {
        title: '',
        state: '',
        replyList: [],
        content: '',
      },
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData(
      {
        faqId: options.faqId,
      },
      () => {
        this.loadFeedbackLog();
      }
    );
  },
  methods: {
    async loadFeedbackLog() {
      const [, res] = await getFaqReply({
        faqId: this.faqId,
      });
      console.log(res, 'res');
      if (res) {
        this.form = res;
      }
    },

    async resolve() {
      const [, res] = await offFaqReply({
        faqId: this.faqId,
      });
      if (res && res.ret == '200') {
        uni.showToast({
          title: res.msg,
          icon: 'error',
        });
        uni.navigateBack();
      } else {
        uni.showToast({
          title: '操作失败',
          icon: 'error',
        });
      }
    },

    reply() {
      uni.redirectTo({
        url: `/subPackages/feedback/index?faqId=${this.faqId || ''}&typeId=${this.form.type}&title=${
          this.form.title || ''
        }&typeName=${this.form.typeName || ''}`,
      });
    },

    async submit() {
      let errField = null;
      for (let i in this.form) {
        if (i !== 'faqImageList' && !this.form[i]) {
          errField = i;
          break;
        }
      }
      if (errField) {
        return uni.showToast({
          title: `请填写${fieldNameMap[errField]}`,
          icon: 'error',
        });
      }
      const { typeId, title, content, faqImageList, feedbackType } = this.form;
      const [, res] = await submitPubFaq({
        typeId,
        title,
        content,
        feedbackType: feedbackType || '',
        faqImageList: JSON.stringify(faqImageList),
      });
      if (res && res.ret == '200') {
        uni.showToast({
          title: res.msg,
        });
        uni.navigateBack();
      } else {
        uni.showToast({
          icon: 'error',
          title: '提交失败',
        });
      }
    },
  },
};
</script>
<style>
@import '../../common/css/global.css';
@import './index.css';
</style>
