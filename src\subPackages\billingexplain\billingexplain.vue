<template>
  <view class="billingexplain">
    <text class="title">一、申请企业支付宝账号：</text>
    <text>方案1：报名前完成企业版支付宝账号的注册。直接至支付宝注册页面注册企业账户。</text>
    <text
      >方案2：报名时完成企业版支付宝账号的注册。进入天猫招商页面，用淘宝会员账号登陆天猫报名系统，通过支付宝检测页面上的注册入口，注册企业账户。</text
    >
    <text class="title">注意：</text>
    <text
      >a：天猫要求您所提供的支付宝账号是一个全新的账号，不可绑定任何淘宝会员ID，也不可作为登陆邮箱绑定任何淘宝或天猫账号。</text
    >
  </view>
</template>

<script>
export default {
  data() {
    return {};
  },
  /**
   * 生命周期函数--监听页面加载
   */ onLoad: function (options) {},
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {},
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {},
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {},
  methods: {},
};
</script>
<style>
@import './billingexplain.css';
</style>
