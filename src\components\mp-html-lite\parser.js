/**
 * @fileoverview html 解析器 (精简版)
 */

// 配置
const config = {
  // 信任的标签（保持标签名不变）
  trustTags: makeMap('a,abbr,b,blockquote,br,code,div,em,h1,h2,h3,h4,h5,h6,i,img,p,span,strong,ol,ul,li'),

  // 块级标签（转为 div，其他的非信任标签转为 span）
  blockTags: makeMap('address,article,aside,body,center,cite,footer,header,html,nav,pre,section'),

  // 要移除的标签
  ignoreTags: makeMap(
    'area,base,canvas,embed,frame,head,iframe,input,link,map,meta,param,script,source,style,textarea,title,track,wbr'
  ),

  // 自闭合的标签
  voidTags: makeMap('area,base,br,col,circle,embed,frame,hr,img,input,link,meta,param,source,track,use,wbr'),

  // html 实体
  entities: {
    lt: '<',
    gt: '>',
    quot: '"',
    apos: "'",
    nbsp: '\xA0',
  },

  // 默认的标签样式
  tagStyle: {
    address: 'font-style:italic',
    big: 'display:inline;font-size:1.2em',
    caption: 'display:table-caption;text-align:center',
    center: 'text-align:center',
    cite: 'font-style:italic',
    dd: 'margin-left:40px',
    mark: 'background-color:yellow',
    pre: 'font-family:monospace;white-space:pre',
    s: 'text-decoration:line-through',
    small: 'display:inline;font-size:0.8em',
    u: 'text-decoration:underline',
    ol: 'display:block;margin:1em 0;padding-left:40px',
    ul: 'display:block;margin:1em 0;padding-left:40px',
    li: 'display:list-item;margin:0.5em 0',
  },
};

const blankChar = makeMap(' ,\r,\n,\t,\f');

/**
 * @description 创建 map
 * @param {String} str 逗号分隔
 */
function makeMap(str) {
  const map = Object.create(null);
  const list = str.split(',');
  for (let i = list.length; i--; ) {
    map[list[i]] = true;
  }
  return map;
}

/**
 * @description 解码 html 实体
 * @param {String} str 要解码的字符串
 * @param {Boolean} amp 要不要解码 &amp;
 * @returns {String} 解码后的字符串
 */
function decodeEntity(str, amp) {
  let i = str.indexOf('&');
  while (i !== -1) {
    const j = str.indexOf(';', i + 3);
    let code;
    if (j === -1) break;
    if (str[i + 1] === '#') {
      // &#123; 形式的实体
      code = parseInt((str[i + 2] === 'x' ? '0' : '') + str.substring(i + 2, j));
      if (!isNaN(code)) {
        str = str.substr(0, i) + String.fromCharCode(code) + str.substr(j + 1);
      }
    } else {
      // &nbsp; 形式的实体
      code = str.substring(i + 1, j);
      if (config.entities[code] || (code === 'amp' && amp)) {
        str = str.substr(0, i) + (config.entities[code] || '&') + str.substr(j + 1);
      }
    }
    i = str.indexOf('&', i + 1);
  }
  return str;
}

/**
 * @description html 解析器
 * @param {Object} vm 组件实例
 */
function Parser(vm) {
  this.options = vm || {};
  this.tagStyle = Object.assign({}, config.tagStyle, this.options.tagStyle);
  this.imgList = vm.imgList || [];
  this.imgList._unloadimgs = 0;
  this.attrs = Object.create(null);
  this.stack = [];
  this.nodes = [];
}

/**
 * @description 执行解析
 * @param {String} content 要解析的文本
 */
Parser.prototype.parse = function (content) {
  new Lexer(this).parse(content);
  // 出栈未闭合的标签
  while (this.stack.length) {
    this.popNode();
  }
  return this.nodes;
};

/**
 * @description 解析样式表
 * @param {Object} node 标签
 * @returns {Object}
 */
Parser.prototype.parseStyle = function (node) {
  const attrs = node.attrs;
  const list = (this.tagStyle[node.name] || '').split(';').concat((attrs.style || '').split(';'));
  const styleObj = {};
  let tmp = '';

  // 转换 width 和 height 属性
  if (attrs.width) {
    styleObj.width = parseFloat(attrs.width) + (attrs.width.includes('%') ? '%' : 'px');
    attrs.width = undefined;
  }
  if (attrs.height) {
    styleObj.height = parseFloat(attrs.height) + (attrs.height.includes('%') ? '%' : 'px');
    attrs.height = undefined;
  }

  for (let i = 0, len = list.length; i < len; i++) {
    const info = list[i].split(':');
    if (info.length < 2) continue;
    const key = info.shift().trim().toLowerCase();
    let value = info.join(':').trim();
    if (!styleObj[key]) {
      styleObj[key] = value;
    }
  }

  node.attrs.style = tmp;
  return styleObj;
};

/**
 * @description 解析到标签名
 * @param {String} name 标签名
 * @private
 */
Parser.prototype.onTagName = function (name) {
  this.tagName = name.toLowerCase();
};

/**
 * @description 解析到属性名
 * @param {String} name 属性名
 * @private
 */
Parser.prototype.onAttrName = function (name) {
  name = name.toLowerCase();
  if (name.substr(0, 5) === 'data-') {
    if (name === 'data-src' && !this.attrs.src) {
      this.attrName = 'src';
    } else {
      this.attrName = undefined;
    }
  } else {
    this.attrName = name;
    this.attrs[name] = 'T'; // boolean 型属性缺省设置
  }
};

/**
 * @description 解析到属性值
 * @param {String} val 属性值
 * @private
 */
Parser.prototype.onAttrVal = function (val) {
  const name = this.attrName || '';
  if (name === 'style' || name === 'href') {
    this.attrs[name] = decodeEntity(val, true);
  } else if (name.includes('src')) {
    this.attrs[name] = decodeEntity(val, true);
  } else if (name) {
    this.attrs[name] = val;
  }
};

/**
 * @description 解析到标签开始
 * @param {Boolean} selfClose 是否有自闭合标识 />
 * @private
 */
Parser.prototype.onOpenTag = function (selfClose) {
  // 拼装 node
  const node = Object.create(null);
  node.name = this.tagName;
  node.attrs = this.attrs;

  this.attrs = Object.create(null);

  const attrs = node.attrs;
  const parent = this.stack[this.stack.length - 1];
  const siblings = parent ? parent.children : this.nodes;
  const close = config.voidTags[node.name];

  // 处理自闭合标签
  if (close) {
    if (config.ignoreTags[node.name]) {
      return;
    }
    // 解析 style
    const styleObj = this.parseStyle(node);
    siblings.push(node);
    return;
  }

  // 处理非自闭合标签
  node.children = [];
  this.stack.push(node);
};

/**
 * @description 解析到标签结束
 * @param {String} name 标签名
 * @private
 */
Parser.prototype.onCloseTag = function (name) {
  name = name.toLowerCase();
  let i;
  for (i = this.stack.length; i--; ) {
    if (this.stack[i].name === name) break;
  }
  if (i !== -1) {
    while (this.stack.length > i) {
      this.popNode();
    }
  }
};

/**
 * @description 出栈
 * @private
 */
Parser.prototype.popNode = function () {
  const node = this.stack.pop();
  const parent = this.stack[this.stack.length - 1];
  const siblings = parent ? parent.children : this.nodes;
  siblings.push(node);
};

/**
 * @description 解析到文本
 * @param {String} text 文本内容
 * @private
 */
Parser.prototype.onText = function (text) {
  // 清理文本内容，移除多余的换行符和空白字符
  text = text.replace(/↵/g, '').replace(/\s+/g, ' ').trim();
  if (!text) return;
  const node = Object.create(null);
  node.type = 'text';
  node.text = decodeEntity(text);
  const parent = this.stack[this.stack.length - 1];
  const siblings = parent ? parent.children : this.nodes;
  siblings.push(node);
};

/**
 * @description html 词法分析器
 * @param {Object} handler 高层处理器
 */
function Lexer(handler) {
  this.handler = handler;
}

/**
 * @description 执行解析
 * @param {String} content 要解析的文本
 */
Lexer.prototype.parse = function (content) {
  this.content = content || '';
  this.i = 0; // 当前解析位置
  this.start = 0; // 当前解析开始位置
  this.state = this.text; // 当前状态
  for (let len = this.content.length; this.i < len; this.i++) {
    this.state();
  }
};

/**
 * @description 检查标签是否有效
 * @param {String} method 高层处理器接口
 * @param {String} content 要检查的内容
 * @private
 */
Lexer.prototype.checkTag = function (method, content) {
  if (method && content) {
    this.handler[method](content);
  }
};

/**
 * @description 文本状态
 * @private
 */
Lexer.prototype.text = function () {
  const content = this.content;
  let i = this.i;
  let j = content.indexOf('<', i); // 查找最近的标签
  if (j === -1) {
    // 没有标签了
    if (i < content.length) {
      this.checkTag('onText', content.substring(i));
      this.i = content.length;
    }
    return;
  }
  if (j > i) {
    // 有文本内容
    this.checkTag('onText', content.substring(i, j));
    this.i = j - 1;
    return;
  }
  const c = content[j + 1];
  if (c === '/' || (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z')) {
    // 标签开始
    this.start = j;
    this.state = this.tagName;
  } else if (c === '!') {
    // 注释
    this.i = content.indexOf('>', j);
    if (this.i === -1) {
      this.i = content.length;
    }
  } else {
    // 其他内容，可能是标签内的文本
    this.checkTag('onText', content.substring(j, j + 1));
  }
};

/**
 * @description 标签名状态
 * @private
 */
Lexer.prototype.tagName = function () {
  const content = this.content;
  let i = this.i;
  let j = content.indexOf('>', i);
  if (j === -1) {
    // 没有关闭标签
    this.i = content.length;
    return;
  }
  const start = this.start;
  let c = content[start + 1];
  if (c === '/') {
    // 结束标签
    this.checkTag('onCloseTag', content.substring(start + 2, j).trim());
  } else {
    // 开始标签
    let name = '';
    let k = start + 1;
    while (k < j && !blankChar[content[k]]) {
      name += content[k++];
    }
    this.checkTag('onTagName', name);
    this.parseAttrs(k, j);
    if (content[j - 1] === '/') {
      // 自闭合标签
      this.checkTag('onOpenTag', true);
    } else {
      this.checkTag('onOpenTag', false);
    }
  }
  this.i = j;
  this.start = j + 1;
  this.state = this.text;
};

/**
 * @description 解析属性
 * @param {Number} i 开始位置
 * @param {Number} j 结束位置
 * @private
 */
Lexer.prototype.parseAttrs = function (i, j) {
  const content = this.content;
  let attrName = '';
  let k = i;
  while (k < j) {
    const c = content[k];
    if (blankChar[c]) {
      k++;
      continue;
    }
    if (c === '/' || c === '>') {
      // 标签结束
      break;
    }
    attrName = '';
    while (k < j && !blankChar[content[k]] && content[k] !== '=') {
      attrName += content[k++];
    }
    if (attrName) {
      this.checkTag('onAttrName', attrName);
    }
    while (k < j && blankChar[content[k]]) {
      k++;
    }
    if (content[k] === '=') {
      k++;
      while (k < j && blankChar[content[k]]) {
        k++;
      }
      if (k < j) {
        let quote = '';
        if (content[k] === '"' || content[k] === "'") {
          quote = content[k++];
        }
        let value = '';
        while (k < j && (quote ? content[k] !== quote : !blankChar[content[k]])) {
          value += content[k++];
        }
        if (quote) {
          k++;
        }
        this.checkTag('onAttrVal', value);
      }
    }
  }
};

module.exports = Parser;
