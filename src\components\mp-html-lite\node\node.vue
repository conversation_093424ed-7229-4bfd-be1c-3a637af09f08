<template>
  <view :id="attrs.id" :class="'_block _' + name + ' ' + attrs.class" :style="attrs.style">
    <block v-for="(n, i) in childs" v-bind:key="i">
      <!-- 图片 -->
      <image
        v-if="n.name === 'img'"
        :id="n.attrs.id"
        :class="'_img ' + n.attrs.class"
        :style="n.attrs.style"
        :src="n.attrs.src"
        mode="widthFix"
        :data-i="i"
        @load="imgLoad"
        @error="mediaError"
        @tap.stop="imgTap"
      />

      <!-- 文本 -->
      <text v-else-if="n.text" decode>{{ n.text }}</text>
      <text v-else-if="n.name === 'br'">\n</text>

      <!-- 链接 -->
      <view
        v-else-if="n.name === 'a'"
        :id="n.attrs.id"
        :class="(n.attrs.href ? '_a ' : '') + n.attrs.class"
        hover-class="_hover"
        :style="'display:inline;' + n.attrs.style"
        :data-i="i"
        @tap.stop="linkTap"
      >
        <node name="span" :childs="n.children" :opts="opts" style="display: inherit" />
      </view>

      <!-- 继续递归 -->
      <view
        v-else-if="n.c === 2"
        :id="n.attrs.id"
        :class="'_block _' + n.name + ' ' + n.attrs.class"
        :style="n.f + ';' + n.attrs.style"
      >
        <node
          v-for="(n2, j) in n.children"
          v-bind:key="j"
          :style="n2.f"
          :name="n2.name"
          :attrs="n2.attrs"
          :childs="n2.children"
          :opts="opts"
        />
      </view>
      <node v-else :style="n.f" :name="n.name" :attrs="n.attrs" :childs="n.children" :opts="opts" />
    </block>
  </view>
</template>

<script module="handler" lang="wxs">
// 行内标签列表
var inlineTags = {
  abbr: true,
  b: true,
  big: true,
  code: true,
  del: true,
  em: true,
  i: true,
  ins: true,
  label: true,
  q: true,
  small: true,
  span: true,
  strong: true,
  sub: true,
  sup: true
}
/**
 * @description 判断是否为行内标签
 */
module.exports = {
  isInline: function (tagName, style) {
    return inlineTags[tagName] || (style || '').indexOf('display:inline') !== -1
  }
}
</script>

<script>
import node from './node';
export default {
  name: 'node',
  options: {
    virtualHost: true,
  },
  data() {
    return {
      ctrl: {},
    };
  },
  props: {
    name: String,
    attrs: {
      type: Object,
      default() {
        return {};
      },
    },
    childs: Array,
    opts: Array,
  },
  components: {
    node,
  },
  mounted() {
    this.$nextTick(() => {
      for (this.root = this.$parent; this.root.$options.name !== 'mp-html'; this.root = this.root.$parent);
    });
  },
  methods: {
    /**
     * @description 图片点击事件
     * @param {Event} e
     */
    imgTap(e) {
      const node = this.childs[e.currentTarget.dataset.i];
      if (node.a) {
        this.linkTap(node.a);
        return;
      }
      if (node.attrs.ignore) return;
      this.root.$emit('imgtap', node.attrs);
    },

    /**
     * @description 图片加载完成事件
     * @param {Event} e
     */
    imgLoad(e) {
      const i = e.currentTarget.dataset.i;
      if (!this.childs[i].w) {
        this.$set(this.ctrl, i, e.detail.width);
      } else if ((this.opts[1] && !this.ctrl[i]) || this.ctrl[i] === -1) {
        this.$set(this.ctrl, i, 1);
      }
    },

    /**
     * @description 链接点击事件
     * @param {Event} e
     */
    linkTap(e) {
      const node = e.currentTarget ? this.childs[e.currentTarget.dataset.i] : {};
      const attrs = node.attrs || e;
      const href = attrs.href;
      this.root.$emit(
        'linktap',
        Object.assign(
          {
            innerText: this.root.getText(node.children || []),
          },
          attrs
        )
      );
    },

    /**
     * @description 错误事件
     * @param {Event} e
     */
    mediaError(e) {
      const i = e.currentTarget.dataset.i;
      const node = this.childs[i];
      if (node.name === 'img') {
        if (this.opts[2]) {
          this.$set(this.ctrl, i, -1);
        }
      }
      if (this.root) {
        this.root.$emit('error', {
          source: node.name,
          attrs: node.attrs,
        });
      }
    },
  },
};
</script>

<style>
/* a 标签默认效果 */
._a {
  padding: 1.5px 0 1.5px 0;
  color: #366092;
  word-break: break-all;
}

/* a 标签点击态效果 */
._hover {
  text-decoration: underline;
  opacity: 0.7;
}

/* 图片默认效果 */
._img {
  max-width: 100%;
  -webkit-touch-callout: none;
}

/* 内部样式 */
._block {
  display: block;
}

._b,
._strong {
  font-weight: bold;
}

._em,
._i {
  font-style: italic;
}

._h1 {
  font-size: 2em;
}

._h2 {
  font-size: 1.5em;
}

._h3 {
  font-size: 1.17em;
}

._h1,
._h2,
._h3,
._h4,
._h5,
._h6 {
  display: block;
  font-weight: bold;
}

/* 列表样式 */
._ol,
._ul {
  display: block;
  margin: 1em 0;
  padding-left: 40px;
}

._li {
  display: list-item;
  margin: 0.5em 0;
}

._ol ._li {
  list-style-type: decimal;
}

._ul ._li {
  list-style-type: disc;
}
</style>
