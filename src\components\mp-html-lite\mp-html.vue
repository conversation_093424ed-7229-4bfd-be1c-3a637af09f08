<template>
  <view id="_root" :class="(selectable ? '_select ' : '') + '_root'" :style="containerStyle">
    <slot v-if="!nodes[0]" />
    <node v-else :childs="nodes" :opts="[lazyLoad, loadingImg, errorImg, showImgMenu, selectable]" name="span" />
  </view>
</template>

<script>
import node from './node/node.vue';
import Parser from './parser.js';

export default {
  name: 'mp-html',
  components: { node },
  data() {
    return {
      nodes: []
    };
  },
  props: {
    containerStyle: {
      type: String,
      default: '',
    },
    content: {
      type: String,
      default: '',
    },
    errorImg: {
      type: String,
      default: '',
    },
    lazyLoad: {
      type: [Boolean, String],
      default: false,
    },
    loadingImg: {
      type: String,
      default: '',
    },
    showImgMenu: {
      type: [Boolean, String],
      default: true,
    },
    selectable: [<PERSON><PERSON><PERSON>, String]
  },
  watch: {
    content(content) {
      this.setContent(content);
    },
  },
  mounted() {
    if (this.content && !this.nodes.length) {
      this.setContent(this.content);
    }
  },
  methods: {
    /**
     * @description 获取文本内容
     * @return {String}
     */
    getText(nodes) {
      let text = '';
      (function traversal(nodes) {
        for (let i = 0; i < nodes.length; i++) {
          const node = nodes[i];
          if (node.type === 'text') {
            text += node.text.replace(/&amp;/g, '&');
          } else if (node.name === 'br') {
            text += '\n';
          } else {
            // 块级标签前后加换行
            const isBlock =
              node.name === 'p' ||
              node.name === 'div' ||
              node.name === 'tr' ||
              node.name === 'li' ||
              (node.name[0] === 'h' && node.name[1] > '0' && node.name[1] < '7');
            if (isBlock && text && text[text.length - 1] !== '\n') {
              text += '\n';
            }
            // 递归获取子节点的文本
            if (node.children) {
              traversal(node.children);
            }
            if (isBlock && text[text.length - 1] !== '\n') {
              text += '\n';
            } else if (node.name === 'td' || node.name === 'th') {
              text += '\t';
            }
          }
        }
      })(nodes || this.nodes);
      return text;
    },

    /**
     * @description 设置内容
     * @param {String} content html 内容
     * @param {Boolean} append 是否在尾部追加
     */
    setContent(content, append) {
      if (!append || !this.imgList) {
        this.imgList = [];
      }
      const nodes = new Parser(this).parse(content);
      this.$set(this, 'nodes', append ? (this.nodes || []).concat(nodes) : nodes);

      this.$nextTick(() => {
        this.$emit('load');
      });
    }
  },
};
</script>

<style>
/* 根节点样式 */
._root {
  padding: 1px 0;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
}

/* 长按复制 */
._select {
  user-select: text;
}
</style>
