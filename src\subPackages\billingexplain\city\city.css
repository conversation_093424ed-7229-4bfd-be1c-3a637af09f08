.searchLetter {
  position: fixed;
  right: 0;
  width: 40px;
  height: 80%;
  text-align: center;
  justify-content: center;
  display: flex;
  flex-direction: column;
  color: #666;
  z-index: 1;
}

/*二级车系  */
.tab2 {
  width: 560rpx;
  height: 70rpx;
  color: #2a2a2a;
  font-size: 30rpx;
  line-height: 70rpx;
  border-bottom: 1px solid #eaeaea;
  padding-left: 30rpx;
}
.tab3 {
  height: 70rpx;
  color: #2a2a2a;
  font-size: 30rpx;
  line-height: 70rpx;
  border-bottom: 1px solid #eaeaea;
  padding-left: 30rpx;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
/*二级内容  */
.scoll_car_service {
  display: flex;
  position: fixed;
  top: 1px;
  left: 30%;
  z-index: 5;
  width: 70%;
  height: 100%;
  box-shadow: 0px 0px 3px 3px #999;
  background-color: #f5f5f5;
}
.scoll_vender_service {
  display: flex;
  position: fixed;
  top: 1px;
  left: 30%;
  z-index: 10;
  width: 70%;
  height: 100%;
  box-shadow: 0px 0px 3px 3px #999;
  background-color: #f5f5f5;
}
.searchLetter view {
  height: 70rpx;
}
.touchClass {
  /* background-color: rgba(0, 0, 0,0.5); */
  font-size: 28rpx;
}
.showSlectedLetter {
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 50%;
  left: 50%;
  margin: -50px;
  width: 100px;
  height: 100px;
  border-radius: 10px;
  font-size: 26px;
  z-index: 1;
}
.selection {
  display: flex;
  width: 100%;
  flex-direction: column;
}
.item_letter {
  display: flex;
  background-color: #f8f8f8;
  /* border-bottom: 1px solid red; */
  height: 30px;
  /* color: #896A0D; */
  font-size: 32rpx;
  padding-left: 10px;
  align-items: center;
}
.item_city {
  background-color: #fff;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  color: #2a2a2a;
  font-size: 30rpx;
  /* align-items: center; */
  display: flex;
}

.car_img {
  width: 167rpx;
  height: 65rpx;
}
/* .car_name{
  position: absolute;
  left: 150rpx;
} */
