"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GetToken = GetToken;
exports.ToPay = ToPay;
exports.toCustomer = toCustomer;
exports.openNavigation = openNavigation;
exports.HarmonyOsShareWxCard = HarmonyOsShareWxCard;
exports.HarmonyOsMakePhoneCall = HarmonyOsMakePhoneCall;
exports.HarmonyOsGetLocal = HarmonyOsGetLocal;
exports.callNativeAutoRenew = callNativeAutoRenew;

// 桥接方法
// 原生端 获取token
function NativeGetToken() {
  return new Promise(function _callee(resolve, reject) {
    var tokenBridge;
    return regeneratorRuntime.async(function _callee$(_context) {
      while (1) {
        switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return regeneratorRuntime.awrap(window.bridge.promisifyDsAsync('bangdao.user.getToken')({
              isNeedLogin: true
            }));

          case 2:
            tokenBridge = _context.sent;

            if (tokenBridge) {
              uni.setStorageSync('app', tokenBridge.app);
              resolve(tokenBridge);
            } else {
              resolve(null);
            }

          case 4:
          case "end":
            return _context.stop();
        }
      }
    });
  });
} // 鸿蒙端 获取token


function HarmonyOsGetToken() {
  return new Promise(function _callee2(resolve, reject) {
    var tokenBridge;
    return regeneratorRuntime.async(function _callee2$(_context2) {
      while (1) {
        switch (_context2.prev = _context2.next) {
          case 0:
            _context2.next = 2;
            return regeneratorRuntime.awrap(window.bridge.promisifyDsAsync('HarmonyOsGetToken')({
              isNeedLogin: true
            }));

          case 2:
            tokenBridge = _context2.sent;
            console.log('鸿蒙端获取的token', tokenBridge);

            if (tokenBridge) {
              uni.setStorageSync('app', tokenBridge.app);
              resolve(tokenBridge);
            } else {
              resolve(null);
            }

          case 5:
          case "end":
            return _context2.stop();
        }
      }
    });
  });
} // 鸿蒙端 获取当前位置


function HarmonyOsGetLocal() {
  return new Promise(function _callee3(resolve, reject) {
    var local;
    return regeneratorRuntime.async(function _callee3$(_context3) {
      while (1) {
        switch (_context3.prev = _context3.next) {
          case 0:
            _context3.next = 2;
            return regeneratorRuntime.awrap(window.bridge.promisifyDsAsync('HarmonyOsGetLocal')());

          case 2:
            local = _context3.sent;
            console.log('鸿蒙端获取的位置信息', local);

            if (local) {
              resolve(local);
            } else {
              resolve(null);
            }

          case 5:
          case "end":
            return _context3.stop();
        }
      }
    });
  });
} // 获取token 和app标识
// app:电动宁德  原生端
// app:鸿蒙电动宁德  鸿蒙端
// i宁德暂定


function GetToken() {
  return new Promise(function _callee4(resolve, reject) {
    var tokenBridge;
    return regeneratorRuntime.async(function _callee4$(_context4) {
      while (1) {
        switch (_context4.prev = _context4.next) {
          case 0:
            tokenBridge = null;
            _context4.next = 3;
            return regeneratorRuntime.awrap(NativeGetToken());

          case 3:
            tokenBridge = _context4.sent;

            if (tokenBridge) {
              _context4.next = 8;
              break;
            }

            _context4.next = 7;
            return regeneratorRuntime.awrap(HarmonyOsGetToken());

          case 7:
            tokenBridge = _context4.sent;

          case 8:
            if (tokenBridge) {
              uni.setStorageSync('token', tokenBridge.data);
              resolve(tokenBridge);
            } else {
              reject();
            }

          case 9:
          case "end":
            return _context4.stop();
        }
      }
    });
  });
} // 去支付


function ToPay(params) {
  return new Promise(function _callee5(resolve, reject) {
    var appName, payBridge, _payBridge;

    return regeneratorRuntime.async(function _callee5$(_context5) {
      while (1) {
        switch (_context5.prev = _context5.next) {
          case 0:
            appName = uni.getStorageSync('app') || '';

            if (!(appName == '电动宁德')) {
              _context5.next = 8;
              break;
            }

            _context5.next = 4;
            return regeneratorRuntime.awrap(window.bridge.promisifyDsAsync('bangdao.pay.bd_appPay')(params));

          case 4:
            payBridge = _context5.sent;

            if (payBridge) {
              resolve(payBridge);
            } else {
              resolve(null);
            }

            _context5.next = 13;
            break;

          case 8:
            if (!(appName === '鸿蒙电动宁德')) {
              _context5.next = 13;
              break;
            }

            _context5.next = 11;
            return regeneratorRuntime.awrap(window.bridge.promisifyDsAsync('HarmonyOsToPay')(params));

          case 11:
            _payBridge = _context5.sent;

            if (_payBridge) {
              resolve(_payBridge);
            } else {
              resolve(null);
            }

          case 13:
          case "end":
            return _context5.stop();
        }
      }
    });
  });
} // 页面跳转 -目前只用于原生端


function toCustomer() {
  window.bridge.promisifyDsAsync('bangdao.user.toCustomer')();
} // 开启导航 -- 目前用于鸿蒙


function openNavigation(params) {
  window.bridge.promisifyDsAsync('HarmonyOsOpenNavigation')(params);
} // 开启导航 -- 目前用于鸿蒙


function HarmonyOsShareWxCard(params) {
  window.bridge.promisifyDsAsync('HarmonyOsShareWxCard')(params);
} // 拨打电话 -- 目前用于鸿蒙


function HarmonyOsMakePhoneCall(params) {
  window.bridge.promisifyDsAsync('HarmonyOsMakePhoneCall')(params);
} // 调用原生自动续费签约


function callNativeAutoRenew(signData) {
  return new Promise(function _callee6(resolve, reject) {
    var autoRenewBridge;
    return regeneratorRuntime.async(function _callee6$(_context6) {
      while (1) {
        switch (_context6.prev = _context6.next) {
          case 0:
            _context6.prev = 0;
            _context6.next = 3;
            return regeneratorRuntime.awrap(window.bridge.promisifyDsAsync('bangdao.autorenew.sign')(signData));

          case 3:
            autoRenewBridge = _context6.sent;

            if (autoRenewBridge) {
              resolve(autoRenewBridge);
            } else {
              resolve(null);
            }

            _context6.next = 11;
            break;

          case 7:
            _context6.prev = 7;
            _context6.t0 = _context6["catch"](0);
            console.error('调用原生自动续费桥接失败:', _context6.t0);
            reject(_context6.t0);

          case 11:
          case "end":
            return _context6.stop();
        }
      }
    }, null, null, [[0, 7]]);
  });
}