page {
    --theme: #1e9cff;
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.flex-row {
    display: flex;
    flex-direction: row;
}

.justify-start {
    display: flex;
    justify-content: flex-start;
}

.justify-center {
    display: flex;
    justify-content: center;
}

.justify-end {
    display: flex;
    justify-content: flex-end;
}

.justify-evenly {
    display: flex;
    justify-content: space-evenly;
}

.justify-around {
    display: flex;
    justify-content: space-around;
}

.justify-between {
    display: flex;
    justify-content: space-between;
}

.align-start {
    display: flex;
    align-items: flex-start;
}

.align-center {
    display: flex;
    align-items: center;
}

.align-end {
    display: flex;
    align-items: flex-end;
}
.flex-wrap {
    flex-wrap: wrap;
}
.nomore {
    margin: 40rpx auto;
    text-align: center;
}

.safe_foot {
    position: fixed;
    bottom: 0;
    width: 100vw;
    padding: 22rpx 24rpx;
    background: #fff;
    box-sizing: border-box;
    padding-bottom: calc(22rpx + constant(safe-area-inset-bottom));
    padding-bottom: calc(22rpx + env(safe-area-inset-bottom));
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.safe_foot .item {
    flex: 1;
}
.safe_foot .item:not(:nth-child(1)) {
    margin-left: 24rpx;
}
