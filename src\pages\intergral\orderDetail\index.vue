<!--
@name: 订单详情页
@description: 描述
@time: 2024/7/26
-->
<template>
  <view class="order-detail">
    <!--        订单状态-->
    <view class="order-state">
      <img class="state-icon" :src="`${IMG_PATH}${detail.status === '01' ? 'wait' : 'right'}.png`" />
      <!-- <text class="state-text">{{ detail.status === '01' ? '待发货' : '已发货' }}</text> -->
      <text class="state-text" v-if="detail.status === '01'">待发货</text>
      <text class="state-text" v-if="detail.status === '02'">已发货</text>
      <text class="state-text" v-if="detail.status === '03'">已评价</text>
    </view>
    <!--        订单详情-->
    <view class="state-info">
      <!--            商品卡片-->
      <view class="goods-card">
        <view class="header">商品信息</view>
        <view class="middle">
          <img class="goods-image" :src="getImage" />
          <view class="goods-info">
            <text class="info-item">{{ detail.goodsName }}</text>
            <text class="info-item">商品数量：{{ detail.number }}件</text>
          </view>
        </view>
      </view>
      <!--            订单卡片-->
      <view class="order-card">
        <view class="header">订单信息</view>
        <view class="content">
          <text class="title">订单编号</text>
          <text class="value">{{ detail.integralId }}</text>
        </view>
        <view class="content">
          <text class="title">下单时间</text>
          <text class="value">{{ detail.exchangeTime }}</text>
        </view>
        <view class="content">
          <text class="title">消耗积分</text>
          <text class="value" v-if="detail.goodsAmt">{{ detail.integral }}积分+{{ detail.goodsAmt }}元</text>
          <text class="value" v-else>{{ detail.integral }}积分</text>
        </view>
        <view class="content" v-if="detail.goodsType === '02'">
          <text class="title">快递公司</text>
          <text class="value">{{ detail.logisticsName || '-' }}</text>
        </view>
        <view class="content" v-if="detail.goodsType === '02'">
          <text class="title">快递单号</text>
          <view class="value">
            <text>{{ detail.logisticsNo || '-' }}</text>
            <img
              v-if="detail.logisticsNo"
              class="copy"
              :src="`${IMG_PATH}copy.png`"
              @click="copy(detail.logisticsNo)"
            />
          </view>
        </view>
        <view class="content" v-if="detail.goodsType === '02'">
          <text class="title">收货地址</text>
          <text class="value">{{ detail.address }}</text>
        </view>
      </view>
      <!--评论-->
      <view class="rate-card" v-if="detail.status === '03'">
        <view class="rate-item" v-for="(item, index) in rateList" :key="index">
          <view>{{ item.rateName }}:</view>
          <select-rate v-model="item.rateValue"></select-rate>
        </view>
      </view>
      <view class="evaluate-content" v-if="detail.status === '03'">
        <textarea v-model="content" maxlength="100" placeholder="请输入评价内容" style="height: 260rpx" disabled />
      </view>
      <view class="upload-img" v-if="imgList.length > 0 && detail.status === '03'">
        <view class="imgs-box">
          <view v-for="(item, index) in imgList" :key="index" class="img-item">
            <image :src="item" />
          </view>
        </view>
      </view>
      <view v-if="detail.status === '03' && evaDetail.replyRemark" class="evaluate-reply">
        商家回复：
        {{ evaDetail.replyRemark }}
      </view>
    </view>
  </view>
</template>

<script>
import { getExchangeDetail } from '@/services/index.js';
import selectRate from '@/components/selectRate/index.vue';

export default {
  name: 'index',
  components: {
    selectRate,
  },
  data() {
    return {
      integralId: '',
      detail: {},
      rateList: [
        {
          id: '0',
          rateName: '总体评分',
          rateValue: 0,
        },
        {
          id: '1',
          rateName: '货物质量',
          rateValue: 0,
        },
        {
          id: '2',
          rateName: '描述相符',
          rateValue: 0,
        },
        {
          id: '3',
          rateName: '物流服务',
          rateValue: 0,
        },
      ],
      evaDetail: {},
      content: '暂无评论~',
      imgList: [],
    };
  },
  onLoad(options) {
    this.integralId = options.integralId;
    this.getExchangeDetail();
  },
  computed: {
    // 商品图片路径
    getImage() {
      const str = `${process.env.VUE_APP_BASE_HOST}/pub/api/v0.1/attachs/${this.detail.fileId}`;
      return str;
    },
  },
  methods: {
    // 查询兑换订单详情
    async getExchangeDetail() {
      const params = {
        integralId: this.integralId,
      };
      const [err, res] = await getExchangeDetail(params);
      let resData = JSON.parse(JSON.stringify(res));
      if (resData) {
        this.detail = resData.exchangeBo;
        this.evaDetail = resData.evaDetail;
        console.log('this.evaDetail', this.evaDetail);
        // 评分
        this.rateList[0].rateValue = Number(this.evaDetail.evaScore) / 2;
        this.rateList[1].rateValue = Number(this.evaDetail.evaItems[0].evaScore) / 2;
        this.rateList[2].rateValue = Number(this.evaDetail.evaItems[1].evaScore) / 2;
        this.rateList[3].rateValue = Number(this.evaDetail.evaItems[2].evaScore) / 2;
        // 评论
        if (this.evaDetail.evaRemark) {
          this.content = this.evaDetail.evaRemark;
        }
        // 图片
        this.imgList = [];
        console.log(this.imgList, 'this.imgList', this.evaDetail);
        if (this.evaDetail.fileIds.length > 0) {
          this.evaDetail.fileIds.forEach((item) => {
            this.imgList.push(`${process.env.VUE_APP_BASE_HOST}/pub/api/v0.1/attachs/${item}`);
          });
        }
      }
    },
    // 复制
    copy(val) {
      uni.setClipboardData({
        data: val,
        success: function () {
          uni.showToast({
            icon: 'none',
            title: '复制成功',
          });
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.order-detail {
  background: linear-gradient(180deg, #1e9cff 0%, #f6f7f9 100%);
  .order-state {
    display: flex;
    padding: 40rpx 534rpx 80rpx 24rpx;
    align-items: center;
    .state-icon {
      width: 47.03rpx;
      height: 47.03rpx;
      margin-right: 16rpx;
    }
    .state-text {
      font-size: 40rpx;
      font-weight: bold;
      line-height: 38rpx;
      color: #ffffff;
    }
  }
  .state-info {
    height: 100%;
    background: #f6f7f9;
    border-radius: 24rpx 24rpx 0 0;
    padding: 35rpx 24rpx 0 24rpx;
    padding-bottom: 60rpx;
    .header {
      margin-bottom: 24rpx;
      font-size: 32rpx;
      font-weight: bold;
      line-height: 38rpx;
      color: #333333;
    }
    .goods-card {
      margin-bottom: 16rpx;
      padding: 24rpx;
      background: white;
      border-radius: 16rpx;
      margin-bottom: 16rpx;
      .middle {
        display: flex;
        .goods-image {
          width: 140rpx;
          height: 140rpx;
          border-radius: 16rpx;
          margin-right: 16rpx;
        }
        .goods-info {
          display: flex;
          flex-direction: column;
          justify-content: center;
          gap: 8rpx;
          grid-gap: 8rpx;
          .info-item:nth-child(1) {
            font-size: 28rpx;
            font-weight: bold;
            line-height: 42rpx;
            color: #333333;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .info-item:nth-child(2) {
            font-size: 26rpx;
            line-height: 40rpx;
            color: #666666;
          }
        }
      }
    }
    .order-card {
      background: white;
      padding: 24rpx;
      background: white;
      border-radius: 16rpx;
      .content {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20rpx;
        .title {
          font-size: 28rpx;
          line-height: 42rpx;
          color: #999999;
        }
        .value {
          font-size: 28rpx;
          line-height: 42rpx;
          text-align: right;
          color: #333333;
          display: flex;
          align-items: center;
          .copy {
            width: 36rpx;
            height: 36rpx;
            margin-left: 8rpx;
          }
        }
      }
    }
    .rate-card {
      padding: 40rpx;
      margin: 16rpx 0;
      border-radius: 24rpx;
      background: #ffffff;
      display: flex;
      flex-direction: column;
      gap: 24rpx;
      grid-gap: 24rpx;
      .rate-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 28rpx;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0em;
        color: rgba(51, 51, 51, 0.898);
      }
    }
    .evaluate-content {
      padding: 40rpx;
      margin-bottom: 16rpx;
      border-radius: 24rpx;
      background: #ffffff;
    }
    .upload-img {
      padding: 40rpx;
      border-radius: 24rpx;
      background: #ffffff;
      .imgs-box {
        display: flex;
        flex-wrap: wrap;
        gap: 24rpx;
        grid-gap: 24rpx;
        margin-top: 24rpx;
        .img-item {
          width: 160rpx;
          height: 160rpx;
          image {
            width: 160rpx;
            height: 160rpx;
          }
          .close-btn {
            position: relative;
            top: -150rpx;
            left: 130rpx;
            font-size: 32rpx;
            background: rgba(0, 0, 0, 0.08);
            border-radius: 50%;
            color: #1e9cff;
            width: 30rpx;
            height: 30rpx;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
        .add-box {
          width: 160rpx;
          height: 160rpx;
          box-sizing: border-box;
          padding: 40rpx 32rpx;
          color: #1e9cff;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          border-radius: 16rpx;
          background: rgba(72, 118, 255, 0.08);
          box-sizing: border-box;
          border: 1rpx solid rgba(30, 156, 255, 0.3);
          .add-box-icon {
            font-weight: bold;
            font-size: 60rpx;
          }
          .add-box-span {
            font-size: 24rpx;
            font-weight: normal;
            line-height: 36rpx;
          }
        }
      }
    }
    .evaluate-reply {
      padding: 40rpx;
      border-radius: 24rpx;
      background: #ffffff;
      margin-top: 16rpx;
      line-height: 1.5;
      letter-spacing: 2rpx;
    }
  }
  .footer {
    z-index: 1;
    .footer-button {
      display: flex;
      gap: 24rpx;
      grid-gap: 24rpx;
    }
  }
}
</style>
