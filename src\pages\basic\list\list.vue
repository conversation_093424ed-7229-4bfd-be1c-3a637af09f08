<template>
  <!-- pages/index/list.wxml -->
  <!-- <text>pages/index/list.wxml</text> -->
  <view>
    <view class="login-header">
      <view class="login-img"><image :src="`${IMG_PATH}<EMAIL>`"></image></view>
      <view class="login-header-message">
        <view class="login-message1">
          亨通电桩-01号桩
          <text>￥100</text>
        </view>
        <view class="login-message2">
          <image :src="`${IMG_PATH}<EMAIL>`"></image>
          厦门软件园充电站
        </view>
        <view class="login-message3">
          <image :src="`${IMG_PATH}<EMAIL>`"></image>
          2017.11.18 06：01
        </view>
        <view id="login-text"><image :src="`${IMG_PATH}<EMAIL>`"></image></view>
      </view>
    </view>

    <view style="background: #eef2f6">
      <view class="login-middle">
        <view>
          <text>状态</text>
          {{ status }}
        </view>
        <view>
          <text>站名</text>
          XCF84928394839
        </view>
        <view>
          <text>桩名</text>
          001号桩
        </view>
        <view>
          <text>枪名</text>
          A桩
        </view>
        <view>
          <text>类型</text>
          快充
        </view>
        <view>
          <text>已充电量</text>
          001号桩
        </view>
        <view>
          <text>已充时长</text>
          1小时30分钟
        </view>
      </view>
      <view class="login-nav">费用信息</view>
      <view class="login-navMsg">
        <view>
          <text>现充金额</text>
          50.'00'元
        </view>
        <view>
          <text>实付金额</text>
          50.'00'元
        </view>
      </view>
    </view>
    <view class="login-button">
      <button v-if="stat == 2" @tap="pand">点评订单</button>
      <button v-if="stat == 1" @tap="pand">去充电</button>
      <button v-if="stat == 0" @tap="pand">支付订单</button>
      <button v-else @tap="pand"></button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      stat: '0',
      put: '',
      status: '待点评',
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */ onLoad: function (options) {
    console.log(options.status);
    var that = this;
    that.setData({
      status: options.status,
    });
    if (options.status == '未支付') {
      that.setData({
        stat: '0',
        put: '',
      });
    } else if (options.status == '待充电') {
      that.setData({
        stat: '1',
        put: '/pages/basic/charge/charge',
      });
    } else if (options.status == '待点评') {
      that.setData({
        stat: '2',
        put: '/subPackages/comment/comment',
      });
    } else {
      that.setData({
        stat: '3',
      });
    }
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    console.log('点此转发');
  },
  methods: {
    pand: function () {
      console.log('......');
      var that = this;
      uni.redirectTo({
        url: that.put,
      });
    },
  },
};
</script>
<style>
@import './list.css';
</style>
