class cookies {
    /*初始化*/
    constructor() {

    }
	/**
     * @param name cookie名称
     * @param value cookie值
     * @param expires 过期时间（单位：秒）
     * 设置cookie
     */
    put (name, value, expires) {
        expires = (expires || 30 * 24 * 60 * 60) * 1000; //默认30天
        var exp = new Date();
        exp.setTime(exp.getTime() + expires);
        try {
            value = JSON.stringify(value);
        }
        catch (e) { }
        document.cookie = name + "=" + escape(value) + ";path=/;expires=" + exp.toGMTString();
    }
    /**
     * 获取cookie
     * @param name
     * @returns {null}
     */
    get (name) {

        //var arr, reg = new RegExp("(^| )" + this.prefix+'_'+name + "=([^;]*)(;|$)");
        var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        if (arr = document.cookie.match(reg)) {
            var result = unescape(arr[2]);
            try {
                return JSON.parse(result);
            }
            catch (e) { }
            return result;
        }
        else {
            return null;
        }
    }
    /**
     * 删除cookie
     * @param name
     */
    remove (name) {
        var exp = new Date();
        exp.setTime(exp.getTime() - 1);
        var cval = this.get(name);
        if (cval != null) {
            document.cookie = name + "=" + cval + ";path=/;expires=" + exp.toGMTString();
        }

    }

    clear () {
        var keys = document.cookie.match(/[^ =;]+(?=\=)/g);
        if (keys) {
            for (var i = keys.length; i--;) {
                var date = new Date();
                date.setTime(date.getTime() - 1);
                document.cookie = keys[i] + "=; path=/expire=" + date.toUTCString();
            }

        }
    }
}

export let Cookies = new cookies();