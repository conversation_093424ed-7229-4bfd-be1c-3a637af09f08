@import '../../../common/css/iconfont';
@import '../../../common/css/styleColor';
/* pages/mapStation/map.wxss */

.map-wrap {
  background-color: #fff;
  position: relative;
}

.controls-item {
  position: absolute;
  width: 80rpx;
  height: 80rpx;
}

.controls-item image {
  width: 100%;
  height: 100%;
}

page-section-spacing {
  position: fixed;
  z-index: 88;
  height: 100vh;
  top: 0;
  left: 0;
  width: 100%;
  text-align: center;
  background-color: rgb(0, 0, 0, 0.5);
}

.cover-view-fixed {
  position: fixed;
  z-index: 1;
  width: 100%;
  height: 100rpx;
  left: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
}

.cover-view {
  position: absolute;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  bottom: 0;
  text-align: center;
  padding-top: 20%;
  color: #fff;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.75);
}

.modal-alert {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  padding-top: 22%;
  z-index: 6667;
  background-color: rgba(0, 0, 0, 0.75);
}

.modal-alert .modal-alert-images {
  width: 630rpx;
  height: 844rpx;
}

.cover-view .slide-view {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  z-index: 88;
}

.slide-cover-close {
  width: 80rpx;
  height: 80rpx;
  display: inline-block;
  margin: 68rpx auto 0 auto;
  border-radius: 50%;
  z-index: 88;
  cursor: pointer;
  position: fixed;
  bottom: 14vh;
  z-index: 6668;
  left: 45%;
}

.cover-view .slide-view .slide-view-image {
  width: 630rpx;
  height: 100%;
  z-index: 88;
}

.page-section-spacing .swiper-remove {
  font-size: 88rpx;
  color: #fff;
  text-align: center;
  margin-top: -36rpx;
  text-align: center;
  z-index: 88;
}

.page-section-spacing .swiper-remove view {
  width: 66rpx;
  height: 66rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 60rpx auto 0 auto;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAAHKklEQVR4Xu2ce2wURRzHf7N7x/XF0hbSIAj4R3kkaAGDwRpIQK0PqoIoCUQJDxH4w0QwJhIIfxkIJEQw8Q9B5BE0kCAIKvioCgnEQiSKKEkp/UNAKmmgrcv1cVzvxsxys52bm9mb295x195ectntdndm9nPf329mfvNAkMUPxhipZI8Qwir3ZeIepQKmI2NVGKp53S9oGQXkAMVtvkIlZRKW24I6/tACMGw+snOnNFkwsnPIBKi0AuLAiEDQa/yRwOHLIgJBr/FH8rx9fzpBpQWQBAwLgZzzf7NQZOUQAWGvkXMptHSA6hMgiSmJYNBr/JFCcgIkAkLBsEeqIhaaZbZ9AeUakINqSJpaTDE8ENF1kXlRfySDE40phwfFXk+L2bkCJIDDgmAh0HP72HQTfO+d+29Sczt+vCuCx4WjuDKC0QiM8WCMoeTeLw5BhNAdHeFmv4aaCnXUOKIUnd0ybcgflcOhBwAoCP5IgInguVZSyoAYOPRZRyBETedaILD+J7OmpTMyP9SDZ2IAQ7W9w96HAMyAD52qKNIPbXzKqJtWAaEYkGTAbDWlam4pAeLgiFRDYNnfbfXmsM8bIyuDIVgaxXioGyiyZzSEbpcEYM9r4/Qda6qNWwwoAkukLNs3pQJJGZAEDmtCFIx+pKmrZNOZ0Jo7oehKDFCcTjB8WgggODig7Vw3PbBtXmVhEAAiHCyR2Sk7biVADnDiFAMAes1B8/nrZmRzFONRmQTDp60hdH2Uoa+tW2B8K4DEqiolJSUFpAhHP/U3FK4+2b6xM4yXgmInNO0AEcJFfrRn+6zS9TMfgi4JqLimQTJzcwSkAEcnPmfd6bsjv2zoOBCJ4qq0v7SLBHUNXXx5QvHCTTMG3YiZm8jslJQkBSSoyhPMicBZ/o05/kxz5FAU49Eu3iVjj2gIXZs+Qp+/6wXjsgQSdeSOTQAVQLS2sp1wrKbSCZzTN3qOY4BhGXvTPiSMAG7NGOmrjUGiKmLVlFRFQkAC02LVQ8xKX3sy+ODRK+ETuaYcgfO+Nnesf/bmWSX/xHySzNyENZsKoAQ431/tLF79Q+i7XPE5yURGfNL2ZwLPPTumqMMBkhogiXosZ0yUAwC+yXvat3bejS5LVrBc+n/RIG33haWl7wJYXRXe3KSmlqCgGCC2lRwHp+agOfuqGfksa1W5W+oI4TGG/nrdAuOEAJJd9fPVfhwgTj0JpnWkqctY93P3L/e7EeiWiagxuenJgifmVRaanKkRRQlVJAIkVc/Uve0bzFD0nXQVOBvpGAHtg/NLSt9XVZENyMH3WLXWtnqz4uOLPRcy3bfKNDQE0LGqyjd5TbXRwqiI+qQEFckAsW0eyzFP3de23uzGxMn1+49RgLaeX1y2kVER3zayazQekKhR6PurBQpfPdp2Kd0hi2yRJqGSL+aWTXy4wuqvsbWaHWyjztoCJDCvuJrr6QPmi9fMnv3ZeqFM5Dva8C36caHxtYMvslQkAsSal4+YV9WnbXu7e/BLmShottIs8KGvLr5RtiQGSKQiISC+avc13YRA7bHWJrdh0mwBSJYvCd8en1NeWTncCtvygIipJQBi/Y9VcxH1vHKs47E/b4bqkmXYH///yPBAzeE5xb8KnLVVm/EmxgOyzKt6X/tbt7ujpN0w4D5DC7QN9YtLPxKYWS8gSeuZKsg/eXfbh51hvGjA0QGAIj/af2FZ2dsAEBZ0Yq0xOeQAyFLQxF2tx8MRqB6IgPw61F9aXl7r6KglgCw45DthZ9vvuR7zcfvjkahjw4qyKQwgOihpRxtFCrIdNAE0fmfrFYyh3G0hcvk5hKD18orysZyC2G6H0MTiAI3b0fovAAzK5RftQ9nuNq4sf8ADJCfYd0CeifUO8tFpK6Q13eukP2n7LRrFY/og45x9VNPQ1YY3yx5N1UnbsWevmnduBxFn7TUUmXCH19XoHe1Q6ovlQWdVrzk8Z4hyZ5WIyQt33Jt8JQx3WH0zBpKlIC9g5oVc+ZHWuJENL2ivErQXBO69YZ/YEqx8HDgMrqryTUl54FDQHiLw4oZ/8nro2cHMaNeDTO8dqJMX2FmwcfOEvOkv91rPatNfJKOsVEHeBCoal0g2Tyivp+A5qCiu6s/rSZxJINk1W95OA+YA8X20uPnSeTuRXKIitjNrO++8XIogcdj8BCtbTXm5mCVFSJai8m45lAKkBLO7zwvqOgYHtB1ZXVCXBBIFxIPS8mpJpgQSDdOyvikBWN4s6hVAok0AdvK5wrLwjknN7eHeZeFRNBIDLolbFg4oqGv4Ru+ycP/ZLdOKc39ZuAMkFpZoAwFWWSxQ+pxoBLZ/biwggcSrSaQuFgyNJNBrMkD9c2sK9m28zU0Upx942+O4A8X7GDZIlxCwk2Rh7w3E7hPEnStvFqD4GtZtqgVMJU37Xm+LrhSweZu8pQDLoRYUppJsdwQXWSs/8j8kbF2ARqThfwAAAABJRU5ErkJggg==');
}

.page-section-spacing .swiper-remove image {
  width: 22rpx;
  height: 22rpx;
}

.page-section-spacing .swiper-item {
  width: 78%;
  height: 85%;
  margin: 117rpx auto 65rpx auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-section-spacing .swiper-item image {
  width: 630rpx;
  height: 844rpx;
}

.fotters {
  width: 100%;
  height: 556rpx;
  position: absolute;
  bottom: 267rpx;
  background: #fff;
  padding: 20rpx 30rpx 0 30rpx;
  box-sizing: border-box;
}

.fotters .fotters-block {
  display: inline-block;
  font-size: 30rpx;
}

.fotters-block-left {
  color: red;
  width: 500rpx;
  min-height: 120rpx;
  max-height: 185rpx;
  height: auto;
  padding: 10rpx 0;
  overflow: hidden;

  /* height: 100rxpx */
}

.fotters-block-right {
  float: right;
  width: 190rpx;
  min-height: 120rpx;
  max-height: 185rpx;
  height: auto;
  padding: 10rpx 0;
  text-align: center;
  color: #2196f3;
  /* margin-bottom:  */
}

.fotters-block-right .icon-guide1 {
  height: 60rpx;
  vertical-align: middle;
}

.fotters-block-right text {
  line-height: 60rpx;
}

.max-w {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.middle-size {
  font-size: 30rpx;
  color: #666;
}

.middle-size-other {
  font-size: 28rpx;
  color: #333;
}

.btn-bottom {
  width: 100%;
}

.btn-bottom view {
  display: inline-block;
  width: 50%;
  text-align: center;
  font-size: 30rpx;
  color: #2196f3;
  box-sizing: border-box;
  line-height: 55rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-bottom .btn-detail {
  border-right: 1rpx solid #e1e1e1;
}

.btn-bottom .btn-comment {
  /* 评价按钮样式 */
}

.vvip-flag {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
  vertical-align: middle;
}

.caidan {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 9vh;
  background: #fff;
  width: 100%;
  display: flex;
  justify-content: space-around;
  text-align: center;
  align-items: center;
  color: #939393;
}

.caidan .listBtn {
  width: 20%;
  font-size: 24rpx;
}

.caidan .on {
  color: #e00a22;
}

.caidan .scan {
  color: #fff;
  padding: 15rpx 0;
  width: 20%;
  border-radius: 100rpx;
  height: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.head-maps {
  width: 100%;
  height: 100rpx;
  position: fixed;
  top: 120rpx;
  background: #e00a22;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.head-maps .head-maps-found {
  display: flex;
  width: 80%;
  background: #fff;
  height: 65rpx;
  align-items: center;
  border-radius: 100rpx;
  padding-left: 30rpx;
  box-sizing: border-box;
}

.head-maps .head-maps-found input {
  width: 80%;
  display: inline-block;
  height: 100%;
  font-size: 30rpx;
  padding-left: 10rpx;
}

.list-found {
  background: #eee;
  width: 100%;
  position: absolute;
  /* top: 100rpx; */
  height: 82.5vh;
  /* #ifdef H5 */
  height: 86.5vh;
  /* #endif */
  box-sizing: border-box;
  z-index: 9;
  /* #ifdef H5 */
  top: 220rpx;
  /* #endif */
}

.list-top-nav {
  width: 100%;
  font-size: 36rpx;
  display: flex;
  align-items: center;
  background: #fff;
  display: flex;
  padding-left: 30rpx;
  font-size: 36rpx;
  font-weight: 600;
  align-items: baseline;
  z-index: 1;
}

.list-top-nav .text {
  margin-left: 25rpx;
}

.city-wrap {
  /* height: calc(100vh - 200rpx); */
}

.list-found .station-list {
  width: 100%;
  /* height: 264rpx; */
  background: #fff;
  margin-bottom: 20rpx;
  box-sizing: border-box;
  padding: 20rpx 30rpx;
}

.station-ul {
  margin: 0 24rpx;
  padding-bottom: 80rpx;
}

.page-hed {
  box-sizing: border-box;
  background: #fff;
  padding: 24rpx;
  color: #666;
  position: relative;
  top: 100rpx;
  z-index: 9898999;
  width: 100%;
}

.page-hed-ul {
  display: flex;
  align-items: center;
  border-radius: 16rpx;
  background: #f5f5f5;
  box-sizing: border-box;
  width: 100%;
  padding: 8rpx;
}

.page-hed-li {
  flex: 1;
  font-family: PingFang SC;
  font-size: 28rpx;
  font-weight: normal;
  text-align: center;
  color: #333333;
  line-height: 64rpx;
}

.hed-li-checked {
  border-radius: 8rpx;
  background: #ffffff;
}

.c-state2 {
  transform: rotate(0deg) scale(0.8) translate(75%, 0%);
  -webkit-transform: rotate(0deg) scale(0.8) translate(75%, 0%);
}

.station-list .list-name {
  color: #333;
  font-size: 30rpx;
  line-height: 42rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.station-list .list-name .guideRight {
  display: flex;
  align-items: center;
  height: 100%;
  color: #2196f3;
  font-size: 24rpx;
}

.station-list .list-name .guideRight icon {
  margin-right: 14rpx;
  height: 100%;
  line-height: 42rpx;
  display: flex;
}

.station-list .list-list-status {
  display: flex;
  width: 100%;
}

.list-status .list-span {
  display: inline-block;
  width: 50%;
  text-align: left;
  color: #333;
  font-size: 30rpx;
  line-height: 60rpx;
}

.list-status view {
  font-size: 24rpx;
  color: #333;
}

.list-status view text {
  font-size: 36rpx;
  color: #2196f3;
  margin: 0 14rpx;
  margin-left: 0;
}

.list-number {
  float: right;
}

.list-number view {
  display: inline-block;
  color: #016cc0;
  justify-content: end;
  /* line-height: 60rpx; */
  font-size: 28rpx;
  text-align: left;
}

.list-number .Itemtype {
  text-align: right;
  line-height: 38rpx;
}

.list-number .Itemtype text {
  display: inline-block;
  width: 36rpx;
  height: 36rpx;
  line-height: 36rpx;
  text-align: center;
  background: #f44336;
  font-size: 20rpx;
  color: #fff;
  border-radius: 8rpx;
  margin-right: 12rpx;
}

.list-number .Itemtype .I {
  margin-left: 27rpx;
  background: #2196f3;
}

.list-address {
  color: #666;
  font-size: 28rpx;
  line-height: 60rpx;
  width: 100%;
  /* overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis; */
}

.color-orange {
  color: #ffc927;
}

.color-gray {
  color: #d0d0d0;
}

.map-icon {
  display: inline-block;
  vertical-align: middle;
  margin-right: 10rpx;
}

.coverImg {
  width: 540rpx;
  height: auto;
  margin: 0 auto;
  margin-top: 20rpx;
  z-index: 176;
  position: absolute;
  right: 14%;
  top: 3%;
}

.maske {
  width: 100%;
  height: 100%;
  background: #000;
  opacity: 0.5;
  position: absolute;
  left: 0;
  top: 0;
}

.coverImgNet {
  width: 62rpx;
  height: 62rpx;
  z-index: 200;
  position: absolute;
  right: 10%;
  top: 2%;
}

.movpf {
  width: 100%;
  height: 100%;
  z-index: 9999999;
  /* background-image: url("https://ndjtcs.evstyle.cn:6443/nd-front/image/<EMAIL>");   */
  /* background-size: 100% 100%;
  /* opacity: .5; */
  /* background-repeat: no-repeat;  */
}

.coverImgMsk {
  width: 100%;
  height: 100%;
  z-index: 1;
  position: absolute;
  top: 0;
  left: 0;
  /* background-image: url("https://ndjtcs.evstyle.cn:6443/nd-front/image/<EMAIL>");   */
  /* background-size: 100% 100%;
  /* background: #000; */
}

.swiperOpint {
  width: 100%;
  height: 100%;
  /* background-image: url('https://ndjtcs.evstyle.cn:6443/nd-front/image/<EMAIL>'); */
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.slide-image {
  width: 540rpx;
  height: 80%;
}

.ad-swiper {
  height: 844rpx;
  width: 100%;
  z-index: 100;
  /* background: red; */
  color: #000;
  font-size: 30rpx;
}

.ad-swiper .wx-swiper-dots.wx-swiper-dots-horizontal {}

.ad-swiper .wx-swiper-dot {
  width: 12rpx;
  height: 12rpx;
  display: inline-flex;
  justify-content: space-between;
  margin-left: -8rpx;
  z-index: 8888;
}

.ad-swiper .wx-swiper-dot::before {
  content: '';
  flex-grow: 1;
  background: rgba(255, 255, 255, 1);
  border-radius: 22rpx;
}

.ad-swiper .wx-swiper-dot-active {
  width: 22rpx;
}

.ad-swiper .wx-swiper-dot-active::before {
  background: #2196f3;
}

.cover-view-imgae {
  position: absolute;
  bottom: 10%;
  left: 30rpx;
}

.cover-view-imgae .slide-imgae,
.cover-view-map .slide-imgae,
.cover-view-phone .slide-imgae {
  width: 120rpx;
  height: 120rpx;
}

.cover-view-push .slide-imgae {
  width: 140rpx;
  height: 124rpx;
}

.cover-view-push {
  top: 4%;
  left: 30rpx;
}

.cover-view-map {
  position: absolute;
  bottom: 10%;
  right: 30rpx;
}

.cover-view-phone {
  position: absolute;
  bottom: 22%;
  right: 30rpx;
}

.cover-view-orders {
  position: absolute;
  bottom: 24%;
  left: 30rpx;
}

.cover-view-orders .slide-imgae {
  width: 120rpx;
  height: 120rpx;
}