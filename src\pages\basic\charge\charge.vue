<template>
  <view>
    <view v-if="Nomoney" class="charge_tap">
      <view>可用余额即将不足，低于{{ remainderUpMoney }}元将自动停止充电</view>
      <button @tap="chongzhi">立即充值</button>
    </view>
    <view class="charge-content">
      <view class="charge-type">
        <block v-if="!chargeData.numberd">{{ chargeStatusText }}</block>
        <text class="type-blue" v-if="chargeData.numberd">充电进度</text>
        <text class="type-number" v-if="chargeData.numberd">{{ chargeData.numberd }}%</text>
      </view>
      <view @tap="gos" class="charge-licenseNo">
        <view
          v-if="custType == '01' && chargeData.carModelName == '' && chargeData.licenseNo == ''"
          class="licenseNo-btn"
        >
          <image :src="`${IMG_PATH}charge-addCar.png`"></image>
          新增车辆
        </view>
        <view
          v-else-if="custType == '02' && chargeData.carModelName == '' && chargeData.licenseNo == ''"
          class="charge_midd_btn"
          >暂无可用车辆</view
        >
        <view v-else class="charge_midd_btn">
          {{ chargeData.carModelName }}{{ chargeData.licenseNo }}
          <text class="icon iconfont icon-arrow_right" size="20"></text>
        </view>
      </view>
      <view class="charge-station">{{ chargeData.stationName }} {{ chargeData.pileName }}</view>
      <view class="charge-orderNo">订单号：{{ orderNo }}</view>
      <view class="charge-ul">
        <view class="charge-li">
          <view class="li-value">
            <text>{{ chargeData.alreadyElecs }}</text>
            kWh
          </view>
          <view class="li-key">已充电量</view>
        </view>
        <view class="charge-li">
          <view class="li-value">
            <text>{{ chargeData.hour }}</text>
            小时
            <text>{{ chargeData.minute }}</text>
            分钟
          </view>
          <view class="li-key">已充时长</view>
        </view>
        <view class="charge-li" @tap="chongzhi">
          <view class="li-value">
            <text>{{ chargeData.alreadyAmounts }}</text>
            元
          </view>
          <view class="li-key">已充金额</view>
          <image class="li-recharge-bg" :src="`${IMG_PATH}charge-recharge.png`"></image>
        </view>
        <view class="charge-li">
          <view class="li-value">
            <text>{{ chargeData.outputPower }}</text>
            kw
          </view>
          <view class="li-key">当前功率</view>
        </view>
        <view class="charge-li">
          <view class="li-value">
            <text>{{ chargeData.maxCurrent }}</text>
            A
          </view>
          <view class="li-key">当前电流</view>
        </view>
        <view class="charge-li">
          <view class="li-value">
            <text>{{ chargeData.chargeVolt }}</text>
            V
          </view>
          <view class="li-key">当前电压</view>
        </view>
      </view>
      <div
        class="vip-content"
        v-if="memberInfo.vipFlag == '0'"
        :style="{ backgroundImage: `url(${IMG_PATH}member/vip-card-bg.png)` }"
      >
        <div class="title">
          开通PLUS会员享受最高月省<span>{{ maxAmt || 0 }}</span
          >元
        </div>
        <div class="con">
          <div class="order-list">
            <div class="order-item" v-for="(item, index) in couponList" :key="index">
              <div class="seq">
                权益
                <span class="seq-num">{{ index + 1 }}</span>
              </div>
              {{ `${item.cpnName}*${item.cpnNum}` }}
            </div>
            <div class="order-item" v-if="memberStation">
              <div class="seq">
                权益
                <span class="seq-num">{{ couponList.length + 1 }}</span>
              </div>
              会员场站享受{{ memberStation }}折优惠
            </div>
            <div class="order-item">
              <div class="seq">
                权益
                <span class="seq-num">{{ memberStation ? couponList.length + 2 : couponList.length + 1 }}</span>
              </div>
              ......
            </div>
          </div>
          <div class="btn" @click="toPlusPay">立即开通</div>
        </div>
      </div>
    </view>
    <div class="bottom-wrap">
      <view class="charge-button" @click="stopCh">
        停止充电
        <text v-if="chargeData.remainTimes">{{ chargeData.remainTimes }}</text>
      </view>
    </div>
    <image class="charge-bg" :src="`${IMG_PATH}charge-bg.png`"></image>
    <image class="charge-battery-bg" :src="`${IMG_PATH}charge-bg1.png`"></image>
    <image class="charge-electri-bg" :src="`${IMG_PATH}electricity.apng.png`"></image>
    <image class="charge-service" @tap="goSevice" :src="`${IMG_PATH}charge-service.png`"></image>

    <!-- <modal
      v-if="!hidden"
      title="确定要结束充电吗?"
      confirm-text="结束充电"
      cancel-text="继续充电"
      @cancel="cancel"
      @confirm="confirm"
      :no-cancel="nocancel"
    >
      <view style="text-align: center">结束后将对订单进行结算,</view>
      <view style="text-align: center">如需继续充电请重新下单.</view>
    </modal> -->
    <uni-popup ref="popup">
      <view class="popup-box">
        <view class="popup-title">确定要结束充电吗?</view>
        <view style="text-align: center; letter-spacing: 2rpx; line-height: 1.5; color: gray"
          >结束后将对订单进行结算,</view
        >
        <view style="text-align: center; letter-spacing: 2rpx; line-height: 1.5; color: gray"
          >如需继续充电请重新下单.</view
        >
        <view class="btns">
          <view @click="cancel" class="cancel-btn">继续充电</view>
          <view @click="confirm" class="confirm-btn">结束充电</view>
        </view>
      </view>
    </uni-popup>

    <!-- <modal
      v-if="!omgd"
      title="充电启动失败"
      confirm-text="重试"
      cancel-text="更换设备"
      @cancel="cancel2"
      @confirm="confirm2"
      :no-cancel="nocancel"
    ></modal> -->
    <uni-popup ref="popupOmgd">
      <view class="popup-box">
        <view class="popup-title">充电启动失败</view>
        <view class="btns">
          <view @click="cancel2" class="cancel-btn">更换设备</view>
          <view @click="confirm2" class="confirm-btn">重试</view>
        </view>
      </view>
    </uni-popup>
    <loading v-if="!hiddenLoading">{{ loadMsg }}</loading>

    <view v-if="aletMsg" class="aletMsg"></view>
    <view v-if="aletMsg" class="count">
      <view class="count_head">充电桩正在启动</view>
      <view class="count_msg">此过程预计60s内完成</view>
      <view class="count_net">请耐心等待</view>
      <view @tap="djs" class="pop">
        <view>{{ cup }}</view>
      </view>
    </view>

    <view v-if="aletMsg2" class="aletMsg"></view>
    <view v-if="aletMsg2" class="count">
      <view class="count_head">充电桩正在结束</view>
      <view class="count_msg">此过程预计60s内完成</view>
      <view class="count_net">请耐心等待</view>
      <view @tap="djs" class="pop">
        <view>{{ cup2 }}</view>
      </view>
    </view>

    <!-- <modal v-if="!omgd2" title="充电停止失败" confirm-text="重试" @confirm="oll" :no-cancel="nocanceldd"></modal> -->
    <uni-popup ref="popupOmgd2">
      <view class="popup-box">
        <view class="popup-title">充电停止失败</view>
        <view class="btns">
          <view @click="oll" class="ok-btn">重试</view>
        </view>
      </view>
    </uni-popup>
    <view v-if="mask" @tap="maskRmove" class="mask"></view>
    <view v-if="mask" class="mask-alers">
      <view class="mask-alers-flex">
        <view>我的爱车</view>
        <view @tap="ads">十 新增</view>
      </view>
      <view class="mask-list" v-for="(item, index) in listTypr" :key="index">
        <view class="mask-list-li">
          <view class="mask-list-lf">{{ item.licenseNo }}</view>
          <view
            @tap="actives"
            :data-msg="item.licenseNo"
            :data-index="index"
            :data-num="item.types"
            class="mask-list-rt"
          >
            <i
              :class="'icon iconfont changes icon-checked:' + (item.types == true ? 'icon-checked' : 'icon-unchecked')"
              size="10"
            ></i>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ENVR } from '@/config/global';
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup';
import uniPopupDialog from '@/components/uni-ui/uni-popup-dialog/uni-popup-dialog';
import { subscribeMessage } from '@/utils/messageCode.js';
import { debounce } from '@/utils/util.js';
import {
  getMemberInfo,
  getConfigDesc,
  getChargingInfo,
  changeChargingStatus,
  cancelOrder,
  selectLicenseNo,
  getPref,
} from '@/services/index.js';
export default {
  components: { uniPopup, uniPopupDialog },
  data() {
    return {
      kind: '启动失败 若多次重试失败 则为桩受损',
      startNos: true,
      remainderUpMoney: '',
      custType: '01',
      brandName: '',
      listTypr: [],
      mask: false,
      carListName: '',
      omgd: true,
      omgd2: true,
      remainTimes: '',
      nameCup: '',
      carModelName: '',
      licenseNo: '',
      Nomoney: false,
      alreadyElecs: 0,
      maxCurrent: 0,
      alreadyElecs: 0,
      chargeVolt: 0,
      hour: 0,
      minute: 0,
      innes: '',
      timesMax: '',
      minutesMax: '',
      judgment: true,
      timesMax: '',
      minutesMax: '',
      numberd: '0',
      msg: '开始充电',
      router: false,
      starts: true,
      nocanceldd: true,
      qidong: true,
      inne: '',
      hidden: true,
      token: '',
      orderNo: '',
      loadMsg: '正在结束...',
      hiddenLoading: true,
      tz: true,
      oped: false,
      opensn: true,
      chargeData: {
        numberd: 0,
        alreadyElecs: '0',
        //已充电量
        alreadyAmounts: '0',
        //已充金额
        chargeVolt: '0',
        //充电电压
        maxCurrent: '0',
        //充电电流
        planChargeAmt: '0',
        //预充金额
        hour: '0',
        //充电时长-小时
        minute: '0',
        //充电时长-分钟
        outputPower: '0',
        remainTimes: '',
        licenseNo: '',
        carModelName: '',
        stationName: '',
        pileName: '',
      },

      aletMsg: false,
      aletMsg2: false,
      cup: 60,
      cup2: 60,
      cupType: false,
      cupType2: false,
      noCars: false,
      chargeStatus: '01',
      chargeStatusText: '',
      olp: false,
      a: false,
      z: false,
      nocancel: '',
      memberInfo: {},
      maxAmt: 0,
      couponList: [],
      memberStation: '',
      memberType: 0,
    };
  },
  onLoad(options) {
    console.log(options.orderNo, '这是options  orderNo');
    if (options?.type) {
      this.memberType = options?.type;
    }
    var that = this;
    var nameCup = uni.getStorageSync('nameCup') || '';
    console.log(nameCup, 'nameCupnameCup');
    that.setData({
      nameCup: nameCup,
    });

    uni.getStorage({
      key: 'token',
      success(res) {
        that.setData({
          token: res.data,
          orderNo: options.orderNo,
        });
        that.init();
        console.log(that.orderNo, '======================');
      },
    });
    if (that.olp) {
      //开始充电
      that.just();
    } else {
      //考虑要不要进来给个菊花加载
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},
  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    uni.setStorageSync('startd', 1);
    var paramd = {};
    var that = this;
    const [, res] = await getPref(paramd);
    if (res) {
      if (res.carList.length > 0) {
        res.carList[0].types = true;
      }
      that.setData({
        opensn: true,
        listTypr: res.carList,
        custType: res.custType,
      });
    }
    // 获取会员信息
    this.getMemberInfo();
    // 会员权益配置
    this.getConfigDesc();
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    console.log('离开了');
  },
  // StatN:function(){
  //   console.log('启动失败点击了重试 0901')
  //   this.setData({
  //     startNos:true,
  //     aletMsg:true
  //   })
  //   // this.init()
  //   this.confirm2()
  // },
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    console.log('离开了监听页面卸载');
    this.setData({
      opensn: false,
    });
    console.log(this.opensn);
  },
  methods: {
    toPlusPay() {
      uni.navigateTo({
        url: '/pages/member/buyPlus/index',
      });
    },
    // 获取会员信息
    async getMemberInfo() {
      const [err, res] = await getMemberInfo();
      if (res) {
        this.memberInfo = res;
      }
    },
    // 获取会员权益配置
    async getConfigDesc() {
      const [, res] = await getConfigDesc({
        vipLevel: this.memberType == 0 ? '5' : '6',
      });
      if (res) {
        this.maxAmt = res.maxAmt || 0;
        this.memberStation = res.memberStation || '';
        this.couponList = [
          ...(res.cashCoupon || []).sort((a, b) => b.cpnAmt - a.cpnAmt).slice(0, 1),
          ...(res.discountCoupon || []).sort((a, b) => a.cpnAmt - b.cpnAmt).slice(0, 1),
        ];
      }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    timeStamp(StatusMinute) {
      var day = parseInt(StatusMinute / 60 / 24);
      var hour = parseInt((StatusMinute / 60) % 24);
      var min = parseInt(StatusMinute % 60);
      StatusMinute = '';
      if (day > 0) {
        StatusMinute = day + '天';
      }
      if (hour > 0) {
        StatusMinute += hour + '小时';
      }
      if (min > 0) {
        StatusMinute += parseFloat(min) + '分钟';
      }
      return StatusMinute;
    },

    chargeStatusFilter() {
      const { chargeStatus } = this;
      let chargeStatusText = '';
      if (chargeStatus === '01') {
        chargeStatusText = '启动中';
      } else if (chargeStatus === '02') {
        chargeStatusText = '充电中';
      } else {
        chargeStatusText = '充电已完成';
      }
      this.setData({
        chargeStatusText,
      });
    },

    async init() {
      var that = this;
      console.log('这里是循环圈');
      var token = that.token;
      var orderNo = that.orderNo;
      // function clock() {
      //   _self.setData({
      //     router: !_self.data.router
      //   })
      // }
      if (that.opensn) {
        const [, res] = await getChargingInfo({
          orderNo: orderNo,
        });
        if (res) {
          console.log(res, '充电详情');
          that.updateChargeData(res);
          that.setData(
            {
              // licenseNo: res.licenseNo,
              // carModelName: res.carModelName,
              remainTimes: res.remainTimes || '',
              remainderUpMoney: uni.getStorageSync('remainderUpMoney') || '1',
              chargeStatus: res.chargeStatus,
            },
            () => {
              that.chargeStatusFilter();
            }
          );
          var Nomoney =
            res.chargeOrderList && Array.isArray(res.chargeOrderList) ? res.chargeOrderList[0].ifRecharge : '';
          console.log(Nomoney, '状态');
          var remainderUpMoney = uni.getStorageSync('remainderUpMoney');
          // if (res.chargeOrderList[0].pileControlStatus == '0901'){
          //   // if (res.chargeOrderList[0].pileControlStatus == '0901') {
          //     console.log('当前 启动失败 0901')
          //     clearInterval(_self.times)
          //     _self.setData({
          //       startNos: false,
          //       aletMsg: false,
          //       kind: res.chargeOrderList[0].controlFailPrompt,
          //       // oped: true //停止
          //     });
          //     return false
          //   // }
          // }

          if (res.chargeStatus == '01') {
            //未开始
            // if (_self.data.qidong) {
            //   _self.setData({
            //     msg: '开始充电'
            //   });
            // } else {
            //   _self.setData({
            //     msg: '启动中'
            //   });
            // }
            that.just();
            that.setData({
              olp: false,
            });
          } else if (res.chargeStatus == '02') {
            //充电中
            console.log('当前正在充电。');
            if (that.tz) {
              that.setData({
                router: !that.router,
                msg: that.chargeData.remainTimes || '',
                hiddenLoading: true,
                judgment: false,
                olp: true,
              });
            } else {
              that.setData({
                // inne: setInterval(clock, 5000),//开始动画
                // router: true,
                msg: that.chargeData.remainTimes || '',
                judgment: false,
              });
            }
            if (Nomoney == '1') {
              that.setData({
                Nomoney: true,
              });
            } else {
              that.setData({
                Nomoney: false,
              });
            }
            // function clock() {
            //   _self.setData({
            //     router: !_self.data.router
            //   })
            // }
            that.setData({
              starts: false,
              // inne: setInterval(clock, 3000),//开始动画
              // judgment: !_self.data.judgment,
              msg: that.chargeData.remainTimes,
              cupType: true,
            });
          } else if (res.chargeStatus == '03') {
            //已完成
            that.setData({
              msg: '充电已完成',
              oped: true,
              opensn: false,
              cupType: true,
              cupType2: true,
            });
            that.inne && clearInterval(that.inne); //停止动画
            that.innes && clearInterval(that.innes); //停止动画
            that.timesMax && clearInterval(that.timesMax); //停止动画
            that.minutesMax && clearInterval(that.minutesMax); //停止动画
            setTimeout(function () {
              that.setData({
                hiddenLoading: true,
              });
              uni.redirectTo({
                url: `/subPackages/ordercharge/ordercharge?orderNo=${orderNo}`,
              });
            }, 3000);
          }
        }
        if (that.oped) {
          return false;
        } else {
          setTimeout(function () {
            that.init();
          }, 5000);
        }
      } else {
        console.log('停止循环');
      }
    },

    chongzhi() {
      uni.navigateTo({
        url: '/subPackages/recharge/recharge',
      });
    },
    stopCh() {
      this._debouncedStopCh();
    },
    _stopChImpl() {
      var that = this;
      ENVR !== 'wx' && that.$refs.popup.open();
      subscribeMessage(['充电结束通知', '充电即将终止提醒'], () => {
        that.$refs.popup.open();
      });
    },

    updateChargeData(dt) {
      //更新充电数据
      var timesDy = new Date();
      console.log(timesDy);
      var that = this;
      if (dt.chargeOrderList && dt.chargeOrderList.length > 0) {
        var chargeData = dt.chargeOrderList[0];
        var remainTimes = that.timeStamp(chargeData.remainTimes);
        console.log(remainTimes, '剩余充电时长');
        var cd = {
          numberd: Number(chargeData.chargeProgress) || 0,
          //充电进度
          alreadyElecs: chargeData.alreadyElecs || '-',
          //已充电量
          alreadyAmounts: chargeData.alreadyAmounts || '-',
          //已充金额
          chargeVolt: chargeData.chargeVolt || '-',
          //充电电压
          maxCurrent: chargeData.maxCurrent || '-',
          //充电电流
          planChargeAmt: chargeData.planChargeAmt || '-',
          //预充金额
          hour: chargeData.alreadyTimes
            ? (Number(chargeData.alreadyTimes - (chargeData.alreadyTimes % 60)) / 60).toFixed(0)
            : '-',
          //充电时长-小时
          minute: chargeData.alreadyTimes ? Number(chargeData.alreadyTimes % 60).toFixed(0) : '-',
          //充电时长-分钟
          outputPower: chargeData.outputPower || '-',
          //当前功率
          remainTimes: remainTimes || '',
          //当前功率
          licenseNo: dt.licenseNo || '',
          carModelName: dt.carModelName || '',
          pileName: chargeData.pileName || '',
          stationName: chargeData.stationName || '',
        };
        that.setData({
          chargeData: cd,
        });
      }
    },

    goSevice() {
      uni.navigateTo({
        url: '/subPackages/customerService/index',
      });
    },

    async just() {
      var that = this;
      function clock() {
        that.setData({
          router: !that.router,
        });
      }
      if (that.judgment) {
        var parmes = {
          controlType: '01',
          orderNo: that.orderNo,
        };
        const [, res] = await changeChargingStatus(parmes, {
          // #ifdef H5
          'content-type': 'application/x-www-form-urlencoded',
          // #endif
        });
        if (res) {
          console.log(res, '开始充电');
          that.setData({
            starts: !that.starts,
            inne: setInterval(clock, 3000),
            //开始动画
            // judgment: !_self.data.judgment,
            judgment: false,
            // msg: '结束充电',
            msg: '正在启动',
          });
          // _self.init()
          that.setData({
            aletMsg: true,
          });
          that.times = setInterval(function () {
            that.setData({
              cup: that.cup - 1,
            });
            if (that.cupType) {
              console.log('在60s内 启动充电成功');
              that.setData({
                cup: 60,
                aletMsg: false,
              });
              clearInterval(that.times);
            } else if (that.cup == 0 && that.cupType == false) {
              console.log('启动充电失败。');
              clearInterval(that.inne);
              clearInterval(that.times);
              return false;
            }
          }, 1000);
          setTimeout(function () {
            console.log('60s时间到 未启动');
            if (uni.getStorageSync('startd') == 0) {
              //不在该页面
              clearInterval(that.times);
              that.setData({
                cup: 60,
                aletMsg: false,
              });
            } else if (uni.getStorageSync('startd') == 1) {
              //在该页面
              clearInterval(that.times);
              that.setData({
                cup: 60,
                aletMsg: false,
              });
              if (that.cupType) {
              } else {
                // that.setData({
                //   omgd: false,
                // });
                that.$refs.popupOmgd.open();
              }
            }
          }, 60000); //60s判断
        } else {
          console.log('失败');
          uni.showToast({
            icon: 'loading',
            title: '加载数据失败',
          });
        }
      } else {
        // _self.setData({
        //   hidden: false //打开弹框
        // })
      }
    },

    ads() {
      uni.navigateTo({
        url: '/subPackages/add/add',
      });
    },

    gos() {
      var that = this;
      if (that.custType == '01') {
        //个人
        console.log('个人');
        that.setData({
          mask: true,
        });
      } else {
        console.log('企业');
        if (that.chargeData.carModelName == '' && that.chargeData.licenseNo == '') {
          console.log('企业  没车没牌');
        } else {
          uni.navigateTo({
            url: '/subPackages/shcar/shcar?orderNo=' + that.orderNo,
          });
        }
      }
    },

    cancel() {
      // this.setData({
      //   hidden: true, //关闭弹框
      // });
      this.$refs.popup.close();
    },

    async cancel2() {
      console.log('更换设备');
      var that = this;
      var params = {
        controlType: '02',
        orderNo: that.orderNo,
      };
      const [, res] = await cancelOrder(params);
      if (res && res.ret == 200) {
        console.log(res, '取消成功');
        that.setData({
          oped: true,
        });
        uni.navigateBack();
      } else {
        console.log('取消失败原因：', res.data.msg);
      }
    },

    confirm2() {
      that.$refs.popupOmgd.close();
      this.setData({
        // omgd: true,
        judgment: true,
      });
      this.just();
    },

    async confirm() {
      var that = this;
      that.$refs.popup.close();
      that.setData({
        // hidden: true,
        //关闭弹框
        // loadMsg: '正在结束..',
        tz: false,
        // hiddenLoading: false,
        aletMsg2: true,
      });
      that.times2 = setInterval(function () {
        that.setData({
          cup2: that.cup2 - 1,
        });
        if (that.cupType2) {
          console.log('在60s内 停止充电成功');
          that.setData({
            cup2: 60,
            aletMsg2: false,
          });
          clearInterval(that.times2);
        } else if (that.cup2 == 0 && that.cupType2 == false) {
          console.log('停止充电失败。');
          clearInterval(that.times2);
          return false;
        }
      }, 1000);
      setTimeout(function () {
        console.log('60s时间到 未停止');
        clearInterval(that.times2);
        that.setData({
          cup2: 60,
          aletMsg2: false,
        });
        if (that.cupType2) {
        } else {
          that.$refs.popupOmgd2.open();
          // that.setData({
          //   omgd2: false,
          // });
        }
      }, 60000); //60s判断

      const [, res] = await changeChargingStatus(
        {
          controlType: '02',
          orderNo: that.orderNo,
        },
        {
          // #ifdef H5
          'content-type': 'application/x-www-form-urlencoded',
          // #endifx禅道d
        }
      );
      if (res) {
        console.log(res, '结束充电');
        if (!that.judgment) {
          // clearInterval(_self.data.inne)//停止动画
          // clearInterval(_self.data.innes)//停止动画
          // clearInterval(_self.data.timesMax)//停止动画
          // clearInterval(_self.data.minutesMax)//停止动画
          // _self.setData({
          //   starts: !_self.data.starts //转盘停止
          // })
          // _self.setData({
          //   judgment: !_self.data.judgment,
          //   msg: '充电结束',
          //   router: false, //按钮中间显示文字的那面
          // })
        }
        禅道;
      }
    },

    oll() {
      console.log('-0-0-');
      this.confirm();
      // this.setData({
      //   omgd2: true,
      // });
      that.$refs.popupOmgd2.close();
    },

    maskRmove() {
      this.setData({
        mask: false,
      });
    },

    async actives(e) {
      console.log(e.currentTarget.dataset.index, '索引');
      console.log(e.currentTarget.dataset.num, '点击状态');
      var that = this;
      var licenseNo = e.currentTarget.dataset.msg;
      console.log(licenseNo, 'this is licenseNo');
      var index = e.currentTarget.dataset.index;
      for (var i = 0; i < that.listTypr.length; i++) {
        var a = 'listTypr[' + i + '].types';
        that.setData({
          [a]: false,
        });
      }
      var z = 'listTypr[' + index + '].types';
      that.setData({
        [z]: true,
        licenseNo: licenseNo,
      });
      var paramd = {
        licenseNo: licenseNo,
        orderNo: that.orderNo,
      };
      const [, res] = await selectLicenseNo(paramd);
      if (res) {
        if (res.ret == 200) {
          var z = 'chargeData.licenseNo';
          that.setData({
            mask: false,
            [z]: licenseNo,
            licenseNo: licenseNo,
          });
        } else {
          console.log(res, '炸了');
        }
      }
    },

    djs() {
      console.log('占位：函数 djs 未声明');
    },
  },
  created() {
    this._debouncedStopCh = debounce(this._stopChImpl, 500);
  },
};
</script>
<style>
@import './charge.css';
</style>
<style lang="scss" scoped>
.bottom-wrap {
  z-index: 999;
}
.vip-content {
  margin: 30rpx auto 20rpx;
  width: 702rpx;
  min-height: 222rpx;
  height: auto;
  padding: 34rpx 24rpx;
  box-sizing: border-box;
  background-size: 100% 100%;
  .title {
    font-size: 37rpx;
    font-weight: 700;
    line-height: 40rpx;
    letter-spacing: 0rpx;
    color: #111111;
    display: flex;
    flex-direction: row;
    align-items: baseline;
    margin-bottom: 30rpx;
    span {
      font-size: 52rpx;
      color: #ff4d00;
    }
  }
  .con {
    display: flex;
    gap: 20rpx;
    grid-gap: 20rpx;
    align-items: flex-end;
    .order-list {
      flex: 1;
      display: flex;
      flex-direction: column;
      font-size: 22rpx;
      color: #333333;
      .order-item {
        margin-bottom: 15rpx;
        width: 100%;
        display: flex;
        align-items: center;
        gap: 10rpx;
        grid-gap: 10rpx;
        .seq {
          padding: 5rpx 20rpx;
          border-radius: 100rpx;
          background: #dcad92;
          font-size: 22rpx;
          color: #5d2f17;
          span {
            font-size: 30rpx;
            font-style: italic;
          }
        }
      }
    }
  }
  .btn {
    border-radius: 192px;
    background: linear-gradient(
        126deg,
        rgba(255, 255, 255, 0) -2%,
        rgba(255, 255, 255, 0.19) 11%,
        rgba(10, 9, 9, 0) 60%
      ),
      linear-gradient(109deg, #5e5a54 0%, #1e1f28 94%);
    box-shadow: inset 0px 1px 0px 0px #fff9ea;
    height: 60rpx;
    padding: 12rpx 22rpx;
    font-size: 24rpx;
    line-height: 36px;
    color: #ffe3c1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    .btn-tip {
      width: 104rpx;
      height: 48rpx;
      position: absolute;
      left: -45rpx;
      top: -20rpx;
      background-size: 100% auto;
      background-repeat: no-repeat;
    }
  }
}
.popup-box {
  background: #fff;
  border-radius: 24rpx;
  width: 80vw;
  .popup-title {
    font-size: 36rpx;
    width: 100%;
    text-align: center;
    margin: 24rpx 0;
    padding: 24rpx 0;
  }
  .btns {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 24rpx;
    border-top: 1rpx solid #cbcbcb;
    .ok-btn {
      font-size: 32rpx;
      font-weight: bold;
      color: rgb(84, 179, 255);
      width: 100%;
      box-sizing: border-box;
      padding: 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-left: 1rpx solid #cbcbcb;
    }
    .cancel-btn {
      font-size: 32rpx;
      font-weight: bold;
      width: 49%;
      box-sizing: border-box;
      padding: 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-right: 1rpx solid #cbcbcb;
    }
    .confirm-btn {
      font-size: 32rpx;
      font-weight: bold;
      color: rgb(84, 179, 255);
      width: 49%;
      box-sizing: border-box;
      padding: 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-left: 1rpx solid #cbcbcb;
    }
  }
}
</style>
