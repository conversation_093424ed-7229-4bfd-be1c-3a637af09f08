import { request as __request } from '../request/request';
import { ENVR, scanBaseurl, defBaseurl, baseBaseurl, pubBaseurl } from '../../config/global';
const baseurl = ENVR === 'wx' ? scanBaseurl : defBaseurl;

// 查询订单可开票列表
export const getOrderInvoicesList = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${baseurl}/v0.1/invoices`,
      query: params,
    }
  );
};

// 开票说明
export const getInvoiceExplain = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${scanBaseurl}/v0.1/paras`,
      query: params,
    }
  );
};
export const setInvoiceAgain = async (params, config = {}) => {
  return __request(
    { 
      ...config,
      // 'content-type': 'application/x-www-form-urlencoded'
     },
    {
      method: 'POST',
      url: `${
        ENVR === 'wx'
          ? scanBaseurl + '/v0.1/account/invoiceAgain'
          : defBaseurl + '/open/invoice/v0.1/account/invoiceAgain'
      }`,
      data: params,
    }
  );
};

export const setAccountInvoice = async (params, config = {}) => {
  return __request(
    {
      ...config,
      // #ifdef H5
      'content-type': 'application/x-www-form-urlencoded',
      // #endif
    },
    {
      method: 'POST',
      url: `${baseurl}/v0.1/account/invoice`,
      data: params,
    }
  );
};
export const setAccountInvoiceRetry = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${
        ENVR === 'wx'
          ? scanBaseurl + '/v0.1/account/invoiceRetry'
          : defBaseurl + '/open/invoice/v0.1/account/invoiceRetry'
      }`,
      data: params,
    }
  );
};

// !小程序接口报错400
/**
 * 获取发票抬头列表
 * @param {Object} params - 请求参数
 * @param {Object} [config={}] - 请求配置选项
 * @returns {Promise} 返回发票抬头列表数据
 * @description 根据环境(微信/默认)使用不同的请求方法和URL获取发票抬头列表
 */
export const getInvoiceTitleList = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: ENVR === 'wx' ? 'POST' : 'GET',
      url: `${
        ENVR === 'wx'
          ? scanBaseurl + '/v0.2/invoice-title-list'
          : defBaseurl + '/open/invoiceTitle/v0.2/invoice-title-list'
      }`,
      data: params,
    }
  );
};
export const getInvoicesLog = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'GET',
      url: `${baseurl}/v0.1/accounts/invoice-log`,
      query: params,
    }
  );
};
export const getNotifyInvoiceEmail = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${
        ENVR === 'wx'
          ? scanBaseurl + '/v0.1/account/notifyInvoiceEmail'
          : defBaseurl + '/open/invoice/v0.1/account/notifyInvoiceEmail'
      }`,
      data: params,
    }
  );
};
// 删除抬头
export const deleteInvoiceTitle = async (params, config = {}) => {
  const queryParameters = Object.keys(params)
    .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&');
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${
        ENVR === 'wx'
          ? scanBaseurl + '/v0.2/invoice-title-delete'
          : defBaseurl + '/open/invoiceTitle/v0.2/invoice-title-delete?' + queryParameters
      }`,
      data: params,
    }
  );
};
// 获取抬头信息
export const getInvoiceTitleInfo = async (params, config = {}) => {
  return __request(
    { ...config },
    {
      method: ENVR === 'wx' ? 'POST' : 'GET',
      url: `${
        ENVR === 'wx'
          ? scanBaseurl + '/v0.2/invoice-title-Info'
          : defBaseurl + '/open/invoiceTitle/v0.2/invoice-title-Info'
      }`,
      data: params,
    }
  );
};
// 添加抬头信息
export const addInvoiceTitle = async (params, config = {}) => {
  const queryParameters = Object.keys(params)
    .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&');
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${
        ENVR === 'wx'
          ? scanBaseurl + '/v0.2/invoice-title-add'
          : defBaseurl + '/open/invoiceTitle/v0.2/invoice-title-add?' + queryParameters
      }`,
      data: params,
    }
  );
};
// 更新抬头信息
export const updataInvoiceTitleInfo = async (params, config = {}) => {
  const queryParameters = Object.keys(params)
    .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&');
  return __request(
    { ...config },
    {
      method: 'POST',
      url: `${
        ENVR === 'wx'
          ? scanBaseurl + '/v0.2/invoice-title-update'
          : defBaseurl + '/open/invoiceTitle/v0.2/invoice-title-update?' + queryParameters
      }`,
      data: params,
    }
  );
};
