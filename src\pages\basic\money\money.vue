<template>
  <view>
    <!-- pages/money/money.wxml -->
    <view class="money">
      <view class="money_top">账户余额</view>
      <view class="money_middle">
        <view class="money_middle_lf">
          <view class="lf_inner">
            <text>￥</text>
            {{ formatNumber(money) }}
            <text>元</text>
          </view>
          <view class="lf_msg">{{ actList.actName }}</view>
        </view>
        <button size="mini" class="topBtn" @tap="jump">充值</button>
        <!-- <button size="mini" class="topBtn" @tap="toCardPasswordExchange">充值卡兑换</button> -->
      </view>
      <view class="money_flex">
        <view class="money_flex_list">
          <view class="net1">￥{{ tixian }}</view>
          <view class="net2">本金</view>
        </view>
        <!-- <view class="money_flex_list count">
          <view class="net1">￥{{ formatNumber(dongjie) }}</view>
          <view class="net2">冻结金额</view>
        </view> -->
        <view class="money_flex_list count">
          <view class="net1">￥{{ formatNumber(moneyKt) }}</view>
          <view class="net2">可退金额</view>
        </view>
        <view class="money_flex_list counts">
          <view class="net1">￥{{ formatNumber(kzj) }}</view>
          <view class="net2">馈赠金额</view>
        </view>
        <view class="money_flex_list counts" @click="toRechargeable">
          <view class="net1">￥{{ formatNumber(czk) }}</view>
          <view class="net2">充值卡</view>
        </view>
      </view>
    </view>

    <view class="money_item">
      <view @tap="Jy" class="item_info">
        交易记录
        <text class="icon iconfont icon-arrow_right" size="20"></text>
      </view>
      <view @tap="refund" class="item_info">
        退款
        <text class="icon iconfont icon-arrow_right" size="20"></text>
      </view>
      <!-- <view bindtap="refundHistory" class='item_info nones'>退款历史<icon class="icon iconfont icon-arrow_right" size="20"></icon></view> -->
      <view @tap="handClickRouter" class="item_info">
        发票
        <text class="icon iconfont icon-arrow_right" size="20"></text>
      </view>
      <view @tap="toCardPasswordExchange" class="item_info">
        充值卡兑换
        <text class="icon iconfont icon-arrow_right" size="20"></text>
      </view>
    </view>
  </view>
</template>

<script>
// pages/money/money.js
import { getBalanceCan, getAccountsActs } from '@/services/index';
export default {
  data() {
    return {
      money: 0,
      list: [],
      tixian: '',
      yhye: '',
      dongjie: '',
      czk: '',
      kzj: '',
      actList: [],
      moneyKt: 0,
      token: null,
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    var that = this;
    that.setData({
      token: uni.getStorageSync('token') || '',
    });
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadData();
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   * 仅在小程序环境下启用
   */
  // #ifndef H5
  onPullDownRefresh: async function () {
    try {
      // 触发数据加载
      await this.loadData();
      // 正常终止下拉刷新
      uni.stopPullDownRefresh();
    } catch (error) {
      // 错误处理
      console.error('下拉刷新失败:', error);
      uni.stopPullDownRefresh();
    }
  },
  // #endif

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
  methods: {
    // 格式化数字为两位小数，并去除末尾多余的0
    formatNumber(value) {
      // 如果值不存在、为空字符串、或者是无效值，返回0
      if (value === undefined || value === null || value === '' || value === 'NaN') {
        return '0';
      }

      // 转换为数字类型
      const num = parseFloat(value);

      // 如果转换后是NaN或不是有限数字，返回0
      if (isNaN(num) || !isFinite(num)) {
        return '0';
      }

      // 格式化为两位小数
      const formattedNum = num.toFixed(2);

      // 去除末尾多余的0和小数点
      // 例如：2.00 -> 2, 2.10 -> 2.1, 2.01 -> 2.01
      return formattedNum.replace(/\.?0+$/, '');
    },

    // 安全的加法运算，防止NaN
    safeAdd(a, b) {
      const numA = parseFloat(a) || 0;
      const numB = parseFloat(b) || 0;

      // 检查是否为有效数字
      const safeA = isNaN(numA) || !isFinite(numA) ? 0 : numA;
      const safeB = isNaN(numB) || !isFinite(numB) ? 0 : numB;

      return safeA + safeB;
    },

    async getMoneyKT() {
      const [, res] = await getBalanceCan();
      if (res) {
        console.log(res, '余额444接口回参', res.onlineCanReturnAmt);
        this.moneyKt = res.onlineCanReturnAmt || 0;
      }
    },

    // 去充值卡页面
    toRechargeable() {
      uni.navigateTo({
        url: '/pages/setting/rechargeable/index',
      });
    },
    // 卡密兑换
    toCardPasswordExchange() {
      uni.navigateTo({
        url: '/pages/setting/cardPasswordExchange/index',
      });
    },
    async loadData() {
      var that = this;
      this.getMoneyKT();
      await this.$store.dispatch('login/getUserInfoCallback').then(async (res) => {
        console.log(res, '用户信息');
        this.setInfo(res);
        const [, result] = await getAccountsActs({
          actType: '07',
        });
        if (result) {
          console.log(result.actList, '优惠活动');
          if (result.actList) {
            that.setData({
              actList: result.actList[0],
            });
          }
        }
        uni.stopPullDownRefresh();
      });
    },
    setInfo(res) {
      const that = this;
      console.log(res, '余额接口回参');

      // 检查 res 是否存在
      if (!res) {
        console.warn('接口返回数据为空');
        return;
      }

      console.log(res.accFreeAmt, '余额剩余');

      // 检查并设置默认值
      const list = res.accList || [];
      console.log(list, 'res.accList');

      // 如果列表为空，设置默认值并返回
      if (!Array.isArray(list) || list.length === 0) {
        that.setData({
          money: res.acctotalAmt || 0,
          list: [],
          tixian: res.accBalance || 0,
          dongjie: 0,
          yhye: res.accBalance || 0,
          kzj: 0,
          czk: 0,
        });
        return;
      }

      var remainderUpMoney = [];
      var a1 = [];
      var a3 = [];
      var a5 = [];
      var a6 = [];
      var a8 = [];
      var a10 = [];
      var a11 = [];

      // 安全地获取账户数据
      const getAccTypeData = (accType) => {
        return list.filter((item) => item && item.accType && item.accType.indexOf(accType) >= 0);
      };
      // 安全地获取账户数据
      const getFindAccTypeDataItem = (accType) => {
        return list.find((item) => item && item.accType && item.accType.indexOf(accType) >= 0);
      };

      remainderUpMoney = getAccTypeData('07');
      a1 = getAccTypeData('01');
      a3 = getAccTypeData('03');
      a5 = getAccTypeData('05');
      a6 = getAccTypeData('06');
      a8 = getAccTypeData('08');
      a10 = getAccTypeData('10');
      a11 = getAccTypeData('11');

      // 安全地获取数值
      const safeNumber = (arr, field) => {
        if (!arr || !arr[0] || arr[0][field] === undefined || arr[0][field] === null) {
          return 0;
        }
        const num = Number(arr[0][field]);
        return isNaN(num) || !isFinite(num) ? 0 : num;
      };

      // 计算馈赠金
      const kzj = safeNumber(remainderUpMoney, 'accFrozen') + safeNumber(remainderUpMoney, 'accFree');

      // 计算冻结金额
      const dongjieJ =
        safeNumber(a1, 'accFrozen') +
        safeNumber(a3, 'accFrozen') +
        safeNumber(a5, 'accFrozen') +
        safeNumber(a6, 'accFrozen') +
        safeNumber(a8, 'accFrozen') +
        safeNumber(a10, 'accFrozen') +
        safeNumber(a11, 'accFrozen') +
        safeNumber(remainderUpMoney, 'accFrozen');

      console.log(remainderUpMoney, 'remainderUpMoney321312');

      // 更新数据，所有值都带默认值
      that.setData({
        money: res.acctotalAmt || 0,
        list: list,
        tixian: res.accBalance || 0,
        dongjie: dongjieJ,
        yhye: res.accBalance || 0,
        kzj: kzj,
        czk: this.safeAdd(getFindAccTypeDataItem('10')?.accFree, getFindAccTypeDataItem('11')?.accFree),
      });

      // 调试输出
      console.log(
        '账户数据统计:',
        {
          remainderUpMoney: remainderUpMoney.length,
          a1: a1.length,
          a3: a3.length,
          a5: a5.length,
          a6: a6.length,
          a8: a8.length,
          kzj,
          dongjieJ,
          czk: getFindAccTypeDataItem('10')?.accFree + getFindAccTypeDataItem('11')?.accFree,
        },
        getFindAccTypeDataItem('10'),
        getFindAccTypeDataItem('11')
      );
    },
    jump() {
      uni.navigateTo({
        url: '/subPackages/recharge/recharge',
      });
    },

    Jy() {
      uni.navigateTo({
        url: '/subPackages/history/history',
      });
    },

    refund() {
      uni.navigateTo({
        url: '/subPackages/refund/refund',
      });
    },

    handClickRouter() {
      uni.navigateTo({
        url: '/subPackages/invoiceopening/invoiceopening',
      });
    },

    refundHistory() {
      uni.navigateTo({
        url: '/subPackages/refundhs/refundhs',
      });
    },
  },
};
</script>
<style>
@import './money.css';
</style>
