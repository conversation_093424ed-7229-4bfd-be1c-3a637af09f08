import { ENVR } from '@/config/global';

export const messageCode = [
  {
    title: '会员购买成功通知',
    templateId: 'A2JLmC8Fh9FZ6a20zmXeKY4IAG3IG1kcjc78rzM99Rk',
  },
  {
    title: '余额不足提醒',
    templateId: 'mAFHkzLvjRtt2tBvWWJ3wezYaL3AKyet9G1efJH7qTI',
  },
  {
    title: '充电结束通知',
    templateId: 'e5ku5wc59YAnOYkpE_oKhf37CwKLKSFFFOgfzm66MiA',
  },
  {
    title: '退款结果通知',
    templateId: 'Va1jmuDbwLL1sO-dHkRMEnblvC2FBIwnaPfw8dCv_Hw',
  },
  {
    title: '充值状态通知',
    templateId: 'BHLOsI_z8Sg1-GWe6IYw_3X5ZcnuGDlTKQExVRgbR5g',
  },
  {
    title: '开票申请结果通知',
    templateId: 'T9gnGnAHMqZMQSnRCcZ9LOSdkHy5PvqnTkfbQ6l1wPU',
  },
  {
    title: '充电开始通知',
    templateId: 'nkmBUOmheCtDRNwJ4VKGMt-ubLR_EkslybRBXqDuYM4',
  },
  {
    title: '充电结算通知',
    templateId: 'gpZJcy-OS_23hJr0YNtmIw7WtI70jl7fiRtcKsSjbwU',
  },
  {
    title: '充电即将终止提醒',
    templateId: 'eAaqFRhWW96_EAPNTvm1MR1CDUqNj4jr-hPy0d-p84g',
  },
];
export function subscribeMessage(templateNameList = [], callback) {
 if(ENVR !== 'wx'){
    return true
 }  
  console.log('触发订阅', templateNameList);
  const tmplIds = messageCode.reduce((cur, nex) => {
    if (templateNameList.includes(nex.title)) {
      cur.push(nex.templateId);
    }
    return cur;
  }, []);
  const push = () => {
    console.log('开始推送', tmplIds);
    wx.requestSubscribeMessage({
      tmplIds: tmplIds,
      success: (res) => {
        console.log('推送成功', res);
        return callback && callback();
      },
      fail: (err) => {
        console.log('推送失败', err);
        return callback && callback();
        // subPackages/placeorder/placeorder
      },
      complete: () => {
        console.log('推送消息');
      },
    });
  };
  wx.getSetting({
    withSubscriptions: true,
    success: (res) => {
      const { subscriptionsSetting } = res;
      if (tmplIds.some((q) => !subscriptionsSetting[q])) {
        console.log(
          '还有消息未询问!',
          tmplIds.filter((q) => !subscriptionsSetting[q])
        );
        push();
        return;
      } else if (!tmplIds.every((q) => subscriptionsSetting[q] && subscriptionsSetting[q] == 'accept')) {
        console.log('还有消息未订阅');
        uni.showModal({
          title: '因通知关闭,无法接收消息提醒',
          confirmText: '去开启',
          content: `开启后,可及时获取消息通知,如 ${templateNameList.join(',')} 等通知`,
          success: function (res) {
            if (res.confirm) {
              wx.openSetting({
                withSubscriptions: true,
                success: () => {
                  push();
                },
              });
            } else {
              push();
            }
          },
          fail: () => {
            push();
          },
        });
      } else {
        console.log('消息已订阅');
        push();
      }
    },
    fail: () => {
      console.log('获取消息订阅失败');
      push();
    },
  });
}
