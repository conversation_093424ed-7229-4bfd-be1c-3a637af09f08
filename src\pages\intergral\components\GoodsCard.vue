<!--
@name: 商品卡片组件
@description: 子组件
@time: 2024/7/25
-->
<template>
  <view class="goods-card">
    <view class="goods-card-content">
      <!--        商品图片-->
      <img :src="getImage" class="goods-image" />
      <!--        商品信息 -->
      <view class="goods-info">
        <text class="goods-name">{{ cardItem.goodsName }}</text>
        <view class="goods-points">
          <text class="points-item">{{ cardItem.goodsIntegral }}</text>
          <text class="points-item" v-if="cardItem.goodsAmt">积分+{{ cardItem.goodsAmt }}元</text>
          <text class="points-item" v-else>积分</text>
          <text class="points-item" v-if="cardItem.goodsFreight">
            {{ cardItem.goodsFreight === '01' ? '包邮' : '到付' }}
          </text>
        </view>
        <text class="storage">库存：{{ cardItem.goodsNumber }}</text>
      </view>
    </view>
    <view class="line"></view>
    <view class="number-box">
      <text class="number-box-text">兑换数量</text>
      <view class="number">
        <text class="limit">每人限购{{ cardItem.goodsPurchase }}件</text>
        <uni-number-box
          v-model="number"
          @change="changeValue"
          :disabled="!showNumberBox"
          :max="cardItem.goodsPurchase"
        />
      </view>
    </view>
  </view>
</template>

<script>
import uniNumberBox from '@/components/uni-ui/uni-number-box/uni-number-box';

export default {
  name: 'GoodsCard',
  components: {
    uniNumberBox,
  },
  props: {
    showNumberBox: {
      type: Boolean,
      default: false,
    },
    cardItem: {
      type: Object,
      default: () => {},
    },
    exchangeNumber: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      number: 0,
    };
  },
  computed: {
    // 商品图片路径
    getImage() {
      const str = `${process.env.VUE_APP_BASE_HOST}/pub/api/v0.1/attachs/${this.cardItem.fileId}`;
      return str;
    },
  },
  mounted() {
    this.number = this.exchangeNumber;
  },
  methods: {
    // 传递数量给外部
    changeValue(val) {
      this.$emit('getExchangeNumber', val);
    },
  },
};
</script>

<style scoped lang="scss">
.goods-card {
  padding: 24rpx;
  background: white;
  border-radius: 16rpx;
  &-content {
    display: flex;
    .goods-image {
      width: 180rpx;
      height: 180rpx;
      border-radius: 16rpx;
      margin-right: 24rpx;
    }
    .goods-info {
      display: flex;
      flex-direction: column;
      justify-content: center;
      .goods-name {
        font-size: 32rpx;
        font-weight: bold;
        line-height: 45rpx;
        letter-spacing: 0.72rpx;
        color: #333333;
        margin-bottom: 12rpx;
      }
      .goods-points {
        color: #1e9cff;
        .points-item:nth-child(1) {
          font-size: 40rpx;
          font-weight: bold;
          margin-right: 4rpx;
        }
        .points-item:nth-child(2) {
          font-size: 24rpx;
          line-height: 29rpx;
        }
        .points-item:nth-child(3) {
          border: 2rpx solid #1e9cff;
          color: #1e9cff;
          border-radius: 8rpx;
          padding: 4rpx 8rpx;
          line-height: 32rpx;
          margin-left: 8rpx;
          background: rgba(30, 156, 255, 0.05);
        }
      }
      .storage {
        margin-top: 40rpx;
        font-size: 26rpx;
        line-height: 40rpx;
        color: #999999;
      }
    }
  }
  .line {
    height: 1rpx;
    width: 100%;
    background: #eeeeee;
    margin: 24rpx 0;
  }
  .number-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    &-text {
      font-size: 28rpx;
      font-weight: bold;
      line-height: 42rpx;
      color: #333333;
    }
    .number {
      display: flex;
      align-items: center;
      .limit {
        margin-right: 16rpx;
        font-size: 24rpx;
        line-height: 32rpx;
        color: #ff4935;
      }
    }
  }
}
</style>
