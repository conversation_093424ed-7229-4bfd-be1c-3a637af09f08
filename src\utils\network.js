import { GetToken } from '@/utils/bridge/index.js';
/**
 * 旧的功能模块由插件将原生微信小程序代码转化过来
 * 因此接口封装和调用方法不做过多改动
 */
import { debounce } from '@/utils/util.js';
import { base } from './base.js';
import store from '@/store/index';

const toLogin = debounce(() => {
  uni.navigateTo({
    url: '/pages/login/login',
  });
}, 500);
function request(url, params, success, fail) {
  this.requestLoading(base + url, params, '', types, success, fail);
}
function requestLoading(url, params, message, types, success, fail) {
  if (message == '') {
  } else {
    uni.showLoading({
      title: message,
    });
  }
  let token = uni.getStorageSync('token');
  uni.request({
    url: url,
    data: params,
    header: {
      'content-type': 'application/json',
      // #ifdef MP-WEIXIN
      minitProgramToken: token,
      // #endif
      // #ifdef H5
      Authorization: token,
      // #endif
    },
    method: types,
    success: function (res) {
      // console.log(res, '方法回参、')
      uni.hideLoading();
      if (res.data.ret == 200 || res.statusCode == 200) {
        success(res);
      } else {
      }
    },
    fail: function (err) {
      uni.hideLoading();
    },
    complete: function (res) {
      if (res.statusCode == 401) {
        // 登录过期，清除登录信息
        uni.removeStorageSync('token');
        store.dispatch('login/setUserInfo', null);
      }
    },
  });
}
function requestForm(url, params, message, types, success, fail) {
  if (message == '') {
  } else {
    uni.showLoading({
      title: message,
    });
  }
  let token = uni.getStorageSync('token');
  uni.request({
    url: url,
    data: params,
    header: {
      'content-type': 'application/x-www-form-urlencoded',
      minitProgramToken: token,
    },
    method: types,
    success: function (res) {
      uni.hideLoading();
      if (res.data.ret == 200 || res.statusCode == 200) {
        success(res);
      } else {
        fail();
      }
    },
    fail: function (err) {
      uni.hideLoading();
      fail();
    },
    complete: function (res) {
      if (res.statusCode == 401) {
        // 登录过期，清除登录信息
        uni.removeStorageSync('token');

        store.dispatch('login/setUserInfo', null);
        // toLogin();
        // uni.miniProgram.postMessage({ data: { type: '401' } });
        // uni.checkSession({
        //   success: function () {
        //     uni.login({
        //       success: (res) => {
        //         if (res.code) {
        //           //token刷新
        //           uni.request({
        //             url: baseUrl + '/wx/v0.1/swapSInfo',
        //             data: {
        //               code: res.code,
        //               // orgCode: '0000001'
        //             },
        //             method: 'GET',
        //             header: {
        //               'content-type': 'application/json',
        //             },
        //             success: (res) => {
        //               uni.setStorage({
        //                 key: 'token',
        //                 data: res.data.token,
        //               });
        //               if (res.data.mobile == null || res.data.mobile == '' || res.data.mobile == undefined) {
        //                 uni.navigateTo({
        //                   url: '../login/login',
        //                 });
        //               }
        //             },
        //             fail: (err) => {
        //               // uni.redirectTo({
        //               //   url: '../login/login'
        //               // })
        //             },
        //           });
        //         } else {
        //         }
        //       },
        //     });
        //   },
        //   fail: function () {
        //     //登录态过期
        //   },
        // });
      }
    },
  });
}

/**
 * 在此基础上开发的新功能模块使用下面的接口封装方法
 * 主要包括请求和返回拦截器
 * 请求拦截器则调用了校验登陆token过期的方法
 */
import { BASE_URL, ENVR } from '@/config/global';
import axios from 'axios';

const customAdapter = (config) => {
  return new Promise((resolve, reject) => {
    const { baseURL, url, headers, data, params } = config;
    // 判断是否是完整路径，不是则拼接基地址
    const requestUrl = url.indexOf('http') > -1 ? url : `${baseURL || ''}${url}`;
    // 组装uni接口请求参数
    const uniCofig = {
      ...config,
      url: requestUrl,
      header: {
        ...config.headers,
      },
    };
    // uni接口中添加data参数，从config的data或params获取，并且接口传参必须是json格式
    if (data || params) {
      try {
        uniCofig.data = JSON.parse(data || params);
      } catch (e) {
        uniCofig.data = data || params;
      }
    }
    uni.request({
      ...uniCofig,
      success(res) {
        resolve({
          ...res,
          status: res.ret,
          statusText: res.msg,
          config,
        });
      },
      fail(err) {
        reject({
          ...err,
          status: err.ret,
          statusText: err,
          msg: err.msg,
          config,
        });
      },
    });
  });
};
const instance = axios.create({
  baseURL: BASE_URL,
  timeout: 300000,
  adapter: customAdapter,
});

// 请求拦截器
instance.interceptors.request.use(
  async (config) => {
    const token = uni.getStorageSync('token');
    // @ts-ignore
    config.headers = {
      ...(ENVR == 'wx' ? { minitProgramToken: token || '' } : { Authorization: token || '' }),
      ...config.headers,
    };
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 返回拦截器
instance.interceptors.response.use(
  // @ts-ignore
  (response) => {
    console.log(response, 'response');

    const { ret, msg } = response.data;
    const originalRequest = response.config;
    const appName = uni.getStorageSync('app') || '';
    if (
      appName == 'i宁德' &&
      (response.statusCode === 401 || response.statusCode === 500) &&
      !originalRequest?._retry
    ) {
      originalRequest._retry = true;
      setTimeout(() => {
        return instance(originalRequest);
      }, 500);
    }
    if (response.statusCode === 401) {
      // 401是登录过期，小程序和h5做不同方式处理
      // 小程序清除登录信息
      // #ifdef MP
      // uni.removeStorageSync('token');
      // store.dispatch('login/setUserInfo', null);
      // setUserInfo(null);
      // #endif

      // H5 桥接器跳转登录页
      // #ifdef H5
      if (uni.getStorageSync('app') != 'i宁德') {
        GetToken();
      }
      // #endif
      return Promise.resolve([
        {
          errorType: 'businessError',
          ...response.data,
        },
        undefined,
      ]);
    }
    if (ret !== 200) {
      uni.showToast({
        title: msg || '系统错误',
        icon: 'none',
      });
      return Promise.resolve([
        {
          errorType: 'businessError',
          ...response.data,
        },
        undefined,
      ]);
    }
    return Promise.resolve([undefined, { ret: response.statusCode, ...response.data }]);
  },
  (error) => {
    return Promise.resolve([
      {
        errorType: 'businessError',
        ...error,
      },
      undefined,
    ]);
  }
);

module.exports = {
  request: request,
  requestLoading: requestLoading,
  requestForm: requestForm,
  instance: instance,
};
