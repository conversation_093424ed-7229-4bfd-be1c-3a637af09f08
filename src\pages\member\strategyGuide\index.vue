<template>
  <div class="wrap">
    <!-- #ifdef MP-WEIXIN -->
    <view
      class="banner"
      :style="'padding-top:' + s_top + 'rpx;height:' + s_height + 'rpx;line-height:' + s_height + 'rpx'"
    >
      <image class="banner-img" @click="goBack" mode="widthFix" :src="`${IMG_PATH}back.png`"></image>
      <view class="banner-title">省钱攻略</view>
    </view>
    <!-- #endif -->
    <div class="content">
      <div class="box">
        <div class="title-bg">
          <image class="title-bg" :src="`${IMG_PATH}member/save-money-text.png`" mode="scaleToFill" />
        </div>
        <div class="text">根据{{ year }}年用户平均充电单价和频次 升级新版PLUS会员后</div>
        <div class="money-text">
          最高月省约 <span>{{ info.maxAmt || 0 }}</span> 元
        </div>
        <div class="table-wrap">
          <div class="table-box">
            <div class="th-wrap">
              <div class="th-item">充电度数(度)</div>
              <div class="th-item">充电频次(次)</div>
              <div class="th-item">用户可省(元)</div>
            </div>
            <div class="th-wrap" v-for="(item, index) in tableData" :key="index">
              <div class="th-item num">{{ item.chargeDegree || '-' }}</div>
              <div class="th-item num">{{ item.chargeFrequency || '-' }}</div>
              <div class="th-item num">{{ item.saveMoney || '-' }}</div>
            </div>
          </div>
        </div>

        <image class="tip-bg" :src="`${IMG_PATH}member/tip-text.png`" mode="widthFix" />
        <div class="tip-text-wrap">
          <!-- <div class="title">
            以月充电量{{ info.vipCharge || 0 }}度为例，1个月充电{{
              info.chargeFrequency || 0
            }}次，每次30度。按照服务费原价{{ info.serviceAmt || 0 }}元，服务费{{ info.serviceAmt || 0 }}元，会员价{{
              info.vipServiceAmt || 0
            }}元计算。具体省钱金额根据城市、电站、时段等因素酌情增减 <br />1000公里：省钱金额={{
              info.vipDisAmt || 0
            }}+{{ info.disAmt || 0 }}+{{ info.cashAmt || 0 }}-{{ info.monthVipPrice || 0 }}={{ info.totalNum || 0 }}元
          </div> -->
          <div class="title">
            当前会员版本月限定优惠次数{{ info.limitNum || 0 }}次，会员场站单次充电最大额度{{
              info.limitAmt || 0
            }}元。具体省钱金额根据电站、时段、电量等因素酌情增减。
          </div>
          <div class="tip">公式：省钱金额=折扣省钱金额+整单直减券+充电满减券-包月费用</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { saveMoneyStrategy } from '@/services/index.js';
import moment from 'moment';
export default {
  props: {},
  components: {},
  data() {
    return {
      s_top: 0,
      s_height: 0,
      tableData: [],
      info: {
        chargeFrequency: '',
        maxAmt: '',
        monthVipPrice: '',
        vipCharge: '',
      },
      year: moment().format('yyyy'),
      memberInfo: {},
      memberType: 0,
    };
  },
  onLoad(options) {
    if (options?.type) {
      this.memberType = options?.type;
    }
  },
  onShow() {
    // #ifdef MP
    this.initTopImg();
    // #endif
  },
  mounted() {
    this.saveMoneyStrategy();
    this.getMemberInfo();
  },
  methods: {
    goBack() {
      // #ifdef H5
      this.$router.go(-1);
      // #endif
      // #ifdef MP-WEIXIN
      uni.navigateBack({
        delta: -1,
      });
      // #endif
    },
    initTopImg() {
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      this.setData({
        s_top: menuButtonInfo?.top * 2 + 20,
        s_height: menuButtonInfo?.height * 2,
      });
    },

    // 获取省钱攻略
    async saveMoneyStrategy() {
      const [, res] = await saveMoneyStrategy({
        vipLevel: this.memberType == 0 ? '5' : '6',
      });
      if (res) {
        this.tableData = res.saveMoneyStrategy.slice(0, 5);
        const { vipDisAmt, disAmt, cashAmt, monthVipPrice } = res;
        const vipDiscount = parseFloat(vipDisAmt) || 0;
        const discount = parseFloat(disAmt) || 0;
        const cashAmount = parseFloat(cashAmt) || 0;
        const monthlyVipPrice = parseFloat(monthVipPrice) || 0;
        // 计算结果
        const result = vipDiscount + discount + cashAmount - monthlyVipPrice;
        // 保留两位小数并转换为数字
        const formattedResult = parseFloat(result.toFixed(2));
        this.info = {
          ...res,
          totalNum: formattedResult,
        };
      }
    },
    // 获取会员信息
    async getMemberInfo() {
      const params = {
        plusEquity: '1',
      };
      const [err, res] = await Api.getMemberInfo(params);
      if (res) {
        this.memberInfo = res;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.wrap {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(166deg, #504a51 -2%, #1e1f28 30%, #1e1f28 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  .banner {
    padding: 20rpx 20rpx 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
    z-index: 2;
    box-sizing: border-box;
    color: #fff;
    .banner-title {
      font-family: 'PingFang SC';
      font-size: 36rpx;
      font-style: normal;
      font-weight: 500;
      flex: 1;
      text-indent: 4rpx;
      color: #000000;
      font-weight: 700;
      text-align: left;
      color: #fff;
    }
    .banner-img {
      width: 48rpx;
      height: 48rpx;
      margin-right: 20rpx;
    }
  }
  .content {
    flex: 1;
    padding: 50rpx 32rpx 32rpx 32rpx;
    width: 100%;
    .box {
      width: 702rpx;
      border-radius: 24px; /* 圆角 */
      box-sizing: border-box;
      position: relative;
      margin: 0 auto;
      overflow: hidden;
      background: #282934;
      padding: 40rpx 32rpx 32rpx 32rpx;
      display: flex;
      align-items: center;
      flex-direction: column;
      &::before {
        content: '';
        position: absolute; /* 绝对定位 */
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border: 2px solid;
        border-image: linear-gradient(
            270deg,
            rgba(241, 209, 190, 0) 0%,
            rgba(241, 209, 190, 0.16) 30%,
            #f0cebc 48%,
            rgba(235, 186, 160, 0.2) 70%,
            rgba(235, 186, 160, 0) 100%
          )
          3;
      }
      .title-bg {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15rpx;
        margin-top: 10rpx;
        image {
          width: 301rpx;
          height: 43rpx;
        }
      }
      .text {
        width: 70%;
        font-family: PingFang SC;
        font-size: 28rpx;
        font-weight: 500;
        line-height: 42rpx;
        text-align: center;
        margin-bottom: 20rpx;
        background: linear-gradient(278deg, #f09e78 6%, #f2d1be 99%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
      .money-text {
        width: 90%;
        text-align: center;
        white-space: wrap;
        font-size: 28rpx;
        text-align: center;
        color: #ffffff;
        margin-bottom: 30rpx;
        span {
          font-size: 68rpx;
          font-weight: bold;
          background: linear-gradient(311deg, #f09e78 -16%, #f2d1be 79%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-fill-color: transparent;
        }
      }

      .table-wrap {
        border-radius: 24rpx;
        opacity: 1;
        background: #363749;
        box-sizing: border-box;
        width: 651rpx;
        padding: 40rpx 27rpx;
        margin: 0 auto;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 16rpx;
        grid-gap: 16rpx;
        justify-content: space-around;
        margin-bottom: 10rpx;
        position: relative;
        overflow: hidden;
        &::before {
          content: '';
          position: absolute; /* 绝对定位 */
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border: 2px solid;
          border-image: linear-gradient(
              270deg,
              rgba(241, 209, 190, 0) 0%,
              rgba(241, 209, 190, 0.16) 30%,
              #f0cebc 48%,
              rgba(235, 186, 160, 0.2) 70%,
              rgba(235, 186, 160, 0) 100%
            )
            3;
        }
        .table-box {
          border: 1px solid #e8ac8d;
          border-radius: 24rpx;
          width: 100%;
          .th-wrap {
            width: 100%;
            box-sizing: border-box;
            display: flex;
            border-bottom: 1px solid #e8ac8d;
            .th-item {
              flex: 1;
              max-width: 33%;
              display: flex;
              justify-content: center;
              align-items: center;
              font-size: 28rpx;
              color: #e8ac8d;
              padding: 18rpx 10rpx;
              border-right: 1px solid #e8ac8d;
              overflow: hidden;
              width: 33%;
              &:last-child {
                border-right: none;
              }
              &.num {
                color: #fff;
              }
            }
            &:last-child {
              border-bottom: none;
            }
          }
        }
      }

      .tip-bg {
        width: 100%;
        width: 659rpx;
        height: 43rpx;
        margin: 20rpx 0;
      }
      .tip-text-wrap {
        border-radius: 24rpx;
        opacity: 1;
        background: #363749;
        padding: 40rpx 27rpx;
        margin-bottom: 20rpx;
        .title {
          white-space: wrap;
          font-size: 24rpx;
          font-weight: 500;
          text-align: center;
          color: #ffffff;
          margin-bottom: 15rpx;
          line-height: 30rpx;
        }
        .tip {
          font-size: 20rpx;
          text-align: center;
          color: rgba(255, 255, 255, 0.81);
        }
      }
      .text-tip {
        font-size: 24rpx;
        color: #ffffff;
        line-height: 30rpx;
      }
    }
  }
}
</style>
