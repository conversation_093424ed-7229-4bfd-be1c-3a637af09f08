import CryptoJS from '../crypto';

const initKey = '+7+hkq4l97VMgGHTufKDEHzfH8FzQ0aw';

class TripleDES {
  readKey(keyStr) {
    return CryptoJS.enc.Base64.parse(keyStr);
  }

  /**
   * 加密
   */
  encrypt(message) {
    let messageJson = message;
    if (typeof message != 'string') {
      try {
        messageJson = JSON.stringify(message);
      } catch (error) {}
    }
    const messageFormat = CryptoJS.enc.Utf8.parse(messageJson);
    const keyHex = this.readKey(initKey);
    const encrypted = CryptoJS.TripleDES.encrypt(messageFormat, keyHex, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    });

    return encrypted.toString();
  }

  /**
   * 解密
   */
  decrypt(message) {
    let newMessage = message.replace(/[\r\n|\r|\n|\s]/g, '');
    const keyHex = this.readKey(initKey);
    const encrypted = CryptoJS.TripleDES.decrypt(newMessage, keyHex, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    });
    const encryptEnd = encrypted.toString(CryptoJS.enc.Utf8);

    return JSON.parse(encryptEnd);
  }
}
export default new TripleDES();
