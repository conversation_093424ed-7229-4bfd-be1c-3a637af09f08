<template>
  <div class="page-wrap">
    <div class="card">
      <div class="text-area">
        <!-- 操作说明: -->
        <!-- #ifdef MP-WEIXIN -->
        <rich-text :nodes="info || testHtml" selectable="true"></rich-text>
        <!-- #endif -->

        <!-- #ifdef H5 -->
        <div v-html="info"></div>
        <!-- #endif -->
      </div>
      <!-- #ifdef MP-WEIXIN -->
      <button open-type="share" ref="share" class="btn">分享好友</button>

      <!-- #endif -->
      <!-- #ifdef H5 -->
      <button class="btn" @click="share">分享好友</button>
      <!-- #endif -->
    </div>
    <div class="banner"><image :src="img || `${IMG_PATH}share-banner.png`" mode="widthFix" /></div>
    <div class="rule">
      <div class="title">
        <div class="line"></div>
        活动规则
        <div class="line2"></div>
      </div>
      <div class="rule-text-area">
        <!-- #ifdef MP-WEIXIN -->
        <rich-text :nodes="ruleInfo || testHtml" selectable="true"></rich-text>
        <!-- #endif -->
        <!-- #ifdef H5 -->
        <div v-html="ruleInfo"></div>
        <!-- #endif -->
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { queryActInProgress } from '@/services/index';
import { addDomainToImgSrc } from '@/utils/index';
import { BASE_URL } from '@/config/global';
import { ToPay, HarmonyOsShareWxCard } from '@/utils/bridge/index.js';
export default {
  props: {},
  data() {
    return {
      info: '',
      ruleInfo: '',
      img: '',
      shareInfo: null,
      testHtml:
        '<h1>测试标题</h1><p>这是一个测试段落</p><ol><li>活动时间:2024年12月1日至2024年12月31日</li><li>活动内容:每成功邀请一位新好友注册，您将获得价值12元的充电优惠券一张</li></ol>',
    };
  },
  computed: {
    ...mapState({
      userInfoOBJ: (state, getters) => getters['login/getUserInfo'],
    }),
    userInfo() {
      return {
        imgUrl: '',
        ...(this.userInfoOBJ ? this.userInfoOBJ : {}),
      };
    },
  },
  onLoad(options) {
    this.queryActInProgress();
    const { mobile, custId } = options;
    if (mobile && custId) {
      this.shareInfo = { mobile, custId };
    }
  },
  methods: {
    // 预处理HTML内容，清理格式问题
    preprocessHtml(html) {
      if (!html) return '';
      // 移除换行符号 ↵
      let cleanHtml = html.replace(/↵/g, '');
      // 清理多余的空白字符
      cleanHtml = cleanHtml.replace(/\s+/g, ' ');
      // 确保标签正确闭合
      cleanHtml = cleanHtml.trim();
      console.log('预处理后的HTML:', cleanHtml);
      return cleanHtml;
    },

    async queryActInProgress() {
      const [, res] = await queryActInProgress({
        actType: '06',
        actState: '2',
      });
      console.log(res, '富文本');
      if (res) {
        // 预处理HTML内容
        const processedActMark = this.preprocessHtml(res.actMark);
        const processedActDetailMarks = this.preprocessHtml(res.actDetailMarks);

        this.info = await addDomainToImgSrc(processedActMark, BASE_URL);
        this.ruleInfo = await addDomainToImgSrc(processedActDetailMarks, BASE_URL);
        this.img = res.fileId ? `${process.env.VUE_APP_BASE_HOST}/pub/api/v0.1/attachs/${res.fileId}` : '';

        console.log('最终设置的info:', this.info);
        console.log('最终设置的ruleInfo:', this.ruleInfo);
      }
    },
    share() {
      console.log(this.userInfo, 'userInfo');
      // #ifdef H5
      if (uni.getStorageSync('app') == '鸿蒙电动宁德') {
        HarmonyOsShareWxCard({
          mobile: this.userInfo.mobile + '',
          custId: this.userInfo.custId + '',
        });
      }
      // const queryString = encodeURIComponent(
      //   `type=share&mobile=${this.userInfo.mobile}&custId=${this.userInfo.custId}`
      // );
      // window.location.href = `weixin://dl/business/?appid=wx602bfd6b5414b967&path=pages/home/<USER>
      // #endif
    },
  },
  onShareAppMessage: function () {
    return {
      title: '电动宁德,精彩缤纷', // 分享的标题
      path:
        '/pages/setting/register/index' +
        `?mobile=${this.shareInfo ? this.shareInfo.mobile : this.userInfo.mobile}&custId=${
          this.shareInfo ? this.shareInfo.custId : this.userInfo.custId
        }`, // 分享的页面路径
      imageUrl: `${this.IMG_PATH}share-logo.png`, // 分享的图片路径（可选）
    };
  },
};
</script>

<style scoped lang="scss">
.page-wrap {
  width: 100%;
  min-height: 100vh;
  background: #f0f5ff;
  background-size: 100% auto;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  padding: 40rpx 24rpx;
  .card {
    background-color: #fff;
    padding: 20rpx;
    border-radius: 20rpx;
    .text-area {
      width: 100%;
      padding: 10rpx 20rpx;
      color: #999999;
      // line-height: 32rpx;
      font-size: 24rpx;
      letter-spacing: 0.5rpx;
      line-height: 1;
      color: #000;
      overflow-x: hidden;
      box-sizing: border-box;
      p {
        padding: 0;
        box-sizing: border-box;
        display: grid;
        place-content: center;
        image img {
          max-width: 100vw !important;
          object-fit: contain !important;
        }
      }

      // 富文本样式优化
      :deep(.mp-html) {
        color: #000;
        line-height: 1.6;

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          margin: 10rpx 0;
          font-weight: bold;
        }

        p {
          margin: 10rpx 0;
          line-height: 1.6;
        }

        ol,
        ul {
          margin: 10rpx 0;
          padding-left: 40rpx;
        }

        li {
          margin: 8rpx 0;
          line-height: 1.6;
        }

        img {
          max-width: 100%;
          height: auto;
          vertical-align: middle;
        }
      }
    }
    .btn {
      padding: 5rpx 26rpx;
      text-align: center;
      background: #1e9cff;
      width: 300rpx;
      border-radius: 20rpx;
      color: #fff;
      margin: 20rpx auto;
      margin-bottom: 0;
    }
  }
  .banner {
    margin: 20rpx auto;
    width: 686rpx;
    image {
      width: 100%;
    }
  }
  .rule {
    .title {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      font-weight: bold;
      line-height: 43.2px;
      text-align: center;
      color: #333333;
      gap: 20rpx;
      grid-gap: 20rpx;
      .line {
        width: 92rpx;
        height: 3rpx;
        background: linear-gradient(270deg, #202020 0%, rgba(23, 23, 23, 0) 100%);
      }
      .line2 {
        width: 92rpx;
        height: 3rpx;
        background: linear-gradient(90deg, #202020 0%, rgba(23, 23, 23, 0) 100%);
      }
    }
    .rule-text-area {
      padding: 24rpx;
      color: #000;
      view {
        color: #000;
      }
      rich-text {
        color: #000;
      }
      // 富文本样式优化
      :deep(.mp-html) {
        color: #000;
        line-height: 1.6;

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          margin: 10rpx 0;
          font-weight: bold;
        }

        p {
          margin: 10rpx 0;
          line-height: 1.6;
        }

        ol,
        ul {
          margin: 10rpx 0;
          padding-left: 40rpx;
        }

        li {
          margin: 8rpx 0;
          line-height: 1.6;
        }

        img {
          max-width: 100%;
          height: auto;
          vertical-align: middle;
        }
      }
    }
  }
}
</style>
