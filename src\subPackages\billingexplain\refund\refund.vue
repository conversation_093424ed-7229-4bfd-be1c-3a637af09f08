<template>
  <view>
    <view v-if="TYPE == 1" class="application">
      <view class="application_head application_height">
        <image :src="`${IMG_PATH}price2x.png`"></image>
        <view class="head_price">
          <text class="head_count">可退款金额：</text>
          <text>¥</text>
          {{ onlineCanReturnAmt }}
        </view>
      </view>
      <view class="application_overflow">
        <view class="application_record" v-for="(item, index) in queryList" :key="index">
          <image
            v-if="
              item.transactionChannel == '1302' || item.transactionChannel == '01' || item.transactionChannel == '10'
            "
            :src="`${IMG_PATH}zfb2x.png`"
          ></image>

          <image
            v-if="
              item.transactionChannel == '07' || item.transactionChannel == '03' || item.transactionChannel == '1303'
            "
            :src="`${IMG_PATH}wx2x.png`"
          ></image>

          <image
            v-if="item.transactionChannel == '13' || item.transactionChannel == '1301'"
            :src="`${IMG_PATH}ysf.png`"
          ></image>

          <view class="record_meeage">
            <view class="record_title">
              <view class="title">充值{{ item.rechargeAmt || 0 }}元</view>
              <view class="price">{{ item.refundableAmt || 0 }}元</view>
            </view>
            <view class="record_price">
              <view class="time">{{ item.createTime }}</view>
              <view class="type">可退款</view>
            </view>
          </view>
        </view>
      </view>
      <view class="application_flex application_position">
        <view class="flex_title">
          <text @tap="handClickhistory">退款记录</text>
          <text class="flex_title_line">|</text>
          <text @tap="handClickInstructions">退款说明</text>
        </view>
        <view v-if="onlineCanReturnAmt == '0.00' || Number(onlineCanReturnAmt) == 0" class="flex_btn flex_btn_not"
          >提交申请</view
        >
        <view v-else @tap="handClickConfirm" class="flex_btn">提交申请</view>
      </view>
    </view>
    <view v-if="TYPE == 2" class="application">
      <view class="application_head application_hight">
        <image :src="`${IMG_PATH}blue2x.png`"></image>
        <view class="head_name">退款申请成功</view>
        <view class="head_msg">预计0-3个工作日退还至原支付账户，请根据各银 行的到账时间进行查询。</view>
      </view>
      <view @tap="handClickBack" class="application_flex">
        <view class="flex_btn">完成</view>
      </view>
    </view>
  </view>
</template>

<script>
import { getBalanceCan, getUserAgreement, submitRefund } from '@/services/index';
export default {
  data() {
    return {
      queryList: [],
      //记录
      onlineCanReturnAmt: '0.00',
      //可退金额
      htmlUrl: null,
      TYPE: 1,
      activeStatus: true,
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */ async onLoad() {
    const _self = this;
    const [, resBal] = await getBalanceCan();
    if (resBal) {
      console.log(resBal, '退款记录', resBal);
      if (resBal && resBal.data) {
        _self.setData({
          queryList: resBal.data || [],
          onlineCanReturnAmt: resBal.onlineCanReturnAmt || '0.00',
        });
      }
    }
    // network.requestLoading(
    //   baseUrl + '/wx/v0.1/balance-can-return-record',
    //   {},
    //   '正在加载数据',
    //   'GET',
    //   (res) => {
    //     console.log(res, '退款记录', res);
    //     if (res && res.data && res.data.data) {
    //       _self.setData({
    //         queryList: res.data.data || [],
    //         onlineCanReturnAmt: res.data.onlineCanReturnAmt || '0.00',
    //       });
    //     }
    //   },
    //   () => {}
    // );
    const [, res] = await getUserAgreement({ code: '0411' });
    if (res) {
      console.log(res, '退款说明', res);
      var htmlUrl = [];
      for (var i = 0; i < res.infoList.length; i++) {
        if (res.infoList[i].title.indexOf('退款说明') >= 0) {
          htmlUrl.push(res.infoList[i]);
        }
      }
      console.log(htmlUrl, 'htmlUrl');
      if (htmlUrl.length > 0) {
        _self.setData({
          htmlUrl: htmlUrl[0].contentUrl,
        });
      }
    }
    // network.requestLoading(
    //   baseUrl + '/wx/v0.1/infos/0411',
    //   {},
    //   '正在加载数据',
    //   'GET',
    //   (res) => {
    //     console.log(res, '退款说明', res);
    //     var htmlUrl = [];
    //     for (var i = 0; i < res.data.infoList.length; i++) {
    //       if (res.data.infoList[i].title.indexOf('退款说明') >= 0) {
    //         htmlUrl.push(res.data.infoList[i]);
    //       }
    //     }
    //     console.log(htmlUrl, 'htmlUrl');
    //     if (htmlUrl.length > 0) {
    //       _self.setData({
    //         htmlUrl: htmlUrl[0].contentUrl,
    //       });
    //     }
    //   },
    //   (err) => {
    //     console.log('退款说明失败', err);
    //   }
    // );
  },
  methods: {
    handClickConfirm() {
      const _self = this;
      if (_self.activeStatus == false) {
        return false;
      }
      uni.showModal({
        title: '是否确定提交退款申请',
        content: '提交后退款金额将会原路返回到您的支付账户,您可以在退款记录查看退款信息。',
        cancelText: '否',
        confirmText: '是',
        success: (res) => {
          if (res.confirm) {
            console.log('用户点击确定');
            _self.handClickSubmit();
          } else if (res.cancel) {
            console.log('用户点击取消');
          }
        },
      });
    },

    async handClickSubmit() {
      const _self = this;
      _self.setData({
        activeStatus: false,
      });

      const [err, res] = await submitRefund({ appChannel: '07' });
      if (err) {
        console.log('提交失败', err);
        setTimeout(() => {
          _self.setData({
            activeStatus: true,
          });
        }, 1000);
        return;
      }
      if (res) {
        console.log(res, '提交');
        if (res.ret == 200) {
          _self.setData({
            TYPE: 2,
          });
          uni.showToast({
            title: res.msg || '操作成功',
            icon: 'success',
            duration: 2000,
          });
        } else {
          _self.setData({
            TYPE: 1,
          });
          uni.showToast({
            title: res.msg || '操作异常',
            icon: 'none',
            duration: 2000,
          });
        }
        setTimeout(() => {
          _self.setData({
            activeStatus: true,
          });
        }, 1000);
      }
      // network.requestLoading(
      //   baseUrl + '/wx/v0.1/online-refund',
      //   {
      //     appChannel: '07',
      //   },
      //   '正在加载数据',
      //   'POST',
      //   (res) => {
      //     console.log(res, '提交');
      //     if (res.data.ret == 200) {
      //       _self.setData({
      //         TYPE: 2,
      //       });
      //       uni.showToast({
      //         title: res.data.msg || '操作成功',
      //         icon: 'success',
      //         duration: 2000,
      //       });
      //     } else {
      //       _self.setData({
      //         TYPE: 1,
      //       });
      //       uni.showToast({
      //         title: res.data.msg || '操作异常',
      //         icon: 'none',
      //         duration: 2000,
      //       });
      //     }
      //     setTimeout(() => {
      //       _self.setData({
      //         activeStatus: true,
      //       });
      //     }, 1000);
      //   },
      //   (err) => {
      //     console.log('提交失败', err);
      //     setTimeout(() => {
      //       _self.setData({
      //         activeStatus: true,
      //       });
      //     }, 1000);
      //   }
      // );
    },

    handClickInstructions() {
      uni.navigateTo({
        url: '/pages/webview/webview?countUrl=' + JSON.stringify(encodeURIComponent(this.htmlUrl)),
      });
    },

    handClickhistory() {
      uni.navigateTo({
        url: '/subPackages/refundhs/refundhs',
      });
    },

    handClickBack() {
      uni.navigateBack({
        delta: 1,
      });
    },
  },
};
</script>
<style>
@import './refund.css';
</style>
