<template>
  <view>
    <div class="top-search">
      <picker mode="date" :value="startTime" fields="month" @change="bindstartTimeChange" class="picker-li">
        <view class="picker-li-wrap">
          <!-- <image class="li-img" :src="`${IMG_PATH}calendar.png`"></image> -->
          <view class="picker">
            {{ startTime ? startTime : '选择日期' }}
          </view>
          <image class="li-img" :src="`${IMG_PATH}icon-up.png`"></image>
        </view>
      </picker>
    </div>
    <template v-if="msg && msg.length > 0">
      <view v-for="(item, index) in msg" :key="index" class="p10">
        <!-- <view class="month">{{ item.date }}</view> -->

        <view class="msg_view">
          <view class="msg" v-for="(item, index1) in item.data" :key="index1">
            <view class="msg_img"><image :src="item.Imgsrc"></image></view>

            <view class="msg_page">
              <view>{{ item.remark || item.msg }}</view>
              <view>{{ item.createTime }}</view>
            </view>

            <view class="msg_price">
              <text>{{ item.amt }}元</text>
            </view>
          </view>
        </view>
      </view>
    </template>
    <view v-else class="empty-box">暂无交易记录</view>
  </view>
</template>

<script>
import moment from 'moment';
import { getAccountsTradelog } from '@/services/index.js';
export default {
  data() {
    return {
      msg: [],
      startTime: '',
    };
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.startTime = moment().format('yyyy-MM');
    this.loadData();
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {},
  methods: {
    bindstartTimeChange(event) {
      const { value } = event.detail;
      this.setData({
        startTime: value,
      });
      this.loadData();
    },
    async loadData() {
      let that = this;
      const [, res] = await getAccountsTradelog({
        timeBegin: that.startTime,
        timeEnd: that.startTime,
      });
      console.log(res, 'res');
      if (res) {
        var a = res.queryList;
        var lp = [];
        for (var i = 0; i < a.length; i++) {
          if (a[i].appType == '01') {
            a[i].msg = '充值';
            a[i].Imgsrc = this.IMG_PATH + 'c2.png';
          } else if (a[i].appType == '02') {
            a[i].msg = '消费';
            a[i].Imgsrc = this.IMG_PATH + 'c2.png';
          } else if (a[i].appType == '05') {
            a[i].msg = '提现';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '06') {
            a[i].msg = '补收';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '08') {
            a[i].msg = '转账';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>'; //?
          } else if (a[i].appType == '09') {
            a[i].msg = '退款';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '13') {
            a[i].msg = '开票';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '14') {
            a[i].msg = '冲正';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '15') {
            a[i].msg = '违章';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '16') {
            a[i].msg = '车损';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '17') {
            a[i].msg = '事故赔偿';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '18') {
            a[i].msg = '物流收入';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else if (a[i].appType == '21') {
            a[i].msg = '原路退款';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          } else {
            a[i].msg = '--';
            a[i].Imgsrc = this.IMG_PATH + '<EMAIL>';
          }
          lp.push(a[i]);
        }
        console.log(lp, 'lp');
        let data = {};
        lp.forEach((item) => {
          let date = item.createTime.substr(0, 7);
          if (!data[date]) {
            data[date] = [];
          }
          data[date].push(item);
        });
        let arr = [];
        Object.keys(data).forEach((date) => {
          arr.push({
            date,
            data: data[date],
          });
        });
        // arr.reverse()
        console.log(arr);
        that.setData({
          msg: arr,
        });
      }
    },
  },
};
</script>
<style>
@import './history.css';
.p10 {
  padding: 10rpx;
}
.empty-box {
  height: 100rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.top-search {
  width: 100%;
  padding: 10rpx 20rpx;
}
.picker-li {
  opacity: 0.7;
  background: #f5f5f5;
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 30rpx;
  width: 50%;
}
.picker-li-wrap {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.li-img {
  width: 30rpx;
  height: 30rpx;
  margin-left: 10rpx;
}
.picker-mid {
  opacity: 0.7;
  padding: 0 40rpx;
  line-height: 70rpx;
}
</style>
