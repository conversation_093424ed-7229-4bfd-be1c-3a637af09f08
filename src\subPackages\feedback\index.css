.mine-page {
  position: relative;
  z-index: 1;
  min-height: 100vh;
}

.form-item {
  background: #fff;
  width: 702rpx;
  height: 96rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  box-sizing: border-box;
  margin: 0 auto 16rpx;
}

.form-item.noheight {
  height: auto;
}

.form-item .label text:nth-child(2) {
  font-size: 32rpx;
  color: #333;
}

.form-item .label text:nth-child(1) {
  color: var(--theme);
}

.form-item .label.grey {
  color: #999;
  font-size: 28rpx;
}

.form-item .value {
  flex: 1;
  text-align: right;
  color: #999999;
  font-size: 28rpx;
}

.form-item .right_arrow {
  width: 48rpx;
  height: 48rpx;
}

.form-item .placeholder {
  color: #999999;
  font-size: 28rpx;
}

.form-item textarea {
  background: rgba(0, 0, 0, 0.04);
  width: 100%;
  padding: 24rpx;
}

.imgUpload {
  margin-top: 24rpx;
}

.imgUpload view {
  width: 160rpx;
  height: 160rpx;
  position: relative;
  margin-right: 24rpx;
  margin-bottom: 24rpx;
}
.imgUpload image {
  width: 160rpx;
  height: 160rpx;
}
.imgUpload view text {
  position: absolute;
  top: -18rpx;
  right: -18rpx;
  width: 36rpx;
  height: 36rpx;
  color: #fff;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #999;
  border-radius: 50%;
}

.submit {
  margin: 40rpx 24rpx;
  display: block;
}


.page-hed {
    box-sizing: border-box;
    background: #fff;
    padding: 24rpx;
    color: #666;
    position: fixed;
    top: 0;
    z-index: 9;
    width: 100%;
  }
  .page-hed-ul {
    margin-top: 10rpx;
    display: flex;
    align-items: center;
    border-radius: 16rpx;
    background: #f5f5f5;
    box-sizing: border-box;
    width: 100%;
    padding: 8rpx;
  }
  .page-hed-li {
    flex: 1;
    font-family: PingFang SC;
    font-size: 28rpx;
    font-weight: normal;
    text-align: center;
    color: #333333;
    line-height: 64rpx;
  }
  .hed-li-checked {
    background: #ffffff;
    color: #1E9CFF;
  }

  .list-found .station-list {
    width: 100%;
    /* height: 264rpx; */
    background: #fff;
    margin-bottom: 20rpx;
    box-sizing: border-box;
    padding: 20rpx 30rpx;
  }
  .station-ul {
    margin: 0 24rpx;
    padding-bottom: 80rpx;
  }

  .list-found {
    position: fixed;
    top: 230rpx;
    background: #eee;
    width: 100%;
    position: absolute;
    /* top: 100rpx; */
    height: 82.5vh;
    box-sizing: border-box;
    z-index: 9;
  }
  .list-top-nav {
    width: 100%;
    font-size: 36rpx;
    display: flex;
    align-items: center;
    background: #fff;
    display: flex;
    padding-left: 30rpx;
    font-size: 36rpx;
    font-weight: 600;
    align-items: baseline;
    z-index: 1;
  }
  .list-top-nav .text {
    margin-left: 25rpx;
  }
  .search-head {
    width: 100%;
    height: 88rpx;
    background: #fff;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20rpx;
    padding-left: 0;
  }
  .search-head input {
    width: 592rpx;
    height: 66rpx;
    border-radius: 8rpx;
    border: 2rpx solid #ccc;
    font-size: 28rpx;
    padding-left: 20rpx;
    padding-right: 20rpx;
    box-sizing: border-box;
    margin-left: 30rpx;
  }
  .search-head .icon {
    line-height: 88rpx;
    font-size: 53rpx;
    color: #666;
    padding-left: 15rpx;
  }
  .search-head .search-lefy {
    padding-left: 20rpx;
    font-size: 42rpx;
    padding-right: 5rpx;
    padding-bottom: 10rpx;
  }
