<!--
@name: 积分兑换
@description: 商品详情页面
@time: 2024/7/25
-->
<template>
  <view class="goods-detail">
    <!--        商品图片、名字、价格-->
    <view class="goods-detail-header">
      <img class="goods-image" :src="getImage" />
      <view class="goods-text">
        <view class="goods-name">
          <text class="name-item">{{ detail.goodsName }}</text>
          <text class="free-shipping" v-if="detail.goodsFreight">{{
            detail.goodsFreight === '01' ? '包邮' : '到付'
          }}</text>
        </view>
        <view class="goods-price">
          <text class="price-item">{{ detail.goodsIntegral }}</text>
          <text class="price-item">积分</text>
          <text class="price-item" v-if="detail.goodsMode == '02'">+{{ detail.goodsAmt }}元</text>
        </view>
      </view>
    </view>
    <!--        商品详情信息-->
    <view class="goods-detail-content">
      <tab-select class="tag-list" :options="tabList" @change="changeTab"></tab-select>
      <view class="detail-title">
        <view class="title-line"></view>
        <text class="title-text">{{ tabTitle }}</text>
        <view class="title-line"></view>
      </view>
      <rich-text :nodes="detail.goodsDetail" v-if="tab == '1'" class="rich-text"></rich-text>
      <view style="height: 240rpx" v-if="tab == '1'"></view>
      <view v-if="tab == '2'" class="evaluate-page">
        <evaluate v-for="(item, index) in evaluateLits" :key="index" :evaluateItem="item"></evaluate>
        <view v-if="evaluateLits.length == 0">
          <AEmpty></AEmpty>
        </view>
      </view>
      <view style="height: 240rpx" v-if="tab == '2'"></view>
    </view>
    <!--        底部按钮-->
    <view class="footer">
      <BottomPannel>
        <view class="footer-button">
          <AppButton class="button-item" type="primary" @click="openDialog">立即兑换</AppButton>
        </view>
      </BottomPannel>
    </view>
    <!--        兑换弹框-->
    <uni-popup ref="popup" background-color="#F6F7F9">
      <view class="popup-content">
        <view class="title">
          <text class="title-text">确认订单</text>
          <img class="close" :src="`${IMG_PATH}close.png`" @click="closePopup" />
        </view>
        <GoodsCard
          class="goods-card"
          :show-number-box="true"
          :card-item="detail"
          @getExchangeNumber="getExchangeNumber"
        />
      </view>
      <BottomPannel>
        <view class="next-step">
          <view class="next-step-text">
            <text class="text-item">应付款：</text>
            <text class="text-item">{{ detail.goodsIntegral * exchangeNumber }}</text>
            <text class="text-item">积分</text>
            <text class="text-item" v-if="detail.goodsAmt">+</text>
            <text class="text-item" v-if="detail.goodsAmt">{{ detail.goodsAmt * exchangeNumber }}</text>
            <text class="text-item" v-if="detail.goodsAmt">元</text>
          </view>
          <AppButton
            :custom-style="{ width: '339rpx' }"
            type="primary"
            @click="jumpToRedeem"
            :disabled="exchangeNumber === 0"
          >
            下一步
          </AppButton>
        </view>
      </BottomPannel>
    </uni-popup>
  </view>
</template>

<script>
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup';
import BottomPannel from '@/components/BottomPannel/index';
import AppButton from '@/components/AppButton/index';
import { getGoodsDetail } from '@/services/index.js';
import GoodsCard from '@/pages/intergral/components/GoodsCard';
import tabSelect from './components/tabSelect';
import evaluate from './components/evaluate';
import AEmpty from '@/components/AEmpty/index';
const baseUrl = require('../../../utils/base').baseDate();

export default {
  name: 'index',
  components: {
    GoodsCard,
    BottomPannel,
    AppButton,
    GoodsCard,
    uniPopup,
    tabSelect,
    evaluate,
    AEmpty,
  },
  data() {
    return {
      goodsId: '',
      detail: {},
      exchangeNumber: 0,
      tabList: [
        {
          id: '1',
          name: '详情',
        },
        {
          id: '2',
          name: '评价',
        },
      ],
      tab: '1',
      tabTitle: '商品详情',
      evaluateLits: [],
    };
  },
  computed: {
    // 商品图片路径
    getImage() {
      const str = `${process.env.VUE_APP_BASE_HOST}/pub/api/v0.1/attachs/${this.detail.fileId}`;
      return str;
    },
  },
  onLoad(options) {
    this.goodsId = options.goodsId;
    this.getGoodsDetail();
  },
  methods: {
    // 跳转到确认订单页面
    jumpToRedeem() {
      if (this.exchangeNumber === 0) {
        return;
      }
      uni.navigateTo({
        url: `/pages/intergral/confirmOrder/index?goodsId=${this.detail.goodsId}&exchangeNumber=${this.exchangeNumber}`,
      });
    },
    // 查询商品详情
    async getGoodsDetail() {
      const params = {
        goodsId: this.goodsId,
      };
      const [err, res] = await getGoodsDetail(params);
      if (res) {
        this.detail = res.detail;
        this.evaluateLits = res.evaList;
        console.log('res.evaluateLits==', this.evaluateLits);
        this.evaluateLits.forEach((item) => {
          item.imgList = [];
          if (item.fileIds && item.fileIds.length > 0) {
            item.fileIds.forEach((it) => {
              item.imgList.push(`${process.env.VUE_APP_BASE_HOST}/pub/api/v0.1/attachs/${it}`);
            });
          }
        });
        this.detail.goodsDetail = this.detail.goodsDetail.replace(/src="/g, `src="${process.env.VUE_APP_BASE_HOST}`);
        this.detail.goodsDetail = this.detail.goodsDetail.replace(
          /<img/g,
          // `<img :style="{ height: 'auto',max-width:'100%' }"`
          "<img style='max-width:100%'"
        );
        console.log(this.detail.goodsDetail);
      }
    },
    // 打开弹框
    openDialog() {
      this.$refs.popup.open('bottom');
    },
    closePopup() {
      this.$refs.popup.close();
    },
    // 获取兑换数量
    getExchangeNumber(data) {
      this.exchangeNumber = data;
    },
    // tab切换
    changeTab(data) {
      this.tab = data.id;
      if (this.tab == '1') {
        // 详情
        this.tabTitle = '商品详情';
      } else {
        // 评价
        this.tabTitle = '评价';
      }
    },
  },
};
</script>

<style scoped lang="scss">
.goods-detail {
  height: 100%;
  &-header {
    margin-bottom: 16rpx;
    background: white;
    .goods-image {
      width: 750rpx;
      height: 750rpx;
    }
    .goods-text {
      padding: 24rpx;
      .goods-name {
        display: flex;
        justify-content: space-between;
        .name-item {
          font-size: 32rpx;
          font-weight: bold;
          line-height: 45rpx;
          color: #333333;
        }
        .free-shipping {
          border: 2rpx solid #1e9cff;
          color: #1e9cff;
          border-radius: 8rpx;
          padding: 4rpx 8rpx;
          line-height: 32rpx;
          background: rgba(30, 156, 255, 0.05);
        }
      }
      .goods-price {
        margin-top: 12rpx;
        color: #1e9cff;
        display: flex;
        align-items: center;
        .price-item:nth-child(1) {
          font-size: 40rpx;
          font-weight: bold;
          margin-right: 4rpx;
        }
        .price-item:nth-child(2) {
          font-size: 24rpx;
          line-height: 29rpx;
        }
      }
    }
  }
  &-content {
    padding: 24rpx;
    // height: calc(100% - 917rpx);
    height: 60%;
    background: white;
    margin-bottom: 24rpx;
    .tag-list {
      margin: 0rpx 0 24rpx 0;
      display: block;
    }
    .detail-title {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 24rpx;
      .title-line {
        width: 241rpx;
        height: 1rpx;
        background: #eeeeee;
      }
      .title-text {
        margin: 0 33.5rpx;
      }
    }
    .rich-text img {
      max-width: 100% !important;
      height: auto !important;
    }
    .evaluate-page {
      height: 60vh;
      overflow-y: scroll;
      background: white;
      scrollbar-width: none; /* 隐藏滚动条 */
    }
    .evaluate-page::-webkit-scrollbar {
      display: none; /* 对于Chrome, Safari和Opera */
    }
  }
  .footer {
    z-index: 1;
    .footer-button {
      display: flex;
      gap: 24rpx;
      grid-gap: 24rpx;
      .button-item {
        flex: 1;
      }
    }
  }
}
.popup-content {
  padding: 24rpx;
  .title {
    display: flex;
    justify-content: center;
    margin: 16rpx 0 24rpx 0;
    align-items: center;
    position: relative;
    .title-text {
      font-size: 32rpx;
      font-weight: bold;
      line-height: 38rpx;
      color: #333333;
    }
    .close {
      position: absolute;
      top: 0;
      right: 0;
      height: 40rpx;
      width: 40rpx;
    }
  }
  .goods-card {
    margin-bottom: 216rpx;
    display: block;
  }
}
.next-step {
  display: flex;
  justify-content: space-between;
  align-items: center;
  &-text {
    .text-item:nth-child(1) {
      font-size: 28rpx;
      line-height: 42rpx;
      color: #333333;
    }
    .text-item:nth-child(2) {
      font-size: 40rpx;
      font-weight: bold;
      color: #1e9cff;
    }
    .text-item:nth-child(3) {
      font-size: 24rpx;
      line-height: 29rpx;
      color: #1e9cff;
      margin-left: 4rpx;
    }
    .text-item:nth-child(4) {
      font-size: 24rpx;
      line-height: 29rpx;
      color: #1e9cff;
      margin-left: 4rpx;
    }
    .text-item:nth-child(5) {
      font-size: 24rpx;
      line-height: 29rpx;
      color: #1e9cff;
      margin-left: 4rpx;
    }
    .text-item:nth-child(6) {
      font-size: 24rpx;
      line-height: 29rpx;
      color: #1e9cff;
      margin-left: 4rpx;
    }
  }
}
::v-deep .uni-popup bottom {
  border-radius: 24rpx;
}
</style>
