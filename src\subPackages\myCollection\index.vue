<template>
  <view class="collection-page">
    <!-- 列表 -->
    <scroll-view scroll-y class="list-found" style="z-index: 66">
      <view class="station-scroll-frame">
        <template>
          <view v-if="list.length > 0">
            <view class="station-scroll" v-for="(item, index) in list" :key="index" @click="handClickJump(item)">
              <view class="scroll-head">
                <view class="scroll-stationName">{{ item.stationInfo.stationName || '' }}</view>
                <view class="scroll-views">
                  <text class="iconfont icondaohang" v-if="item.stationInfo.distance > 1000"
                    >{{ item.stationInfo.distance | toFix }}km</text
                  >
                  <text
                    class="iconfont icondaohang"
                    v-if="item.stationInfo.distance > 0 && item.stationInfo.distance <= 1000"
                    >{{ item.stationInfo.distance | fixed }}km</text
                  >
                </view>
              </view>
              <view class="price-sorting">
                <div class="rate-wrap">
                  <rate v-model="item.stationInfo.evaScore" class="rate-box"></rate>
                  <view class="pile-status">
                    <view class="scroll-gun-view" v-if="item.dcNums">
                      <view class="scroll-gunlist">快</view>
                      <text>{{ (item.dcFreeNums || 0) + '/' + (item.dcNums || 0) }}</text>
                    </view>
                    <view class="scroll-gun-view" v-if="item.acNums">
                      <view :class="['scroll-gunlist', 'scroll-dc']">慢</view>
                      <text> {{ (item.acFreeNums || 0) + '/' + (item.acNums || 0) }}</text>
                    </view>
                  </view>
                </div>
                <div class="money font">充电价格: {{ item.stationInfo.priceRemarkNum || 0 }}元/度起</div>
                <div class="address font"></div>
              </view>
              <view class="scroll-gun-type">
                <template v-if="item.stationInfo.tagList && Array.isArray(item.stationInfo.tagList)">
                  <div class="tag-item" v-for="(it, itIndex) in item.stationInfo.tagList" :key="itIndex">
                    {{ it.tagName }}
                  </div>
                </template>
              </view>
            </view>
            <!-- <view @click="handClickLunch()" class="scroll-end">
            点击查看更多电站
          </view> -->
          </view>
          <view v-else class="default-nothing">
            <image class="default-graph" :src="defaultGraph"></image>
            <view>暂无数据</view>
          </view>
        </template>
      </view>
    </scroll-view>
  </view>
</template>
<script>
import network from '@/utils/network.js';
import { mapGetters } from 'vuex';
import { baseDate } from '@/utils/base.js';
import rate from '@/components/rate/index.vue';
import { getCollectChargingStation } from '@/services/index.js';
import { getLocation } from '@/utils/index.js';
import { HarmonyOsGetLocal } from '@/utils/bridge/index.js';
export default {
  components: { rate },
  data() {
    return {
      title: '我的收藏',
      list: [],
      longitudeWX: '',
      latitudeWX: '',
      defaultGraph: '',
      longitude: '',
      latitude: '',
    };
  },
  computed: {},
  async onShow() {
    const app = uni.getStorageSync('app');
    console.log(app, 'app');
    // #ifdef H5
    if (app == '鸿蒙电动宁德') {
      const localInfo = await HarmonyOsGetLocal();
      if (localInfo) {
        const local = JSON.parse(JSON.stringify(localInfo));
        this.longitude = local.longitude;
        this.latitude = local.latitude;
        this.initData();
      }
    } else {
      const localInfo = await getLocation();
      if (localInfo) {
        this.longitude = localInfo.longitude;
        this.latitude = localInfo.latitude;
        this.initData();
      }
    }
    // #endif
    // #ifdef MP
    const localInfo = await getLocation();
    if (localInfo) {
      this.longitude = localInfo.longitude;
      this.latitude = localInfo.latitude;
      this.initData();
    }
    // #endif
  },
  onLoad() {},
  methods: {
    async initData() {
      const that = this;
      const params = {
        prefType: '01',
        positionLon: this.longitude,
        positionLat: this.latitude,
      };
      console.log(params, 'params');
      const [, res] = await getCollectChargingStation(params);
      if (res && res.ret == '200') {
        that.list = res.prefList;
        console.log(this.list, 'list');
      }
    },
    // async getDistanceApi() {},
    handClickJump(item) {
      const { stationId, distance } = item.stationInfo;
      uni.navigateTo({
        url: `/pages/basic/station/station?stationId=${stationId}&distance=${distance}`,
      });
    },
  },
  mounted() {
    console.log(this.getToken, '-0-0-0-0');
  },
  filters: {
    toFix: (value, count = 2) => {
      var num = (value * 1) / 1000;
      return num.toFixed(count);
    },
    fixed: (value, count = 2) => {
      return value.toFixed(count);
    },
  },
};
</script>
<style lang="scss" scoped>
.collection-page {
  box-sizing: border-box;
  position: relative;
  height: 100vh;
  width: 100%;
  padding-bottom: 30upx;
  background: #efefef;
  padding: 10rpx;
  // background: linear-gradient(180deg, #c8f3d5 0%, #f5fff9 17%, #f7fff6 37%, #fffefe 100%);

  .collection_head {
    // position: absolute;
    // left: 0;
    // top: 0;
    // right: 0;
    width: 100%;
    height: 100upx;
    // z-index: 88;
    // background: $themeBg;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    align-items: center;
    padding: 0 40upx;

    .head_input {
      width: 100%;
      height: 65upx;
      border-radius: 100upx;
      background: #fff;
      overflow: hidden;
      padding-left: 20upx;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      box-shadow: 0px 2px 16px 0px rgba(113, 127, 142, 0.06);
      .iconsoushuo {
        font-size: 41upx;
        color: gray;
        margin-right: 15upx;
      }
      input {
        font-size: 30upx;
        color: #999;
      }
    }
  }

  .list-found {
    width: 100%;
    height: calc(100vh - 310upx);

    z-index: 30;

    .station-scroll-frame {
      width: 100%;
      height: 100%;
      overflow-y: auto;
      padding-bottom: 20upx;
      .default-nothing {
        display: flex;
        flex-wrap: wrap;
        align-content: center;
        .default-graph {
          display: inline-block;
          margin: 0 auto;
          margin-top: 166upx;
        }
        view {
          font-size: 30upx;
          color: #999;
          font-weight: 500;
          width: 100%;
          text-align: center;
          margin-top: -60upx;
        }
      }
      .station-scroll {
        width: 100%;
        min-height: 218upx;
        background: #ffffff;
        box-sizing: border-box;
        padding: 32upx 32upx 5upx 32upx;
        border-radius: 16upx;
        margin-bottom: 24upx;
        box-shadow: 0px 2px 16px 0px rgba(113, 127, 142, 0.06);
        .scroll-head {
          font-size: 34upx;
          color: rgba(34, 34, 34, 0.8);
          font-weight: 600;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .scroll-stationName {
            width: 63%;
            white-space: nowrap;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .scroll-views {
            min-width: 124upx;
            height: 52upx;
            padding: 0 10upx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 400;
            font-size: 24upx;
            color: #2196f3;
            text {
              margin-right: 10upx;
            }
          }
        }
        .scroll-p {
          display: flex;
          align-items: center;
          color: #666;
          font-size: 24upx;
          font-weight: 400;
          margin: 15upx 0;
          view {
            width: 24upx;
            height: 24upx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 22upx;
            color: rgba(27, 139, 255, 1);
            font-weight: 500;
            margin-right: 8upx;
            background: rgba(36, 143, 255, 0.12);
            border-radius: 4px;
          }
        }
        .scroll-type {
          display: flex;
          flex-wrap: wrap;
          view {
            min-width: 64upx;
            padding: 0 8upx;
            margin-right: 8upx;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 24upx;
            color: rgba(255, 131, 1, 1);
            font-weight: 400;
            height: 36upx;
            background: rgba(255, 131, 0, 0.1);
            border-radius: 4upx;
            margin-top: 17upx;
          }
        }
        .scroll-head {
          font-size: 34upx;
          color: rgba(34, 34, 34, 0.8);
          font-weight: 600;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .scroll-stationName {
            width: 63%;
            white-space: nowrap;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .scroll-views {
            min-width: 124upx;
            height: 52upx;
            padding: 0 10upx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 400;
            font-size: 24upx;
            color: #2196f3;
            text {
              margin-right: 10upx;
            }
          }
        }
        .scroll-p {
          display: flex;
          align-items: center;
          color: #666;
          font-size: 24upx;
          font-weight: 400;
          margin: 15upx 0;
          view {
            width: 24upx;
            height: 24upx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 22upx;
            color: rgba(27, 139, 255, 1);
            font-weight: 500;
            margin-right: 8upx;
            background: rgba(36, 143, 255, 0.12);
            border-radius: 4px;
          }
        }
        .scroll-type {
          display: flex;
          flex-wrap: wrap;
          view {
            min-width: 64upx;
            padding: 0 8upx;
            margin-right: 8upx;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 24upx;
            color: rgba(255, 131, 1, 1);
            font-weight: 400;
            height: 36upx;
            background: rgba(255, 131, 0, 0.1);
            border-radius: 4upx;
            margin-top: 17upx;
          }
        }
        .price-sorting {
          width: 100%;
          display: flex;
          max-height: 122upx;
          margin: 16upx 0;
          flex-direction: column;
          .rate-wrap {
            display: flex;
            width: 100%;
            .rate-box {
              width: 50%;
            }
          }
          .pile-status {
            height: 100%;
            display: flex;
            align-content: center;
            width: 50%;
            margin-left: auto;
            justify-content: flex-end;
            .scroll-gun-view {
              display: flex;
              line-height: 50upx;
              align-items: center;
              margin-right: 24upx;
              color: rgba(51, 51, 51, 1);
              font-size: 24upx;
              font-weight: 500;
              .scroll-gunlist {
                width: 42upx;
                height: 42upx;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24upx;
                font-weight: 500;
                background: rgba(241, 35, 21);
                padding: 5rpx;
                border-radius: 50%;
                color: #fff;
                margin-right: 8upx;
              }
              text {
                color: rgba(153, 153, 153, 1);
                font-weight: 500;
                font-size: 24upx;
                margin-left: 6upx;
              }
              .scroll-dc {
                color: #fff;
                background: rgba(33, 150, 243);
                padding: 5rpx;
                border-radius: 50%;
              }
            }
          }

          .prices {
            flex: 1;
            font-size: 38upx;
            color: rgba(238, 110, 45, 1);
            font-weight: 600;
            align-items: baseline;
            justify-content: flex-end;
            display: flex;
            .prices-view {
              display: flex;
              font-size: 24upx;
              margin-left: 8upx;
              color: rgba(179, 179, 179, 1);
              font-weight: 400;
              .price-num {
                text-decoration: line-through;
              }
              .price-dy {
              }
            }
          }
          .font {
            margin-top: 10rpx;
            line-height: 30rpx;
            color: #79819a;
            font-size: 24rpx;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
        .scroll-gun-type {
          border-top: 1px solid #f5f5f5;
          height: 64upx;
          width: 100%;
          display: flex;
          align-items: center;
          color: #666;
          font-size: 24upx;
          font-weight: 400;
          .tag-item {
            color: #2790de;
            border: 1px solid #2790de;
            border-radius: 8rpx;
            margin-left: 10rpx;
            padding: 3px 5px;
          }
        }
      }
      .scroll-end {
        width: 100%;
        height: 66upx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 26upx;
        color: #666;
        font-weight: 500;
        padding-bottom: 40upx;
      }
    }
    .station-padding {
      padding-top: 88upx;
    }
  }
}
</style>
