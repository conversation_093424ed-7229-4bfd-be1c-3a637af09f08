<template>
  <view class="billinghistory">
    <view class="billinghistory-lists" v-for="(item, index) in orderList" :key="index">
      <view class="li-top">
        <view>
          <view class="top-title">{{ item.invoiceTitle }}</view>
          <view class="top-time">{{ item.appTime }}</view>
        </view>
        <view class="top-amount">
          <text>¥</text>
          {{ item.invoiceAmt }}
        </view>
      </view>

      <view class="li-mid">
        <view class="mid-mode">{{ item.invoiceMode === '01' ? '按行程' : '按金额' }}</view>
        <view class="mid-no">开票编号：{{ (item.appList && item.appList[0] && item.appList[0].invoiceAppNo) || '' }}</view>
      </view>

      <view class="li-bottom">
        <text
          :class="
            'bottom-desc ' +
            (item.invoiceStatus === '02' ? 'success' : '') +
            ' ' +
            (item.invoiceStatus === '03' ? 'fail' : '') +
            ' ' +
            (item.invoiceStatus === '01' ||
            item.invoiceStatus === '04' ||
            (item.invoiceStatus === '02' && item.invoiceAgainStatus === '01')
              ? 'examine'
              : '')
          "
        >
          {{ item.invoiceStatusName }}
        </text>
        <view class="li-btns">
          <view
            class="li-button"
            v-if="item.invoiceStatus === '02' && item.invoiceAgainStatus !== '01'"
            @tap="invoicePdfNow"
            :data-pdf="item.invoicePdf"
            >查看发票</view
          >
          <view
            class="li-button"
            v-if="item.invoiceStatus === '02' && item.invoiceAgainStatus !== '01'"
            @tap="notifyInvoiceEmail"
            :data-obj="item"
            >重发至邮箱</view
          >
          <view
            class="li-button"
            v-if="item.invoiceStatus === '02' && item.invoiceAgainStatus !== '01'"
            @tap="goReopen"
            :data-obj="item"
            data-type="reopen"
            >申请重开</view
          >
          <view class="li-button" v-if="item.invoiceStatus === '03'" @tap="goReopen" :data-obj="item" data-type="retry"
            >重试</view
          >
        </view>
      </view>

      <view class="li-remark" v-if="item.invoiceStatus === '01'"
        >尊敬的客户：您好，您的发票申请预计3个工作日会开票并发送至您的邮箱，请耐心等待！</view
      >

      <view class="li-remark remark-orange" v-if="item.invoiceAgainStatus === '03' && item.invoiceStatus === '02'"
        >您的重开申请未通过，若需重开请重新申请。</view
      >

      <view class="li-remark remark-orange" v-if="item.invoiceAgainRemark">重开失败，请重试！</view>
    </view>
    <view v-if="orderList.length === 0" class="myorder-image">
      <image :src="`${IMG_PATH}main-data-null.png`" mode="heightFix" />
      <text>当前暂无开票历史</text>
    </view>
  </view>
</template>

<script>
import { getInvoicesLog, getNotifyInvoiceEmail } from '@/services/index.js';
// pages/billinghaseMoreory/billinghaseMoreory.js
export default {
  data() {
    return {
      token: '',
      orderList: [],
      haseMore: true,
      pageNum: 1,
      totalNum: 5,
      isRefresh: false,
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */ onLoad: function () {
    this.setData(
      {
        token: uni.getStorageSync('token'),
      },
      () => {
        this.loadData();
      }
    );
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    if (this.isRefresh) {
      this.setData(
        {
          pageNum: 1,
          orderList: [],
          isRefresh: false,
        },
        () => {
          this.loadData();
        }
      );
    }
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.setData(
      {
        pageNum: 1,
        orderList: [],
        isRefresh: false,
      },
      () => {
        this.loadData();
        uni.stopPullDownRefresh();
      }
    );
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    const { haseMore, pageNum } = this;
    if (haseMore) {
      this.setData(
        {
          pageNum: pageNum + 1,
        },
        () => {
          this.loadData();
        }
      );
    }
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {},
  methods: {
    loadData: function () {
      var that = this;
      const { token, orderList, pageNum, totalNum } = this;
      uni.showLoading({
        title: '加载中',
      });
      const params = {
        pageNum: pageNum,
        totalNum: totalNum,
      };
      that.getInvoicesLog(params);
      // uni.request({
      //   url: baseUrl + `/wx/v0.1/accounts/invoice-log?pageNum=${pageNum}&totalNum=${totalNum}`,
      //   data: {},
      //   method: 'GET',
      //   header: {
      //     'content-type': 'application/json',
      //     minitProgramToken: token,
      //   },
      //   success: (res) => {
      //     uni.hideLoading();
      //     const { ret, msg, myInvoiceList } = res.data;
      //     if (ret === 200) {
      //       const invoiceList = myInvoiceList.map((item) => {
      //         return {
      //           ...item,
      //         };
      //       });
      //       that.setData({
      //         orderList: [...orderList, ...invoiceList],
      //         haseMore: invoiceList.length > 0 ? true : false,
      //       });
      //     } else {
      //       uni.showToast({
      //         title: msg || ret,
      //         icon: 'none',
      //         duration: 2000,
      //       });
      //     }
      //   },
      //   fail: (err) => {
      //     console.log(err, '请求出问题。');
      //     uni.hideLoading();
      //   },
      // });
    },

    async getInvoicesLog(params) {
      let that = this;
      const { token, orderList, pageNum, totalNum } = this;
      const [err, res] = await getInvoicesLog(params);
      uni.hideLoading();
      if (err) {
        return;
      }

      if (res) {
        const { ret, msg, myInvoiceList } = res;
        if (ret === 200) {
          const invoiceList = myInvoiceList.map((item) => {
            return {
              ...item,
            };
          });
          that.setData({
            orderList: [...orderList, ...invoiceList],
            haseMore: invoiceList.length > 0 ? true : false,
          });
        } else {
          uni.showToast({
            title: msg || ret,
            icon: 'none',
            duration: 2000,
          });
        }
      }
    },

    goReopen: function (event) {
      const { obj, type } = event.currentTarget.dataset;
      if (!obj) {
        console.error('goReopen: obj is undefined');
        return;
      }
      const {
        appList = [],
        invoiceAmt = '',
        email = '',
        invoiceTitle = '',
        invoiceTitleType = '',
        invoiceType = '',
        taxNum = '',
        recipients = '',
        phoneNo = '',
        invoiceAppNo = '',
        bankAccount = '',
        bankName = '',
      } = obj;
      let orderListArr = [];
      let mehArr = [];
      let timeArr = [];
      if (Array.isArray(appList)) {
        appList.forEach((item) => {
          if (item) {
            orderListArr.push(item.orderNo || '');
            mehArr.push(item.invoiceAmt || '');
            timeArr.push(item.successTime || '');
          }
        });
      }
      uni.setStorage({
        key: 'meh',
        data: mehArr,
      });
      uni.setStorage({
        key: 'time',
        data: timeArr,
      });
      uni.setStorage({
        key: 'reopenObj',
        data: {
          email,
          invoiceTitle,
          invoiceTitleType,
          invoiceType,
          taxNum,
          recipients,
          phoneNo,
          invoiceAppNo,
          bankAccount,
          bankCode: bankName,
        },
      });
      uni.navigateTo({
        url: `/subPackages/billingpersonal/billingpersonal?reopenType=${type}&price=${invoiceAmt}&msg=${orderListArr.join(
          ','
        )}`,
      });
    },

    invoicePdfNow: function (event) {
      const { pdf } = event.currentTarget.dataset;
      if (!pdf) {
        uni.showToast({
          title: '暂无发票！',
          icon: 'none',
          duration: 2000,
        });
        return;
      }

      // #ifdef H5
      // H5环境下预览PDF（适用于内嵌到原生App中）
      this.previewPdfInH5(pdf);
      // #endif

      // #ifndef H5
      // 非H5环境下的处理逻辑（小程序等）
      uni.showLoading({
        title: '加载中',
      });
      uni.downloadFile({
        url: pdf,
        success: (res) => {
          uni.openDocument({
            filePath: res.tempFilePath,
            fileType: 'pdf',
            fail: () => {
              uni.showToast({
                title: `文件打开失败`,
              });
            },
            complete: () => {
              uni.hideLoading();
            },
          });
        },
        fail: () => {
          uni.showToast({
            title: `文件打开失败`,
          });
          uni.hideLoading();
        },
      });
      // #endif
    },

    // H5环境下预览PDF的方法（适用于内嵌到原生App中）
    previewPdfInH5: function (pdfUrl) {
      // 显示加载中提示
      uni.showLoading({
        title: '加载中...',
      });

      try {
        // 检查URL是否已经包含协议
        if (!/^https?:\/\//i.test(pdfUrl)) {
          // 如果没有协议，添加https协议
          pdfUrl = 'https:' + pdfUrl;
        }

        // 创建预览容器
        const pdfContainer = document.createElement('div');
        pdfContainer.className = 'pdf-viewer-container';
        pdfContainer.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.8);
          z-index: 9999;
          display: flex;
          flex-direction: column;
        `;

        // 创建顶部工具栏
        const toolbar = document.createElement('div');
        toolbar.className = 'pdf-toolbar';
        toolbar.style.cssText = `
          height: 44px;
          background-color: #2196f3;
          color: white;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 15px;
        `;

        // 创建标题
        const title = document.createElement('div');
        title.innerText = '发票预览';
        title.style.cssText = `
          font-size: 16px;
        `;

        // 创建关闭按钮
        const closeBtn = document.createElement('div');
        closeBtn.innerText = '关闭';
        closeBtn.style.cssText = `
          font-size: 14px;
          padding: 5px 10px;
        `;
        closeBtn.onclick = () => {
          document.body.removeChild(pdfContainer);
        };

        // 添加按钮到工具栏
        toolbar.appendChild(title);
        toolbar.appendChild(closeBtn);

        // 创建内容容器
        const contentContainer = document.createElement('div');
        contentContainer.style.cssText = `
          flex: 1;
          overflow: hidden;
          position: relative;
        `;

        // 创建canvas容器
        const canvasContainer = document.createElement('div');
        canvasContainer.id = 'pdf-canvas-container';
        canvasContainer.style.cssText = `
          width: 100%;
          height: 100%;
          overflow: auto;
          background-color: #f5f5f5;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 10px 0;
        `;

        // 组装DOM结构
        contentContainer.appendChild(canvasContainer);
        pdfContainer.appendChild(toolbar);
        pdfContainer.appendChild(contentContainer);
        document.body.appendChild(pdfContainer);

        // 动态加载PDF.js库（确保不影响主包大小）
        const script = document.createElement('script');
        script.src = 'https://cdn.staticfile.net/pdf.js/3.4.120/pdf.min.js';
        script.onload = () => {
          // PDF.js加载完成后初始化PDF查看器
          const pdfjsLib = window['pdfjs-dist/build/pdf'];

          // 设置worker路径
          pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdn.staticfile.net/pdf.js/3.4.120/pdf.worker.min.js';

          // 加载PDF文档
          const loadingTask = pdfjsLib.getDocument(pdfUrl);
          loadingTask.promise
            .then((pdf) => {
              uni.hideLoading();

              // 显示第一页
              this.renderPdfPage(pdf, 1, canvasContainer);

              // 创建页面导航
              this.createPdfNavigation(pdf, canvasContainer);
            })
            .catch((error) => {
              console.error('加载PDF失败:', error);
              uni.hideLoading();
              uni.showToast({
                title: '加载PDF失败，请重试',
                icon: 'none',
                duration: 2000,
              });
            });
        };

        script.onerror = () => {
          console.error('加载PDF.js库失败');
          uni.hideLoading();
          uni.showToast({
            title: '加载预览组件失败',
            icon: 'none',
            duration: 2000,
          });
          document.body.removeChild(pdfContainer);
        };

        document.body.appendChild(script);
      } catch (error) {
        console.error('预览PDF时出错:', error);
        uni.hideLoading();
        uni.showToast({
          title: '预览失败，请重试',
          icon: 'none',
          duration: 2000,
        });
      }
    },

    // 渲染PDF页面
    renderPdfPage: function (pdf, pageNumber, container) {
      // 获取指定页面
      pdf.getPage(pageNumber).then((page) => {
        const viewport = page.getViewport({ scale: 1.0 });

        // 计算适合屏幕的缩放比例
        const containerWidth = container.clientWidth - 20; // 减去内边距
        const scale = containerWidth / viewport.width;
        const scaledViewport = page.getViewport({ scale });

        // 创建canvas元素
        const canvas = document.createElement('canvas');
        canvas.className = 'pdf-page';
        canvas.dataset.page = pageNumber; // 添加页码数据属性，用于导航
        canvas.width = scaledViewport.width;
        canvas.height = scaledViewport.height;
        canvas.style.marginBottom = '10px';
        canvas.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';

        // 添加页码标签
        const pageLabel = document.createElement('div');
        pageLabel.innerText = `第 ${pageNumber} 页 / 共 ${pdf.numPages} 页`;
        pageLabel.style.cssText = `
          text-align: center;
          margin-bottom: 10px;
          color: #666;
          font-size: 14px;
        `;

        // 将canvas添加到容器
        container.appendChild(pageLabel);
        container.appendChild(canvas);

        // 渲染PDF内容到canvas
        const context = canvas.getContext('2d');
        const renderContext = {
          canvasContext: context,
          viewport: scaledViewport,
        };

        page.render(renderContext);
      });
    },

    // 创建PDF导航控件
    createPdfNavigation: function (pdf, container) {
      // 如果只有一页，不需要导航
      if (pdf.numPages <= 1) return;

      // 创建导航容器
      const navContainer = document.createElement('div');
      navContainer.style.cssText = `
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0,0,0,0.5);
        border-radius: 20px;
        padding: 5px 15px;
        display: flex;
        align-items: center;
      `;

      // 创建"上一页"按钮
      const prevBtn = document.createElement('div');
      prevBtn.innerText = '上一页';
      prevBtn.style.cssText = `
        color: white;
        padding: 5px 10px;
        font-size: 14px;
      `;
      prevBtn.onclick = () => {
        const currentPage = parseInt(container.querySelector('.pdf-page')?.dataset.page || 1);
        if (currentPage > 1) {
          // 清空容器
          container.innerHTML = '';
          // 渲染上一页
          this.renderPdfPage(pdf, currentPage - 1, container);
        }
      };

      // 创建"下一页"按钮
      const nextBtn = document.createElement('div');
      nextBtn.innerText = '下一页';
      nextBtn.style.cssText = `
        color: white;
        padding: 5px 10px;
        font-size: 14px;
      `;
      nextBtn.onclick = () => {
        const currentPage = parseInt(container.querySelector('.pdf-page')?.dataset.page || 1);
        if (currentPage < pdf.numPages) {
          // 清空容器
          container.innerHTML = '';
          // 渲染下一页
          this.renderPdfPage(pdf, currentPage + 1, container);
        }
      };

      // 组装导航
      navContainer.appendChild(prevBtn);
      navContainer.appendChild(nextBtn);
      document.body.appendChild(navContainer);
    },

    notifyInvoiceEmail: function (event) {
      const obj = event.currentTarget.dataset.obj || {};
      const invoiceAppNo = obj.invoiceAppNo || '';
      const { token } = this;
      const params = {
        orderNo: invoiceAppNo,
      };
      uni.showLoading({
        title: '发送中',
      });
      this.getNotifyInvoiceEmail(params);
      // uni.request({
      //   url: baseUrl + '/wx/v0.1/account/notifyInvoiceEmail',
      //   data: params,
      //   method: 'POST',
      //   header: {
      //     'content-type': 'application/json',
      //     minitProgramToken: token,
      //   },
      //   success: (res) => {
      //     uni.hideLoading();
      //     const { ret, msg } = res.data;
      //     if (ret === 200) {
      //       uni.showToast({
      //         title: '发送成功',
      //         icon: 'success',
      //         duration: 2000,
      //       });
      //     } else {
      //       uni.showToast({
      //         title: msg || ret,
      //         icon: 'none',
      //         duration: 2000,
      //       });
      //     }
      //   },
      //   fail: (err) => {
      //     uni.hideLoading();
      //     console.log(err, '请求出问题。');
      //   },
      // });
    },

    async getNotifyInvoiceEmail(params) {
      let that = this;
      const [err, res] = await getNotifyInvoiceEmail(params, {
        'content-type': 'application/x-www-form-urlencoded',
      });
      uni.hideLoading();
      if (err) {
        return;
      }
      if (res) {
        const { ret, msg } = res;
        if (ret === 200) {
          uni.showToast({
            title: '发送成功',
            icon: 'success',
            duration: 2000,
          });
        } else {
          uni.showToast({
            title: msg || ret,
            icon: 'none',
            duration: 2000,
          });
        }
      }
    },
  },
};
</script>
<style>
@import './billinghistory.css';
</style>
