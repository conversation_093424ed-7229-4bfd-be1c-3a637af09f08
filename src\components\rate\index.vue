<template>
  <div class="rate-wrap">
    <div class="rate-item" v-for="(item, index) in list" :key="index">
      <img :src="item" alt="" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Number | String,
      default: 0,
    },
  },
  components: {},
  data() {
    return {};
  },
  computed: {
    activeImg() {
      return this.IMG_PATH + '<EMAIL>';
    },
    activeBanImg() {
      return this.IMG_PATH + '<EMAIL>';
    },
    normalImg() {
      return this.IMG_PATH + '<EMAIL>';
    },
    list() {
      let arr = [];
      for (let i = 1; i <= parseInt(+this.value); i++) {
        arr.push(this.activeImg);
      }
      if (+this.value % 1 > 0) {
        arr.push(this.activeBanImg);
      }
      if (arr.length > 5) {
        arr.slice(0, 5);
      } else {
        arr = [...arr, ...Array(5 - arr.length).fill(this.normalImg)];
      }
      return arr;
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.rate-wrap {
  display: flex;
  flex-direction: row;
  height: 50rpx;
  align-items: center;
  .rate-item {
    margin-right: 5rpx;
    img {
      width: 40rpx;
      height: 40rpx;
    }
  }
}
</style>
