.jusa {
  padding-bottom: 300rpx;
}
.yhj {
  width: 100%;
  height: 80rpx;
  background: #fff;
  color: #343434;
  font-size: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.yhj view {
  width: 33%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.yhj .active {
  border-bottom: 2rpx solid #3297f3;
  color: #3297f3;
}
.list {
  width: 690rpx;
  height: 214rpx;
  background: #fff;
  margin: 0 auto;
  margin-top: 30rpx;
  box-sizing: border-box;
  display: flex;
}
.list .listL {
  width: 462rpx;
  height: 100%;
  display: flex;
  box-sizing: border-box;
  padding: 40rpx 28rpx;
  align-content: space-between;
  flex-wrap: wrap;
}
.list .listL text {
  color: #666;
  font-size: 24rpx;
  display: block;
  width: 100%;
}
.list .listL .net1 {
  font-size: 28rpx;
  color: #666;
}
.list .listL .net2 {
  color: #666;
  font-size: 24rpx;
}
.list .listL .net3 {
  color: #999999;
  font-size: 24rpx;
}
.list .listR {
  width: 228rpx;
  height: 100%;
  background: #3297f3;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  position: relative;
}
.list .listR view {
  font-size: 54rpx;
  color: #fff;
  z-index: 2;
}
.list .listR view text {
  font-size: 24rpx;
  padding-left: 10rpx;
}
.list .listR image {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
  left: 0;
  top: 0;
}
.goCupe {
  width: 690rpx;
  height: 80rpx;
  background: #fff;
  color: #3297f3;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  margin: 30rpx auto 0 auto;
}
.pullNos {
  width: 100%;
  position: fixed;
  bottom: 0;
  z-index: 99;
}
