<template>
  <view class="mine-page">
    <!-- <custom-head title="客服专区" isback :isScroll="isScroll" /> -->

    <!-- 使用scroll-view实现懒加载 -->
    <scroll-view
      class="feedback-scroll-view"
      scroll-y
      :scroll-top="scrollTop"
      @scrolltolower="onScrollToLower"
      @scroll="onScroll"
      :lower-threshold="50"
      :enable-back-to-top="true"
    >
      <view class="feedback-list">
        <view
          class="feedbackCard"
          @tap="todetai"
          :data-faqid="item.faqId"
          v-for="(item, index) in feedbackList"
          :key="item.faqId"
        >
          <view class="topbox">
            <text>{{ item.typeName }}</text>
            <text :class="item.state === '01' ? 'red' : 'blue'">{{ item.state === '01' ? '待解决' : '已解决' }}</text>
          </view>

          <view class="feedback_title">{{ item.title }}</view>

          <view class="feedback_time">{{ item.pubDate }}</view>
        </view>

        <!-- 加载状态提示 -->
        <view class="loading-status" v-if="feedbackList.length > 0">
          <view v-if="loading" class="loading-text">
            <text>加载中...</text>
          </view>
          <view v-else-if="noMoreData" class="no-more-text">
            <text>没有更多数据了</text>
          </view>
          <view v-else class="load-more-text">
            <text>上拉加载更多</text>
          </view>
        </view>

        <!-- 空数据提示 -->
        <view class="nodata" v-if="!feedbackList.length && !loading">
          <image :src="`${IMG_PATH}nodata.png`" mode="widthFix" />
          <text>暂无数据</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部按钮固定在页面底部 -->
    <view class="safe_foot flex-row">
      <main-btn ghost @click="feedback" class="item">添加反馈</main-btn>
      <main-btn @click="phoneCall" class="item">客服电话</main-btn>
    </view>
  </view>
</template>

<script>
// import customHead from '@/components/custom-head';
import mainBtn from '@/components/main-btn';
import { baseDateNo } from '@/utils/base';
const baseUrl = baseDateNo();
import network from '../../utils/network';
import { util, getWxEnv } from '@/utils/util';
import { makePhoneCall } from '@/utils/index.js';
const request = network.requestLoading;
import { getFaqList, getConfigOption } from '@/services/index.js';
export default {
  components: {
    // customHead,
    mainBtn,
  },
  data() {
    return {
      feedbackList: [],
      pageNum: 1,
      totalNum: 10, // 每页加载数量
      phone: null,
      isScroll: false,
      loading: false, // 是否正在加载
      noMoreData: false, // 是否没有更多数据
      scrollTop: 0, // scroll-view的滚动位置
      isFirstLoad: true, // 是否首次加载
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadCustomerPhone();
  },
  async onShow() {
    // 重置数据并加载第一页
    this.setData(
      {
        feedbackList: [],
        pageNum: 1,
        loading: false,
        noMoreData: false,
        isFirstLoad: true,
      },
      () => {
        this.loadData();
      }
    );
  },
  methods: {
    async loadCustomerPhone() {
      const [, res] = await getConfigOption({ paraCode: 'serviceTel' });
      if (res && res.ret == '200') {
        this.setData({
          phone: res.list[0].paramValue,
        });
      }
    },

    async loadData() {
      // 防止重复加载
      if (this.loading || this.noMoreData) {
        return;
      }

      this.setData({ loading: true });

      try {
        const [, res] = await getFaqList({
          pageNum: this.pageNum,
          totalNum: this.totalNum,
        });

        if (res && res.ret == '200') {
          const newList = res.faqReplyList || [];

          if (newList.length === 0) {
            // 没有更多数据
            this.setData({ noMoreData: true });
          } else {
            // 合并新数据
            this.setData({
              feedbackList: [...this.feedbackList, ...newList],
            });

            // 如果返回的数据少于请求的数量，说明没有更多数据了
            if (newList.length < this.totalNum) {
              this.setData({ noMoreData: true });
            }
          }

          console.log('加载数据成功:', res);
        } else {
          uni.showToast({
            title: '加载失败，请重试',
            icon: 'none',
          });
        }
      } catch (error) {
        console.error('加载数据失败:', error);
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none',
        });
      } finally {
        this.setData({
          loading: false,
          isFirstLoad: false,
        });
      }
    },

    // scroll-view滚动到底部时触发
    onScrollToLower() {
      console.log('滚动到底部，加载更多数据');
      if (!this.loading && !this.noMoreData) {
        this.setData(
          {
            pageNum: this.pageNum + 1,
          },
          () => {
            this.loadData();
          }
        );
      }
    },

    // scroll-view滚动时触发
    onScroll(e) {
      const scrollTop = e.detail.scrollTop;

      // 更新滚动状态，用于控制头部样式等
      if (scrollTop > 10 && !this.isScroll) {
        this.setData({
          isScroll: true,
        });
      } else if (scrollTop <= 10 && this.isScroll) {
        this.setData({
          isScroll: false,
        });
      }
    },

    // 下拉刷新
    onRefresh() {
      console.log('下拉刷新');
      this.setData(
        {
          feedbackList: [],
          pageNum: 1,
          loading: false,
          noMoreData: false,
          isFirstLoad: true,
        },
        () => {
          this.loadData();
        }
      );
    },

    phoneCall() {
      if (this.phone) {
        makePhoneCall(this.phone);
      } else {
        uni.showToast({
          title: '暂无客服电话',
          icon: 'none',
        });
      }
    },

    feedback() {
      uni.navigateTo({
        url: '/subPackages/feedback/index',
      });
    },

    todetai(e) {
      const faqId = e.currentTarget.dataset.faqid;
      const typeName = e.currentTarget.dataset.typename;
      uni.navigateTo({
        url: '/subPackages/feedbackDetail/index?faqId=' + faqId,
      });
    },
  },
};
</script>
<style>
@import './index.css';
</style>
