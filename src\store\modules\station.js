import { getHomeStation<PERSON><PERSON> } from "../../services/homeService"
const state = {
    staitonInfo: null
}
const getters = {
    getStationInfo: state => {
        return state.staitonInfo
    },
}
const actions = {
    async initStation ({ commit }, option) {
        try {
            const info = await getHomeStationApi(option);
            if (info.staitonInfo) {
                commit('SET_STATIONINFO', info.staitonInfo);
                return info.staitonInfo
            }
            return
        } catch (error) {

            return Promise.reject(error)
        }
    }
}
const mutations = {
    ['SET_STATIONINFO'] (state, time) {
        state.staitonInfo = time;
    },
}
export const station = {
    namespaced: true,
    state,
    getters,
    actions,
    mutations
}