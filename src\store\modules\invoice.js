
const state = {
    nowSelectInfo: null,
    historyList: [
    ],
    historyItemInfo: null

}
const getters = {
    getSelectInfo: state => {
        return state.nowSelectInfo
    },
    getHistoryList: state => {
        return state.historyList

    },
    getHistoryItemInfo: state => {
        return state.historyItemInfo
    }
}
const actions = {
    /** 
     * 重置订单操作等待时间
    */
    addHistory ({ commit }, item) {
        commit('ADD_HISTORY', item)
    },
    /** 
     * 设置当前开票订单信息
    */
    setSelectInfo ({ commit }, item) {
        commit('SET_SELECT_INFO', item)
    },
    saveHistoryItemInfo ({ commit }, item) {
        commit('SET_HISTORY_INFO', item)
    },
}
const mutations = {
    ['SET_SELECT_INFO'] (state, info) {
        state.nowSelectInfo = info;
    },
    ['ADD_HISTORY'] (state, item) {
        let has = false;
        for (let index = 0; index < state.historyList.length; index++) {
            const element = state.historyList[index];
            if (element.invoiceTitle == item.invoiceTitle) {
                state.historyList[index] = item;
                has = true;
                break;
            }
        }
        if (!has) {
            state.historyList.push(item);
        }

    },
    ['SET_HISTORY_INFO'] (state, item) {
        state.historyItemInfo = item;
    },
    ['SET_HISTORY_INFO'] (state, item) {
        state.historyItemInfo = item;
    },
    ['SET_HISTORY_INFO'] (state, item) {
        state.historyItemInfo = item;
    },
    ['SET_HISTORY_INFO'] (state, item) {
        state.historyItemInfo = item;
    },
}
export const invoice = {
    namespaced: true,
    state,
    getters,
    actions,
    mutations
}