.billingorder-list {
  height: 199rpx;
  background-color: #fff;
  color: #666;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.billing-order-list-wrap {
  box-sizing: border-box;
  /* #ifdef MP-WEIXIN */
  padding-bottom: 120rpx;
  /* #endif */
  /* #ifdef H5 */
  padding-bottom: 60px;
  padding-top: 20px;
  /* #endif */
}


.billingorder-list .list-one {
  position: relative;
  padding: 0 30rpx;
  height: 88rpx;
  line-height: 88rpx;
}

.billingorder-list .list-two {
  position: relative;
  padding: 0 30rpx;
  height: 110rpx;
  line-height: 110rpx;
}

.billingorder-list .list-two::after {
  position: absolute;
  left: 0;
  right: 0;
  content: '';
  height: 1rpx;
  background-color: #e1e1e1;
}

.billingorder-list .list-two label text {
  font-weight: bold;
  margin-right: 10rpx;
}

.ordercharge-btn {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 5px;
  height: 160rpx;
  background-color: #fff;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.ordercharge-btn .color-pas {
  background-color: #e1e1e1;
}

.ordercharge-desc {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
  font-size: 28rpx;
  line-height: 1.4;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #e1e1e1;
}

.desc-left {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-right: 20rpx;
}

.desc-text {
  color: #333;
  font-size: 28rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.number-highlight {
  color: #2196f3;
  font-weight: bold;
  font-size: 32rpx;
}

.price-highlight {
  color: #2196f3;
  font-weight: bold;
  font-size: 32rpx;
}

.limit-tip {
  color: #666;
  font-size: 24rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 1;
}

.limit-number {
  color: #666;
  font-weight: normal;
}

.desc-right {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.help-link {
  color: #2196f3;
  font-size: 24rpx;
  text-decoration: underline;
  white-space: nowrap;
}

.ordercharge-btns {
  display: flex;
  align-items: center;
  padding-left: 30rpx;
}

.checkbox-li {
  margin-right: 50rpx;
}

.myorder-image {
  text-align: center;
  color: #b2b2b2;
  font-size: 32rpx;
  margin-top: 40vh;
}

.myorder-image image {
  display: block;
  width: 70rpx;
  height: 86rpx;
  margin: 0 auto 20rpx auto;
}

.list-desc {
  text-align: right;
  color: #2196f3;
  line-height: 80rpx;
  padding-right: 20rpx;
  background: #fff;
  position: fixed;
  width: 100vw;
  top: 0;
  right: 0;
  z-index: 10;
}

.list-picker {
  position: fixed;
  width: 100vw;
  top: 0rpx;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  z-index: 10;
}

.list-picker .btn-yellow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  background: #001affce;
  border-radius: 50%;
  padding: 0 5rpx;
}

.picker-li {
  opacity: 0.7;
  background: #f5f5f5;
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.li-img {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

.picker-mid {
  opacity: 0.7;
  padding: 0 40rpx;
  line-height: 70rpx;
}

.fr-end {
  margin-left: 20rpx;
}

.btn-fixed {
  width: 230rpx;
  color: #fff;
  height: 100rpx;
  line-height: 100rpx;
  border-radius: 0;
}

.banner {
  background: #2196f3;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  position: fixed;
  top: 5px;
  left: 0;
  width: 100vw;
  z-index: 2;
}

.banner-img {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

.banner-title {
  font-family: 'PingFang SC';
  font-size: 32rpx;
  font-style: normal;
  font-weight: 500;
  color: #fff;
  flex: 1;
}

.banner-btn {
  width: auto;
  border-radius: 37rpx;
  background: rgba(0, 0, 0, 0.1);
  padding: 0rpx 24rpx;
  color: #fff;
  text-align: right;
  text-shadow: 0px 8rpx 8rpx rgba(0, 0, 0, 0.25);
  font-family: 'PingFang SC';
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 50rpx;
  margin-left: auto;
}

.flex-full {
  flex: 1;
}

.footer-desc {
  padding: 32rpx 0;
  /* max-height: 500rpx; */
  height: calc(100% - 200rpx);
  color: #333;
  font-family: 'PingFang SC';
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 45rpx;
  min-height: 200rpx;
  overflow-y: auto;
  font-weight: 600;
}