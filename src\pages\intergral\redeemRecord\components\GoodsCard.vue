<template>
  <view class="goods-card" @click="jumpToOrderDetail">
    <view class="header">
      <text class="order-id">订单号：{{ cardItem.integralId }}</text>
      <text class="state" v-if="cardItem.status === '01'">待发货</text>
      <text class="state" v-if="cardItem.status === '02'">已发货</text>
      <text class="state" v-if="cardItem.status === '03'">已评价</text>
    </view>
    <view class="line"></view>
    <view class="middle">
      <img :src="getImage" class="goods-image" />
      <view class="goods-info">
        <text class="info-item">{{ cardItem.goodsName }}</text>
        <text class="info-item">商品数量：{{ cardItem.number }}件</text>
        <text class="info-item">下单时间：{{ cardItem.exchangeTime }}</text>
        <text class="info-item" v-show="couponIntrMap"
          >{{ couponIntrMap && cardItem.goodsVrType ? couponIntrMap[cardItem.goodsVrType].name || '' : '' }}：{{
            couponIntrMap && cardItem.goodsVrType ? couponIntrMap[cardItem.goodsVrType].configDesc || '' : ''
          }}</text
        >
      </view>
    </view>
    <view class="line"></view>
    <view class="footer">
      <view class="express-number">
        <text v-if="cardItem.logisticsName && cardItem.logisticsNo && cardItem.goodsType">
          {{ cardItem.logisticsName }}: {{ cardItem.logisticsNo }}</text
        >
      </view>
      <text class="points" v-if="cardItem.goodsAmt">{{ cardItem.integral }}积分+{{ cardItem.goodsAmt }}元</text>
      <text class="points" v-else>{{ cardItem.integral }}积分</text>
    </view>
    <view class="bottom-btn" v-if="cardItem.status == '02'">
      <view class="btn-box" @click.stop="goEvaluate">去评价</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'GoodsCard',
  props: {
    cardItem: {
      type: Object,
      default: () => {},
    },
    couponIntrMap: {
      type: Object,
      default: null,
    },
  },
  computed: {
    // 商品图片路径
    getImage() {
      const str = `${process.env.VUE_APP_BASE_HOST}/pub/api/v0.1/attachs/${this.cardItem.fileId}`;
      return str;
    },
  },
  methods: {
    // 跳转到订单详情
    jumpToOrderDetail() {
      uni.navigateTo({
        url: `/pages/intergral/orderDetail/index?integralId=${this.cardItem.integralId}`,
      });
    },
    // 去评价
    goEvaluate() {
      uni.navigateTo({
        url: `/pages/intergral/evaluate/index?integralId=${this.cardItem.integralId}&goodsId=${this.cardItem.goodsId}`,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.goods-card {
  padding: 24rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  .line {
    background: #eeeeee;
    height: 1rpx;
    width: 100%;
    margin: 24rpx 0;
  }
  .header {
    display: flex;
    justify-content: space-between;
    .order-id {
      font-size: 28rpx;
      line-height: 42rpx;
      color: #9e9e9e;
    }
    .state {
      font-size: 28rpx;
      line-height: 42rpx;
      color: #1e9cff;
    }
  }
  .middle {
    display: flex;
    .goods-image {
      width: 140rpx;
      height: 140rpx;
      border-radius: 16rpx;
      margin-right: 16rpx;
    }
    .goods-info {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 8rpx;
      grid-gap: 8rpx;
      .info-item:nth-child(1) {
        font-size: 28rpx;
        font-weight: bold;
        line-height: 42rpx;
        color: #333333;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .info-item:nth-child(2),
      .info-item:nth-child(3),
      .info-item:nth-child(4) {
        font-size: 26rpx;
        line-height: 40rpx;
        color: #666666;
      }
    }
  }
  .footer {
    display: flex;
    justify-content: space-between;
    .express-number {
      font-size: 28rpx;
      line-height: 42rpx;
      color: #9e9e9e;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .points {
      font-size: 32rpx;
      font-weight: bold;
      line-height: 38rpx;
      color: #333333;
      min-width: 116rpx;
      text-align: right;
    }
  }
  .bottom-btn {
    display: flex;
    justify-content: flex-end;
    margin-top: 24rpx;
    .btn-box {
      padding: 10rpx 32rpx;
      border: 1px solid #1e9cff;
      border-radius: 100rpx;
      color: #1e9cff;
    }
  }
}
</style>
