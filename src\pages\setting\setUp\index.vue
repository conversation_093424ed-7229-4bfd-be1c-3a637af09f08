<template>
  <div class="wrap">
    <div
      class="wrap-item"
      :class="{ mt10: index == 0 }"
      v-for="(item, index) in list"
      :key="index"
      @click="toCallback(item)"
    >
      <div class="text">{{ item.title }}</div>
      <image :src="arrow" alt="" />
    </div>
    <button type="primary" class="btnOne" size="mini" @click="loginOut">退出登录</button>
    <button type="info" class="btn" size="mini" plain="true" @click="logOff">注销</button>
  </div>
</template>

<script>
export default {
  props: {},
  components: {},
  data() {
    return {
      list: [
        {
          title: '关于我们',
          methods: () => {
            uni.navigateTo({
              url: '/pages/setting/aboutUs/index',
            });
          },
        },
        {
          title: '个人中心',
          methods: () => {
            uni.navigateTo({
              url: '/pages/basic/information/index',
            });
          },
        },
        // {
        //   title: '自动续费管理',
        //   methods: () => {
        //     uni.navigateTo({
        //       url: '/pages/setting/renewalManagement/index',
        //     });
        //   },
        // },
      ],
    };
  },
  computed: {
    arrow() {
      return this.IMG_PATH + 'right_arrow.png';
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    toCallback(item) {
      item.methods && item.methods(item);
    },
    loginOut() {
      this.$store.dispatch('login/logout');
    },
    logOff() {
      this.$store.dispatch('login/logOff');
    },
  },
};
</script>

<style scoped lang="scss">
.mt10 {
  margin-top: 10rpx;
}
.wrap {
  background: rgb(244, 246, 247);
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.wrap-item {
  margin-top: 30rpx;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  padding: 30rpx 20rpx;
  box-sizing: border-box;
  border-top: 1rpx solid rgb(241, 243, 244);
  border-bottom: 1rpx solid rgb(241, 243, 244);
  image {
    width: 40rpx;
    height: 40rpx;
  }
  .text {
    color: rgb(168, 174, 181);
    font-size: 26rpx;
  }
}
.btn {
  margin: 15rpx auto;
  width: 90%;
  margin-bottom: 200rpx;
  margin-top: 40rpx;
  background: transparent;
  border: 1px solid #ec2626;
  color: #ec2626;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 10rpx;
}
.btnOne {
  margin-top: 80rpx;
  width: 90%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: rgb(0, 168, 225);
  border-radius: 10rpx;
}
</style>
