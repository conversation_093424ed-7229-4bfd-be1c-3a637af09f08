<template>
  <view class="popup-bg">
    <view class="popup-content">
      <view class="popup-title">
        {{ title }}
        <image @tap="closeModal" class="close-btn" :src="`${IMG_PATH}close.png`"></image>
      </view>
      <slot></slot>
      <slot name="footer"></slot>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {};
  },
  mixins: [],
  props: {
    title: String, // 简化的定义方式
  },
  methods: {
    closeModal: function () {
      this.$emit('closeModal', {
        detail: '',
      });
    },
  },
  created: function () {},
};
</script>
<style>
.popup-bg {
  position: fixed;
  height: 100vh;
  width: 100vw;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10;
  display: flex;
  align-items: flex-end;
}
.popup-content {
  background: #fff;
  border-radius: 16rpx 16rpx 0px 0px;
  padding: 0px 30rpx env(safe-area-inset-bottom);
  box-sizing: border-box;
  width: 100vw;
  height: 70%;
}
.popup-title {
  position: relative;
  height: 100rpx;
  text-align: center;
  line-height: 100rpx;
  color: #333;
  font-family: 'PingFang SC';
  font-size: 30rpx;
  font-style: normal;
  font-weight: 500;
  border-bottom: 2rpx solid #e1e1e1;
  box-sizing: border-box;
}
.close-btn {
  position: absolute;
  right: 0rpx;
  top: 30rpx;
  width: 40rpx;
  height: 40rpx;
}
</style>
