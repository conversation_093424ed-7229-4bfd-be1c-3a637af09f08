import { BASE_URL, OTHER_TYPE } from '../../config/global';
import Vue from 'vue';
import Request from './request';

const service = new Request();
const url = window ? (window.location.origin === process.env.VUE_APP_BASE_HOST ? BASE_URL : '') : '';
// const url = BASE_URL;
service.setConfig((config) => {
  /* 设置全局配置 */
  config.baseUrl = url;
  config.timeout = 10000;
  return config;
});
service.validateStatus = (statusCode) => {
  return statusCode === 200;
};

service.interceptor.request((config, cancel) => {
  /* 请求之前拦截器 */

  const store = Vue.prototype.$store;

  const { token, refreshToken } = store.getters['login/getToken'];

  config.header = {
    'content-type': 'application/json;charset=UTF-8',
    // minitProgramToken: `${token || localStorage.getItem('tokenNd') || ''}`, //支付宝
    minitProgramToken: uni.getStorageSync('token') || '',
    // Authorization: `Bearer ${token || localStorage.getItem('tokenNd') || ''}`,
    // Client:'h5',
    // Device:'1.0.0',
    // SysVersion:'1.0.0',
    Version: '1.0.0',
  };

  //   config.header = {
  //     ...headerOption,
  //     ...{ Authorization: `Bearer ${token || '4f85e30231c24ab18e969309d52a5c4b'}` },
  //   }
  /*
    if (!token) { // 如果token不存在，调用cancel 会取消本次请求，但是该函数的catch() 仍会执行
      cancel('token 不存在') // 接收一个参数，会传给catch((err) => {}) err.errMsg === 'token 不存在'
    }
    */
  return config;
});

const logOutCallBack = () => {
  const store = Vue.prototype.$store;
  const routes = getCurrentPages();
  const length = routes.length;

  if (
    routes[length - 1] &&
    routes[length - 1].route != 'pages/login/login' &&
    routes[length - 1].route != 'pages/new/new'
  ) {
    // #ifdef MP-ALIPAY
    const currentPage = routes[routes.length - 1]; //获取当前页面的对象
    let beforeQuery = [];

    const queryInfo = currentPage.$vm.$mp.query;
    for (const key in queryInfo) {
      const element = queryInfo[key];
      beforeQuery.push(`${key}=${element}`);
    }
    let beforePath = currentPage.route + '?' + beforeQuery.join('&');
    store.dispatch('login/setStartPath', beforePath);

    // #endif

    uni.hideLoading();
    setTimeout(() => {
      store.dispatch('login/logout');
    }, 0);
  }
  return Promise.reject();
};

const errorCallBack = async (response) => {
  const store = Vue.prototype.$store;
  const { token, refreshToken, tokenNd, refreshTokenNd } = Vue.prototype.$store.getters['login/getToken'];
  if (response.statusCode == 401) {
    // uni.showToast({
    //   title: '登录已失效，请重新进入',
    //   icon: 'none',
    // })
    setTimeout(() => {
      localStorage.removeItem('tokenNd');
    }, 2000);
    // const outBack = new Promise((resolve) => {
    //   setTimeout(function() {
    //     resolve()
    //   }, 500)
    // })
    try {
      const { token, refreshToken } = Vue.prototype.$store.getters['login/getToken'];
      if (refreshToken) {
        const info = await store.dispatch('login/getRefreshToken');
        if (info) {
          return service.request(response.config);
        } else {
          // #ifndef H5
          outBack.then(logOutCallBack).catch((e) => {});
          // #endif
          // #ifdef H5
          await store.dispatch('login/login', {
            otherType: OTHER_TYPE,
            loginType: '03',
          });
          return service.request(response.config);
          // #endif
        }
      } else {
        // if (response.config.url == '/api/v0.1/bike/info') {
        // } else {
        //     uni.reLaunch({
        //       url: '/pages/loginHt/index',
        //     })
        // }
        uni.showToast({
          title: '登录已失效，请重新进入',
          icon: 'none',
        });
        setTimeout(() => {}, 2000);
      }
    } catch (error) {
      console.log(error, 'error刷新失败');
      //  #ifdef MP-ALIPAY
      if (error.ret == 400 && error.msg == '找到多个令牌或者令牌不存在') {
        uni.reLaunch({
          url: '/pages/newHt/index',
        });
      }
      // #endif
    }
  } else if (response.statusCode == 408) {
    return Promise.reject({
      msg: '网络超时',
    });
  } else if (response.statusCode != 200) {
    // #ifdef H5

    if (response.errMsg == 'request:fail') {
      return Promise.reject({
        msg: '网络超时',
      });
    }
    // #endif
    return Promise.reject(response.data);
  }
};

service.interceptor.response(
  async (response) => {
    /* 请求之后拦截器 */

    // if (response.data.code !== 200) { // 服务端返回的状态码不等于200，则reject()
    //   return Promise.reject(response)
    // }
    // if (response.config.custom.verification) { // 演示自定义参数的作用
    //   return response.data
    // }

    // let waitCall = new Promise((resole) => {
    //     setTimeout(() => {
    //         resole()
    //     }, 0)
    // })
    try {
      // await waitCall()
      if (response.data.ret == 200 || response.statusCode == 200) {
        return response.data;
      } else if (response.data.ret == 300) {
        uni.reLaunch({
          url: '/pages/mapHt/index',
        });
        return Promise.reject(response.data);
      } else {
        return Promise.reject(response.data);
      }
    } catch (error) {
      return Promise.reject(error);
    }
  },
  async (response) => {
    // 请求错误做点什么
    return errorCallBack(response);
  }
);

export const Ajax = async (points) => {
  let params = {};
  params.url = points.url || '';
  params.method = points.method || 'GET';
  params.hideLoad = points.hideLoad || false;
  if (points.headers) {
    params.header = points.headers;
  }

  //上传图片参数
  if (points.upload) {
    params.upload = points.upload;
  }
  //超时时间
  params.timeout = points.timeout || 10000;

  if (points.baseURL) {
    params.baseUrl = points.baseURL;
  }

  params[points.method.toLowerCase() == 'get' || points.method.toLowerCase() == 'delete' ? 'params' : 'data'] =
    points.data || {};
  const store = Vue.prototype.$store;
  try {
    await store.dispatch('common/checkNetWork');
  } catch (error) {
    return Promise.reject({
      msg: '网络不可用,请稍后重试',
    });
  }

  return service.request(params);
};
