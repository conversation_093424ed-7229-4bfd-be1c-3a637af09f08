"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Cookies = void 0;

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }

var cookies =
/*#__PURE__*/
function () {
  /*初始化*/
  function cookies() {
    _classCallCheck(this, cookies);
  }
  /**
      * @param name cookie名称
      * @param value cookie值
      * @param expires 过期时间（单位：秒）
      * 设置cookie
      */


  _createClass(cookies, [{
    key: "put",
    value: function put(name, value, expires) {
      expires = (expires || 30 * 24 * 60 * 60) * 1000; //默认30天

      var exp = new Date();
      exp.setTime(exp.getTime() + expires);

      try {
        value = JSON.stringify(value);
      } catch (e) {}

      document.cookie = name + "=" + escape(value) + ";path=/;expires=" + exp.toGMTString();
    }
    /**
     * 获取cookie
     * @param name
     * @returns {null}
     */

  }, {
    key: "get",
    value: function get(name) {
      //var arr, reg = new RegExp("(^| )" + this.prefix+'_'+name + "=([^;]*)(;|$)");
      var arr,
          reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");

      if (arr = document.cookie.match(reg)) {
        var result = unescape(arr[2]);

        try {
          return JSON.parse(result);
        } catch (e) {}

        return result;
      } else {
        return null;
      }
    }
    /**
     * 删除cookie
     * @param name
     */

  }, {
    key: "remove",
    value: function remove(name) {
      var exp = new Date();
      exp.setTime(exp.getTime() - 1);
      var cval = this.get(name);

      if (cval != null) {
        document.cookie = name + "=" + cval + ";path=/;expires=" + exp.toGMTString();
      }
    }
  }, {
    key: "clear",
    value: function clear() {
      var keys = document.cookie.match(/[^ =;]+(?=\=)/g);

      if (keys) {
        for (var i = keys.length; i--;) {
          var date = new Date();
          date.setTime(date.getTime() - 1);
          document.cookie = keys[i] + "=; path=/expire=" + date.toUTCString();
        }
      }
    }
  }]);

  return cookies;
}();

var Cookies = new cookies();
exports.Cookies = Cookies;