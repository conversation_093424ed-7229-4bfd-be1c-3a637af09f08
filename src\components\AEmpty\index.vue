<template>
  <view class="AEmpty">
    <img class="empty-icon" :src="`${IMG_PATH}empty.png`" />
    <text class="none-text">{{ noneText }}</text>
  </view>
</template>

<script>
export default {
  name: 'index',
  props: {
    noneText: {
      type: String,
      default: '暂无数据',
    },
  },
};
</script>

<style scoped lang="scss">
.AEmpty {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .empty-icon {
    width: 400rpx;
    height: 400rpx;
  }
  .none-text {
    font-size: 28rpx;
    font-weight: bold;
    line-height: 42rpx;
    color: #333333;
    margin-top: 28.08rpx;
  }
}
</style>
