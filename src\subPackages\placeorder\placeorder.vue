<template>
  <view class="placeorder">
    <view class="placeorder-state">
      <view class="state-pile">{{ name }}</view>
      <!-- <view class="blue-color">{{items.equipName}}</view> -->
      <view class="state-id">
        <text>设备ID：{{ qrcode }}</text>
        <text class="fault" v-if="items.faultConditions === '01'">疑似故障</text>
        <text class="fault-icon" v-if="items.faultConditions === '01'" @click="openFaultDialog">?</text>
      </view>
      <view class="rated-main">
        <view>
          额定电压
          <text>{{ items.voltage || pageCount.voltage }}V</text>
        </view>
        <view>
          额定电流
          <text>{{ items.current || '' }}A</text>
        </view>
        <view>
          额定功率
          <text>{{ items.power || '' }}KW</text>
        </view>
      </view>
    </view>
    <!-- <view class="cost-main">
    <view wx:if='{{items.gunSubtype=="01"}}' class="cost-pile">充电桩类型：快充</view>
    <view wx:else class="cost-pile">充电桩类型：慢充</view> -->
    <!-- <view class="cost-price">当前充电价格：{{chargePrice||money}}<view class="fr gray-color"></view></view> -->
    <!-- <view class="cost-price">充电服务费：{{ fuwu }}<view class="fr gray-color"></view></view> 
  </view> -->
    <view class="recharge-amount">
      <!-- <view class="other-amount"><text>其它金额：</text><input type='digit' bindinput='change' class="fr" placeholder="{{price}}" /></view>
    <view class="amount-btn">
      <button bindtap='active' data-price='30' class="btn {{currentTab==0 ? 'active' : ''}}">30元</button>
      <button bindtap='active' data-price='50' class="btn {{currentTab==1 ? 'active' : ''}}">50元</button>
      <button bindtap='active' data-price='100' class="btn {{currentTab==2 ? 'active' : ''}}">100元</button>
    </view> -->
    </view>
    <view class="fixed_btn">
      <view class="charging_note">
        使用[电动宁德]应用充电需先充值,如需退款,请进入【我的账户】-
        <view class="refund_note" @tap.native="refund">【退款】</view>
        主动申请。如需帮助,请拨打客服电话
        <view @tap="calling" class="calling">
          <text class="call_text">{{ serviceTel }}</text>
          <image class="call_img" :src="`${IMG_PATH}p3.png`"></image>
        </view>
      </view>
      <view class="start_charge">
        <button @tap="jump" class="btn blue-bg">启动充电</button>
      </view>
    </view>
    <view class="cars">
      <view class="Carhav">
        <view class="Carhav-left commons">
          当前单价：
          <!--          会员价-->
          <text v-if="vipFlag">{{ vipPrice }}</text>
          <!--          原价-->
          <text v-else>{{ fuwu }}</text>
          <!-- <button>全天</button> -->
        </view>
        <view @tap="jiage" class="Carhav-right">
          价格详情
          <text class="icon iconfont icon-arrow_right" size="20"></text>
        </view>
      </view>
    </view>

    <view class="cars">
      <view @tap="coupon" class="Carhav">
        <view class="Carhav-left Carhav-lefts">
          <image :src="`${IMG_PATH}z4.png`"></image>
          优惠券
        </view>
        <view class="Carhav-right carsIcon">
          <text v-if="haveYHJ">自动匹配优惠券</text>
          <text v-else>暂无可用优惠券</text>
          <!-- <icon class="icon iconfont icon-arrow_right" size="20"></icon> -->
        </view>
      </view>
    </view>

    <view class="cars carNop">
      <view class="Carhav">
        <view class="Carhav-left Carhav-fonts">
          <text v-if="haveYHJ"
            >在有满足使用条件优惠券的情况下，系统将自动为您匹配优惠比例最大的优惠券进行金额结算。</text
          >
          <text v-else>暂无可用优惠券</text>
        </view>
      </view>
    </view>
    <div class="receive-card" v-if="showCard">
      您还有优惠券待领取
      <div class="btn" @click="jumpCup">去领取</div>
      <div class="close" @click="() => (showCard = false)">X</div>
    </div>
    <view class="payMes">
      <view class="payMes_head">
        <image :src="`${IMG_PATH}z2.png`"></image>
        账户余额
        <text v-if="payNo">(余额不足)</text>
      </view>
      <view class="payMes_bottom">
        <view class="balan">账户余额</view>
        <view class="balance">
          {{ yueP }}
          <text>元</text>
          <button @tap="chongzhi">充值</button>
        </view>
      </view>
    </view>

    <view v-if="parkingSyaFlag" class="cars">
      <view v-if="nohave" @tap="ads" class="Carbtn">新增车辆</view>
      <view v-else class="Carhav">
        <view class="Carhav-left">
          <image :src="`${IMG_PATH}z1.png`"></image>
          我的爱车
        </view>
        <view @click="maskT" class="Carhav-right">
          {{ licenseNo }}
          <text class="icon iconfont icon-arrow_down" size="20"></text>
        </view>
      </view>
    </view>

    <view v-else class="cars">
      <view class="Carbtn">暂无可选车辆</view>
    </view>
    <!-- <view wx:if="{{parkingSyaFlag}}" class='cars-explain'>
    <view class='cars-explain-top'>停车费说明</view>
    <view class='cars-explain-msg'>{{parkPrice}}</view>
  </view> -->
    <view v-if="mask" @tap="maskRmove" class="mask"></view>
    <view v-if="mask" class="mask-alers">
      <view class="mask-alers-flex">
        <view>我的爱车</view>
        <view v-if="sh" @tap="ads">十 新增</view>
        <view v-else></view>
      </view>

      <view class="mask-list" v-for="(item, index) in list" :key="index">
        <view class="mask-list-li">
          <view class="mask-list-lf">{{ item.licenseNo }}</view>
          <view
            @click="actives"
            :data-msg="item.licenseNo"
            :data-index="index"
            :data-num="item.types"
            class="mask-list-rt"
          >
            <!-- #ifdef MP-WEIXIN -->
            <i
              :class="'icon iconfont changes icon-checked:' + (item.types == true ? 'icon-checked' : 'icon-unchecked')"
              size="10"
            ></i>
            <!-- #endif -->
            <!-- #ifdef H5 -->
            <image v-if="!item.types" :src="`${IMG_PATH}icon-radio-blue.png`" mode="scaleToFill" />
            <image v-if="item.types" :src="`${IMG_PATH}icon-battery.png`" mode="scaleToFill" />
            <!-- #endif -->
          </view>
          <!-- <view bindtap='remove' data-prefId="{{item.prefId}}" class='x'>X</view> -->
        </view>
      </view>
    </view>

    <view class="charging">
      <view class="charging_head">
        <view class="head">
          <image :src="`${IMG_PATH}p7.png`"></image>
        </view>
        <view class="middles">
          <view class="middle_type">停车信息</view>
        </view>
      </view>
      <view class="charging_middles">
        <view class="middle_list">
          <text>{{ parkPrice }}</text>
        </view>
      </view>
    </view>
    <!-- <view wx:if='{{aletMsg}}' class='aletMsg'></view>
<view wx:if='{{aletMsg}}' class='count'>
  <view class='count_head'>充电桩正在启动</view>
  <view class='count_msg'>此过程预计60s内完成</view>
  <view class='count_net'>请耐心等待</view>
  <view bindtap='djs' class='pop'>
    <view>{{cup}}</view>
  </view>
</view> -->
    <!-- <modal
      style="z-index: 9999"
      v-if="!hidden"
      :title="tit"
      :confirm-text="zuo"
      :cancel-text="you"
      @cancel="cancel"
      @confirm="confirm"
      :no-cancel="nocancel"
    ></modal> -->
    <uni-popup ref="popup">
      <view class="popup-box">
        <view class="popup-title">{{ tit }}</view>
        <view class="btns">
          <view @click="cancel" class="cancel-btn">{{ you }}</view>
          <view @click="confirm" class="confirm-btn">{{ zuo }}</view>
        </view>
      </view>
    </uni-popup>

    <!-- <modal style="z-index:9999" hidden="{{alet}}" confirm-text="{{LF}}" cancel-text="{{RT}}" bindcancel="tijia" bindconfirm="txing" no-cancel="{{nocancel}}">
    您还未添加充电车辆，无法享受免费停车优惠
  </modal> -->

    <!-- <modal
      style="z-index: 9999"
      v-if="!alet"
      :confirm-text="RT"
      :cancel-text="LF"
      @cancel="txing"
      @confirm="tijia"
      :no-cancel="nocancel"
      >请添加充电车辆信息</modal
    > -->
    <uni-popup ref="popupAlet">
      <view class="popup-box">
        <!-- <view class="popup-title">请添加充电车辆信息</view> -->
        <view style="text-align: center; letter-spacing: 2rpx; line-height: 1.5; color: gray">请添加充电车辆信息</view>
        <view class="btns">
          <view @click="txing" class="cancel-btn">{{ LF }}</view>
          <view @click="tijia" class="confirm-btn">{{ RT }}</view>
        </view>
      </view>
    </uni-popup>

    <!-- <modal
      style="z-index: 9999"
      title="余额不足"
      v-if="!modePrice"
      @cancel="clocse"
      @confirm="chongzhi"
      :confirm-text="modeLf"
      :cancel-text="modeRT"
    >
      您当前余额不足{{ promptBalance }}元，充电过程中可能因余额不足导致充电结束
    </modal> -->
    <uni-popup ref="popupModePrice">
      <view class="popup-box">
        <view class="popup-title">余额不足</view>
        <view style="text-align: center; letter-spacing: 2rpx; line-height: 1.5; color: gray"
          >您当前余额不足{{ promptBalance }}元，充电过程中可能因余额不足导致充电结束</view
        >
        <view class="btns">
          <view @click="clocse" class="cancel-btn">{{ modeRT }}</view>
          <view @click="chongzhi" class="confirm-btn">{{ modeLf }}</view>
        </view>
      </view>
    </uni-popup>

    <!-- <modal
      style="z-index: 9999"
      title="余额不足"
      v-if="!modeToPrice"
      @confirm="chongzhi"
      confirm-text="去充值"
      :no-cancel="true"
    >
      您当前余额不足{{ availableBalance }}元，无法启动充电，请您预先充值
    </modal> -->
    <uni-popup ref="popupModeToPrice">
      <view class="popup-box">
        <view class="popup-title">余额不足</view>
        <view style="text-align: center; letter-spacing: 2rpx; line-height: 1.5; color: gray"
          >您当前余额不足{{ availableBalance }}元，无法启动充电，请您预先充值</view
        >
        <view class="btns">
          <view @click="chongzhi" class="ok-btn">去充值</view>
        </view>
      </view>
    </uni-popup>

    <!-- <modal
      style="z-index: 9999"
      title="插枪提示"
      v-if="!chaqiang"
      @confirm="alcom"
      @cancel="clocseC"
      confirm-text="已插枪,启动充电"
      cancel-text="取消"
    >
      当前检测枪状态为未插枪，请确认是否已插枪
    </modal> -->
    <uni-popup ref="popupChaqiang">
      <view class="popup-box">
        <view class="popup-title">插枪提示</view>
        <view style="text-align: center; letter-spacing: 2rpx; line-height: 1.5; color: gray"
          >当前检测枪状态为未插枪，请确认是否已插枪</view
        >
        <view class="btns">
          <view @click="clocseC" class="cancel-btn">取消</view>
          <view @click="alcom" class="confirm-btn">已插枪,启动充电</view>
        </view>
      </view>
    </uni-popup>

    <loading v-if="!hiddenLoading">{{ Msg }}</loading>
  </view>
</template>

<script>
import { subscribeMessage } from '@/utils/messageCode.js';
import {
  getUserInfo,
  getAccountsCoupons,
  getChargingInfoByGunId,
  getMsgUnused,
  getPref,
  submitOrder,
  getChargingInfo,
  geGunInfo,
  deletePref,
} from '@/services/index.js';
import { makePhoneCall } from '@/utils/index.js';
import { ENVR } from '@/config/global';
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup';
import uniPopupDialog from '@/components/uni-ui/uni-popup-dialog/uni-popup-dialog';
export default {
  components: { uniPopup, uniPopupDialog },
  data() {
    return {
      receiveCpnList: [],
      pageCount: {
        voltage: '',
      },

      // chargePrice:'',
      accPayCustAmt: '',

      custType: '01',
      god: true,
      sh: true,
      payNo: false,
      fuwu: '',
      LF: '取消',
      RT: '添加车辆',
      tit: '您确定要删除该车辆吗？',
      zuo: '确定',
      you: '取消',
      hidden: true,
      money: '',
      token: '',
      items: '',
      price: '30',
      currentTab: 0,
      gunId: '',
      name: '',
      hiddenLoading: true,
      alet: true,
      mask: false,
      Msg: '加载中..',
      parkingSyaFlag: true,
      parkPrice: '',
      nohave: true,
      list: [],
      prefid: '',
      licenseNo: '',
      licenseNoOne: '',
      gunIdd: '',
      named: '',
      noCars: false,
      modePrice: true,
      modeLf: '去充值',
      modeRT: '继续充电',
      modeToPrice: true,
      yueP: '',
      orderNo: '',
      typeDown: false,
      stationId: '',
      city: '',
      cha: 0,
      chaqiang: true,
      cup: 60,
      cupType: false,
      aletMsg: false,
      youhuiID: '',
      haveYHJ: true,
      cpnAmt: '暂无优惠券可用',
      availableBalance: '',
      promptBalance: '',
      qrcode: '',
      serviceTel: '0593-2059687',
      a: false,
      z: false,
      nocancel: '',
      vipPrice: '',
      vvipFlag: false,
      vipFlag: false,
      showCard: false,
    };
  },
  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    var that = this;
    that.setData({
      modeToPrice: true,
      modePrice: true,
      // cpnAmt: wx.getStorageSync('cpnAmt') || '',
      // youhuiID: wx.getStorageSync('youhuiID') || '',
      availableBalance: uni.getStorageSync('availableBalance') || '5',
      promptBalance: uni.getStorageSync('promptBalance') || '10',
    });
    that.$refs.popupModePrice.close();
    that.$refs.popupModeToPrice.close();

    this.getUserInfo(false);
    that.foundCars();
    var parmeType = {
      useStatus: '01',
      pageNum: '1',
      totalNum: '100',
      stationId: that.stationId,
      city: that.city,
      amount: '99999',
    };
    const [, res] = await getAccountsCoupons(parmeType);
    if (res) {
      console.log(res, '优惠券111111');
      let now = new Date();
      let cha = null;
      let result = null;
      if (res.queryList.length > 0) {
        res.queryList.forEach((item) => {
          let expDate = new Date(item.expDate.replace(/-/g, '/') + ' 23:59:59');
          let CHA = expDate - now;
          if (CHA > 0) {
            if (!cha || !result || CHA < cha) {
              cha = CHA;
              result = item;
            } else if (CHA === cha) {
              if (
                Number(result.cpnAmt.substr(0, result.length - 1)) <
                Number(item.cpnAmt.substr(0, item.cpnAmt.length - 1))
              ) {
                result = item;
              }
            }
          }
        });
        console.log(result, '优先级 优惠券');
        var cpnId;
        var cpnAmt;
        cpnAmt = result.cpnName;
        cpnId = result.cpnId;

        // if (wx.getStorageSync('youhuiID') || '')
        var cpnIds = uni.getStorageSync('youhuiID') || cpnId;
        var cpnAmts = uni.getStorageSync('cpnName') || cpnAmt;
        console.log(cpnIds, '111', uni.getStorageSync('youhuiID'), '222');
        console.log(cpnAmts, '333', uni.getStorageSync('cpnName'), '444');
        if (uni.getStorageSync('youhuiID') == 0 && uni.getStorageSync('cpnName') == '不使用优惠券') {
          var cpnIds = '';
          var cpnAmts = '';
          that.setData({
            haveYHJ: true,
            youhuiID: '',
            cpnAmt: '不使用优惠券',
          });
        } else {
          that.setData({
            haveYHJ: true,
            youhuiID: cpnIds,
            cpnAmt: '存在【' + cpnAmts + '】优惠券可使用',
          });
        }
        uni.setStorageSync('youhuiID', cpnIds);
        uni.setStorageSync('cpnAmt', cpnAmts);
        uni.setStorageSync('cpnName', cpnAmts);
      } else {
        var cpnId;
        var cpnAmt;
        cpnAmt = '';
        cpnId = '';
        that.setData({
          haveYHJ: false,
          youhuiID: cpnId,
          cpnAmt: cpnAmt,
        });
        uni.setStorageSync('youhuiID', '');
        uni.setStorageSync('cpnAmt', '');
      }
    }
    this.getCoupons();
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    // console.log(options, '上个页面传参。gunId');
    var that = this;
    if (!options.titles) {
      // if (options.msg.replace(/.*"name":"(\d+).*/, "$1")!='扫码来的'){
      console.log('当前不是扫码来的');
      var data = options.msg ? JSON.parse(options.msg) : {};
      console.log(data, '当前不是扫码来的7778');
      var gunId = data.gunId || data.msg;
      var qrcode = data.qrcode;
      var name = data.name;
      var city = data.city;
      var stationId = data.stationId;
      var danjia = data.danjia;
      var that = this;
      that.setData({
        gunIdd: gunId,
        gunId: gunId,
        qrcode: qrcode,
        named: name,
        city: city,
        stationId: stationId,
      });
      uni.setStorageSync('nameCup', name);
      var params = { gunId: gunId };
      this.geGunInfo(params, { 'content-type': 'application/x-www-form-urlencoded' });
    } else {
      console.log(options, '当前为扫码跳转');
      var gunId = options.msg;
      that.setData({
        gunId: gunId,
        qrcode: gunId,
        gunIdd: gunId,
        named: 'name',
      });
      var params = {
        qrCode: gunId,
      };
      const [, res] = await getChargingInfoByGunId(params);
      if (res) {
        console.log(res, '充电数据查询面板');
        if (res.ret == 400 || res.ret == 406) {
          that.setData({
            Msg: res.msg,
            hiddenLoading: false,
          });
          // wx.showToast({
          //   icon: 'loading',
          //   title: res.msg,
          // })
          setTimeout(function () {
            uni.navigateBack();
          }, 1500);
          return false;
          // setTimeout(function() {
          //   wx.redirectTo({
          //     url: '../codecharge/codecharge'
          //   })
          // }, 2000)
        } else {
          that.setData({
            items: res,
            pageCount: res,
            gunId: res.gunId,
            name: res.displayGunName,
            stationId: res.stationId,
            city: res.city,
            vipPrice: res.defAmt.vipPrice || 0,
            fuwu: res.defAmt.priceRemark || 0,
            vipFlag: res.defAmt.vipFlag,
            vvipFlag: res.defAmt.vvipFlag,
          });
          uni.setStorageSync('nameCup', name);
          that.setData({
            parkPrice: res.parkPrice,
          });
          that.foundCars();
          this.getUserInfo();
        }
        console.log(res, '订单信息--扫码');
        var parmeType = {
          // getChannel: '02',
          useStatus: '01',
          pageNum: '1',
          totalNum: '100',
          stationId: that.stationId,
          city: that.city,
          amount: '99999',
        };
        that.getAccountsCouponsInfo(parmeType);
      }
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    uni.removeStorageSync('youhuiID');
    console.log(uni.getStorageSync('youhuiID'), '离开页面的事后查询youhuiID');
    uni.removeStorageSync('cpnAmt');
    uni.removeStorageSync('cpnName');
    console.log(uni.getStorageSync('cpnAmt'), '离开页面的事后查询cpnAmt');
    console.log(uni.getStorageSync('cpnName'), '离开页面的事后查询cpnName');
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
  methods: {
    //获取用户信息-并判断金额(根据传参来判断是否走该部分逻辑)
    async getUserInfo(flag = false) {
      const that = this;
      const [, res] = await getUserInfo();
      if (res) {
        this.yueP = res.accFreeAmt;
        if (Number(res.accPayCustAmt) < Number(that.availableBalance)) {
          console.log('余额不足,小于', that.availableBalance, '元');
          that.setData({
            modeToPrice: false,
          });
          that.$refs.popupModeToPrice.open();
          return false;
        } else if (Number(res.accPayCustAmt) < Number(that.promptBalance)) {
          console.log('余额不足,小于', that.promptBalance, '元111');
          that.setData({
            modePrice: false,
          });
          that.$refs.popupModePrice.open();
        } else {
          console.log('余额足够，大于2元');
        }
        if (flag) {
          this.accPayCustAmt = res.accPayCustAmt;
        }
      }
    },
    // 获取订单信息
    async geGunInfo(params) {
      const that = this;
      console.log(params, 'params');
      const [, res] = await geGunInfo(params);
      if (res) {
        console.log(res, '订单信息');
        console.log(res.voltage, '这是电压的值');
        that.setData({
          items: res,
          fuwu: res.priceRemark || 0,
          vipFlag: res.vipFlag,
          // gunId: res.gunId,
        });
        this.getUserInfo();
        that.setData({
          parkPrice: res.parkPrice,
        });
        that.foundCars();
      }
    },
    async getAccountsCouponsInfo(params) {
      const [, res] = await getAccountsCoupons(params);
      if (res) {
        console.log(res, '优惠券111111');
        let now = new Date();
        let cha = null;
        let result = null;
        if (res.queryList.length > 0) {
          res.queryList.forEach((item) => {
            let expDate = new Date(item.expDate.replace(/-/g, '/') + ' 23:59:59');
            let CHA = expDate - now;
            if (CHA > 0) {
              if (!cha || !result || CHA < cha) {
                cha = CHA;
                result = item;
              } else if (CHA === cha) {
                if (
                  Number(result.cpnAmt.substr(0, result.length - 1)) <
                  Number(item.cpnAmt.substr(0, item.cpnAmt.length - 1))
                ) {
                  result = item;
                }
              }
            }
          });
          console.log(result, '优先级 优惠券');
          var cpnId;
          var cpnAmt;
          cpnAmt = result.cpnName;
          cpnId = result.cpnId;
          that.setData({
            haveYHJ: true,
            youhuiID: cpnId,
            cpnAmt: cpnAmt,
          });
          uni.setStorageSync('youhuiID', cpnId);
          uni.setStorageSync('cpnAmt', cpnAmt);
        } else {
          var cpnId;
          var cpnAmt;
          console.log('没有优惠券 不知道会不会报错');
          console.log('22222');
          cpnAmt = '暂无优惠券可用';
          cpnId = '';
          that.setData({
            haveYHJ: false,
            youhuiID: cpnId,
            cpnAmt: cpnAmt,
          });
          uni.setStorageSync('youhuiID', '');
          uni.setStorageSync('cpnAmt', '');
        }
      }
    },
    jumpCup() {
      uni.navigateTo({
        url: '/subPackages/gocup/gocup',
      });
    },
    // 获取是否有可以领取的优惠券
    async getCoupons() {
      var that = this;
      var parmeType = {
        prodBusiType: '02',
      };
      const [, res] = await getMsgUnused(parmeType);
      if (res) {
        this.receiveCpnList = res.cpnList;
        if (this.receiveCpnList.length > 0) this.showCard = true;
      }
    },
    clocse() {
      this.setData({
        modePrice: true,
      });
      this.$refs.popupModePrice.close();
    },

    clocseC() {
      this.setData({
        chaqiang: true,
      });
      this.$refs.popupChaqiang.close();
    },

    alcom() {
      //已插枪 启动充电
      this.setData({
        cha: 1,
        chaqiang: true,
      });
      this.$refs.popupChaqiang.close();
      this.tr();
    },

    jiage() {
      uni.navigateTo({
        url: '/pages/basic/standard/standard?stationId=' + this.stationId,
      });
    },

    coupon() {
      var that = this;
      uni.setStorageSync('cityPont', that.city);
      uni.setStorageSync('stationIdPint', that.stationId);
      uni.navigateTo({
        url: '/subPackages/coupon/coupon',
      });
    },

    DB() {},

    djs() {
      let that = this;
      var msg = that.cup;
      that.setData({
        aletMsg: true,
      });
      that.times = setInterval(function () {
        that.setData({
          cup: that.cup - 1,
        });
        if (that.cupType) {
          console.log('在60s内 启动充电成功');
          that.setData({
            cup: 60,
            aletMsg: false,
          });
          clearInterval(that.times);
        } else if (that.cup == 0 && that.cupType == false) {
          console.log('启动充电失败。');
          uni.showToast({
            icon: 'loading',
            title: '启动失败',
          });
          that.setData({
            cup: 60,
            aletMsg: false,
          });
          clearInterval(that.times);
        }
      }, 1000);
    },

    active(event) {
      console.log(event.target.dataset.price);
      var that = this;
      if (event.target.dataset.price == 30) {
        that.setData({
          currentTab: 0,
        });
      } else if (event.target.dataset.price == 50) {
        that.setData({
          currentTab: 1,
        });
      } else {
        that.setData({
          currentTab: 2,
        });
      }
      that.setData({
        price: event.target.dataset.price,
      });
    },

    maskT() {
      var that = this;
      console.log(that.custType, 'this is custType');
      if (that.custType == '01') {
        //个人用户
        console.log('//个人用户');
        that.setData({
          mask: true,
        });
      } else if (that.custType == '02') {
        console.log('//商户');
        uni.navigateTo({
          url: '/subPackages/shcar/shcar?licenseNo=' + that.licenseNo,
        });
      }
    },

    maskRmove() {
      this.setData({
        mask: false,
      });
    },

    change(value) {
      console.log(value.detail.value);
      var that = this;
      that.setData({
        price: value.detail.value,
      });
    },

    ads() {
      uni.navigateTo({
        url: '/subPackages/add/add',
      });
    },

    actives(e) {
      console.log(e.currentTarget.dataset.index, '索引');
      console.log(e.currentTarget.dataset.num, '点击状态');
      var that = this;
      var licenseNo = e.currentTarget.dataset.msg;
      console.log(licenseNo, 'this is licenseNo');
      var index = e.currentTarget.dataset.index;
      for (var i = 0; i < that.list.length; i++) {
        var a = 'list[' + i + '].types';
        that.setData({
          [a]: false,
        });
      }
      var z = 'list[' + index + '].types';
      that.setData({
        [z]: true,
        licenseNo: licenseNo,
        mask: false,
      });
      console.log(that.licenseNo, '车牌号');
    },

    cancel() {
      this.$refs.popup.close();
      this.setData({
        hidden: true, //关闭弹框
      });
      console.log('不取消');
    },

    tijia() {
      this.$refs.popupAlet.close();
      this.setData({
        alet: true,
      });
      this.ads();
      //跳转到添加车辆页面
    },

    async confirm() {
      var that = this;
      console.log('确定取消????  删除？？？？');
      var prefid = that.prefid;
      var params = {
        prefId: prefid,
      };
      const [, res] = await deletePref(params);
      if (res) {
        console.log(res, '删除成功');
        that.foundCars();
      }
      that.setData({
        hidden: true, //关闭弹框
      });
      that.$refs.popup.close();
    },
    // 订阅消息
    subscribeMessage(callback) {
      // return subscribeMessage(['充电开始通知', '余额不足提醒', '充电结算通知'], callback);
      return subscribeMessage(['充电结束通知'], callback);
    },
    async tr() {
      var that = this;
      var price = that.price;
      var gunId = that.gunId;
      var licenseNo = that.licenseNo || '';
      var cpnId = uni.getStorageSync('youhuiID') || '';
      console.log(licenseNo, '订单提交时候的车牌号');
      console.log(cpnId, '订单提交时候的cpnId');
      var ifForce;
      if (that.cha == 0) {
        ifForce = 0; //第一次下单确认是否插枪
      } else if (that.cha == 1) {
        ifForce = 1; //确认是否插枪 强制
      }

      if (ENVR === 'wx') {
        var params = {
          applyMode: '07',
          equipId: gunId,
          tariffType: '03',
          licenseNo: licenseNo,
          ifForce: ifForce,
          reqType: '07',
          // cpnId: cpnId,
          // autoCpn: that.haveYHJ ? 2 : 0, // 有优惠券：2，没有优惠券：0
          autoCpn: 2,
        };
        that.subscribeMessage(async () => {
          that.setData({
            hiddenLoading: false,
          });
          const [err, res] = await submitOrder(params, {
            // #ifdef H5
            'content-type': 'application/x-www-form-urlencoded',
            // #endif
          });
          that.setData({
            hiddenLoading: true,
          });
          if (res) {
            if (res.ret == 200) {
              that.setData({
                orderNo: res.orderNo,
              });
              uni.setStorage({
                key: 'price',
                data: that.price,
              });
              //启动充电
              uni.removeStorageSync('licenseNoOint');
              // _self.just()
              uni.redirectTo({
                url: '/pages/basic/charge/charge?orderNo=' + res.orderNo + '&types=1',
              });
            }
          }
          if (err) {
            if (err.ret == 400) {
              console.log('0000000不能用啊啊啊啊啊啊啊啊啊啊啊啊啊啊');
              that.setData({
                Msg: err.msg || '设备不可用',
              });
              setTimeout(function () {
                uni.navigateBack();
              }, 2000);
            } else if (err.ret == 1004) {
              that.setData({
                chaqiang: false,
              });
              that.$refs.popupChaqiang.open();
            } else if (err.ret == 402) {
              uni.showModal({
                title: '提示',
                showCancel: false,
                content: err.msg || '您当前账户本金不足1元，无法启动充电，请您预先充值',
                success(result) {
                  if (result.confirm) {
                    console.log('用户点击确定');
                  } else if (result.cancel) {
                    console.log('用户点击取消');
                  }
                },
              });
            }
          }
        });
      } else {
        that.setData({
          hiddenLoading: false,
        });
        var params = {
          applyMode: '01',
          equipId: gunId,
          tariffType: '03',
          licenseNo: licenseNo,
          ifForce: ifForce,
          // autoCpn: that.haveYHJ ? 2 : 0, // 有优惠券：2，没有优惠券：0
          autoCpn: 2,
        };
        const [err, res] = await submitOrder(params, {
          // #ifdef H5
          'content-type': 'application/x-www-form-urlencoded',
          // #endif
        });
        console.log(res, '订单提交');
        if (res) {
          if (res.ret == 200) {
            that.setData({
              orderNo: res.orderNo,
              hiddenLoading: true,
            });
            uni.setStorage({
              key: 'price',
              data: that.price,
            });
            //启动充电
            uni.removeStorageSync('licenseNoOint');
            // _self.just()
            uni.redirectTo({
              url: '/pages/basic/charge/charge?orderNo=' + res.orderNo + '&types=1',
            });
          }
        }
        if (err) {
          if (err.ret == 400) {
            console.log('0000000不能用啊啊啊啊啊啊啊啊啊啊啊啊啊啊');
            that.setData({
              Msg: err.msg || '设备不可用',
            });
            setTimeout(function () {
              uni.navigateBack();
            }, 2000);
          } else if (err.ret == 1004) {
            that.setData({
              chaqiang: false,
              hiddenLoading: true,
            });
            that.$refs.popupChaqiang.open();
          } else if (err.ret == 402) {
            that.setData({
              hiddenLoading: true,
            });
            uni.showModal({
              title: '提示',
              showCancel: false,
              content: err.msg || '您当前账户本金不足1元，无法启动充电，请您预先充值',
              success(result) {
                if (result.confirm) {
                  console.log('用户点击确定');
                } else if (result.cancel) {
                  console.log('用户点击取消');
                }
              },
            });
          }
        }
      }
    },

    txing() {
      //继续提交
      this.setData({
        alet: true,
      });
      this.$refs.popupAlet.close();
      // this.tr()
    },

    remove(e) {
      console.log(e);
      var prefid = e.target.dataset.prefid;
      this.setData({
        hidden: false,
        prefid: prefid,
      });
      that.$refs.popup.open();
    },

    jump() {
      console.log(this.noCars, 'true就是没车');
      this.tr();
    },

    chongzhi() {
      console.log('点击了去充值');
      uni.navigateTo({
        url: '/subPackages/recharge/recharge',
      });
    },

    async foundCars() {
      var that = this;
      const [, res] = await getPref();
      if (res) {
        console.log(res, '我的爱车');
        console.log(res.custType, ' 01是个人  02是企业');
        that.setData({
          custType: res.custType,
        });
        console.log(res.carList.length, '我的爱车数量');

        // var as =  res.carList.findIndex(item => item.licenseNo === '沪555555')

        if (res.carList.length > 0) {
          if (uni.getStorageSync('licenseNoOint')) {
            console.log('是商户 且选择了车牌了');
            that.setData({
              list: res.carList,
              nohave: false,
              licenseNo: uni.getStorageSync('licenseNoOint'),
              noCars: false,
            });
          } else {
            console.log('不是商户 且没选择车牌');
            that.setData({
              list: res.carList,
              nohave: false,
              licenseNo: res.carList[0].licenseNo,
              noCars: false,
            });
          }
          var z = 'list[0].types';
          that.setData({
            [z]: true,
          });
        } else {
          that.setData({
            nohave: true,
            mask: false,
            noCars: true,
          });
        }
        if (res.custType == '01') {
          that.setData({
            parkingSyaFlag: true,
          });
        } else if (res.custType == '02' && res.carList.length > 0) {
          console.log('企业用户且有车');
          that.setData({
            sh: false,
          });
        } else if (res.custType == '02' && res.carList.length == 0) {
          console.log('企业用户却没车');
          that.setData({
            parkingSyaFlag: false,
            sh: false,
          });
        }
      }
    },

    //客服电话
    calling() {
      makePhoneCall(this.serviceTel);
    },

    //退款
    refund() {
      uni.navigateTo({
        url: '/subPackages/refund/refund',
      });
    },

    async init() {
      var that = this;
      console.log('这里是循环圈');
      var orderNo = that.orderNo;
      var parmes = {
        orderNo: orderNo,
      };
      const [, res] = await getChargingInfo(parmes);
      if (res) {
        console.log(res, '充电详情');
        if (res.chargeStatus == '01') {
          //未开始
          console.log('当前未开始。');
        } else if (res.chargeStatus == '02') {
          //充电中
          console.log('当前正在充电。');
          that.setData({
            typeDown: true,
            // hiddenLoading:true,
            cupType: true,
          });
          uni.redirectTo({
            url: '/pages/basic/charge/charge?orderNo=' + that.orderNo,
          });
        } else if (res.chargeStatus == '03') {
          //已完成
          console.log('当前已完成。');
          that.setData({
            typeDown: true,
            // hiddenLoading: true
          });
          uni.redirectTo({
            url: '/pages/basic/charge/charge?orderNo=' + that.orderNo,
          });
        }
      }
    },
    // 打开疑似故障弹框
    openFaultDialog() {
      uni.showModal({
        title: '提示',
        content: '该充电桩疑似故障，若无法启动充电，请更换其他充电桩充电！',
        showCancel: false,
      });
    },
  },
};
</script>
<style>
@import './placeorder.css';
</style>
<style scoped lang="scss">
.receive-card {
  width: 100%;
  background-color: #fdecd8;
  color: #f79f4d;
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 30rpx;
  .btn {
    margin-left: auto;
    color: #fff;
    background: #c8723c;
    font-size: 28rpx;
    padding: 5rpx 10rpx;
    box-sizing: border-box;
    border-radius: 5rpx;
  }
  .close {
    margin-left: 20rpx;
  }
}
.popup-box {
  background: #fff;
  border-radius: 24rpx;
  width: 80vw;
  .popup-title {
    font-size: 36rpx;
    width: 100%;
    text-align: center;
    margin: 24rpx 0;
    padding: 24rpx 0;
  }
  .btns {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 24rpx;
    border-top: 1rpx solid #cbcbcb;
    .ok-btn {
      font-size: 32rpx;
      font-weight: bold;
      color: rgb(84, 179, 255);
      width: 100%;
      box-sizing: border-box;
      padding: 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-left: 1rpx solid #cbcbcb;
    }
    .cancel-btn {
      font-size: 32rpx;
      font-weight: bold;
      width: 49%;
      box-sizing: border-box;
      padding: 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-right: 1rpx solid #cbcbcb;
    }
    .confirm-btn {
      font-size: 32rpx;
      font-weight: bold;
      color: rgb(84, 179, 255);
      width: 49%;
      box-sizing: border-box;
      padding: 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-left: 1rpx solid #cbcbcb;
    }
  }
}
</style>
