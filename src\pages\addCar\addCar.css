.addCar-msg {
  width: 100%;
  height: 343rpx;
  background: #fff;
  box-sizing: border-box;
  margin-top: 20rpx;
}
.addCar-msg .message {
  width: 90%;
  height: 255rpx;
  margin: 0 auto;
  box-sizing: border-box;
  display: flex;
  border-bottom: 2rpx solid #ccc;
}
.addCar-msg .message .message-left {
  width: 268rpx;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.addCar-msg .message .message-left image {
  width: 268rpx;
  height: 128rpx;
}
.addCar-msg .message .message-right {
  height: 100%;
  width: 400rpx;
  margin-left: 18rpx;
  display: flex;
  align-content: space-around;
  flex-wrap: wrap;
  box-sizing: border-box;
  padding: 30rpx 20rpx;
}
.addCar-msg .message .message-right view {
  width: 100%;
  line-height: 30rpx;
  font-size: 26rpx;
  color: #666666;
}
.addCar-msg .message .message-right .message-text {
  width: auto;
}
.addCar-msg .message .message-right .message-text text {
  color: #333;
  font-size: 24rpx;
  height: 40rpx;
  background: #eee;
  display: flex;
  text-align: center;
  align-items: center;
  padding: 5rpx;
  box-sizing: border-box;
  border-radius: 4rpx;
}
.addCar-msg .message .message-right .message-name {
  color: #333;
}
.addCar-msg .addCar-delet {
  width: 90%;
  height: 87rpx;
  /* background: red; */
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: 0 30rpx;
}
.addCar-msg .addCar-delet view {
  font-size: 26rpx;
  color: #666;
  display: flex;
  align-items: center;
}
.addCar-msg .addCar-delet view icon {
  font-size: 40rpx;
  display: flex;
  margin-right: 20rpx;
}
.fr {
  padding-bottom: -1rpx;
}
.addCar-msg .addCar-delet view image {
  width: 40rpx;
  height: 37rpx;
  margin-right: 15rpx;
}
.alls {
  padding-bottom: 88rpx;
}
.add-button {
  width: 100%;
  height: 80rpx;
  box-sizing: border-box;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}

.add-button .add-button-bt {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 28rpx;
}
