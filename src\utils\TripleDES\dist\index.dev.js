"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;

var _encUtf = _interopRequireDefault(require("crypto-js/enc-utf8"));

var _encBase = _interopRequireDefault(require("crypto-js/enc-base64"));

var _modeEcb = _interopRequireDefault(require("crypto-js/mode-ecb"));

var _padPkcs = _interopRequireDefault(require("crypto-js/pad-pkcs7"));

var _tripledes = _interopRequireDefault(require("crypto-js/tripledes"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }

var initKey = '+7+hkq4l97VMgGHTufKDEHzfH8FzQ0aw';

var TripleDES =
/*#__PURE__*/
function () {
  function TripleDES() {
    _classCallCheck(this, TripleDES);
  }

  _createClass(TripleDES, [{
    key: "readKey",
    value: function readKey(keyStr) {
      return _encBase["default"].parse(keyStr);
    }
    /**
     * 加密
    */

  }, {
    key: "encrypt",
    value: function encrypt(message) {
      var messageJson = message;

      if (typeof message != 'string') {
        try {
          messageJson = JSON.stringify(message);
        } catch (error) {}
      }

      var messageFormat = _encUtf["default"].parse(messageJson);

      var keyHex = this.readKey(initKey);

      var encrypted = _tripledes["default"].encrypt(messageFormat, keyHex, {
        'mode': _modeEcb["default"],
        'padding': _padPkcs["default"]
      });

      return encrypted.toString();
    }
    /**
     * 解密
    */

  }, {
    key: "decrypt",
    value: function decrypt(message) {
      var newMessage = message.replace(/[\r\n|\r|\n|\s]/g, '');
      var keyHex = this.readKey(initKey);

      var encrypted = _tripledes["default"].decrypt(newMessage, keyHex, {
        'mode': _modeEcb["default"],
        'padding': _padPkcs["default"]
      });

      var encryptEnd = encrypted.toString(_encUtf["default"]);
      return JSON.parse(encryptEnd);
    }
  }]);

  return TripleDES;
}();

var _default = new TripleDES();

exports["default"] = _default;