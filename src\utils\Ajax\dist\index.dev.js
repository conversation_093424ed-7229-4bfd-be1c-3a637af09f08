'use strict';

Object.defineProperty(exports, '__esModule', {
  value: true,
});
exports.Ajax = void 0;

var _global = require('../../config/global');

var _vue = _interopRequireDefault(require('vue'));

var _request = _interopRequireDefault(require('./request'));

function _interopRequireDefault(obj) {
  return obj && obj.__esModule ? obj : { default: obj };
}

function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    if (enumerableOnly)
      symbols = symbols.filter(function (sym) {
        return Object.getOwnPropertyDescriptor(object, sym).enumerable;
      });
    keys.push.apply(keys, symbols);
  }
  return keys;
}

function _objectSpread(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? arguments[i] : {};
    if (i % 2) {
      ownKeys(source, true).forEach(function (key) {
        _defineProperty(target, key, source[key]);
      });
    } else if (Object.getOwnPropertyDescriptors) {
      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
      ownKeys(source).forEach(function (key) {
        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
      });
    }
  }
  return target;
}

function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}

var service = new _request['default']();
service.setConfig(function (config) {
  /* 设置全局配置 */
  config.baseUrl = _global.BASE_URL;
  config.timeout = 10000;
  return config;
});

service.validateStatus = function (statusCode) {
  return statusCode === 200;
};

service.interceptor.request(function (config, cancel) {
  /* 请求之前拦截器 */
  var store = _vue['default'].prototype.$store;
  var _store$getters$login = store.getters['login/getToken'],
    token = _store$getters$login.token,
    refreshToken = _store$getters$login.refreshToken; //   console.log(token, '这是请求的token、外部获取')
  //   console.log(refreshToken, '这是请求的refreshToken、外部获取')
  //   console.log(`接口地址${config.url}+++  参数:${JSON.stringify(config.data)}`)

  var methodName = config.method.toUpperCase();

  var headerOption = _objectSpread(
    {},
    {
      'content-type':
        methodName === 'POST' &&
        config.url != '/scan/wx/v0.1/guns/qrcode' &&
        config.url != '/scan/wx/v0.3/order' &&
        config.url != '/scan/wx/v0.1/order-charge-list' &&
        config.url != '/scan/wx/v0.1/charge_order_dtl' &&
        config.url != '/scan/wx/v0.1/eva-dtl' &&
        config.url != '/scan/wx/v0.1/order-charge-setbacks' &&
        config.url != '/scan/wx/v0.1/order-charge-controls' &&
        config.url != '/scan/wx/v0.1/cancel-order' &&
        config.url != '/scan/wx/v0.1/account/form' &&
        config.url != '/def/api/v0.1/online-refund' && // config.url != '/cst/api/v0.4/login' &&
        config.url != '/scan/wx/v0.1/account/invoice'
          ? 'application/x-www-form-urlencoded'
          : 'application/json;charset=UTF-8',
      channel: _global.LOGIN_CHANNEL,
    },
    {},
    config.header
  );

  config.header = _objectSpread(
    {},
    headerOption,
    {},
    {
      minitProgramToken: ''.concat(token || localStorage.getItem('tokenNd') || ''),
      //支付宝
      Authorization: 'Bearer '.concat(token || localStorage.getItem('tokenNd') || ''),
      // Client:'h5',
      // Device:'1.0.0',
      // SysVersion:'1.0.0',
      Version: '1.0.0',
    }
  ); //   config.header = {
  //     ...headerOption,
  //     ...{ Authorization: `Bearer ${token || '4f85e30231c24ab18e969309d52a5c4b'}` },
  //   }

  /*
    if (!token) { // 如果token不存在，调用cancel 会取消本次请求，但是该函数的catch() 仍会执行
      cancel('token 不存在') // 接收一个参数，会传给catch((err) => {}) err.errMsg === 'token 不存在'
    }
    */

  return config;
});

var logOutCallBack = function logOutCallBack() {
  var store = _vue['default'].prototype.$store;
  var routes = getCurrentPages();
  var length = routes.length;

  if (
    routes[length - 1] &&
    routes[length - 1].route != 'pages/login/login' &&
    routes[length - 1].route != 'pages/new/new'
  ) {
    // #ifdef MP-ALIPAY
    var currentPage = routes[routes.length - 1]; //获取当前页面的对象

    var beforeQuery = [];
    var queryInfo = currentPage.$vm.$mp.query;

    for (var key in queryInfo) {
      var element = queryInfo[key];
      beforeQuery.push(''.concat(key, '=').concat(element));
    }

    var beforePath = currentPage.route + '?' + beforeQuery.join('&');
    store.dispatch('login/setStartPath', beforePath); // #endif

    uni.hideLoading();
    setTimeout(function () {
      store.dispatch('login/logout');
    }, 0);
  }

  return Promise.reject();
};

var errorCallBack = function errorCallBack(response) {
  var store,
    _Vue$prototype$$store,
    token,
    refreshToken,
    tokenNd,
    refreshTokenNd,
    _Vue$prototype$$store2,
    _token,
    _refreshToken,
    info;

  return regeneratorRuntime.async(
    function errorCallBack$(_context) {
      while (1) {
        switch ((_context.prev = _context.next)) {
          case 0:
            store = _vue['default'].prototype.$store;
            (_Vue$prototype$$store = _vue['default'].prototype.$store.getters['login/getToken']),
              (token = _Vue$prototype$$store.token),
              (refreshToken = _Vue$prototype$$store.refreshToken),
              (tokenNd = _Vue$prototype$$store.tokenNd),
              (refreshTokenNd = _Vue$prototype$$store.refreshTokenNd);

            if (!(response.statusCode == 401)) {
              _context.next = 34;
              break;
            }

            console.log(8888, '登录过期 退回首页', token, refreshToken);
            console.log(8888, '新token', tokenNd, refreshTokenNd); // uni.showToast({
            //   title: '登录已失效，请重新进入',
            //   icon: 'none',
            // })

            setTimeout(function () {
              localStorage.removeItem('tokenNd');
            }, 2000); // const outBack = new Promise((resolve) => {
            //   setTimeout(function() {
            //     resolve()
            //   }, 500)
            // })

            _context.prev = 6;
            (_Vue$prototype$$store2 = _vue['default'].prototype.$store.getters['login/getToken']),
              (_token = _Vue$prototype$$store2.token),
              (_refreshToken = _Vue$prototype$$store2.refreshToken);
            console.log('token, refreshToken ', _token, _refreshToken);

            if (!_refreshToken) {
              _context.next = 24;
              break;
            }

            _context.next = 12;
            return regeneratorRuntime.awrap(store.dispatch('login/getRefreshToken'));

          case 12:
            info = _context.sent;

            if (!info) {
              _context.next = 17;
              break;
            }

            return _context.abrupt('return', service.request(response.config));

          case 17:
            console.log(11111111, '刷新失败了'); // #ifndef H5

            outBack.then(logOutCallBack)['catch'](function (e) {}); // #endif
            // #ifdef H5

            _context.next = 21;
            return regeneratorRuntime.awrap(
              store.dispatch('login/login', {
                otherType: _global.OTHER_TYPE,
                loginType: '03',
              })
            );

          case 21:
            return _context.abrupt('return', service.request(response.config));

          case 22:
            _context.next = 26;
            break;

          case 24:
            // if (response.config.url == '/api/v0.1/bike/info') {
            // } else {
            //     uni.reLaunch({
            //       url: '/pages/loginHt/index',
            //     })
            // }
            uni.showToast({
              title: '登录已失效，请重新进入',
              icon: 'none',
            });
            setTimeout(function () {}, 2000);

          case 26:
            _context.next = 32;
            break;

          case 28:
            _context.prev = 28;
            _context.t0 = _context['catch'](6);
            console.log(_context.t0, 'error刷新失败'); //  #ifdef MP-ALIPAY

            if (_context.t0.ret == 400 && _context.t0.msg == '找到多个令牌或者令牌不存在') {
              uni.reLaunch({
                url: '/pages/newHt/index',
              });
            } // #endif

          case 32:
            _context.next = 42;
            break;

          case 34:
            if (!(response.statusCode == 408)) {
              _context.next = 38;
              break;
            }

            return _context.abrupt(
              'return',
              Promise.reject({
                msg: '网络超时',
              })
            );

          case 38:
            if (!(response.statusCode != 200)) {
              _context.next = 42;
              break;
            }

            if (!(response.errMsg == 'request:fail')) {
              _context.next = 41;
              break;
            }

            return _context.abrupt(
              'return',
              Promise.reject({
                msg: '网络超时',
              })
            );

          case 41:
            return _context.abrupt('return', Promise.reject(response.data));

          case 42:
          case 'end':
            return _context.stop();
        }
      }
    },
    null,
    null,
    [[6, 28]]
  );
};

service.interceptor.response(
  function _callee(response) {
    return regeneratorRuntime.async(
      function _callee$(_context2) {
        while (1) {
          switch ((_context2.prev = _context2.next)) {
            case 0:
              _context2.prev = 0;

              if (!(response.data.ret == 200 || response.statusCode == 200)) {
                _context2.next = 5;
                break;
              }

              return _context2.abrupt('return', response.data);

            case 5:
              if (!(response.data.ret == 300)) {
                _context2.next = 10;
                break;
              }

              uni.reLaunch({
                url: '/pages/mapHt/index',
              });
              return _context2.abrupt('return', Promise.reject(response.data));

            case 10:
              return _context2.abrupt('return', Promise.reject(response.data));

            case 11:
              _context2.next = 16;
              break;

            case 13:
              _context2.prev = 13;
              _context2.t0 = _context2['catch'](0);
              return _context2.abrupt('return', Promise.reject(_context2.t0));

            case 16:
            case 'end':
              return _context2.stop();
          }
        }
      },
      null,
      null,
      [[0, 13]]
    );
  },
  function _callee2(response) {
    return regeneratorRuntime.async(function _callee2$(_context3) {
      while (1) {
        switch ((_context3.prev = _context3.next)) {
          case 0:
            return _context3.abrupt('return', errorCallBack(response));

          case 1:
          case 'end':
            return _context3.stop();
        }
      }
    });
  }
);

var Ajax = function Ajax(points) {
  var params, store;
  return regeneratorRuntime.async(
    function Ajax$(_context4) {
      while (1) {
        switch ((_context4.prev = _context4.next)) {
          case 0:
            params = {};
            params.url = points.url || '';
            params.method = points.method || 'GET';
            params.hideLoad = points.hideLoad || false;

            if (points.headers) {
              params.header = points.headers;
            } //上传图片参数

            if (points.upload) {
              params.upload = points.upload;
            } //超时时间

            params.timeout = points.timeout || 10000;

            if (points.baseURL) {
              params.baseUrl = points.baseURL;
            }

            params[
              points.method.toLowerCase() == 'get' || points.method.toLowerCase() == 'delete' ? 'params' : 'data'
            ] = points.data || {};
            store = _vue['default'].prototype.$store;
            _context4.prev = 10;
            _context4.next = 13;
            return regeneratorRuntime.awrap(store.dispatch('common/checkNetWork'));

          case 13:
            _context4.next = 18;
            break;

          case 15:
            _context4.prev = 15;
            _context4.t0 = _context4['catch'](10);
            return _context4.abrupt(
              'return',
              Promise.reject({
                msg: '网络不可用,请稍后重试',
              })
            );

          case 18:
            return _context4.abrupt('return', service.request(params));

          case 19:
          case 'end':
            return _context4.stop();
        }
      }
    },
    null,
    null,
    [[10, 15]]
  );
};

exports.Ajax = Ajax;
