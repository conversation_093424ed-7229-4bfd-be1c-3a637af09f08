{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.options": {"extensions": [".js", ".vue", ".ts", ".tsx"]}, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[dotenv]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "compile-hero.disable-compile-files-on-did-save-code": false}