<template>
  <div class="wrap">
    <div class="content">
      <rich-text :nodes="content"></rich-text>
    </div>
  </div>
</template>

<script>
import { openInfo } from '@/services/index.js';
export default {
  props: {},
  components: {},
  data() {
    return {
      // renew 《自动续费服务协议》
      // member《宁德会员服务协议》
      content: '',
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  onLoad(options) {
    const { code, title } = options;
    this.code = code;
    this.openInfo();
    uni.setNavigationBarTitle({
      title: title || '协议',
    });
  },
  methods: {
    goBack() {
      uni.navigateBack({
        delit: -1,
      });
    },
    // 获取会员协议
    async openInfo() {
      // 0412 会员协议
      // let arr = ['0412', '0209'];
      let arr = [this.code];
      const params = {
        infoTypeList: arr,
      };
      const [err, res] = await openInfo(params);
      if (res) {
        let list = res.infoList;
        this.content = list.find((item) => item.infoType === this.code)?.content;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.wrap {
  background: #fff;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  .content {
    padding: 40rpx 24rpx;
    flex: 1;
  }
  .banner {
    padding: 20rpx 20rpx 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
    z-index: 2;
    box-sizing: border-box;
    color: #fff;
    .banner-title {
      font-family: 'PingFang SC';
      font-size: 36rpx;
      font-style: normal;
      font-weight: 500;
      flex: 1;
      text-indent: 4rpx;
      color: #000000;
      font-weight: 700;
      text-align: left;
      color: #fff;
    }
    .banner-img {
      width: 48rpx;
      height: 48rpx;
      margin-right: 20rpx;
    }
  }
}
</style>
