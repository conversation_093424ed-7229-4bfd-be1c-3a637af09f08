
  
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s;
}
.fade-leave-active {
  display: none;
  opacity: 0;
}
.fade-enter,
.fade-leave-active {
  opacity: 0;
}

.dropDown-enter-active,
.dropDown-leave-active {
  transition: all 0.5s;
  transform-origin: center top;
}
.dropDown-enter {
  transform: translateY(-100%);
}
.dropDown-leave-active {
  transform: translateY(-100%);
}

.dropUp-enter-active,
.dropUp-leave-active {
  transition: all 0.5s;
  transform-origin: center bottom;
}
.dropUp-enter {
  transform: translateY(100%);
}
.dropUp-leave-active {
  transform: translateY(100%);
}

.dropLeft-enter-active,
.dropLeft-leave-active {
  transition: all 0.5s;
  transform-origin: right center;
}
.dropLeft-enter {
  transform: translateX(-100%);
}
.dropLeft-leave-active {
  transform: translateX(-100%);
}

.dropRight-enter-active,
.dropRight-leave-active {
  transition: all 0.5s;
  transform-origin: left center;
}
.dropRight-enter {
  transform: translateX(100%);
}
.dropRight-leave-active {
  transform: translateX(100%);
}

.vux-pop-out-enter-active,
.vux-pop-out-leave-active,
.vux-pop-in-enter-active,
.vux-pop-in-leave-active {
  will-change: transform;
  transition: all 400ms;
  height: 100%;
  position: absolute;
  backface-visibility: hidden;
  perspective: 1000;
  width: 100%;
  left: 0;
  top: 0;
  transition: all 0.3s;
}
.vux-pop-in-enter,
.vux-pop-out-leave-to {
  transform: translateX(100%);
}

.vux-pop-out-enter,
.vux-pop-in-leave-to {
  transform: translateX(-100%);
}