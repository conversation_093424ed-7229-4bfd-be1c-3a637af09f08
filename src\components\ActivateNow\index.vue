<template>
  <view class="buy-plus" :style="{ backgroundImage: `url(${IMG_PATH}member/user-card-bg.png)` }">
    <view class="buy-plus-text">
      <image :src="`${IMG_PATH}member/member-icon/icon-diamond.png`" mode="widthFix" />
      开通PLUS会员享受专属权益</view
    >
    <div class="btn" @click="jumpToBuy">立即开通</div>
  </view>
</template>

<script>
import AppButton from '@/components/AppButton/index';

export default {
  name: 'index',
  components: {
    AppButton,
  },
  methods: {
    // 去开通
    jumpToBuy() {
      uni.navigateTo({
        url: '/pages/member/buyPlus/index',
      });
    },
  },
};
</script>

<style scoped lang="scss">
.buy-plus {
  display: flex;
  align-items: center;
  background-size: 100% auto;
  width: 100%;
  height: 104rpx;
  border-radius: 16rpx;
  padding: 28rpx 29rpx 28rpx 29rpx;
  margin-bottom: 16rpx;
  &-text {
    font-size: 28rpx;
    font-weight: bold;
    line-height: 40rpx;
    font-variation-settings: 'opsz' auto;
    margin-right: 63rpx;
    display: flex;
    color: #111111;
    align-items: center;
    image {
      width: 50rpx;
      height: auto;
      margin-right: 10rpx;
    }
  }
  .btn {
    border-radius: 192rpx;
    background: linear-gradient(
        126deg,
        rgba(255, 255, 255, 0) -2%,
        rgba(255, 255, 255, 0.19) 11%,
        rgba(10, 9, 9, 0) 60%
      ),
      linear-gradient(109deg, #5e5a54 0%, #1e1f28 94%);
    box-shadow: inset 0px 1px 0px 0px #fff9ea;
    height: 60rpx;
    padding: 12rpx 22rpx;
    font-size: 24rpx;
    line-height: 36px;
    color: #ffe3c1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    .btn-tip {
      width: 104rpx;
      height: 48rpx;
      position: absolute;
      left: -45rpx;
      top: -20rpx;
      background-size: 100% auto;
      background-repeat: no-repeat;
    }
  }
}
</style>
