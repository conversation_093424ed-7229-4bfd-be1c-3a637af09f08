<template>
  <view>
    <view class="searchLetter touchClass">
      <view
        :style="'height:' + itemH + 'px'"
        :data-letter="item.name"
        @touchstart.stop.prevent="searchStart"
        @touchmove.stop.prevent="searchMove"
        @touchend.stop.prevent="searchEnd"
        v-for="(item, index) in searchLetter"
        :key="index"
      >
        {{ item.name }}
      </view>
    </view>

    <block v-if="isShowLetter">
      <view class="showSlectedLetter">
        {{ showLetter }}
      </view>
    </block>
    <!-- 一级品牌 -->
    <scroll-view :scroll-y="true" :style="'height:' + winHeight + 'px'" @scroll="bindScroll" :scroll-top="scrollTop">
      <view class="selection" v-for="(item, index) in cityList" :key="item.shou_zm">
        <view class="item_letter" @tap="toOther" :style="item.shou_zm === '*' ? 'padding-left:0' : ''">
          <image
            class="car_img"
            :src="ct.brandImg || IMG_PATH + 'default-car.png'"
            mode="aspectFit"
            v-if="item.shou_zm === '*'"
          ></image>
          <view v-if="item.shou_zm === '*'" class="btn-other">其他车型</view>
          <template v-else>{{ item.shou_zm }}</template>
        </view>

        <view @tap="jump" class="item_city" :data-city="ct.brandId" v-for="(ct, index1) in item.cityInfo" :key="ct.id">
          <image class="car_img" :src="ct.brandImg || IMG_PATH + 'default-car.png'" mode="aspectFit"></image>

          <label class="car_name">{{ ct.brandName }}</label>
        </view>
      </view>
    </scroll-view>
    <!-- 二级车系 -->
    <scroll-view
      style=""
      :scroll-y="true"
      :style="'height:' + car_type_hight + ';display:' + two_none"
      class="scoll_car_service"
    >
      <view class="selection" v-for="(item, index) in car_series" :key="item.vender">
        <view class="item_letter">{{ item.vender }}</view>

        <label
          class="tab2"
          @tap="btn_name"
          :data-item="item.car_series"
          v-for="(item, index1) in item.car_series"
          :key="item.car_series"
        >
          {{ item.car_series }}
        </label>
      </view>
    </scroll-view>
    <!-- 三级 -->
    <scroll-view
      :scroll-y="true"
      :style="'height:' + car_type_hight + ';display:' + three_none"
      class="scoll_vender_service"
    >
      <view v-for="(item, index) in car_vend_ser" :key="index">
        <!-- <view class="item_letter">{{item.vender}}</view> -->

        <text class="tab3" @tap="btn_sel_name" :data-item="item">{{ item }}</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getVehicleInfo } from '@/services/index.js';

//城市检索的首字母
const abc = [
  '*',
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'J',
  'K',
  'L',
  'M',
  'N',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'W',
  'X',
  'Y',
  'Z',
];
export default {
  data() {
    return {
      searchLetter: [],
      showLetter: '',
      winHeight: 0,
      tHeight: 0,
      bHeight: 0,
      msg: [],
      startPageY: 0,
      cityList: [],
      isShowLetter: false,
      scrollTop: 0,
      three_none: 'none',
      two_none: 'none',

      // 二级
      car_series: [],

      // 获取到二级当前记录下来的值
      car_ser: null,

      // 三级
      car_vend_ser: ['阿波罗', '宝马'],

      cityObj: [],

      shuju: {
        car_name: [],
      },

      car_pinpai: {
        pinpai: null,
      },

      zrf: '',

      ct: {
        brandId: '',
        id: '',
        brandImg: '',
        brandName: '',
      },

      car_type_hight: '',
    };
  },
  onReady() {},
  async onLoad() {
    // 生命周期函数--监听页面加载
    var that = this;
    // uni.request({
    //   method: "post",
    //   url: '请求第一层的接口',
    //   success (e) {
    //     that.data.cityObj = e.data.data.data;
    //     uni.setStorageSync("key", that.data.cityObj);
    //   }
    // })
    var params = {
      getLevel: '01',
      fuelTyle: '01,03',
    };
    const [, res] = await getVehicleInfo(params);
    if (res && res.ret == '200') {
      that.setData({
        msg: res.brandList,
      });
      that.cityObj = res.brandList;
      uni.setStorageSync('key', that.cityObj);
      that.shuju.car_name = uni.getStorageSync('key');
      var cityList = that.cityListFun();
      var sysInfo = uni.getSystemInfoSync();
      var winHeight = sysInfo.windowHeight;

      //添加要匹配的字母范围值
      //1、更加屏幕高度设置子元素的高度
      var itemH = winHeight / abc.length;
      var tempObj = [];
      for (var i = 0; i < abc.length; i++) {
        var temp = {};
        temp.name = abc[i];
        temp.tHeight = i * itemH;
        temp.bHeight = (i + 1) * itemH;
        tempObj.push(temp);
      }

      console.log('====================================');
      console.log({
        winHeight: winHeight,
        itemH: itemH,
        searchLetter: tempObj,
        cityList: cityList,
      });
      console.log('====================================');
      that.setData({
        winHeight: winHeight,
        itemH: itemH,
        searchLetter: tempObj,
        cityList: cityList,
      });
      console.log(cityList);
    } else {
      uni.showToast({
        icon: 'loading',
        title: '加载数据失败',
      });
    }
  },
  onShareAppMessage() {
    // 用户点击右上角分享
    return {
      title: 'title',
      // 分享标题
      desc: 'desc',
      // 分享描述
      path: 'path', // 分享路径
    };
  },
  methods: {
    jump(e) {
      console.log(e.target.dataset.city);
      uni.navigateTo({
        url: '/subPackages/car/car?brandid=' + e.target.dataset.city,
      });
    },

    searchStart(e) {
      var showLetter = e.currentTarget.dataset.letter;
      var pageY = e.touches[0].pageY;
      this.setScrollTop(showLetter);
      this.nowLetter(pageY);
      this.setData({
        showLetter: showLetter,
        startPageY: pageY,
        isShowLetter: true,
      });
    },

    searchMove(e) {
      var pageY = e.touches[0].pageY;
      var startPageY = this.startPageY;
      var tHeight = this.tHeight;
      var bHeight = this.bHeight;
      if (startPageY - pageY > 0) {
        //向上移动
        if (pageY < tHeight) {
          // showLetter=this.mateLetter(pageY,this);
          this.nowLetter(pageY);
        }
      } else {
        //向下移动
        if (pageY > bHeight) {
          // showLetter=this.mateLetter(pageY,this);
          this.nowLetter(pageY);
        }
      }
    },

    searchEnd(e) {
      // var showLetter=e.currentTarget.dataset.letter;
      var that = this;
      setTimeout(function () {
        that.setData({
          isShowLetter: false,
        });
      }, 1000);
    },

    toOther() {
      uni.redirectTo({
        url: '/subPackages/carOther/index',
      });
    },

    nowLetter(pageY) {
      //当前选中的信息
      var letterData = this.searchLetter;
      var bHeight = 0;
      var tHeight = 0;
      var showLetter = '';
      for (var i = 0; i < letterData.length; i++) {
        if (letterData[i].tHeight <= pageY && pageY <= letterData[i].bHeight) {
          bHeight = letterData[i].bHeight;
          tHeight = letterData[i].tHeight;
          showLetter = letterData[i].name;
          break;
        }
      }
      this.setScrollTop(showLetter);
      this.setData({
        bHeight: bHeight,
        tHeight: tHeight,
        showLetter: showLetter,
        startPageY: pageY,
      });
    },

    bindScroll(e) {},

    setScrollTop(showLetter) {
      var scrollTop = 0;
      var cityList = this.cityList;
      var cityCount = 0;
      var initialCount = 0;
      for (var i = 0; i < cityList.length; i++) {
        if (showLetter == cityList[i].shou_zm) {
          scrollTop = initialCount * 30 + cityCount * 41;
          break;
        } else {
          initialCount++;
          cityCount += cityList[i].cityInfo.length;
        }
      }
      this.setData({
        scrollTop: scrollTop,
      });
    },

    xz() {
      this.setData({
        zrf: 'block',
      });
    },

    // 这里点击一级品牌获取第二层数据
    // btn_pinpai(e) {
    //   var that = this;

    //   that.car_pinpai.pinpai = e.currentTarget.dataset.city;

    //   uni.request({
    //     url: '请求第二层的接口',
    //     method: "post",
    //     data: {
    //       brand: that.car_pinpai.pinpai
    //     },
    //     success(data) {
    //       that.data.car_series = data.data.data.data;

    //       that.setData({
    //         three_none: "none",
    //         two_none: "block",
    //         car_series: that.data.car_series
    //       })
    //     }
    //   })
    //   // uni.reLaunch({
    //   //   url: "../car_shop/car_shop?pinpai=" + pinpai
    //   // })

    // },
    // 点击二级车系获取第三层数据
    // btn_name(e) {
    //   var that = this;
    //   that.data.car_ser = e.currentTarget.dataset.item;

    //   uni.request({
    //     url: '请求第三层的数据接口',
    //     method: 'post',
    //     data: {
    //       brand: that.car_pinpai.pinpai,
    //       car_series: that.data.car_ser
    //     },
    //     success(res) {
    //       that.setData({
    //         two_none: "none",
    //         three_none: "block",
    //         car_vend_ser: res.data.data
    //       })
    //     },
    //     fail(res) {},
    //     complete(res) {},
    //   })
    // },
    // 点击三级title传值跳转页面
    btn_sel_name(e) {
      var that = this;
      var sel_name = e.currentTarget.dataset.item;
      var model = that.car_pinpai.pinpai.concat(that.car_ser, sel_name);
      uni.redirectTo({
        url: '../daiban/daiban?model=' + model,
      });
    },

    cityListFun() {
      var that = this;
      var tempObj = [];
      abc.forEach((shou_zm) => {
        var cityInfo = [];
        that.shuju.car_name.forEach((item) => {
          if (shou_zm == item.spell) {
            cityInfo.push({
              brandName: item.brandName,
              brandImg: item.brandImg,
              brandId: item.brandId,
            });
          }
        });
        tempObj.push({
          shou_zm,
          cityInfo,
        });
      });
      return tempObj;

      // for (var i = 0; i < searchLetter.length; i++) {
      //     var shou_zm = searchLetter[i];
      //     var cityInfo = [];
      //     var tempArr = {};
      //     var msg = {}
      //     tempArr.shou_zm = shou_zm;
      //     for (var j = 0; j < that.shuju.car_name.length; j++) {
      //         if (shou_zm == that.shuju.car_name[j].spell) {
      //             msg.brandName = that.shuju.car_name[j].brandName
      //             msg.brandImg = that.shuju.car_name[j].brandImg
      //             msg.brandId = that.shuju.car_name[j].brandId
      //             cityInfo.push(msg);
      //             // cityInfo.push(that.shuju.car_name[j].brandImg);
      //         }
      //     }
      //     tempArr.cityInfo = cityInfo;
      //     tempObj.push(tempArr);
      // }
      // return tempObj;
    },

    searchLetterFun() {
      return searchLetter;
    },

    btn_name() {
      console.log('占位：函数 btn_name 未声明');
    },
  },
};
</script>
<style>
@import './cars.css';
</style>
