@import './common/css/global.css';
/**app.wxss**/

page {
    background-color: #f5f5f5;
}
.container {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding: 200rpx 0;
    box-sizing: border-box;
}
.blue-bg {
    background-color: #2196f3;
}
.orange-bg {
    background-color: #ff9250;
}
.red-color {
    color: #e00a22;
}
.gray-color {
    color: #666;
}
.blue-color {
    color: #2196f3;
}
.fr {
    float: right;
}
.btn-fiexd {
    position: fixed;
    left: 30rpx;
    right: 30rpx;
    bottom: 30rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 4px;
    color: #fff;
    font-size: 32rpx;
}
radio .wx-radio-input.wx-radio-input-checked {
    background-color: #2196f3 !important;
    border-color: #2196f3 !important;
}
checkbox .wx-checkbox-input {
    width: 24rpx;
    height: 24rpx;
    /* zoom: 65%; */
}
button {
    border-radius: 4px;
}
button::after {
    border-color: transparent;
}
