<template>
  <div class="charts">
    <canvas
      canvas-id="column"
      id="column"
      class="charts"
      @touchstart="touchstart"
      @touchmove="touchmove"
      @touchend="touchend"
    />
  </div>
</template>

<script>
import uCharts from './u-charts.js';
export default {
  props: {
    chartData: {
      type: Object,
      default: () => {
        return {
          categories: [],
          series: [],
        };
      },
    },
  },
  components: {},
  data() {
    return {
      cWidth: 750,
      cHeight: 350,
      uChartsInstance: null,
      opts: {
        color: ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'],
        padding: [15, 10, 0, 15],
        dataLabel: false,
        dataPointShape: false,
        enableScroll: false,
        legend: {},
        xAxis: {
          disableGrid: true,
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2,
          data: [
            {
              min: 0,
              max: 150,
            },
          ],
        },
        extra: {
          line: {
            type: 'curve',
            width: 2,
            activeType: 'hollow',
            linearType: 'custom',
            onShadow: true,
            animation: 'horizontal',
          },
        },
      },
      // chartData: {},
    };
  },
  watch: {
    chartData: {
      handler(newVal, oldVal) {
        this.drawCharts('column', this.chartData);
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    getServerData() {
      //模拟从服务器获取数据时的延时
      setTimeout(() => {
        //模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
        let res = {
          categories: ['2018', '2019', '2020', '2021', '2022', '2023'],
          series: [
            {
              name: '成交量A',
              linearColor: [
                [0, '#1890FF'],
                [0.25, '#00B5FF'],
                [0.5, '#00D1ED'],
                [0.75, '#00E6BB'],
                [1, '#90F489'],
              ],
              setShadow: [3, 8, 10, '#1890FF'],
              data: [15, 45, 15, 45, 15, 45],
            },
          ],
        };
        this.setData({ chartData: JSON.parse(JSON.stringify(res)) });
        this.drawCharts('column', this.chartData);
      }, 500);
    },
    drawCharts(id, data) {
      this.cWidth = (600 / 750) * wx.getSystemInfoSync().windowWidth;
      this.cHeight = (450 / 750) * wx.getSystemInfoSync().windowWidth;
      if (!this.uChartsInstance) {
        const ctx = wx.createCanvasContext(id, this);
        this.uChartsInstance = new uCharts({
          type: 'line',
          context: ctx,
          width: this.cWidth,
          height: this.cHeight,
          categories: data.categories,
          series: data.series,
          animation: true,
          timing: 'easeOut',
          duration: 1000,
          rotate: false,
          rotateLock: false,
          background: '#282934',
          color: ['#FF944D', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'],
          padding: [15, 0, 15, 0],
          fontSize: 13,
          fontColor: '#8d9193',
          dataLabel: false,
          dataPointShape: true,
          dataPointShapeType: 'solid',
          touchMoveLimit: 60,
          enableScroll: true,
          enableMarkLine: false,
          legend: {
            show: false,
          },
          xAxis: {
            disableGrid: true,
            disabled: false,
            axisLine: true,
            axisLineColor: '#CCCCCC',
            calibration: false,
            fontColor: '#8d9193',
            fontSize: 12,
            lineHeight: 20,
            marginTop: 0,
            rotateLabel: false,
            rotateAngle: 45,
            itemCount: 5,
            boundaryGap: 'center',
            splitNumber: 5,
            gridColor: '#CCCCCC',
            gridType: 'solid',
            dashLength: 4,
            gridEval: 1,
            scrollShow: false,
            scrollAlign: 'left',
            scrollColor: '#A6A6A6',
            scrollBackgroundColor: '#EFEBEF',
            title: '',
            titleFontSize: 13,
            titleOffsetY: 0,
            titleOffsetX: 0,
            titleFontColor: '#8d9193',
            formatter: '',
          },
          yAxis: {
            gridType: 'solid',
            dashLength: 2,
            disabled: false,
            disableGrid: false,
            splitNumber: 5,
            gridColor: '#b4b4b4',
            padding: 10,
            showTitle: false,
            data: [],
          },
          extra: {
            line: {
              type: 'curve',
              width: 2,
              activeType: 'hollow',
              linearType: 'none',
              onShadow: false,
              animation: 'horizontal',
            },
            tooltip: {
              showBox: true,
              showArrow: false,
              showCategory: false,
              bgColor: '#000000',
              bgOpacity: 0.7,
              gridType: 'solid',
              dashLength: 4,
              gridColor: '#CCCCCC',
              boxPadding: 3,
              fontSize: 13,
              lineHeight: 20,
              fontColor: '#FFFFFF',
              legendShow: true,
              legendShape: 'auto',
              splitLine: true,
              horizentalLine: false,
              xAxisLabel: false,
              yAxisLabel: false,
              labelBgColor: '#FFFFFF',
              labelBgOpacity: 0.7,
              labelFontColor: '#666666',
            },
            markLine: {
              type: 'solid',
              dashLength: 4,
              data: [],
            },
          },
        });
      } else {
        this.uChartsInstance.updateData({
          categories: data.categories,
          series: data.series,
        });
      }
    },
    touchstart(e) {
      this.uChartsInstance && this.uChartsInstance.scrollStart(e);
    },
    touchmove(e) {
      this.uChartsInstance && this.uChartsInstance.scroll(e);
    },
    touchend(e) {
      this.uChartsInstance && this.uChartsInstance.scrollEnd(e);
      this.uChartsInstance && this.uChartsInstance.touchLegend(e);
      this.uChartsInstance && this.uChartsInstance.showToolTip(e);
    },
  },
};
</script>

<style scoped lang="scss">
.charts {
  margin: 15rpx 0;
  width: 100%;
  height: 100%;
  z-index: 99;
}
</style>
