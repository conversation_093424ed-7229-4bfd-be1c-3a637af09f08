// const VConsole = require('vconsole')
// new VConsole()
// #ifdef H5

window.addEventListener('unhandledrejection', function (e) {
  // Event新增属性
  // @prop {Promise} promise - 状态为rejected的Promise实例
  // @prop {String|Object} reason - 异常信息或rejected的内容

  // 会阻止异常继续抛出，不让Uncaught(in promise) Error产生
  e.preventDefault();
});
import './utils/H5Api/ican-H5Api';
// #ifdef H5
import bridge from './utils/dsBridge';
if (!window.bridge) {
  window.bridge = bridge;
}
// #endif
import { LOGIN_CHANNEL } from './config/global';
if (process.env.VUE_APP_MODE === 'dev') {
  //   const VConsole = require('vconsole')
  //   new VConsole()
}
const VConsole = require('vconsole');
new VConsole();
if (LOGIN_CHANNEL == '0602') {
  ls.navigationStatus({ navigationShow: false });
}

// #endif

import Vue from 'vue';

import App from './App';
// import qs from 'qs'

import store from './store';

import commonMinxin from './mixins/common';

import './assets/styles/common.scss';
import './assets/styles/loading.css';

Vue.prototype.$store = store;

Vue.config.productionTip = false;

App.mpType = 'app';

// const getPageQueryCommon = ()=>{
//   return qs.parse(window.location.href.split('?')[1]);
// }

// const userQuery = getPageQueryCommon()
// Vue.prototype.$userQuery = userQuery;

Vue.mixin(commonMinxin);

// 全局mixins，用于实现setData等功能，请勿删除！';
import zpMixins from '@/uni_modules/zp-mixins/index.js';
Vue.use(zpMixins);
Vue.mixin(zpMixins);

const app = new Vue({
  store,
  ...App,
});
app.$mount();
