<!--
@name: 会员中心页面
@description: 会员中心主页面
@time: 2024/8/12
-->
<template>
  <view class="member-center" :style="{ backgroundImage: getBackGround }">
    <div class="load-wrap" v-if="loading">
      <div class="loading"></div>
    </div>
    <template v-else>
      <!--        用户等级卡片-->
      <view class="member-center-card">
        <MemberCard :member-info="memberInfo" />
      </view>
      <!--        权益列表、立即开通、购买记录-->
      <view class="member-center-content">
        <!--            权益列表-->
        <EquityList ref="EquityList" :member-info="memberInfo" />
        <!--            立即开通-->
        <ActivateNow v-if="memberInfo.vipFlag === '0'" />
        <!--            购买记录-->
        <Record class="record" />
      </view>
    </template>
    <!--    升级提示弹框-->
    <uni-popup ref="levelPopup">
      <view class="level-content">
        <view
          class="level-dialog"
          :style="{ backgroundImage: `url(${IMG_PATH}member/level-dialog.png)`, backgroundSize: 'cover' }"
        >
          <text class="name">尊贵的客户{{ userInfo.custNickname || '' }}</text>
          <text class="title">恭喜升级到v{{ memberInfo.rankNo }}</text>
          <view class="level">————获得以下等级特权————</view>
          <view class="list">
            <EquityList :member-info="memberInfo" :showTitle="false" :description="false" :show-info="false" />
          </view>
        </view>
        <img class="close-button" :src="`${IMG_PATH}member/level-close.png`" @click="closeLevelPopup" />
      </view>
    </uni-popup>
  </view>
</template>

<script>
import EquityList from '../components/EquityList';
import Record from './components/Record';
import MemberCard from './components/MemberCard';
import { getLevelFlag, updateLevelFlag, getMemberInfo } from '@/services/index.js';
import { mapState } from 'vuex';
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup';
import ActivateNow from '@/components/ActivateNow/index';

export default {
  name: 'index',
  components: {
    MemberCard,
    Record,
    EquityList,
    uniPopup,
    ActivateNow,
  },
  data() {
    return {
      levelFlag: '0',
      memberInfo: {},
      loading: true,
    };
  },
  computed: {
    ...mapState({
      userInfo: (state, getters) => getters['login/getUserInfo'],
    }),
    getBackGround() {
      const str = this.memberInfo.vipFlag === '1' ? 'member-center-plus-bg' : 'member-center-bg';
      return `url(${this.IMG_PATH}member/${str}.png)`;
    },
  },
  async onLoad() {
    await this.getMemberInfoFn();
    this.getLevelFlag();
    this.getUserInfo();
  },
  methods: {
    // 查询是否升级
    async getLevelFlag() {
      const [err, res] = await getLevelFlag({});
      if (res) {
        this.levelFlag = res.levelFlag;
        if (this.levelFlag === '1') {
          this.openLevelPopup();
        }
      }
    },
    // 更新升级弹框状态
    async updateLevelFlag() {
      const [err, res] = await updateLevelFlag();
      if (err) {
        throw err;
      }
    },
    // 打开升级弹框
    openLevelPopup() {
      this.$refs.levelPopup.open();
      this.updateLevelFlag();
    },
    // 关闭弹框
    closeLevelPopup() {
      this.$refs.levelPopup.close();
    },
    // 获取用户信息
    getUserInfo() {
      this.$store.dispatch('login/getUserInfoCallback');
    },
    // 获取会员信息
    async getMemberInfoFn() {
      this.loading = true;
      const [err, res] = await getMemberInfo();
      this.loading = false;
      if (res) {
        this.memberInfo = res;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.member-center {
  height: 100%;
  background-size: 100% 100%;
  background-position: center top -88px;
  position: relative;
  background-repeat: no-repeat;
  &-card {
    padding: 32rpx 24rpx 0 24rpx;
  }
  &-content {
    position: absolute;
    top: 309rpx;
    width: 100%;
    min-height: calc(100% - 309rpx);
    background: white;
    border-radius: 24rpx 24rpx 0 0;
    padding: 40rpx 24rpx 0 24rpx;
  }
  .load-wrap {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .level-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    .level-dialog {
      width: 558.25rpx;
      height: 792.4rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      background-size: contain;
      border-radius: 0 0 24rpx 24rpx;
      padding: 190rpx 0rpx;
      .name {
        font-size: 26rpx;
        line-height: 40rpx;
        color: #666666;
        margin-bottom: 3rpx;
      }
      .title {
        font-size: 36rpx;
        font-weight: bold;
        line-height: 42rpx;
        letter-spacing: 0.58rpx;
        color: #333333;
        margin-bottom: 21rpx;
      }
      .level {
        font-size: 22rpx;
        line-height: 26rpx;
        color: #666666;
      }
      .list {
        width: 100%;
      }
    }
    .close-button {
      margin-top: 40rpx;
      width: 60rpx;
      height: 60rpx;
    }
  }
}
</style>
