<template>
  <view>
    <button :class="'mainbtn' + (ghost ? ' ghost' : '')" @tap="click"><slot></slot></button>
  </view>
</template>

<script>
// components/main-btn/index.js
export default {
  data() {
    return {};
  },
  /**
   * 组件的属性列表
   */
  props: {
    ghost: {
      type: Boolean,
      default: false,
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    click() {
      this.$emit('click', {
        detail: '',
      });
    },
  },
  created: function () {},
};
</script>
<style>
@import './index.css';
</style>
