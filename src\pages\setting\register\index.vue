<template>
  <div class="page-wrap">
    <view class="login">
      <image :src="`${IMG_PATH}share-title-logo.jpg`" mode="widthFix"></image>
      <view class="section-phone">
        <text>手机号码{{ time }}</text>
        <input @input="phoneStatus" type="number" maxlength="11" placeholder="请输入手机号码" />
        <button @tap="down" v-if="time" class="btn-phone btn-phonesd styleColorB">获取验证码</button>
        <button v-else class="btn-phone">{{ times }}s重新获取</button>
      </view>
      <view class="section-code">
        <text>验证码</text>
        <input @input="passStatus" maxlength="6" type="number" placeholder="请输入验证码" />
      </view>
      <text class="txt-notice">绑定手机后可接收短信通知、登录APP及保护您的账号安全</text>
      <button @tap="open" v-if="nice" class="btn-submit styleColorB">确认注册</button>
      <button v-else style="background: #eee" class="btn-submit">确认注册</button>
    </view>
    <!-- <div class="btn-wrap"> -->
    <!-- #ifdef MP-WEIXIN -->
    <!-- <div class="btn" @click="downAppLink(iphoneLink)">
      <image :src="`${IMG_PATH}iphone.png`" mode="widthFix" />
      iPhone 下载
    </div>
    <div class="btn" @click="downAppLink(androidLink)">
      <image :src="`${IMG_PATH}android.png`" mode="widthFix" />
      Android 下载
    </div>
    <div class="tip">请点击按钮复制下载链接,使用浏览器打开下载.</div> -->

    <!-- #endif -->
    <!-- #ifdef H5 -->
    <!-- <a class="btn" :href="iphoneLink" target="_blank">
        <image :src="`${IMG_PATH}iphone.png`" mode="widthFix" />
        iPhone 下载
      </a>
      <a class="btn" target="_blank" :href="androidLink">
        <image :src="`${IMG_PATH}android.png`" mode="widthFix" />
        Android 下载
      </a> -->
    <!-- #endif -->

    <!-- </div> -->
    <div class="banner"><image :src="img || `${IMG_PATH}share-banner.png`" mode="widthFix" /></div>
    <div class="rule">
      <div class="title">
        <div class="line"></div>
        活动规则
        <div class="line2"></div>
      </div>
      <div class="rule-text-area">
        <!-- #ifdef MP-WEIXIN -->
        <RichText :content="ruleInfo" selectable="true" show-img-menu="true"></RichText>
        <!-- #endif -->
        <!-- #ifdef H5 -->
        <div v-html="info"></div>
        <!-- #endif -->
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { register, getVerifyCode, login, refreshToken, queryActInProgress } from '@/services/index.js';
import { addDomainToImgSrc } from '@/utils/index';
import { BASE_URL } from '@/config/global';
import RichText from '../share/RichText.vue';
export default {
  props: {},
  components: { RichText },
  data() {
    return {
      ruleInfo: '',
      times: 59,
      time: true,
      phoneMain: false,
      passMain: false,
      phone: '',
      pass: '',
      nice: false,
      iphoneLink: '',
      androidLink: '',
      inviterMobile: '',
      inviterCustId: '',
      token: '',
      img: '',
      loading: false,
    };
  },
  onLoad(options) {
    const that = this;
    this.queryActInProgress();
    // 跳转连接处理，二维码跳转取q中的值，连接分享跳转取id中的值
    this.inviterMobile = options?.mobile || '';
    this.inviterCustId = options?.custId || '';
    if (this.userInfo && this.userInfo.mobile && this.userInfo.custId) {
      uni.showModal({
        title: '提示',
        content: `当前已登录宁德小程序,注册后将以新用户登录.`,
        confirmText: '确认',
        cancelText: '返回首页',
        success(res) {
          if (res.cancel) {
            uni.navigateTo({
              url: '/pages/home/<USER>',
            });
          }
        },
      });
    }
    uni.login({
      success: async (res) => {
        console.log('res.coderes.code', res.code);
        if (res.code) {
          //token刷新
          const [, result] = await refreshToken({
            code: res.code,
            // orgCode: '0000001'
          });
          if (result) {
            console.log(result.token, '根据code的回参');
            that.token = result.token;
          }
        } else {
          console.log('获取用户登录态失败！' + res.errMsg);
        }
      },
    });
  },
  computed: {
    ...mapState({
      userInfo: (state, getters) => getters['login/getUserInfo'],
    }),
  },
  async mounted() {
    // this.iphoneLink = await this.getConfigOption('iphoneDownaLink');
    // this.androidLink = await this.getConfigOption('androidDownLink');
  },
  methods: {
    async queryActInProgress() {
      const [, res] = await queryActInProgress({
        actType: '06',
        actState: '2',
      });
      console.log(res, 'res');
      if (res) {
        this.ruleInfo = await addDomainToImgSrc(res.actDetailMarks, BASE_URL);
        this.img = res.fileId ? `${process.env.VUE_APP_BASE_HOST}/pub/api/v0.1/attachs/${res.fileId}` : '';
      }
    },
    // 获取系统参数
    async getConfigOption(code) {
      const [, res] = await Api.getConfigOption({ paraCode: code });
      if (res && res.list && Array.isArray(res.list) && res.list.length > 0) {
        return res.list[0]?.paramValue;
      }
      return '';
    },
    down: async function () {
      if (this.phone == this.inviterMobile) {
        uni.showModal({
          title: '提示',
          content: `注册的手机号不能与邀请人手机号相同`,
          confirmText: '确认',
          showCancel: false,
        });
        return false;
      }
      var that = this;
      var sPhone = that.phone;
      if (that.phone == '') {
        uni.showToast({
          title: '号码不能为空',
          icon: 'loading',
          duration: 1000,
        });
        return false;
      } else if (!/^1\d{10}$/.test(sPhone)) {
        console.log('手机号码格式错误');
        uni.showToast({
          icon: 'loading',
          title: '手机号码格式错误',
        });
        return false;
      }
      this.time = false;
      var interval = undefined;
      var time = that.times;
      // clearInterval(interval)
      interval = setInterval(function () {
        if (time == 0) {
          console.log('时间到');
          clearInterval(interval);
          that.time = true;
          that.times = 59;
        } else {
          that.times = time--;
        }
        console.log(time, that.times);
      }, 1000);
      var token = that.token;
      console.log(that.phone, '获取验证码');
      const [res, err] = await getVerifyCode({
        mobile: that.phone,
        verifyType: '05',
      });
      if (res && res.ret == 200) {
        console.log(res, '获取验证码');
        uni.showToast({
          title: '发送成功',
          icon: 'success',
          duration: 1000,
        });
      } else {
        console.log(err | '', '获取验证码请求出问题。');
      }
      // uni.request({
      //   url: baseUrl + '/wx/v0.1/send-msg',
      //   data: {
      //     mobile: that.phone,
      //     verifyType: '05',
      //   },
      //   method: 'POST',
      //   header: {
      //     'content-type': 'application/json',
      //     minitProgramToken: token,
      //   },
      //   success: (res) => {
      //     console.log(res, '获取验证码');
      //     uni.showToast({
      //       title: '发送成功',
      //       icon: 'success',
      //       duration: 1000,
      //     });
      //   },
      //   fail: (err) => {
      //     console.log(err, '获取验证码请求出问题。');
      //   },
      // });
    },

    phoneStatus: function (value) {
      console.log(value);
      var that = this;
      if (!value.detail.value || value.detail.value == ' ') {
        console.log('空的');
        this.phoneMain = false;
      } else {
        this.phoneMain = true;
        this.phone = value.detail.value;
      }
      console.log(that.phone, '电话');
      this.nice = that.phoneMain && that.passMain;
    },
    passStatus: function (value) {
      console.log(value.detail.value);
      console.log(value);
      var that = this;
      if (!value.detail.value || value.detail.value == ' ') {
        console.log('空的');
        this.passMain = false;
      } else {
        this.passMain = true;
        this.pass = value.detail.value;
      }
      console.log(that.pass, '验证码');
      this.nice = that.phoneMain && that.passMain;
    },
    open: async function () {
      if (this.loading) return;
      var that = this;
      if (this.phone == this.inviterMobile) {
        uni.showModal({
          title: '提示',
          content: `注册的手机号不能与邀请人手机号相同`,
          confirmText: '确认',
          showCancel: false,
        });
        return false;
      }
      var sPhone = that.phone;
      if (!/^1\d{10}$/.test(sPhone)) {
        console.log('手机号码格式错误');
        uni.showToast({
          icon: 'loading',
          title: '手机号码格式错误',
        });
        return false;
      }
      console.log('手机号码正确');
      var token = that.token;
      var a = {
        mobile: that.phone,
        verifyCode: that.pass,
      };
      console.log(a, '登录入参');
      this.loading = true;
      const [err, res] = await register(
        {
          mobile: that.phone,
          verifyCode: that.pass,
          inviterMobile: this.inviterMobile,
          inviterCustId: this.inviterCustId,
        },
        {
          minitProgramToken: token || '',
        }
      );
      this.loading = false;
      console.log(res, 'res', err);
      if (res && res.ret == '200') {
        uni.showToast({
          title: '注册成功,稍后自动跳转首页',
          icon: 'success',
          duration: 1200,
        });
        uni.setStorageSync('token', token);
        this.$store.dispatch('login/getUserInfoCallback').then(() => {
          setTimeout(function () {
            uni.navigateTo({
              url: '/pages/home/<USER>',
            });
          }, 1200);
        });
      }
      this.clear();
    },
    clear() {
      // this.phone = '';
      // this.pass = '';
      this.time = true;
    },
    downAppLink(url) {
      console.log('wx', 'wx');
      wx.navigateToMiniProgram({
        appId: 'wx602bfd6b5414b967', //必传值
        success(res) {
          // 打开成功
        },
        fail(res) {
          //打开失败
        },
      });
      // uni.navigateTo({
      //   url: '/pages/setting/down/index' + '?url=' + url + '&title=' + '下载',
      // });
      // uni.setClipboardData({
      //   data: url, // 替换为你的 iOS 下载链接
      //   success: function () {
      //     wx.showToast({
      //       title: '下载链接已复制，去浏览器粘贴下载',
      //     });
      //   },
      // });
    },
  },
};
</script>

<style scoped lang="scss">
.page-wrap {
  width: 100%;
  min-height: 100vh;
  background: #ffffff;
  background-size: 100% auto;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  padding: 40rpx 24rpx;
  box-sizing: border-box;
  .login image {
    display: block;
    width: 280rpx;
    margin: 15rpx auto 40rpx auto;
    border-radius: 50%;
  }
  .section-phone {
    position: relative;
    height: 90rpx;
    line-height: 90rpx;
    padding: 0 255rpx 0 200rpx;
    background-color: #fff;
    box-sizing: border-box;
  }
  .section-phone::after {
    content: '';
    position: absolute;
    left: 30rpx;
    right: 0;
    bottom: 0;
    display: block;
    border-bottom: 1rpx solid #e5e5e5;
  }
  .section-phone text {
    position: absolute;
    left: 30rpx;
    top: 0;
    width: 170rpx;
    height: 90rpx;
    color: #000;
    font-size: 32rpx;
  }
  .section-phone input {
    width: 100%;
    height: 90rpx;
    font-size: 32rpx;
  }
  .section-phone button {
    position: absolute;
    top: 0;
    right: 0;
    display: block;
    width: 255rpx;
    height: 90rpx;
    color: #828282;
    font-size: 32rpx;
    border-left: 1rpx solid #e5e5e5;
  }
  .btn-phone {
    height: 90rpx;
    line-height: 90rpx;
    color: #fff;
    background-color: #fff;
    border-radius: 0;
    font-size: 26rpx;
  }
  .section-phone .btn-phonesd {
    background-color: #e00a22;
    color: #fff;
  }
  .btn-phone::after {
    border-color: transparent;
  }
  .section-code {
    position: relative;
    height: 90rpx;
    line-height: 90rpx;
    padding: 0 30rpx 0 200rpx;
    background-color: #fff;
  }
  .section-code text {
    position: absolute;
    left: 30rpx;
    top: 0;
    width: 170rpx;
    height: 90rpx;
    color: #000;
    font-size: 32rpx;
  }
  .section-code input {
    width: 100%;
    height: 90rpx;
    font-size: 32rpx;
  }
  .txt-notice {
    display: block;
    padding-top: 26rpx;
    padding-bottom: 40rpx;
    font-size: 25rpx;
    color: #9f9f9f;
    text-align: center;
  }
  .btn-submit {
    height: 88rpx;
    line-height: 88rpx;
    color: #fff;
    background-color: #e12037;
    border-radius: 15px;
    font-size: 32rpx;
    margin: 0 30rpx;
    margin-bottom: 45rpx;
  }
  .btn-submit::after {
    border-color: transparent;
  }
  .txt-read {
    display: block;
    padding-top: 30rpx;
    font-size: 24rpx;
    color: #9f9f9f;
    text-align: center;
  }
  .txt-read .inlines {
    color: #576b95;
    display: inline-block;
  }
  .btn-wrap {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    color: #379af7;
    margin-bottom: 30rpx;
    flex-wrap: wrap;
    .tip {
      width: 100%;
      line-height: 35rpx;
      text-align: center;
      margin-top: 30rpx;
      color: #828282;
    }
    .btn {
      display: flex;
      align-items: center;
      padding: 20rpx;
      box-shadow: 1px 1px 1px 1px rgba(0, 0, 0, 0.1);
      border-radius: 10rpx;
    }
    image {
      width: 40rpx;
    }
  }
  .banner {
    margin: 20rpx auto;
    width: 686rpx;
    image {
      width: 100%;
    }
  }
  .rule {
    .title {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      font-weight: bold;
      line-height: 43.2px;
      text-align: center;
      color: #333333;
      gap: 20rpx;
      grid-gap: 20rpx;
      .line {
        width: 92rpx;
        height: 3rpx;
        background: linear-gradient(270deg, #202020 0%, rgba(23, 23, 23, 0) 100%);
      }
      .line2 {
        width: 92rpx;
        height: 3rpx;
        background: linear-gradient(90deg, #202020 0%, rgba(23, 23, 23, 0) 100%);
      }
    }
    .rule-text-area {
      padding: 24rpx;
      color: #000;
      view {
        color: #000;
      }
      rich-text {
        color: #000;
      }
    }
  }
}
</style>
