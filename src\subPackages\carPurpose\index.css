.type-box {
  margin: 40rpx 24rpx 0 24rpx;
}
.type-box-title {
  font-size: 32rpx;
  font-weight: bold;
  line-height: normal;
  text-align: center;
  letter-spacing: 0em;
  color: #333333;
}
.type-box-cars {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10rpx;
  grid-gap: 10rpx;
}
.type-box-car-item {
  position: relative;
  padding: 10rpx;
  background: #fff;
  margin-top: 24rpx;
  border-radius: 12rpx;
  box-sizing: border-box;
  border: 2rpx solid #fff;
}
.type-box-car-item-image {
  width: 145rpx;
  height: 145rpx;
}
.type-box-car-item-name {
  font-size: 24rpx;
  font-weight: bold;
  line-height: normal;
  text-align: center;
  letter-spacing: 0rem;
  color: #333333;
  margin-top: 16rpx;
}
.type-box-car-item-select {
  border: 2rpx solid #1e9cff;
}
.type-box-car-item-select-icon {
  position: absolute;
  top: 0;
  left: 0;
  width: 36rpx;
  height: 36rpx;
}
.footer {
  z-index: 1;
  margin: 0;
}
.footer .footer-button {
  display: flex;
  gap: 24rpx;
  grid-gap: 24rpx;
}
.footer .footer-button .button-item {
  flex: 1;
}
