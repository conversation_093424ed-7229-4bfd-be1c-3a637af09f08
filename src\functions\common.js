import qs from 'qs';
import Vue from 'vue';
import { isUrl } from '../functions/verify';

/**
 * 支付错误信息
 */
export const hasPayErrorCommon = (status) => {
  let isError = true;
  switch (Number(status)) {
    case 9000:
      isError = false;
      break;
    case 8000:
      break;
    case 4000:
      uni.showToast({
        title: '支付失败',
        icon: 'none',
      });
      break;
    case 5000:
      uni.showToast({
        title: '重复请求',
        icon: 'none',
      });
      break;
    case 6001:
      uni.showToast({
        title: '取消支付',
        icon: 'none',
      });
      break;
    case 6002:
      uni.showToast({
        title: '网络连接出错',
        icon: 'none',
      });
      break;

    default:
      break;
  }
  return isError;
};
/**
 * 扫码
 */
export const scanCodeCommon = () => {
  return new Promise((resolve, reject) => {
    // #ifdef  H5
    ls.qrCode((res) => {
      if (res.code == 200) {
        resolve(res.data.scanString);
        return;
      } else {
        reject({ msg: '未能识别二维码' });
        return;
      }
    });

    // #endif

    // #ifndef MP-WEIXIN

    uni.scanCode({
      onlyFromCamera: true,
      success: (res) => {
        console.log('扫码成功++++', res);
        let code = res.result;
        let scanCode = '';
        const store = Vue.prototype.$store;
        store.dispatch('order/setThirdCallBackOrderNo', '');
        if (/^\d+$/.test(code)) {
          console.log('纯数字 qrCode=1234');
          scanCode = code;
        } else {
          scanCode = code.replace(/[\r\n\s]/g, '');
        }
        resolve(scanCode);
      },
      fail: (err) => {
        console.log('扫码失败++++', err);
        reject(err);
      },
    });

    // #endif
  });
};

/**
 * 获取剪切板内容
 */
export const getClipboardDataCommon = () => {
  return new Promise((resolve, reject) => {
    // #ifdef  H5
    ls.clipboard((res) => {
      if (res.code == 200) {
        resolve(res.data);
        return;
      } else {
        reject({ msg: '' });
        return;
      }
    });

    // #endif

    // #ifndef H5

    uni.getClipboardData({
      success: (res) => {
        resolve(res.data);
      },
      fail: (err) => {
        reject(err);
      },
    });

    // #endif
  });
};

/**
 * 复制到剪切板
 */
export const setClipboardDataCommon = (value) => {
  return new Promise((resolve, reject) => {
    // #ifdef  H5
    // ls.clipboard((res) => {
    //     if (res.code == 200) {
    //         resolve(res.data)
    //         return
    //     } else {
    //         reject({ msg: '' })
    //         return
    //     }
    // })
    uni.setClipboardData({
      data: value,
      success: (res) => {
        resolve(res.data);
      },
      fail: (err) => {
        reject(err);
      },
    });

    // #endif

    // #ifndef H5

    uni.setClipboardData({
      data: value,
      success: (res) => {
        resolve(res.data);
      },
      fail: (err) => {
        reject(err);
      },
    });

    // #endif
  });
};

/**
 * 拨号
 */
export const makePhoneCallCommon = (phoneNumber) => {
  // #ifdef  H5
  ls.callNo({ tel: phoneNumber });
  // #endif
  // #ifndef H5
  uni.makePhoneCall({
    phoneNumber: phoneNumber, //仅为示例
  });
  // #endif
};

/**
 * 开启地图导航
 */
export const openLocationCommon = (info) => {
  // #ifdef  H5
  const { bridge } = window;
  if (bridge && bridge.hasNativeMethod) {
    if (bridge.hasNativeMethod('ls_open_location')) {
      bridge.call('ls_open_location', {
        latitude: info.latitude,
        longitude: info.longitude,
        name: info.name,
      });
      return;
    }
  }
  // #endif
  uni.openLocation({
    success: function () {
      console.log('success');
    },
    ...info,
  });
};

export const jumpUrlByTypeCommon = (parms) => {
  console.log('跳转parms', parms);
  if (parms.linkType == '01') {
    console.log('此处是唤起支付宝自己的页面');
    my.ap.navigateToAlipayPage({
      // 例如跳转到共享单车页面，其 schema 格式为：
      // alipays://platformapi/startapp?appId=60000155&chInfo=ch_${appid}，${appid} 替换为自己的16位 appid，例如：
      path: parms.contentUrl,
      success: (res) => {},
      fail: (error) => {},
    });
  } else if (parms.linkType == '02') {
    if (isUrl(parms.contentUrl)) {
      uni.navigateTo({
        url: '/pages/webViews/index?url=' + parms.contentUrl,
      });
    } else {
      uni.navigateTo({
        url: parms.contentUrl,
      });
    }
    console.log('此处是webView页面');
  } else if (parms.linkType == '03') {
    uni.navigateToMiniProgram({
      appId: parms.appId,
      path: parms.contentUrl,
      extraData: parms.extraData,
      success(res) {
        // 打开成功
      },
    });
  }
};

/**
 * base64转blob二进制对象
 * @param {*} base64Data
 */
export function dataURLtoBlobCommon(base64Data) {
  let byteString;
  if (base64Data.split(',')[0].indexOf('base64') >= 0) byteString = atob(base64Data.split(',')[1]);
  else byteString = unescape(base64Data.split(',')[1]);
  let mimeString = base64Data.split(',')[0].split(':')[1].split(';')[0];
  let ia = new Uint8Array(byteString.length);
  for (let i = 0; i < byteString.length; i++) {
    ia[i] = byteString.charCodeAt(i);
  }
  return new Blob([ia], { type: mimeString });
}

/**
 * 深拷贝对象
 * @param {要拷贝对象} obj
 */
export const copyObjectCommon = (obj) => {
  let str,
    newobj = Array.isArray(obj) === true ? [] : {};
  if (typeof obj !== 'object') {
    return;
  } else if (JSON) {
    (str = JSON.stringify(obj)), //系列化对象
      (newobj = JSON.parse(str)); //还原
  } else {
    for (let i in obj) {
      newobj[i] = typeof obj[i] === 'object' ? copyObjectCommon(obj[i]) : obj[i];
    }
  }
  return newobj;
};

/**
 * 计算字符串长度
 * @param {*} str
 */
export const strlenCommon = (str) => {
  let len = 0;
  for (let i = 0; i < str.length; i++) {
    let c = str.charCodeAt(i);
    //单字节加1
    if ((c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f)) {
      len++;
    } else {
      len += 2;
    }
  }
  return len;
};

/**
 * 格式化电话号码
 */
export const getFormatPhoneCommon = (phone) => {
  let reg = /(\d{3})\d{4}(\d{4})/;
  return phone.replace(reg, '$1****$2');
};

/**
 * 获取两个坐标距离
 * @param srcCoords
 * @param destCoords
 * @returns {number}
 */
export const getDistanceCommon = (srcCoords, destCoords) => {
  var LONGITUDE_RATE = 102834.74258026089786013677476285;
  var LATITUDE_RATE = 111712.69150641055729984301412873;
  var b = Math.abs((srcCoords.longitude - destCoords.longitude) * LONGITUDE_RATE);
  var a = Math.abs((srcCoords.latitude - destCoords.latitude) * LATITUDE_RATE);
  return Math.ceil(Math.sqrt(a * a + b * b));
};

// /**
//  * 触发 window.resize
//  */
// export const dispatchResizeEventCommon = () => {
//     const event = document.createEvent('HTMLEvents')
//     event.initEvent('resize', true, true)
//     event.eventType = 'message'
//     window.dispatchEvent(event)
// }

/**
 * 获取URL参数
 */
export const getPageQueryCommon = () => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const currentPath = currentPage.route;
  return qs.parse(currentPath.href.split('?')[1]);
};

// //友盟统计页面
// export const sendYMCPage = () =>{
// 	if (window._czc) {
// 		let location = window.location
// 		let contentUrl = location.pathname + location.hash
// 		let refererUrl = '/'
// 		window._czc.push(['_trackPageview', contentUrl, refererUrl])
// 	}
// }
// //友盟统计事件
// export const sendYMCEvent = (category,action,label,value,nodeid) =>{
// 	if (window._czc) {
// 		window._czc.push(["_trackEvent",category,action]);
// 	}
// }

export const getDestCommon = (distance) => {
  const dest = Number(distance);

  if (dest > 1000) {
    return Math.round(dest / 100) / 10 + '千米';
  } else {
    return dest + '米';
  }
};

export const getFormatTime = (time) => {
  let hour = '-';
  let min = '-';
  if (time) {
    const now = Number(time);

    let h = Math.floor(now / 60);

    if (h < 10) {
      hour = '0' + h;
    } else {
      hour = h;
    }
    let m = Math.floor(now % 60);
    if (m < 10) {
      min = '0' + m;
    } else {
      min = m;
    }
  }
  return {
    hour,
    min,
  };
};
