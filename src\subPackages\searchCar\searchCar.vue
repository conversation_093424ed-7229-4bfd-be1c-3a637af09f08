<template>
  <view>
    <view class="search-head">
      <!-- <text class="icon iconfont icon-arrow_left search-lefy" size="30" @click="back"></text> -->
      <input @input="inputs" placeholder="查找车型" />
      <text class="icon iconfont icon-search" size="30"></text>
      <!-- <input placeholder='查找车型'></input>
    <icon class="icon iconfont icon-search" size="30"></icon> -->
    </view>

    <view class="search-middle">
      <view class="search-add" style="border-bottom: 1rpx solid #cbcbcb">
        <view>
          <view class="add-head-bull styleColorB"></view>
          我的爱车
        </view>
        <view @click="add" class="styleColorA">添加爱车</view>
      </view>
      <view v-if="Not" class="search-msg">添加爱车可匹配充电车型</view>
      <view v-else class="search-push">
        <text v-for="(item, index) in list" :key="index">{{ item.brandName }} {{ item.modelName }}</text>
      </view>
    </view>

    <view class="search-middle search-list">
      <view class="search-add">
        <view>
          <view class="add-head-bull styleColorB"></view>
          充电记录
        </view>
      </view>
      <view class="list_li" v-for="(item, index) in List" :key="index">
        <view class="list_li_lf">
          <view v-if="!item.brandName">
            {{ item.modelName }}
            <text class="samlls">{{ item.custName }}</text>
          </view>
          <view v-else>
            {{ item.brandName }} {{ item.modelName }}
            <text class="samlls">{{ item.custName }}</text>
          </view>
          <view class="samlls">{{ item.displayGunName }}</view>
        </view>

        <view class="list_li_rt">{{ item.timeFor }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import { getOpenChargingInfo, getPref } from '@/services/index.js';
export default {
  data() {
    return {
      List: [],
      list: [],
      Not: false,
      msg: '',
      LT: [],
      Msg: '',
      stationId: '',
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    console.log(options, '传参');
    var that = this;
    that.setData({
      stationId: options.stationId,
    });
    let params = {
      stationId: options.stationId,
    };
    const [, res] = await getOpenChargingInfo(params);
    if (res) {
      console.log(res, '七天内充电记录');
      var list = res.list;
      if (res.list && res.list.length > 0) {
        for (var i = 0; i < res.list.length; i++) {
          var start = new Date(res.list[i].chargingTime);
          console.log(res.list[i].chargingTime, 'res.list.chargingTimeres.data.list.chargingTime');
          var end = new Date();
          console.log(end, '-', start, 'pop');
          var ti = Math.ceil(end - start) / 1000 / 60;
          if (ti < 1 && ti > 0) {
            var T = '不到1分钟'; //最近充电不到一分钟
          } else if (ti > 1 && ti < 60) {
            var T = Math.ceil(ti) + '分钟前'; //最近充电xx分钟前
          } else if (ti >= 60 && ti <= 1440) {
            var T = parseInt(ti / 60) + '小时前'; //最近充电xx分钟前
          } else if (ti > 1440) {
            var T = res.list[i].chargingTime; //超过一天  显示后台发送的时间，
          }
          list[i].timeFor = T;
        }
        console.log(list, '整合后的');
        for (var i = 0; i < list.length; i++) {
          if (list[i].brandName == null) {
            list[i].brandName = '';
          }
        }
        console.log(list, '哈哈哈哈哈');
        that.setData({
          List: list,
          LT: list,
        });
      } else {
      }
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},
  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    let params = {
      stationId: this.stationId,
    };
    var that = this;
    const [, res] = await getPref(params);
    if (res && res.carList) {
      if (res.carList.length > 0) {
        that.setData({
          list: res.carList,
          Not: false,
        });
      } else if (res.carList.length == 0) {
        that.setData({
          Not: true,
        });
      }
    }
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
  methods: {
    inputs(e) {
      console.log(e.detail.value);
      this.setData({
        msg: e.detail.value,
      });
      var Msg = e.detail.value;
      var list = [];
      var that = this;
      that.setData({
        Msg: e.detail.value,
      });
      if (!Msg) {
        console.log('空值');
        that.setData({
          List: that.LT,
        });
        console.log(that.LT, '_self.data.LT');
        return false;
      }
      for (var i = 0; i < that.LT.length; i++) {
        console.log(Msg, '这是搜索之');
        if (that.LT[i].brandName.indexOf(Msg) > -1 || that.LT[i].modelName.indexOf(Msg) > -1) {
          list.push(that.LT[i]);
        }
        console.log(list, '当前搜索到的');
      }
      that.setData({
        List: list,
      });
    },

    add() {
      uni.navigateTo({
        url: '/subPackages/add/add',
      });
    },
    back(params) {
      uni.navigateBack();
    },
  },
};
</script>
<style>
.search-head {
  width: 100%;
  height: 88rpx;
  background: #fff;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
}
.search-head input {
  width: 592rpx;
  height: 66rpx;
  border-radius: 8rpx;
  border: 1rpx solid #ccc;
  font-size: 28rpx;
  padding-left: 20rpx;
  padding-right: 20rpx;
  box-sizing: border-box;
}
.search-head .icon {
  line-height: 88rpx;
  font-size: 53rpx;
  color: #666;
  padding-left: 15rpx;
}
.search-head .search-lefy {
  padding-left: 20rpx;
  font-size: 42rpx;
  padding-right: 5rpx;
  /* padding-bottom: 10rpx; */
}
.search-middle {
  background: #fff;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}
.search-middle .search-add {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333;
  font-size: 26rpx;
  height: 88rpx;
  box-sizing: border-box;
  padding-right: 30rpx;
  margin-left: 30rpx;
  /* border-bottom: 1rpx solid #CBCBCB */
}
.search-middle .search-msg {
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #999;
}
.search-middle .search-push {
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 20rpx 30rpx 20rpx 30rpx;
}
.search-middle .search-push text {
  font-size: 26rpx;
  color: #666;
  margin-right: 30rpx;
  line-height: 41rpx;
}
.search-middle .add-head-bull {
  width: 7rpx;
  height: 28rpx;
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 3rpx;
  margin-right: 12rpx;
}
.search-list .list_li {
  /* height: 120rpx; */
  margin-left: 30rpx;
  padding-right: 30rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
  color: #666;
  border-top: 1rpx solid #cbcbcb;
}
.search-list .list_li .list_li_lf {
  width: 50%;
  display: flex;
  flex-wrap: wrap;
  align-content: space-around;
  box-sizing: border-box;
  padding: 15rpx 0;
}
.search-list .list_li .list_li_lf view {
  width: 100%;
  font-size: 28rpx;
  color: #333;
}
.search-list .list_li .list_li_lf text {
  margin-left: 10rpx;
}
.search-list .list_li .list_li_lf .samlls {
  font-size: 26rpx;
  color: #666;
}
/* .search-list .list_li view{
  width: 50%;
  display: flex;
  align-items: center;
  height: 100%;
} */
.search-list .list_li .list_li_rt {
  width: 40%;
  line-height: 120rpx;
  text-align: right;
  font-size: 24rpx;
  color: #666;
}
</style>
