<template>
  <!-- pages/mobilephone/mobilephone.wxml -->
  <view class="mobilephone">
    <input class="tell" type="number" placeholder="请输入您的手机号码" />
    <button class="btn-fiexd blue-bg styleColorB">确认绑定</button>
  </view>
</template>

<script>
// pages/mobilephone/mobilephone.js
export default {
  data() {
    return {};
  },
  /**
   * 生命周期函数--监听页面加载
   */ onLoad: function (options) {},
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {},
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {},
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {},
  methods: {},
};
</script>
<style>
@import './mobilephone.css';
</style>
