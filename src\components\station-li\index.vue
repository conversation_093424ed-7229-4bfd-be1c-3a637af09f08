<template>
  <div class="station-li" @click="jump(currentItem)">
    <div :class="{ 'li-disabled': currentItem.stationStatus === '04' }">
      <!-- 可选内容 -->
      <div class="station-li-top">
        <img class="li-top-img" :src="currentItem.stationUrl || locationImg" />
        <view class="li-top-msg">
          <view class="msg-title">{{ currentItem.stationName || '' }}</view>
          <view class="msg-more">
            <view class="more-fast" v-if="currentItem.dcNums">快</view>
            <view v-if="currentItem.dcNums">
              空
              <text>{{ currentItem.dcFreeNums || 0 }}</text>
              /{{ currentItem.dcNums || 0 }}
            </view>
            <view :class="'more-slow ' + (currentItem.dcNums ? 'more-slow-margin' : '')" v-if="currentItem.acNums"
              >慢</view
            >
            <view v-if="currentItem.acNums">
              空
              <text>{{ currentItem.acFreeNums || 0 }}</text>
              /{{ currentItem.acNums || 0 }}
            </view>
          </view>
        </view>
      </div>
      <view class="station-li-price" :class="{ isVip: memberInfo.vipFlag == '1' }">
        <view class="price-normal">{{ currentItem.priceRemarkNum || 0 }}</view>
        <view>元/度起</view>
        <!--        会员日折上折-->
        <view class="price-vip-box" v-if="currentItem.vvipFlag && currentItem.vipFlag">
          <div class="label">
            会员折上折 :
            <div class="card">
              <div class="card-wrap"></div>
              <div class="box"></div>
            </div>
          </div>
          <div class="money">¥ {{ currentItem.vipPrice || 0 }}</div>
          <!-- <text class="price-text">{{ currentItem.vipPrice || 0 }}</text>
          <text class="price-unit">元/度起</text>
          <img class="price-vvip-img" :src="`${IMG_PATH}member/vvipFlag.png`" /> -->
        </view>
        <!-- v-else-if="currentItem.vipFlag"        会员站-->
        <view class="price-vip-box" v-else-if="currentItem.vipFlag">
          <div class="label">
            会员价 :
            <div class="card">
              <div class="card-wrap"></div>
              <div class="box"></div>
            </div>
          </div>
          <div class="money">¥ {{ currentItem.vipPrice || 0 }}</div>
          <!-- <text class="price-text">{{ currentItem.vipPrice || 0 }}</text>
          <text class="price-unit">元/度起</text> -->
          <!-- <img class="price-vip-img" :src="`${IMG_PATH}member/vipFlag.png`" /> -->
        </view>
      </view>
      <view class="station-li-tags">
        <view
          :class="'tags-li-external  ' + (item.tagName === '对外开放' ? '' : 'tags-li-internal')"
          v-for="(item, itemIndex) in currentItem.runTagList"
          :key="itemIndex"
        >
          <img class="external-img" :src="item.tagName === '对外开放' ? tag1 : tag2" />
          <view>{{ item.tagName }}</view>
        </view>
        <view class="tags-ul">
          <view class="tags-li" v-for="(item, itemIndex) in currentItem.tagList" :key="itemIndex">{{
            item.tagName
          }}</view>
        </view>
        <view
          class="station-distance"
          v-if="currentItem.distance > 1000"
          :style="{ color: theme == 'newYear' ? '#ff1100' : '#2d9aff' }"
        >
          <img class="distance-img" :src="`${IMG_PATH_UI}index-position.png`" />
          <view>{{ currentItem.distance | toFix }}km</view>
        </view>
        <view
          class="station-distance"
          :style="{ color: theme == 'newYear' ? '#ff1100' : '#2d9aff' }"
          v-if="currentItem.distance > 0 && currentItem.distance <= 1000"
        >
          <img class="distance-img" :src="`${IMG_PATH_UI}index-position.png`" />
          <view>{{ currentItem.distance | fixed }}m</view>
        </view>
      </view>
    </div>
    <div class="li-repair" v-if="currentItem.stationStatus === '04'">
      <img class="repair-icon" :src="`${IMG_PATH}index-tag3.png`" />
      <div>检修中</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    currentItem: {
      type: Object, // 简化的定义方式
    },
    pageType: {
      type: String,
      default: '',
    },
    memberInfo: {
      type: Object,
      default: () => {
        return {
          vipFlag: '0',
        };
      },
    },
  },
  components: {},
  data() {
    return {};
  },
  computed: {
    tag1() {
      return this.IMG_PATH + 'index-tag1.png';
    },
    tag2() {
      return this.IMG_PATH + 'index-tag2.png';
    },
    locationImg() {
      return this.IMG_PATH + 'bgk_img_17_1.png';
    },
  },
  watch: {},
  onLoad() {},
  mounted() {},
  methods: {
    closeModal: function () {
      this.triggerEvent('closeModal', '');
    },
    jump(item) {
      const { stationId, distance } = item;
      if (this.$props.pageType === 'rtnStation') {
        this.$emit('rtnStationInfo', {
          ...item,
        });
      } else {
        uni.navigateTo({
          url: `/pages/basic/station/station?stationId=${stationId}&distance=${distance}`,
        });
      }
    },
  },
  filters: {
    toFix: (value, count = 2) => {
      var num = (value * 1) / 1000;
      return num.toFixed(count);
    },
    fixed: (value, count = 2) => {
      return value.toFixed(count);
    },
  },
};
</script>

<style scoped lang="scss">
.station-li {
  margin-top: 16rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  background: #ffffff;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

.station-li-top {
  display: flex;
}
.li-top-img {
  width: 224rpx;
  height: 144rpx;
  border-radius: 16rpx;
  margin-right: 16rpx;
}
.li-top-msg {
  flex: 1;
}
.msg-title {
  font-family: PingFang SC;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 38rpx;
  height: 76rpx;
  color: #333333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.msg-more {
  margin-top: 26rpx;
  display: flex;
  align-items: center;
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: normal;
  line-height: 32rpx;
  color: #999999;
}
.msg-more text {
  font-size: 28rpx;
  font-weight: 500;
  line-height: 42rpx;
  color: #333333;
  margin-left: 4rpx;
}
.more-fast {
  border-radius: 8rpx;
  background: rgba(27, 139, 255, 0.12);
  width: 40rpx;
  height: 40rpx;
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: normal;
  color: #1b8bff;
  text-align: center;
  line-height: 40rpx;
  margin-right: 8rpx;
}
.more-slow {
  border-radius: 8rpx;
  background: rgba(54, 197, 96, 0.12);
  width: 40rpx;
  height: 40rpx;
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: normal;
  color: #36c560;
  text-align: center;
  line-height: 40rpx;
  margin: 0 8rpx 0 0rpx;
}
.more-slow-margin {
  margin-left: 40rpx;
}
.station-li-price {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: normal;
  line-height: 32rpx;
  color: #b3b3b3;
}
.station-li-price.isVip {
  .price-normal {
    color: #b3b3b3;
    font-family: PingFang SC;
    font-size: 24rpx;
    font-weight: normal;
    line-height: 32rpx;
    text-decoration: line-through;
  }
}
.price-normal {
  font-family: D-DIN;
  font-size: 40rpx;
  font-weight: bold;
  line-height: normal;
  color: #eb5f39;
  margin-right: 4rpx;
}
.price-vip {
  font-family: D-DIN;
  font-size: 32rpx;
  font-weight: normal;
  line-height: normal;
  color: #333333;
  margin: 0 4rpx 0 24rpx;
  display: flex;
  align-items: center;
  .price-text {
    font-family: D-DIN;
    font-size: 32rpx;
    color: #333333;
  }
  .price-unit {
    font-size: 24rpx;
    line-height: 32rpx;
    color: #b3b3b3;
    margin-left: 4rpx;
  }
}
.price-vip-box {
  font-family: D-DIN;
  height: 40rpx;
  background: #feebe0;
  display: flex;
  margin: 0 4rpx 0 24rpx;
  font-size: 22rpx;
  color: #fff;
  line-height: 40rpx;
  border-radius: 10rpx;
  // overflow: hidden;

  .label {
    padding: 0 12rpx;
    height: 100%;
    background-color: #333333;
    color: #ffe7da;
    position: relative;
    border-radius: 12rpx 0 12rpx 12rpx;
    .card {
      width: 24rpx;
      height: 24rpx;
      position: absolute;
      right: -24rpx;
      top: 0;
      border-color: transparent;
      background: transparent;
      overflow: hidden;
      padding: 0;
      .card-wrap {
        width: 100%;
        height: 100%;
        background: #333333;
        z-index: 0;
      }
      .box {
        position: absolute;
        width: 48rpx;
        height: 48rpx;
        background-color: #feebe0;
        border-radius: 48rpx;
        bottom: -24rpx;
        right: -24rpx;
        z-index: 1;
      }
    }
  }
  .money {
    padding: 0 12rpx;
    font-size: 24rpx;
    color: #333333;
    z-index: 2;
  }
}
.price-vip-img {
  width: 54rpx;
  height: 30rpx;
  margin-left: 8rpx;
}
.price-vvip-img {
  width: 126rpx;
  height: 30rpx;
  margin-left: 8rpx;
}
.station-li-tags {
  margin-top: 16rpx;
  display: flex;
  align-items: center;
}
.tags-ul {
  display: flex;
  overflow-x: auto;
  margin-right: 20rpx;
  white-space: nowrap;
  flex: 1;
}
.tags-ul::-webkit-scrollbar {
  display: none;
}
.tags-li {
  margin-right: 12rpx;
  border-radius: 8rpx;
  display: flex;
  padding: 4rpx 8rpx;
  box-sizing: border-box;
  border: 2rpx solid #ff8301;
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: normal;
  line-height: 32rpx;
  color: #ff8301;
  position: relative;
}
.li-repair {
  position: absolute;
  left: 268rpx;
  top: 207rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5rpx 8rpx;
  border-radius: 16rpx;
  background: rgba(0, 0, 0, 0.5);
  font-family: PingFang SC;
  font-size: 26rpx;
  font-weight: normal;
  color: #ffffff;

  .repair-icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
  }
}
.tags-li-first {
  border: 2rpx solid #36c560;
  color: #36c560;
}
.tags-li-external {
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  background: #1e9cff;
  justify-content: center;
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: normal;
  color: #ffffff;
  margin-right: 12rpx;
  padding: 4rpx 10rpx;
}
.tags-li-internal {
  background: #ff8301;
}
.external-img {
  width: 28rpx;
  height: 28rpx;
  margin-right: 4rpx;
}
.station-distance {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: normal;
  line-height: 32rpx;

  z-index: 1;
}
.distance-img {
  width: 28rpx;
  height: 28rpx;
  margin-right: 4rpx;
}
.li-disabled {
  opacity: 0.2;
}
</style>
