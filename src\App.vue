<script>
import { mapActions } from 'vuex';
import store from './store';
import { GetToken } from '@/utils/bridge/index.js';
// import { md5 } from 'js-md5';
import { getINDToken } from '@/services/index.js';
export default {
  data() {
    return {};
  },
  onLaunch(options) {
    // #ifdef H5
    console.log(window.location.href, '当前页2面');
    // #endif
    uni.setStorageSync('app', '');
    // 设置默认token，仅用于开发环境
    // const tokenToStore =
    //   'Bearer eyJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************.LwAoEcfyQyIA6ppyn0JA4zkBVpSN6v1zFPlEzAsQhSg';
    // uni.setStorageSync('token', tokenToStore);
    // uni.setStorageSync('app', 'i宁德');
    // uni.setStorageSync('app', '鸿蒙电动宁德');
    // uni.setStorageSync('app', '电动宁德');

    // // 如果URL中包含queryToken参数，则使用该参数作为token
    // if (options && options.query && options.query.queryToken) {
    //   const queryTokenValue = options.query.queryToken;
    //   uni.setStorageSync('token', queryTokenValue);
    // }
    // #ifdef MP-WEIXIN
    uni.setStorageSync('app', '小程序');
    // #endif

    console.log('测试打印:', options);
    // #ifdef H5
    if (options && options.query && options.query.code) {
      // i宁德
      console.log('--------------', options.query.code);
      uni.setStorageSync('app', 'i宁德');
      // this.getINDToken(options.query.code)
      this.getAppTokenIND(options.query.code);
    } else {
      this.getAppToken();
    }
    // #endif
    // else {
    // uni.setStorageSync('token', '');
    // uni.miniProgram.postMessage({ data: { token } });
    // }
    if (options.query) {
      let { qrCode, connectorID, operatorID, orderNo, qrCodeContent, exchangeCode, code, state, type, mobile, custId } =
        options.query;
      console.log(type, 'type');
      if (type == 'share') {
        uni.navigateTo({
          url: '/pages/setting/register/index' + `?mobile=${mobile}&custId=${custId}`,
        });
        return;
      }
      if (code && state) {
        // i宁德TODO:
        // uni.setStorageSync('app', 'i宁德');
        // this.getINDToken(code, state);
      }
      if (qrCode) {
        let urlCode = qrCode.replace(/[\r|\n|\s]/g, '');
        console.log('urlCode:', urlCode);
        var str = urlCode;
        var message;
        var result;
        if (str.lastIndexOf('\/')) {
          message = str.lastIndexOf('\/');
          result = str.substring(message + 1, str.length);
        } else {
          result = urlCode;
        }

        console.log('扫码最终result：', result);
        uni.reLaunch({
          url: '/pages/mapHt/index?qrCode=' + result,
        });
        // this.setQurcode(urlCode)
      }
      if (exchangeCode) {
        this.setExchangeCode(exchangeCode);
      }
      if (qrCodeContent) {
        let qrCodeContentInfo = decodeURIComponent(qrCodeContent);
        let urlQrCode = qrCodeContentInfo.replace(/[\r\n|\r|\n|\s]/g, '');
        this.setQurcode(urlQrCode);
      }
      if (connectorID && operatorID) {
        let hlhtUrl = `hlht://${decodeURIComponent(connectorID)}.${decodeURIComponent(operatorID)}/`;
        console.log(66666, hlhtUrl);
        this.setQurcode(hlhtUrl);
      }
      if (orderNo) {
        //thirdCallBackOrderNo
        this.setThirdCallBackOrderNo(orderNo);
      }
    }

    // if (options.path == 'pages/team/index') {
    this.setStartPath(options.path);
    // }

    // #ifndef H5

    this.initUpdateApp();

    // #endif

    this.checkNetWork();

    uni.onNetworkStatusChange((res) => {
      console.log(res.isConnected);
      console.log(res.networkType);
      this.changeNetWork(res.isConnected);
    });

    // #ifdef MP-ALIPAY
    //收藏检测
    uni.onFavorite((res) => {
      if (res.success) {
        if (res.action == 'addToFavorite') {
          console.log('收藏成功....', res);
        } else if (res.action == 'removeFromFavorite') {
          console.log('收藏成功....', res);
        }
      }
    });
    // #endif
    // #ifdef MP-WEIXIN
    store.dispatch('config/initConfig');
    // #endif
    uni.setStorageSync('banneStatus', '1');
  },
  onShow(options) {
    console.log('App Show', options.query);
    // if (options.scene == '10000007') {
    //   uni.showToast({
    //     title: '解析失败，请使用下方扫码重新尝试',
    //     icon: 'none'
    //   })
    // }

    if (options.query) {
      let { qrCode, connectorID, operatorID, orderNo, qrCodeContent, exchangeCode, type, mobile, custId } =
        options.query;
      if (type == 'share') {
        wx.miniapp.shareMiniProgramMessage({
          // 注： 小程序原始id 不是小程序 appid，通常是 gh_xxx 开头的内容， 如 gh_d43f693ca31f
          userName: 'gh_4f72571e5c8f',
          path: 'pages/setting/register/index' + `?mobile=${mobile}&custId=${custId}`,
          title: '电动宁德,精彩缤纷',
          imagePath: `${this.IMG_PATH}share-logo.png`, // 分享的图片路径（可选）
          webpageUrl: '',
          withShareTicket: false,
          miniprogramType: 2,
          scene: 0,
          success(res) {
            wx.showToast({
              title: '成功：分享小程序',
            });
            console.log(res);
          },
          fail() {
            wx.showToast({
              title: '失败：分享小程序',
            });
          },
        });
      }
      if (qrCode) {
        let urlCode = qrCode.replace(/[\r\n|\r|\n|\s]/g, '');
        this.setQurcode(urlCode);
      }
      if (exchangeCode) {
        this.setExchangeCode(exchangeCode);
      }
      if (qrCodeContent) {
        let qrCodeContentInfo = decodeURIComponent(qrCodeContent);
        let urlQrCode = qrCodeContentInfo.replace(/[\r\n|\r|\n|\s]/g, '');
        this.setQurcode(urlQrCode);
      }
      if (connectorID && operatorID) {
        let hlhtUrl = `hlht://${decodeURIComponent(connectorID)}.${decodeURIComponent(operatorID)}/`;
        console.log(66666, hlhtUrl);
        this.setQurcode(hlhtUrl);
      }
      if (orderNo) {
        //thirdCallBackOrderNo
        this.setThirdCallBackOrderNo(orderNo);
      }
    }
    this.$store.dispatch('login/getUserInfoCallback');
    // if (options.query && options.query.qrCode) {
    //   let qrcode = options.query.qrCode.replace(/[\r\n|\r|\n|\s]/g,'');

    //   this.setQurcode(qrcode)
    // }
  },
  onHide() {
    console.log('App Hide');
  },
  methods: {
    ...mapActions('common', ['checkNetWork', 'changeNetWork']),
    ...mapActions('login', ['setQurcode', 'setStartPath', 'setExchangeCode']),
    ...mapActions('order', ['setThirdCallBackOrderNo']),

    // 专门预加载基础分包
    preloadBasicSubpackage() {
      console.log('开始预加载基础分包...');

      // 检查平台和API可用性
      // #ifdef MP-WEIXIN
      if (typeof wx !== 'undefined' && wx.preloadSubpackages) {
        // 获取网络状态
        uni.getNetworkType({
          success: (res) => {
            // 只在WiFi环境下预加载分包，避免消耗用户流量
            if (res.networkType === 'wifi' || process.env.NODE_ENV === 'development') {
              wx.preloadSubpackages({
                packages: [
                  {
                    name: 'pages/basic',
                    success: () => {
                      console.log('基础分包预下载成功');
                    },
                    fail: (err) => {
                      console.error('基础分包预下载失败:', err);
                    },
                  },
                ],
              });
            } else {
              console.log('非WiFi环境，跳过预加载基础分包');
            }
          },
        });
      } else {
        console.log('当前环境不支持预加载分包');
      }
      // #endif

      // #ifndef MP-WEIXIN
      console.log('当前平台不支持预加载分包');
      // #endif
    },
    initUpdateApp() {
      console.log(11111);
      if (uni.canIUse('getUpdateManager')) {
        const updateManager = uni.getUpdateManager();

        updateManager.onCheckForUpdate((res) => {
          // 请求完新版本信息的回调
          console.log(res.hasUpdate);
          if (res.hasUpdate) {
            updateManager.onUpdateReady((updateRes) => {
              uni.showModal({
                title: '更新提示',
                content: '新版本已经准备好，是否重启应用？',
                success(info) {
                  if (info.confirm) {
                    // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                    updateManager.applyUpdate();
                  }
                },
              });
            });
          }
        });

        updateManager.onUpdateFailed((res) => {
          // 新的版本下载失败
          uni.showModal({
            title: '更新失败',
            content: '更新小程序失败,请删除后台小程序后再重新开启',
            success(info) {
              console.log(44444, '小程序更新失败');
            },
          });
        });
      } else {
        // #ifdef  MP-ALIPAY
        uni.ap.updateAlipayClient({
          success: () => {
            uni.alert({
              title: '升级成功',
            });
          },
          fail: () => {
            uni.alert({
              title: '升级失败',
            });
          },
        });
        // #endif
      }
    },
    change(qrCode) {
      var url = qrCode;
      var arrObj = url.split('?');
      if (arrObj.length > 1) {
        var arrPara = arrObj[1].split('&');
        var arr;
        for (var i = 0; i < arrPara.length; i++) {
          arr = arrPara[i].split('=');
          if (arr != null && arr[0] == 'qrcode') {
            return arr[1];
          }
        }
        return '';
      } else {
        return url;
      }
    },
    getAppToken() {
      console.log('测试打印', uni.getStorageSync('app'));
      if (uni.getStorageSync('app') == 'i宁德') {
        return;
      }
      const timeoutPromise = new Promise((resolve, reject) => {
        setTimeout(() => {
          reject(new Error('Operation timed out.'));
        }, 5000); // 设置超时时间，单位为毫秒
      });
      const tokenBridge = GetToken();
      console.log(tokenBridge, 'tokenBridge');
      Promise.race([tokenBridge, timeoutPromise])
        .then((result) => {
          if (result.code == '9999') {
            const tokenToStore = result.data;
            uni.setStorageSync('token', tokenToStore);
            this.$store.dispatch('login/getUserInfoCallback');
          }
        })
        .catch((error) => {
          console.log('error:tokenBridge', error);
        });
    },
    async getAppTokenIND(code) {
      try {
        console.log('开始获取i宁德token, code:', code);

        // 设置超时处理
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error('获取token超时'));
          }, 150000); // 15秒超时
        });

        // 创建获取token的Promise
        const tokenPromise = new Promise(async (resolve, reject) => {
          try {
            // 调用getINDToken方法获取token
            const [tokenErr, tokenRes] = await getINDToken({ code });

            if (tokenErr) {
              console.error('获取token失败:', tokenErr);
              reject(tokenErr);
              return;
            }

            console.log('获取token成功:', tokenRes);

            if (tokenRes && tokenRes.token) {
              // 保存token和用户信息
              const tokenValue = 'Bearer ININGDE' + tokenRes.token + '-' + (tokenRes.userInfo?.mobile || '');
              uni.setStorageSync('token', tokenValue);

              if (tokenRes.userInfo && tokenRes.userInfo.toonNo) {
                uni.setStorageSync('toonno', tokenRes.userInfo.toonNo);
              }

              // 获取用户信息
              this.$store.dispatch('login/getUserInfoCallback');
              resolve(tokenRes);
            } else {
              console.error('token数据格式不正确:', tokenRes);
              reject(new Error('token数据格式不正确'));
            }
          } catch (error) {
            console.error('获取token过程中发生错误:', error);
            reject(error);
          }
        });

        // 使用Promise.race处理超时
        await Promise.race([tokenPromise, timeoutPromise]);
      } catch (error) {
        console.error('getAppTokenIND执行失败:', error);

        // 显示错误提示
        uni.showToast({
          title: '登录失败，请重试',
          icon: 'none',
          duration: 2000,
        });
      }
    },

    // i宁德授权
    async getINDToken(code) {
      new Promise(async (resolve, reject) => {
        // 获取token
        const [tokenErr, tokenRes] = await getINDToken({ code });
        if (tokenErr) {
          console.log('获取token失败');
          reject();
          return;
        }
        console.log(tokenRes, 'tokenRes');
        if (tokenRes) {
          uni.setStorageSync('token', 'Bearer ININGDE' + tokenRes?.token + '-' + tokenRes?.userInfo?.mobile);
          uni.setStorageSync('toonno', tokenRes?.userInfo?.toonNo);
          resolve(tokenRes);
        }
      });

      // const [signErr, signRes] = await getSign(signParam);
      // if(signErr){
      //   console.log('获取sign失败');
      //   return
      // }
      // 获取 accessToken 接口
      // uni.request({
      //   url: `https://uias.evstyle.cn/auth/token?clientId=20250319872153&grantType=authorizationCode&code=${code}&redirectUri=https://ndjtcs.evstyle.cn:6443/ndcharge/pages/home/<USER>
      //   // data: {
      //     // clientId:'20250319872153',
      //     // grantType:'authorizationCode',
      //     // code,
      //     // redirectUri: 'https://ndjtcs.evstyle.cn:6443/ndcharge/pages/home/<USER>',
      //     // sign:signRes?.sign ?? '',
      //   // },
      //   method: 'GET',
      //   header: {
      //     'content-type': 'application/json',
      //   },
      //   success: (res) => {
      //     console.log(res.data, '根据code的回参data');
      //     uni.setStorageSync('token', res.data?.accessToken);
      //     uni.setStorageSync('app', 'i宁德');
      //     // 获取用户信息接口
      //     uni.request({
      //       url: `https://uias.evstyle.cn/open/info/getUserInfo`,
      //       data: {
      //         accessToken: res.data?.accessToken,
      //       },
      //       method: 'GET',
      //       success: (info) => {
      //         console.log(info.data, '根据accessToken的回参data');
      //         uni.setStorageSync('toonno', info.data?.toonNo": );
      //       },
      //       fail: (err) => {
      //         console.log('获取授权信息err',err);
      //       },
      //     });
      //   },
      //   fail: (err) => {
      //     console.log('授权err',err);

      //   },
      // });
    },
  },
};
</script>

<style lang="scss">
@import './app.css';
@import './common/css/iconfont.css';
@import './common/css/styleColor';
@import './common/css/global.css';
/* #ifdef H5 */
uni-page-head {
  display: none;
} /* #endif */
/*每个页面公共css */
#app {
  width: 100%;
  height: 100%;
}
uni-page-body,
uni-page-refresh {
  height: 100%;
}

.feedback-placeholder {
  color: #efefef;
  font-size: 26upx;
}

.close-btn {
  position: fixed;
  top: 25upx;
  left: 30upx;
  z-index: 1000;
  font-size: 40upx;
}
.list-more {
  width: 100%;
  padding: 10upx 0;
  text-align: center;
  color: #9499a7;
  font-size: 24upx;
}
.emptys {
  width: 100%;
  position: relative;
  font-size: 32upx;
  view {
    width: 100%;
    display: flex;
    justify-content: center;
    font-size: 32upx;
    color: #9499a7;
  }
  text {
    font-size: 32upx;
  }
  img,
  image {
    width: 544upx;
    height: 400upx;
    margin: 0 auto;
    display: block;
  }
  .status {
    margin-top: -100upx;
    width: 100%;
    font-size: 32upx;
    text-align: center;
  }
  .refreshbtn {
    width: 192upx;
    height: 64upx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 26upx;
    margin: 0 auto;
    margin-top: 20upx;
  }
}
// .activeTop {
//   margin-top: 29upx;
//   color: #b1b6c2;
//   .roll {
//     position: relative;
//     display: inline-block;
//     &::before {
//       position: absolute;
//       z-index: 10;
//       width: 100upx;
//       top: 50%;
//       left: -140upx;
//       height: 1px;
//       content: "";
//       background-color: #b1b6c2;
//     }
//     &::after {
//       position: absolute;
//       z-index: 10;
//       width: 100upx;
//       top: 50%;
//       right: -140upx;
//       height: 1px;
//       content: "";
//       background-color: #b1b6c2;
//     }
//   }
// }
</style>
