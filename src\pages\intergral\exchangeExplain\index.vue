<template>
  <view class="content">
    <!-- <view class="title">兑换说明</view> -->
    <view class="spans">
      <!-- {{ content.introduce[0] }} -->
      <rich-text :nodes="content"></rich-text>
    </view>
  </view>
</template>

<script>
import { openInfo } from '@/services/index.js';

export default {
  data() {
    return {
      vipConfig: {},
      cardList: [],
      // content: {
      //   introduce: '',
      //   remark: '',
      // },
      content: '',
    };
  },
  computed: {
    memberList() {
      return [
        {
          label: '积分兑换',
          text: [this.vipConfig?.isExchangeExplain || '-'],
          show: this.vipConfig?.isExchange == '1',
          code: '0604',
        },
      ];
    },
  },
  methods: {
    // 查询会员页面的配置信息，例如权益列表、协议说明、等级说明
    async openInfo() {
      // 0412 会员协议
      let arr = ['0502'];
      const params = {
        infoTypeList: arr,
      };
      const [err, res] = await openInfo(params);
      if (res) {
        console.log('res', res);
        let list = res.infoList;
        this.content = list[0].content;
      }
    },
  },
  async onLoad() {
    // await this.getConfigDesc();
    await this.openInfo();
  },
};
</script>

<style scoped lang="scss">
.content {
  padding: 24rpx;
  .title {
    text-align: center;
    font-size: 36rpx;
  }
  .spans {
    font-size: 24rpx;
    color: #333333;
    padding: 20rpx 0;
    line-height: 30rpx;
    margin-bottom: 10rpx;
    display: flex;
    flex-direction: column;
    gap: 15rpx;
    grid-gap: 15rpx;
  }
}
</style>
