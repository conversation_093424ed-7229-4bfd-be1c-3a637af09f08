<template>
  <view class="add">
    <view @tap="colse" class="addImgs">
      <image :src="img" mode="aspectFit" />
    </view>
    <!-- <view class="add-head add-height">
      <view style="font-size: 26rpx">
        <view class="add-head-bull styleColorB"></view>
        请您添加实际使用车型，我们将根据车型为您适配充电站
      </view>
    </view>
    <view @tap="choice" class="add-head add-height add-bt">
      <view>车型</view>
      <view class="add-head-cars">
        {{ carName }}
        <text class="icon iconfont icon-arrow_right" size="20"></text>
      </view>
    </view> -->
    <!-- <view class='add-head add-height add-bt'>
    <view>车型</view>
    <view class='add-head-cars'>{{carName}}

    </view>
  </view> -->
    <view class="add-head">
      <view>
        <view class="add-head-bull styleColorB"></view>
        请您添加真实有效的车辆
      </view>
      <view @tap="jump" v-if="change" class="add-head-cars styleColorA">
        切换八位车牌号
        <text class="icon iconfont icon-arrow_right" size="20"></text>
      </view>
      <view @tap="jumps" v-else class="add-head-cars styleColorA">
        切换七位车牌号
        <text class="icon iconfont icon-arrow_right" size="20"></text>
      </view>
    </view>
    <view v-if="chepai" id="wrap" class="add-middle add-bt">
      <view
        @tap="act"
        :data-index="index"
        :data-message="'message' + index"
        :class="'viewInput ' + (math[index].focus == true ? 'actives' : '')"
        v-for="(item, index) in math"
        :key="index"
      >
        {{ item.message }}
      </view>
    </view>
    <view v-else id="wrap" class="add-middle add-bt">
      <view
        @tap="actd"
        :data-index="index"
        :data-message="'message' + index"
        :class="'viewInput ' + (mathd[index].focus == true ? 'actives' : '')"
        v-for="(item, index) in mathd"
        :key="index"
      >
        {{ item.message }}
      </view>
    </view>
    <view @tap="choicePurpose" class="add-head add-height add-bt" style="margin-bottom: 0rpx">
      <view><span style="color: red">*</span>车辆用途</view>
      <view class="add-head-cars">
        {{ carPurpose }}
        <text class="icon iconfont icon-arrow_right" size="20"></text>
      </view>
    </view>
    <view @tap="choice" class="add-head add-height add-bt" style="margin-bottom: 0rpx">
      <view><span style="color: red">*</span>爱车车型</view>
      <view class="add-head-cars">
        {{ carName }}
        <text class="icon iconfont icon-arrow_right" size="20"></text>
      </view>
    </view>
    <view @tap="openDate" class="add-head add-height add-bt" style="margin-bottom: 0rpx">
      <view>生产年份</view>
      <view class="add-head-cars">
        {{ carYear }}
        <text class="icon iconfont icon-arrow_right" size="20"></text>
      </view>
    </view>
    <view class="add-head add-height add-bt-no-margin">
      <view><span style="color: red">*</span>续航里程(KM)</view>
      <!-- <view class="add-head-cars">
        {{ carRange }}
        <text class="icon iconfont icon-arrow_right" size="20"></text>
      </view> -->
      <view class="add-head-cars">
        <input type="number" placeholder="请填写" v-model="carRange" style="text-align: right" @blur="validateInput" />
      </view>
    </view>

    <view @tap="selectMapLocation" class="add-head add-height add-bt">
      <view>常用充电区域</view>
      <view class="add-head-cars">
        {{ area }}
        <text class="icon iconfont icon-arrow_right" size="20"></text>
      </view>
    </view>
    <view class="add-button">
      <view @tap="open" class="add-button-bt styleColorB">确定</view>
    </view>

    <view v-if="codPank" class="">
      <view v-if="keyboard" class="keyboard">
        <!-- <view bindtap="push" class="code">中文</view> -->
        <view class="code" @tap="colse">关闭</view>
        <view @tap="deleteChar" class="delete">删除</view>
        <view class="li" @tap="clicKey" :data-type="item" v-for="(item, index) in list" :key="index">{{
          item.code
        }}</view>
      </view>
      <view v-else class="keyboard">
        <!-- <view bindtap="pull" class="code">ABC</view> -->
        <view class="code" @tap="colse">关闭</view>
        <view class="delete" @tap="deleteChar">删除</view>
        <view class="li" @tap="clicKey" :data-type="item" v-for="(item, index) in listBack" :key="index">{{
          item.code
        }}</view>
      </view>
    </view>

    <dateRange ref="dateRange" @change="changeDate" :value="dateList"></dateRange>
    <uni-popup ref="popup" type="dialog" :mask-click="false">
      <view
        class="popup-content"
        :style="{
          backgroundImage: `url(${IMG_PATH}img_add_intergral_dialog.png)`,
        }"
      >
        <div class="intergral" v-if="cpnName">
          <span>{{ cpnName }}</span>
        </div>
        <div class="intergral" v-if="!cpnName && integralNumber">
          积分 <span>+{{ integralNumber }}</span>
        </div>
        <view class="btn" @click="hideSignInDialog"></view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import Index from '../../components/AEmpty/index.vue';
import dateRange from './dateRange.vue';
import moment from 'moment';
import { integralRule, getPref, setPref } from '@/services/index.js';
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup';
import uniPopupDialog from '@/components/uni-ui/uni-popup-dialog/uni-popup-dialog';
export default {
  components: { Index, dateRange, uniPopup, uniPopupDialog },
  data() {
    return {
      math: [
        {
          message: '',
          mt: 7,
          focus: true,
        },
        {
          message: '',
          mt: 7,
          focus: false,
        },
        {
          message: '',
          mt: 7,
          focus: false,
        },
        {
          message: '',
          mt: 7,
          focus: false,
        },
        {
          message: '',
          mt: 7,
          focus: false,
        },
        {
          message: '',
          mt: 7,
          focus: false,
        },
        {
          message: '',
          mt: 7,
          focus: false,
        },
      ],

      mathd: [
        {
          message: '',
          mt: 7,
          focus: true,
        },
        {
          message: '',
          mt: 7,
          focus: false,
        },
        {
          message: '',
          mt: 7,
          focus: false,
        },
        {
          message: '',
          mt: 7,
          focus: false,
        },
        {
          message: '',
          mt: 7,
          focus: false,
        },
        {
          message: '',
          mt: 7,
          focus: false,
        },
        {
          message: '',
          mt: 7,
          focus: false,
        },
        {
          message: '',
          mt: 8,
          focus: false,
        },
      ],

      change: true,

      list: [
        {
          code: '1',
        },
        {
          code: '2',
        },
        {
          code: '3',
        },
        {
          code: '4',
        },
        {
          code: '5',
        },
        {
          code: '6',
        },
        {
          code: '7',
        },
        {
          code: '8',
        },
        {
          code: '9',
        },
        {
          code: '0',
        },
        {
          code: 'Q',
        },
        {
          code: 'W',
        },
        {
          code: 'E',
        },
        {
          code: 'R',
        },
        {
          code: 'T',
        },
        {
          code: 'Y',
        },
        {
          code: 'U',
        },
        // {
        //   code: 'I',
        // },
        // {
        //   code: 'O',
        // },
        {
          code: 'P',
        },
        {
          code: 'A',
        },
        {
          code: 'S',
        },
        {
          code: 'D',
        },
        {
          code: 'F',
        },
        {
          code: 'G',
        },
        {
          code: 'H',
        },
        {
          code: 'J',
        },
        {
          code: 'K',
        },
        {
          code: 'L',
        },
        {
          code: 'Z',
        },
        {
          code: 'X',
        },
        {
          code: 'C',
        },
        {
          code: 'V',
        },
        {
          code: 'B',
        },
        {
          code: 'N',
        },
        {
          code: 'M',
        },
        {
          code: '港',
        },
        {
          code: '澳',
        },
        {
          code: '学',
        },
      ],

      listBack: [
        {
          code: '京',
        },
        {
          code: '津',
        },
        {
          code: '渝',
        },
        {
          code: '沪',
        },
        {
          code: '冀',
        },
        {
          code: '晋',
        },
        {
          code: '辽',
        },
        {
          code: '吉',
        },
        {
          code: '黑',
        },
        {
          code: '苏',
        },
        {
          code: '浙',
        },
        {
          code: '皖',
        },
        {
          code: '闽',
        },
        {
          code: '赣',
        },
        {
          code: '鲁',
        },
        {
          code: '豫',
        },
        {
          code: '鄂',
        },
        {
          code: '湘',
        },
        {
          code: '粤',
        },
        {
          code: '琼',
        },
        {
          code: '川',
        },
        {
          code: '贵',
        },
        {
          code: '云',
        },
        {
          code: '陕',
        },
        {
          code: '甘',
        },
        {
          code: '青',
        },
        {
          code: '蒙',
        },
        {
          code: '桂',
        },
        {
          code: '宁',
        },
        {
          code: '新',
        },
        {
          code: '藏',
        },
        {
          code: '使',
        },
        {
          code: '领',
        },
        {
          code: '警',
        },
        {
          code: '学',
        },
        {
          code: '港',
        },
        {
          code: '澳',
        },
      ],

      iner: false,
      codPank: false,
      message0: '',
      message1: '',
      message2: '',
      message3: '',
      message4: '',
      message5: '',
      message6: '',
      message7: '',
      JX1: false,
      JX2: false,
      JX3: false,
      JX4: false,
      JX5: false,
      JX6: false,
      JX7: false,
      JX8: false,
      city: '',
      Nub: '',
      demo: 0,
      demd: 0,
      token: '',
      chepai: false, // 默认8位车牌模式
      change: false, // 默认8位车牌模式
      carPurpose: '',
      carName: '',
      carYear: '',
      carRange: '',
      area: '',
      modelId: '',
      img: '',
      locationData: null, // 存储位置的经纬度信息
      first: false,
      keyboard: false,
      demod: 0,
      z1: '',
      z2: '',
      z3: '',
      z4: '',
      z5: '',
      z6: '',
      z7: '',
      z8: '',
      d1: false,
      d2: false,
      d3: false,
      d4: false,
      d5: false,
      d6: false,
      d7: false,
      f1: true, // 8位车牌第一位默认获得焦点
      f2: false,
      f3: false,
      f5: false,
      f6: false,
      f7: false,
      f8: false,
      message: '',
      vla: false,
      info: false,
      msgd: '',
      msg: '',
      border: false,
      focus: false,
      dateTime: [new Date(), new Date()],
      point: {
        signIn: 0,
        improve: 0,
      },
      cpnName: '',
      integralNumber: '',
      popupShow: true,
      // 防抖相关
      lastClickTime: 0,
      debounceDelay: 0.5 * 60 * 1000, // 3分钟 = 3 * 60 * 1000 毫秒
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(options, '回参');
    let that = this;

    // 标记页面是首次加载，用于区分是否需要清空数据
    that.isFirstLoad = true;

    if (options.carName) {
      console.log('有入参');
      that.setData({
        carName: options.carName,
        modelId: options.modelId,
        // brandId: options.brandId,
        img: options.img || this.IMG_PATH + 'default-car.png',
      });
    } else {
      console.log('没有入参');
      that.setData({
        carName: '',
        img: this.IMG_PATH + 'default-car.png',
      });
    }
    uni.getStorage({
      key: 'token',
      success(res) {
        that.setData({
          token: res.data,
        });
      },
    });

    // 检查是否是从上级页面重新进入，如果是则清空数据
    const shouldClearData = !uni.getStorageSync('addPageDataPreserve');

    if (shouldClearData) {
      console.log('从上级页面进入，清空车牌数据');
      // 清空车牌数据
      that.clearLicensePlateData();
    } else {
      console.log('从子页面返回，保留车牌数据');
      // 从store恢复数据
      that.carPurpose = that.$store.state.common.userPurpose;
      that.math = that.$store.state.common.licensePlate;
      that.mathd = that.$store.state.common.licensePlate8;
      that.change = that.$store.state.common.licensePlateChange;
      that.chepai = that.change; // 保持一致性
    }

    // 默认设置为8位车牌模式（如果没有从store恢复状态）
    if (shouldClearData) {
      that.change = false; // false表示8位车牌
      that.chepai = false; // false表示8位车牌
    }

    // 监听地图选点返回的位置信息
    uni.$on('locationSelected', this.handleLocationSelected);
  },

  onUnload() {
    // 移除事件监听
    uni.$off('locationSelected');
  },
  /**
/**
 * 生命周期函数--监听页面显示
 */
  onShow() {
    console.log('页面显示 - onShow');

    // 处理从车型选择页面返回的数据
    const selectedCar = uni.getStorageSync('selectedCar');
    if (selectedCar) {
      console.log(selectedCar, 'selectedCar');
      let that = this;
      if (selectedCar.carName) {
        console.log('有入参');
        that.setData({
          carName: selectedCar.carName,
          modelId: selectedCar.modelId,
          // brandId: selectedCar.brandId,
          img: selectedCar.img || this.IMG_PATH + 'default-car.png',
        });
      } else {
        console.log('没有入参');
        that.setData({
          carName: '',
          img: this.IMG_PATH + 'default-car.png',
        });
      }
      // 使用完后清除存储
      uni.removeStorageSync('selectedCar');
    }

    // 如果不是首次加载，说明是从子页面返回，恢复数据
    if (!this.isFirstLoad) {
      console.log('从子页面返回，恢复车牌数据');
      this.carPurpose = this.$store.state.common.userPurpose;
      this.math = this.$store.state.common.licensePlate;
      this.mathd = this.$store.state.common.licensePlate8;
      this.change = this.$store.state.common.licensePlateChange;
      this.chepai = this.change; // 保持一致性
    }

    // 标记不再是首次加载
    this.isFirstLoad = false;
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    console.log('页面隐藏 - onHide');
    // 清除数据保留标记，表示用户离开了页面
    uni.removeStorageSync('addPageDataPreserve');
  },
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},

  mounted() {
    this.getPoints();
  },
  methods: {
    // 清空车牌数据的方法
    clearLicensePlateData() {
      console.log('清空车牌数据');

      // 清空7位车牌数据
      this.math = [
        { message: '', mt: 7, focus: true },
        { message: '', mt: 7, focus: false },
        { message: '', mt: 7, focus: false },
        { message: '', mt: 7, focus: false },
        { message: '', mt: 7, focus: false },
        { message: '', mt: 7, focus: false },
        { message: '', mt: 7, focus: false },
      ];

      // 清空8位车牌数据
      this.mathd = [
        { message: '', mt: 7, focus: true },
        { message: '', mt: 7, focus: false },
        { message: '', mt: 7, focus: false },
        { message: '', mt: 7, focus: false },
        { message: '', mt: 7, focus: false },
        { message: '', mt: 7, focus: false },
        { message: '', mt: 7, focus: false },
        { message: '', mt: 8, focus: false },
      ];

      // 清空其他相关数据
      this.carPurpose = '';
      this.demo = 0;
      this.demod = 0;

      // 清空message变量
      this.message0 = '';
      this.message1 = '';
      this.message2 = '';
      this.message3 = '';
      this.message4 = '';
      this.message5 = '';
      this.message6 = '';
      this.message7 = '';

      // 更新store
      this.$store.dispatch('common/setLicensePlate', this.math);
      this.$store.dispatch('common/setLicensePlate8', this.mathd);
      this.$store.dispatch('common/setUserPurpose', '');
      this.$store.dispatch('common/setUserPurposeValue', '');
    },

    open() {
      var that = this;

      // // 防抖检查：检查距离上次点击是否超过3分钟
      // const currentTime = Date.now();
      // if (currentTime - that.lastClickTime < that.debounceDelay) {
      //   const remainingTime = Math.ceil((that.debounceDelay - (currentTime - that.lastClickTime)) / 1000 / 60);
      //   uni.showToast({
      //     title: `请等待${remainingTime}秒后再试`,
      //     icon: 'none',
      //     duration: 2000,
      //   });
      //   return false;
      // }

      if (that.chepai == true) {
        var message =
          that.math[0].message +
          that.math[1].message +
          that.math[2].message +
          that.math[3].message +
          that.math[4].message +
          that.math[5].message +
          that.math[6].message;
        console.log(message);
        if (
          that.math[0].message == '' ||
          that.math[1].message == '' ||
          that.math[2].message == '' ||
          that.math[3].message == '' ||
          that.math[4].message == '' ||
          that.math[5].message == '' ||
          that.math[6].message == ''
        ) {
          uni.showToast({
            title: '请输入正确车牌号',
            icon: 'loading',
            duration: 1000,
          });
          return false;
        }
      } else if (that.chepai == false) {
        var message =
          that.mathd[0].message +
          that.mathd[1].message +
          that.mathd[2].message +
          that.mathd[3].message +
          that.mathd[4].message +
          that.mathd[5].message +
          that.mathd[6].message +
          that.mathd[7].message;
        console.log(message);
        if (that.mathd[7].message == '') {
          uni.showToast({
            title: '请输入正确车牌号',
            icon: 'loading',
            duration: 1000,
          });
          return false;
        }
      }
      if (that.$store.state.common.userPurposeValue == '' || that.carPurpose == '') {
        uni.showToast({
          title: '请选择车辆用途',
          icon: 'loading',
          duration: 1000,
        });
        return false;
      }
      if (that.$store.state.common.userPurposeValue == '' || that.carName == '') {
        uni.showToast({
          title: '请选择爱车车型',
          icon: 'loading',
          duration: 1000,
        });
        return false;
      }
      if (that.$store.state.common.userPurposeValue == '' || that.carRange == '') {
        uni.showToast({
          title: '请输入续航里程',
          icon: 'loading',
          duration: 1000,
        });
        return false;
      }
      const params = {
        licenseNo: message,
        modelId: that.modelId,
        modelName: that.carName,
        brandId: that.brandId,
        carUseType: that.$store.state.common.userPurposeValue,
        prodYear: that.carYear,
        carRange: that.carRange,
        // 如果有位置数据，添加经纬度信息
        ...(that.locationData
          ? {
              lat: that.locationData.latitude,
              lon: that.locationData.longitude,
              normalAddr: that.locationData.address,
            }
          : {}),
      };

      // 更新最后点击时间
      that.lastClickTime = Date.now();

      this.getPref(params);
    },
    deleteChar() {
      var that = this;
      console.log('删除操作 - chepai:', that.chepai, 'demo:', that.demo, 'demod:', that.demod);

      // 判断当前是七位还是八位车牌
      // change和chepai都为false时表示8位车牌，都为true时表示7位车牌
      var isSevenDigit = that.change === true && that.chepai === true;

      console.log('删除判断 - change:', that.change, 'chepai:', that.chepai, 'isSevenDigit:', isSevenDigit);

      if (isSevenDigit) {
        // 七位车牌号删除逻辑
        var currentIndex = that.demo;
        console.log('七位车牌删除，当前索引:', currentIndex);

        // 检查当前位置是否有值
        var currentMsg = 'math[' + currentIndex + '].message';
        var currentValue = that.math[currentIndex] ? that.math[currentIndex].message : '';

        console.log('当前位置值:', currentValue);

        if (currentValue && currentValue !== '') {
          // 当前位置有值，删除当前位置的值
          var currentFocus = 'math[' + currentIndex + '].focus';
          var messageVar = 'message' + currentIndex;

          // 清除所有焦点
          var z1 = 'math[0].focus';
          var z2 = 'math[1].focus';
          var z3 = 'math[2].focus';
          var z4 = 'math[3].focus';
          var z5 = 'math[4].focus';
          var z6 = 'math[5].focus';
          var z7 = 'math[6].focus';

          that.setData({
            [currentMsg]: '',
            [messageVar]: '',
            [z1]: false,
            [z2]: false,
            [z3]: false,
            [z4]: false,
            [z5]: false,
            [z6]: false,
            [z7]: false,
            [currentFocus]: true,
          });

          console.log('删除了当前第', currentIndex, '位字符');
        } else if (currentIndex > 0) {
          // 当前位置为空，删除前一位并移动焦点
          var prevIndex = currentIndex - 1;
          var prevMsg = 'math[' + prevIndex + '].message';
          var prevFocus = 'math[' + prevIndex + '].focus';
          var prevMessageVar = 'message' + prevIndex;

          // 清除所有焦点
          var z1 = 'math[0].focus';
          var z2 = 'math[1].focus';
          var z3 = 'math[2].focus';
          var z4 = 'math[3].focus';
          var z5 = 'math[4].focus';
          var z6 = 'math[5].focus';
          var z7 = 'math[6].focus';

          that.setData({
            [prevMsg]: '',
            [prevMessageVar]: '',
            [z1]: false,
            [z2]: false,
            [z3]: false,
            [z4]: false,
            [z5]: false,
            [z6]: false,
            [z7]: false,
            [prevFocus]: true,
            demo: prevIndex,
          });

          console.log('删除了前一位第', prevIndex, '位字符，焦点移动到', prevIndex);
        } else {
          console.log('已经是第一位且为空，无法删除');
        }
      } else {
        // 八位车牌号删除逻辑
        var currentIndex = that.demod;
        console.log('八位车牌删除，当前索引:', currentIndex);

        // 检查当前位置是否有值
        var currentMsg = 'mathd[' + currentIndex + '].message';
        var currentValue = that.mathd[currentIndex] ? that.mathd[currentIndex].message : '';

        console.log('当前位置值:', currentValue);

        if (currentValue && currentValue !== '') {
          // 当前位置有值，删除当前位置的值
          var currentFocus = 'mathd[' + currentIndex + '].focus';
          var messageVar = 'message' + currentIndex;

          // 清除所有焦点
          var z1 = 'mathd[0].focus';
          var z2 = 'mathd[1].focus';
          var z3 = 'mathd[2].focus';
          var z4 = 'mathd[3].focus';
          var z5 = 'mathd[4].focus';
          var z6 = 'mathd[5].focus';
          var z7 = 'mathd[6].focus';
          var z8 = 'mathd[7].focus';

          that.setData({
            [currentMsg]: '',
            [messageVar]: '',
            [z1]: false,
            [z2]: false,
            [z3]: false,
            [z4]: false,
            [z5]: false,
            [z6]: false,
            [z7]: false,
            [z8]: false,
            [currentFocus]: true,
          });

          console.log('删除了当前第', currentIndex, '位字符');
        } else if (currentIndex > 0) {
          // 当前位置为空，删除前一位并移动焦点
          var prevIndex = currentIndex - 1;
          var prevMsg = 'mathd[' + prevIndex + '].message';
          var prevFocus = 'mathd[' + prevIndex + '].focus';
          var prevMessageVar = 'message' + prevIndex;

          // 清除所有焦点
          var z1 = 'mathd[0].focus';
          var z2 = 'mathd[1].focus';
          var z3 = 'mathd[2].focus';
          var z4 = 'mathd[3].focus';
          var z5 = 'mathd[4].focus';
          var z6 = 'mathd[5].focus';
          var z7 = 'mathd[6].focus';
          var z8 = 'mathd[7].focus';

          that.setData({
            [prevMsg]: '',
            [prevMessageVar]: '',
            [z1]: false,
            [z2]: false,
            [z3]: false,
            [z4]: false,
            [z5]: false,
            [z6]: false,
            [z7]: false,
            [z8]: false,
            [prevFocus]: true,
            demod: prevIndex,
          });

          console.log('删除了前一位第', prevIndex, '位字符，焦点移动到', prevIndex);
        } else {
          console.log('已经是第一位且为空，无法删除');
        }
      }
    },
    async getPref(params) {
      const [err, res] = await setPref(params, {
        // #ifdef H5
        'content-type': 'application/x-www-form-urlencoded',
        // #endif
      });
      if (res) {
        if (res.ret == 200) {
          if (res.integralNumber != 'max times' && res.integralNumber != 'not exist') {
            this.cpnName = res?.cpnName ?? '';
            this.integralNumber = res?.integralNumber ?? 0;
            // 显示弹窗
            this.$refs?.popup && this.$refs?.popup?.open();
          } else {
            uni.showToast({
              title: `添加成功`,
              icon: 'success',
              duration: 1200,
            });
            uni.seStorageSync('selectedCar', '');
          }

          let emptyMath = [
            {
              message: '',
              mt: 7,
              focus: true,
            },
            {
              message: '',
              mt: 7,
              focus: false,
            },
            {
              message: '',
              mt: 7,
              focus: false,
            },
            {
              message: '',
              mt: 7,
              focus: false,
            },
            {
              message: '',
              mt: 7,
              focus: false,
            },
            {
              message: '',
              mt: 7,
              focus: false,
            },
            {
              message: '',
              mt: 7,
              focus: false,
            },
          ];
          this.$store.dispatch('common/setLicensePlate', emptyMath);
          this.$store.dispatch('common/setLicensePlateChange', true);
          this.$store.dispatch('common/setUserPurpose', '');
          this.$store.dispatch('common/setUserPurposeValue', '');
        } else {
          uni.showToast({
            title: res.msg || '添加失败',
            icon: 'none',
            duration: 1200,
          });
        }
      }
    },
    hideSignInDialog() {
      this.$refs?.popup && this.$refs?.popup?.close();
      //  setTimeout(function () {
      uni.navigateBack({
        changed: true,
      });
      // }, 1200);
    },
    // 车量用途选择
    choicePurpose() {
      console.log('跳转', this.math, this.change);
      console.log(this.mathd, this.change);

      // 保存当前数据到store
      this.$store.dispatch('common/setLicensePlate', this.math);
      this.$store.dispatch('common/setLicensePlate8', this.mathd);
      this.$store.dispatch('common/setLicensePlateChange', this.change);

      // 设置数据保留标记，表示要保留当前填写的数据
      uni.setStorageSync('addPageDataPreserve', true);

      uni.redirectTo({
        url: '/subPackages/carPurpose/index',
      });
    },
    choice() {
      // 保存当前数据到store
      this.$store.dispatch('common/setLicensePlate', this.math);
      this.$store.dispatch('common/setLicensePlate8', this.mathd);
      this.$store.dispatch('common/setLicensePlateChange', this.change);

      // 设置数据保留标记，表示要保留当前填写的数据
      uni.setStorageSync('addPageDataPreserve', true);

      uni.navigateTo({
        url: '/subPackages/cars/cars',
      });
    },

    jump() {
      console.log('切换到8位车牌模式');
      var that = this;

      // 设置8位车牌的基本状态
      that.setData({
        first: false,
        keyboard: false,
        change: false, // false表示8位车牌
        chepai: false, // false表示8位车牌
        demo: 0, // 7位车牌的当前位置重置
        demod: 0, // 8位车牌的当前位置重置
      });

      // 清空8位车牌的所有输入内容
      var z1 = 'mathd[0].message';
      var z2 = 'mathd[1].message';
      var z3 = 'mathd[2].message';
      var z4 = 'mathd[3].message';
      var z5 = 'mathd[4].message';
      var z6 = 'mathd[5].message';
      var z7 = 'mathd[6].message';
      var z8 = 'mathd[7].message';

      // 清空7位车牌的所有焦点
      var d1 = 'math[0].focus';
      var d2 = 'math[1].focus';
      var d3 = 'math[2].focus';
      var d4 = 'math[3].focus';
      var d5 = 'math[4].focus';
      var d6 = 'math[5].focus';
      var d7 = 'math[6].focus';

      // 设置8位车牌的焦点
      var f1 = 'mathd[0].focus';
      var f2 = 'mathd[1].focus';
      var f3 = 'mathd[2].focus';
      var f4 = 'mathd[3].focus';
      var f5 = 'mathd[4].focus';
      var f6 = 'mathd[5].focus';
      var f7 = 'mathd[6].focus';
      var f8 = 'mathd[7].focus';

      that.setData({
        // 清空8位车牌内容
        [z1]: '',
        [z2]: '',
        [z3]: '',
        [z4]: '',
        [z5]: '',
        [z6]: '',
        [z7]: '',
        [z8]: '',

        // 清空7位车牌焦点
        [d1]: false,
        [d2]: false,
        [d3]: false,
        [d4]: false,
        [d5]: false,
        [d6]: false,
        [d7]: false,

        // 设置8位车牌焦点（第一位获得焦点）
        [f1]: true,
        [f2]: false,
        [f3]: false,
        [f4]: false,
        [f5]: false,
        [f6]: false,
        [f7]: false,
        [f8]: false,

        // 清空所有message变量
        message0: '',
        message1: '',
        message2: '',
        message3: '',
        message4: '',
        message5: '',
        message6: '',
        message7: '',
      });

      console.log('8位车牌模式初始化完成');
    },

    jumps() {
      console.log('2');
      var that = this;
      that.setData({
        first: false,
        keyboard: false,
      });
      var z1 = 'math[0].message';
      var z2 = 'math[1].message';
      var z3 = 'math[2].message';
      var z4 = 'math[3].message';
      var z5 = 'math[4].message';
      var z6 = 'math[5].message';
      var z7 = 'math[6].message';
      var d1 = 'math[0].focus';
      var d2 = 'math[1].focus';
      var d3 = 'math[2].focus';
      var d4 = 'math[3].focus';
      var d5 = 'math[4].focus';
      var d6 = 'math[5].focus';
      var d7 = 'math[6].focus';
      var f1 = 'mathd[0].focus';
      var f2 = 'mathd[1].focus';
      var f3 = 'mathd[2].focus';
      var f5 = 'mathd[4].focus';
      var f6 = 'mathd[5].focus';
      var f7 = 'mathd[6].focus';
      var f8 = 'mathd[7].focus';
      that.setData({
        demo: 0,
        demod: 0,
        chepai: true,
        change: true,
        [d1]: true,
        [d2]: false,
        [d3]: false,
        [d4]: false,
        [d5]: false,
        [d6]: false,
        [d7]: false,
        [f1]: false,
        [f2]: false,
        [f3]: false,
        [d4]: false,
        [f5]: false,
        [f6]: false,
        [f7]: false,
        [f8]: false,
        [z1]: '',
        [z2]: '',
        [z3]: '',
        [z4]: '',
        [z5]: '',
        [z6]: '',
        [z7]: '',
        message0: '',
        message1: '',
        message2: '',
        message3: '',
        message4: '',
        message5: '',
        message6: '',
        message7: '',
        first: true,
      });
    },

    fou(e) {
      console.log(e, '000');
      var that = this;
      var index = e.currentTarget.dataset.types;
      var msg = e.detail.value;
      var message = 'message' + index;
      that.setData({
        [message]: msg,
      });
      var a = '_self.data.' + message;
      console.log(a, '---------');
      if (!msg) {
        console.log('当前值是空的');
      } else {
        console.log(a, '当前值是不为空的');
        var z1 = that.math[0].focus;
        var z2 = that.math[1].focus;
        var z3 = that.math[2].focus;
        var z4 = that.math[3].focus;
        var z5 = that.math[4].focus;
        var z6 = that.math[5].focus;
        var z7 = that.math[6].focus;
        var z8 = that.math[7].focus;
        that.setData({
          [z1]: false,
          [z2]: false,
          [z3]: false,
          [z4]: false,
          [z5]: false,
          [z6]: false,
          [z7]: false,
          [z8]: false,
        });
        var qq = index + 1;
        var vla = 'math[' + qq + '].focus';
        console.log(vla);
        that.setData({
          [vla]: true,
        });
      }
    },

    act(e) {
      var that = this;
      console.log(e, 'eee');
      console.log(e.target.dataset.index, '这是div的索引');
      that.setData({
        codPank: true,
      });
      if (e.target.dataset.index == 0) {
        that.setData({
          first: false,
          keyboard: false,
        });
      } else {
        that.setData({
          first: true,
          keyboard: true,
        });
      }
      var z1 = 'math[0].focus';
      var z2 = 'math[1].focus';
      var z3 = 'math[2].focus';
      var z4 = 'math[3].focus';
      var z5 = 'math[4].focus';
      var z6 = 'math[5].focus';
      var z7 = 'math[6].focus';
      that.setData({
        [z1]: false,
        [z2]: false,
        [z3]: false,
        [z4]: false,
        [z5]: false,
        [z6]: false,
        [z7]: false,
      });
      var info = 'math[' + e.target.dataset.index + '].focus';
      that.setData({
        demo: e.target.dataset.index,
        [info]: true,
        // [z1]: false,
      });
    },

    actd(e) {
      var that = this;
      console.log(e, 'eee');
      console.log(e.target.dataset.index, '这是div的索引');
      that.setData({
        codPank: true,
      });
      if (e.target.dataset.index == 0) {
        that.setData({
          first: false,
          keyboard: false,
        });
      } else {
        that.setData({
          first: true,
          keyboard: true,
        });
      }
      var z1 = 'mathd[0].focus';
      var z2 = 'mathd[1].focus';
      var z3 = 'mathd[2].focus';
      var z4 = 'mathd[3].focus';
      var z5 = 'mathd[4].focus';
      var z6 = 'mathd[5].focus';
      var z7 = 'mathd[6].focus';
      var z8 = 'mathd[7].focus';
      that.setData({
        [z1]: false,
        [z2]: false,
        [z3]: false,
        [z4]: false,
        [z5]: false,
        [z6]: false,
        [z7]: false,
        [z8]: false,
      });
      var info = 'mathd[' + e.target.dataset.index + '].focus';
      that.setData({
        demod: e.target.dataset.index,
        [info]: true,
        [z1]: false,
      });
    },

    clicKey(e) {
      console.log(e.target.dataset.type.code, '909');
      console.log(e, '要索引');
      var data = e.target.dataset.type.code;
      var that = this;
      console.log(that.demo, 'demo的值');
      console.log(that.demod, 'demod的值');
      if (that.demo < 0) {
        that.setData({
          first: false,
          keyboard: false,
        });
      } else {
        that.setData({
          first: true,
          keyboard: true,
        });
      }
      if (that.demod < 0) {
        that.setData({
          first: false,
          keyboard: false,
        });
      } else {
        that.setData({
          first: true,
          keyboard: true,
        });
      }
      if (that.chepai == true) {
        if (that.demo == 6) {
          var msgd = 'math[' + that.demo + '].message';
          that.setData({
            message6: data,
            [msgd]: data,
          });
          console.log(that.math[6].message, '第六格');
          return false;
        }
        var message = 'message[' + that.demo + ']';
        var msg = 'math[' + that.demo + '].message';
        var border1 = that.demo + 1;
        var border = 'math[' + border1 + '].focus';
        var z1 = 'math[0].focus';
        var z2 = 'math[1].focus';
        var z3 = 'math[2].focus';
        var z4 = 'math[3].focus';
        var z5 = 'math[4].focus';
        var z6 = 'math[5].focus';
        var z7 = 'math[6].focus';
        that.setData({
          [z1]: false,
          [z2]: false,
          [z3]: false,
          [z4]: false,
          [z5]: false,
          [z6]: false,
          [z7]: false,
          demo: border1,
        });
        that.setData({
          [message]: data,
          [msg]: data,
          [border]: true,
        });
      } else {
        if (that.demod == 7) {
          var msgd = 'mathd[' + that.demod + '].message';
          that.setData({
            message7: data,
            [msgd]: data,
          });
          console.log(that.mathd[7].message, '第7格');
          return false;
        }
        var message = 'message[' + that.demod + ']';
        var msg = 'mathd[' + that.demod + '].message';
        var border1 = that.demod + 1;
        var border = 'mathd[' + border1 + '].focus';
        var z1 = 'mathd[0].focus';
        var z2 = 'mathd[1].focus';
        var z3 = 'mathd[2].focus';
        var z4 = 'mathd[3].focus';
        var z5 = 'mathd[4].focus';
        var z6 = 'mathd[5].focus';
        var z7 = 'mathd[6].focus';
        that.setData({
          [z1]: false,
          [z2]: false,
          [z3]: false,
          [z4]: false,
          [z5]: false,
          [z6]: false,
          [z7]: false,
          demod: border1,
        });
        that.setData({
          [message]: data,
          [msg]: data,
          [border]: true,
        });
      }
    },

    colse() {
      this.setData({
        codPank: false,
      });
    },

    push() {
      var that = this;
      that.setData({
        keyboard: false,
      });
    },

    pull() {
      var that = this;
      that.setData({
        keyboard: true,
      });
    },

    cli(e) {
      console.log(e.target.dataset.types, 'e');
      var index = e.target.dataset.types;
      var type = 'message' + index;
      console.log(index);
      var that = this;
      that.setData({
        demo: index,
      });
    },

    // 打开时间选择
    openDate() {
      // this.$refs.date && this.$refs.date.show();
      const dateList = this.dateTime.map((q) => moment(q).format('yyyy'));
      this.$refs.dateRange && this.$refs.dateRange.open(dateList);
    },
    // 选择时间
    changeDate(date) {
      console.log(date);
      this.carYear = date[0];
    },
    // 里程数输入
    validateInput() {
      // 只允许数字、小数点
      this.carRange = this.carRange.replace(/[^0-9.]/g, '');
      // 防止多个小数点
      let pointIndex = this.carRange.indexOf('.');
      if (pointIndex !== -1) {
        this.carRange = this.carRange.slice(0, pointIndex + 1) + this.carRange.slice(pointIndex + 1).replace(/\./g, '');
      }
    },
    // 获取绑定积分
    async getPoints() {
      const [, res] = await integralRule();
      if (res && res.list) {
        this.point = res.list.reduce(
          (cur, nex) => {
            if (nex.eventType == '05') cur.signIn = nex.integralNo;
            if (nex.eventType == '02') cur.improve = nex.integralNo;
            return cur;
          },
          { signIn: 0, improve: 0 }
        );
      }
    },

    // 跳转到地图选点页面
    selectMapLocation() {
      // 构建URL参数
      let url = '/subPackages/mapSelect/index';

      // 如果已经选择了位置，则传递经纬度
      if (this.locationData && this.locationData.latitude && this.locationData.longitude) {
        url += `?latitude=${this.locationData.latitude}&longitude=${this.locationData.longitude}`;
      }
      console.log(url, 'url');
      uni.navigateTo({
        url: url,
      });
    },

    // 处理地图选点返回的位置信息
    handleLocationSelected(data) {
      console.log('接收到位置信息:', data);
      if (data) {
        // 如果有地址信息，优先使用地址
        if (data.address) {
          this.area = data.address;
        } else if (data.location) {
          this.area = data.location;
        }

        // 保存经纬度信息，可能在提交表单时需要
        this.locationData = {
          latitude: data.latitude,
          longitude: data.longitude,
          address: data.address,
        };
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import './add.css';
.popup-content {
  width: 468rpx;
  height: 468rpx;
  background-size: 100% 100%;
  display: flex;
  align-items: flex-end;
  flex-direction: column;
  justify-content: flex-end;
  .intergral {
    width: 100%;
    height: 40rpx;
    text-align: center;
    span {
      color: #4598fe;
      font-weight: 600;
    }
  }
  .btn {
    width: 100%;
    height: 90rpx;
    margin-bottom: 48rpx;
  }
}
</style>
