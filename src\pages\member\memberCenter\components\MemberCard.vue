<!--
@name: 用户卡片
@description: 用户等级等信息
@time: 2024/8/12
-->
<template>
  <view>
    <view v-if="imgName" class="member-card" :style="{ backgroundImage: `url(${IMG_PATH}member/card-${imgName}.png)` }">
      <!--            去续费-->
      <view
        v-if="memberInfo.vipFlag === '1'"
        class="header-button"
        @click="jumpToBuyVip"
        :style="{ backgroundImage: `url(${IMG_PATH}member/member-center-buy-bg.png)` }"
      >
        <text class="content">去续费</text>
      </view>
      <!--            头像-->
      <view class="member-name">
        <img class="profile" :src="userInfo.imgUrl || `${IMG_PATH}defaultAvatar.png`" />
        <view>
          <view class="name-rank">
            <text class="name" :style="{ color: mainColor }"
              >{{ userInfo.custNickname || '未设置昵称' }}{{ theme }}</text
            >
            <img class="user-vip-rank" :src="`${IMG_PATH}member/rank-${rankBackGround}.png`" v-if="rankBackGround" />
            <text v-if="memberInfo.vipFlag === '0'" class="mobile" :style="{ color: mainColor }"
              >({{ desensitization(userInfo.mobile) }})</text
            >
          </view>
          <view v-if="memberInfo.vipFlag === '1'">
            <text class="time" :style="{ color: mainColor }">有效期至：{{ memberInfo.vipExpireTime }}</text>
          </view>
        </view>
      </view>
      <!--            等级-->
      <view class="member-rank">
        <text class="plus" v-if="memberInfo.vipFlag === '1'">PULS会员</text>
        <text class="discription" v-else :style="{ color: descriptionColor }">{{ memberInfo.rankName }}</text>
        <view v-if="rankDescription">
          <text class="rank-detail" :style="{ color: mainColor }" @click="openDescription">等级说明</text>
          <img class="arrow" :src="`${IMG_PATH}member/arrow-${imgName}.png`" />
        </view>
      </view>
      <!--            进度条-->
      <view class="member-progress" v-if="memberInfo.vipFlag === '0'">
        <view class="progress">
          <view class="progress-bg"></view>
          <view class="progress-charge" :style="{ width: getProgress }"></view>
        </view>
        <view v-if="memberInfo.rankNo !== '4'" class="ratio" :style="{ color: mainColor }">
          <text class="ratio-item">{{ memberInfo.chargePq }}</text>
          <text class="ratio-item">/{{ memberInfo.rankMax }}</text>
        </view>
        <text v-else :style="{ color: mainColor }" class="max-level-text">已达到最高等级，继续保持！</text>
      </view>
    </view>
    <!--        等级说明弹框-->
    <uni-popup class="popup-content" ref="rankPopup" background-color="#F6F7F9">
      <view class="title">
        <text class="title-text">等级说明</text>
        <img class="close" :src="`${IMG_PATH}close.png`" @click="closeRank" />
      </view>
      <view class="content">
        <rich-text :nodes="rankDescription"></rich-text>
      </view>
      <view class="line"></view>
      <view class="footer-button">
        <AppButton type="primary" @click="closeRank"> 我知道了 </AppButton>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { openInfo } from '@/services/index.js';
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup';
import AppButton from '@/components/AppButton/index';
import { desensitization } from '@/utils/util.js';
import { mapState } from 'vuex';

export default {
  name: 'MemberCard',
  components: {
    uniPopup,
    AppButton,
  },
  props: {
    memberInfo: {
      type: Object,
      default: () => {},
    },
  },
  watch: {},
  data() {
    return {
      rankDescription: '',
      theme: '1', // 当前会员主题
      map: {
        plus: {
          color: '#E9E2EB',
          descriptionColor: '#3CA4E7',
          name: 'plus',
        },
        1: {
          color: '#B87E5D',
          descriptionColor: '#D36E30',
          name: 'qt',
        },
        2: {
          color: '#66778B',
          descriptionColor: '#6B8BCB',
          name: 'by',
        },
        3: {
          color: '#B88529',
          descriptionColor: '#CE8300',
          name: 'hj',
        },
        4: {
          color: '#2B85C2',
          descriptionColor: '#3CA4E7',
          name: 'bj',
        },
      },
    };
  },
  computed: {
    ...mapState({
      userInfo: (state, getters) => getters['login/getUserInfo'],
    }),
    // 主题图片名称
    imgName() {
      return `${this.map[this.theme]?.name}`;
    },
    // 主题字体颜色
    mainColor() {
      return `${this.map[this.theme]?.color}`;
    },
    // 主题等级描述字体颜色
    descriptionColor() {
      return `${this.map[this.theme]?.descriptionColor}`;
    },
    // 等级标签
    rankBackGround() {
      return this.map[this.theme]?.name;
    },
    // 进度条
    getProgress() {
      let length = 418;
      if (this.memberInfo?.rankMax !== 0) {
        length = (this.memberInfo?.chargePq / this.memberInfo?.rankMax) * 418;
      }
      return `${length}rpx`;
    },
  },
  watch: {
    memberInfo: {
      handler() {
        this.getTheme();
      },
      immediate: true,
    },
  },
  mounted() {
    this.openInfo();
  },
  methods: {
    desensitization,
    // 获取主题
    getTheme() {
      let str = this.memberInfo?.rankNo;
      if (this.memberInfo.vipFlag === '1') {
        str = 'plus';
      }
      this.theme = str;
    },
    // 查询会员等级说明
    async openInfo() {
      // 0412 会员协议  0205 会员说明 0210 会员等级说明  0209 积分规则说明
      let arr = ['0210'];
      const params = {
        infoTypeList: arr,
      };
      const [err, res] = await openInfo(params);
      if (res) {
        let list = res.infoList;
        let obj = list.find((item) => item.infoType === '0210');
        this.rankDescription = obj?.content;
      }
    },
    // 打开等级描述
    openDescription() {
      this.$refs.rankPopup.open('bottom');
    },
    // 关闭等级
    closeRank() {
      this.$refs.rankPopup.close();
    },
    // 跳转到购买会员页面
    jumpToBuyVip() {
      uni.navigateTo({
        url: '/pages/member/buyPlus/index',
      });
    },
  },
};
</script>

<style scoped lang="scss">
.member-card {
  padding: 40rpx 0 0 40rpx;
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  height: 330rpx;
  background-size: cover;
  width: 702rpx;
  .header-button {
    margin: -36rpx 0 10rpx -36rpx;
    font-size: 26rpx;
    line-height: 40rpx;
    color: #ffffff;
    width: 129rpx;
    height: 48rpx;
    background-size: contain;
    .content {
      margin-left: 20rpx;
    }
  }
  .member-name {
    display: flex;
    align-items: center;
    margin-bottom: 36rpx;
    .profile {
      width: 100rpx;
      height: 100rpx;
      border-radius: 100rpx;
    }
    .name-rank {
      display: flex;
      margin-bottom: 8rpx;
      .name {
        font-size: 36rpx;
        font-weight: bold;
        line-height: 42rpx;
        letter-spacing: 0.58rpx;
        color: #66778b;
        margin-left: 16rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: 200rpx;
      }
      .user-vip-rank {
        margin-left: 8rpx;
        background-size: cover;
        width: 99rpx;
        height: 42rpx;
      }
    }
    .mobile {
      font-size: 26rpx;
      line-height: 40rpx;
      color: #66778b;
      margin-left: 8rpx;
    }
    .time {
      margin-top: 8rpx;
      font-size: 22rpx;
      line-height: 26rpx;
      margin-left: 16rpx;
    }
  }
  .member-rank {
    margin-right: 40rpx;
    display: flex;
    align-items: flex-end;
    .discription {
      font-size: 32rpx;
      line-height: 38rpx;
    }
    .plus {
      font-size: 32rpx;
      font-weight: bold;
      line-height: 38rpx;
      background: linear-gradient(103deg, #e5e6f8 7%, #c4cef4 33%, #bcc5e6 53%, #dfd3f0 68%, #f4d2db 102%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
    .rank-detail {
      font-size: 24rpx;
      line-height: 32rpx;
      margin-left: 18rpx;
    }
    .arrow {
      width: 24rpx;
      height: 24rpx;
    }
  }
  .member-progress {
    margin-top: 8rpx;
    display: flex;
    align-items: center;
    .progress {
      height: 8rpx;
      margin-right: 17rpx;
      background: rgba(0, 0, 0, 0.2);
      border-radius: 6rpx;
      position: relative;
      &-bg {
        background: rgba(0, 0, 0, 0.2);
        width: 418rpx;
      }
      &-charge {
        top: 0;
        position: absolute;
        background: white;
      }
      &-bg,
      &-charge {
        height: 8rpx;
        border-radius: 6px;
      }
    }
    .ratio {
      font-variation-settings: 'opsz' auto;
      &-item:nth-child(1) {
        font-size: 24rpx;
        font-weight: bold;
      }
      &-item:nth-child(2) {
        font-size: 18rpx;
      }
    }
    .max-level-text {
      font-size: 18rpx;
    }
  }
}
.popup-content {
  .title {
    display: flex;
    justify-content: center;
    margin: 16rpx 0 24rpx 0;
    align-items: center;
    padding: 24rpx;
    position: relative;
    .title-text {
      font-size: 32rpx;
      font-weight: bold;
      line-height: 38rpx;
      color: #333333;
    }
    .close {
      position: absolute;
      top: 24rpx;
      right: 24rpx;
      height: 40rpx;
      width: 40rpx;
    }
  }
  .content {
    padding: 35rpx 24rpx 42rpx 24rpx;
    font-size: 26rpx;
    line-height: 40rpx;
    color: #3d3d3d;
    overflow: scroll;
    max-height: 700rpx;
    overflow-y: auto;
  }
  .line {
    background: #eeeeee;
    width: 100%;
    margin-bottom: 23rpx;
    height: 1rpx;
  }
  .footer-button {
    padding: 0 24rpx 92rpx 24rpx;
  }
}
</style>
