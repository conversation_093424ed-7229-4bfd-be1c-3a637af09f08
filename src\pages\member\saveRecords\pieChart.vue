<template>
  <div class="charts">
    <canvas
      canvas-id="pie"
      id="pie"
      class="charts"
      @touchstart="touchstart"
      @touchmove="touchmove"
      @touchend="touchend"
    />
  </div>
</template>

<script>
import uCharts from './u-charts.js';
export default {
  props: {
    chartData: {
      type: Object,
      default: () => {
        return {
          categories: [],
          series: [],
        };
      },
    },
  },
  components: {},
  data() {
    return {
      cWidth: 750,
      cHeight: 350,
      uChartsInstance: null,
      opts: {
        color: ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'],
        padding: [15, 10, 0, 15],
        dataLabel: false,
        dataPointShape: false,
        enableScroll: false,
        legend: {},
        xAxis: {
          disableGrid: true,
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2,
          data: [
            {
              min: 0,
              max: 150,
            },
          ],
        },
        extra: {
          line: {
            type: 'curve',
            width: 2,
            activeType: 'hollow',
            linearType: 'custom',
            onShadow: true,
            animation: 'horizontal',
          },
        },
      },
      // chartData: {},
    };
  },
  watch: {
    chartData: {
      handler(n, o) {
        if (n) {
          this.drawCharts('pie', this.chartData);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  onReady() {
    // this.getServerData();
  },
  // watch: {
  //   chartData: {
  //     handler(newVal, oldVal) {
  //       // this.getServerData();
  //       // this.drawCharts('pie', this.chartData);
  //     },
  //     deep: true,
  //     immediate: true,
  //   },
  // },
  methods: {
    // getServerData() {
    //   //模拟从服务器获取数据时的延时
    //   setTimeout(() => {
    //     //模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
    //     let res = {
    //       series: [
    //         {
    //           data: [
    //             { name: '会员折扣', value: 50, labelText: '会员折扣' },
    //             { name: '卡券优惠', value: 30, labelText: '卡券优惠' },
    //           ],
    //         },
    //       ],
    //     };
    //     this.setData({ chartData: JSON.parse(JSON.stringify(res)) });
    //     this.drawCharts('pie', this.chartData);
    //   }, 500);
    // },
    drawCharts(id, data) {
      this.cWidth = (600 / 750) * wx.getSystemInfoSync().windowWidth;
      this.cHeight = (450 / 750) * wx.getSystemInfoSync().windowWidth;
      console.log(id, data);
      if (!this.uChartsInstance) {
        const ctx = wx.createCanvasContext(id, this);
        this.uChartsInstance = new uCharts({
          type: 'ring',
          context: ctx,
          width: this.cWidth,
          height: this.cHeight,
          series: data.series,
          animation: true,
          timing: 'easeOut',
          duration: 1000,
          rotate: false,
          rotateLock: false,
          background: '#282934',
          color: ['#FAFAFA', '#E8AC8D', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'],
          padding: [15, 5, 15, 5],
          fontSize: 13,
          fontColor: '#8d9193',
          dataLabel: true,
          dataPointShape: true,
          dataPointShapeType: 'solid',
          touchMoveLimit: 60,
          enableScroll: true,
          enableMarkLine: false,
          legend: {
            show: true,
            position: 'bottom',
            lineHeight: 25,
          },
          extra: {
            ring: {
              ringWidth: 15,
              activeOpacity: 0.5,
              activeRadius: 60,
              offsetAngle: 0,
              labelWidth: 10,
              border: true,
              borderWidth: 3,
              borderColor: '#282934',
              centerColor: '#282934',
              linearType: 'none',
              offsetAngle: 90,
            },
            tooltip: {
              showBox: true,
              showArrow: true,
              showCategory: false,
              borderWidth: 0,
              borderRadius: 0,
              borderColor: '#000000',
              borderOpacity: 0.7,
              bgColor: '#000000',
              bgOpacity: 0.7,
              gridType: 'solid',
              dashLength: 4,
              gridColor: '#CCCCCC',
              boxPadding: 3,
              fontSize: 13,
              lineHeight: 20,
              fontColor: '#FFFFFF',
              legendShow: true,
              legendShape: 'auto',
              splitLine: true,
              horizentalLine: false,
              xAxisLabel: false,
              yAxisLabel: false,
              labelBgColor: '#FFFFFF',
              labelBgOpacity: 0.7,
              labelFontColor: '#666666',
            },
          },
        });
      } else {
        this.uChartsInstance.updateData({
          series: data.series,
        });
      }
    },
    touchstart(e) {
      this.uChartsInstance && this.uChartsInstance.scrollStart(e);
    },
    touchmove(e) {
      this.uChartsInstance && this.uChartsInstance.scroll(e);
    },
    touchend(e) {
      this.uChartsInstance && this.uChartsInstance.scrollEnd(e);
      this.uChartsInstance && this.uChartsInstance.touchLegend(e);
      this.uChartsInstance && this.uChartsInstance.showToolTip(e);
    },
  },
};
</script>

<style scoped lang="scss">
.charts {
  margin: 15rpx 0;
  width: 100%;
  height: 100%;
  z-index: 99;
}
</style>
