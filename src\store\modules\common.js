import amap from '../../utils/Amap/amap-wx';
import { WX_AMAP_KEY } from '../../config/global';
const amapPlugin = new amap.AMapWX({
  key: WX_AMAP_KEY,
});
const state = {
  waitTime: 0,
  network: true,
  hasGG: false,
  saveLocationInfo: {
    latitude: null,
    longitude: null,
    city: null,
    province: null,
  },
  lastLocation: {
    latitude: null,
    longitude: null,
    city: null,
    province: null,
    updateTime: '',
  },
  userPurpose: '',
  userPurposeValue: '',
  licensePlate: [
    {
      message: '',
      mt: 7,
      focus: true,
    },
    {
      message: '',
      mt: 7,
      focus: false,
    },
    {
      message: '',
      mt: 7,
      focus: false,
    },
    {
      message: '',
      mt: 7,
      focus: false,
    },
    {
      message: '',
      mt: 7,
      focus: false,
    },
    {
      message: '',
      mt: 7,
      focus: false,
    },
    {
      message: '',
      mt: 7,
      focus: false,
    },
  ],
  licensePlate8: [
    {
      message: '',
      mt: 7,
      focus: true,
    },
    {
      message: '',
      mt: 7,
      focus: false,
    },
    {
      message: '',
      mt: 7,
      focus: false,
    },
    {
      message: '',
      mt: 7,
      focus: false,
    },
    {
      message: '',
      mt: 7,
      focus: false,
    },
    {
      message: '',
      mt: 7,
      focus: false,
    },
    {
      message: '',
      mt: 7,
      focus: false,
    },
    {
      message: '',
      mt: 8,
      focus: false,
    },
  ],
  licensePlate0: '',
  licensePlate1: '',
  licensePlate2: '',
  licensePlate3: '',
  licensePlate4: '',
  licensePlate5: '',
  licensePlate6: '',
  licensePlate7: '',
  licensePlateChange: true,
  // lastLocation: {
  //     latitude: 24.531449,
  //     longitude: 118.150295,
  //         city: '厦门市',
  //         province: '厦门市'
  //     updateTime: ******************************************
  // },
};
const getters = {
  getWaitTime: (state) => {
    return state.waitTime;
  },
  getNetWork: (state) => {
    return state.network;
  },
  getHasGG: (state) => {
    return state.hasGG;
  },
  getSaveLocationInfo: (state) => {
    return state.saveLocationInfo;
  },
  getLastLocation: (state) => {
    return state.lastLocation;
  },
  getUserPurpose: (state) => {
    return state.userPurpose;
  },
};
const actions = {
  setHasGG({ commit }, has) {
    commit('SET_HASGG', has);
  },
  startWait({ commit }) {
    var date = new Date();
    var time = date.getTime();
    time += 60000;
    commit('SET_WAITTIME', time);
  },
  stopWait({ commit }) {
    commit('SET_WAITTIME', 0);
  },
  setSaveLocation({ commit }, info) {
    const { lat, lon, city, province } = info;

    const locationInfo = {
      latitude: lat,
      longitude: lon,
      city: city,
      province: province || city,
    };
    commit('SAVE_LOCATION_INFO', locationInfo);
  },
  restLocation({ commit }) {
    commit('RESET_LAST_LOCATION');
  },
  getLocation({ commit, state, dispatch }, city) {
    return new Promise(async (resole, reject) => {
      try {
        await dispatch('common/checkNetWork', {}, { root: true });
      } catch (error) {
        reject({
          msg: '网络不可用,请稍后重试',
        });
        return;
      }
      if (state.lastLocation.latitude && state.lastLocation.longitude) {
        if (city && (!state.lastLocation.city || !state.lastLocation.province)) {
        } else {
          let now = new Date();
          const nowTime = now.getTime();
          if (state.lastLocation.updateTime > nowTime) {
            console.log(555555, state.lastLocation);
            resole(state.lastLocation);
            return;
          }
        }
      }

      console.log(44444444);

      // #ifdef  H5
      console.log('H5环境下获取位置');

      // 检测是否在内嵌环境中
      const isInEmbeddedApp = window.self !== window.top;
      console.log('是否在内嵌环境中:', isInEmbeddedApp);

      // 存储位置请求状态的键名
      const LOCATION_REQUEST_KEY = 'location_request_timestamp';

      // 检查是否应该显示位置请求
      // 我们限制位置请求的频率，避免频繁打扰用户
      const shouldShowLocationRequest = () => {
        try {
          const lastRequestTime = localStorage.getItem(LOCATION_REQUEST_KEY);
          const now = Date.now();

          // 如果没有记录或者上次请求时间超过24小时，则显示请求
          if (!lastRequestTime || now - parseInt(lastRequestTime) > 24 * 60 * 60 * 1000) {
            localStorage.setItem(LOCATION_REQUEST_KEY, now.toString());
            return true;
          }

          console.log('位置请求已在24小时内显示过，使用缓存的位置信息');
          return false;
        } catch (e) {
          // localStorage可能不可用（隐私模式等）
          console.error('无法访问localStorage:', e);
          return true;
        }
      };

      // 使用标准geolocation API获取位置
      if (navigator.geolocation) {
        // 如果不应该显示位置请求，并且我们有缓存的位置，则使用缓存的位置
        if (!shouldShowLocationRequest() && state.lastLocation.latitude && state.lastLocation.longitude) {
          console.log('使用缓存的位置信息');
          resole(state.lastLocation);
          return;
        }

        // 设置获取位置的选项
        const options = {
          enableHighAccuracy: true, // 尝试获取最精确的位置
          timeout: 10000, // 10秒超时
          maximumAge: 30 * 60 * 1000, // 允许使用30分钟内的缓存位置
        };

        // 尝试获取位置
        navigator.geolocation.getCurrentPosition(
          function (position) {
            // 成功获取位置
            const latitude = position.coords.latitude; // 纬度
            const longitude = position.coords.longitude; // 经度

            console.log(`获取位置成功: 纬度 ${latitude}, 经度 ${longitude}`);

            // 构建位置信息对象
            let locationInfo = {
              latitude: latitude,
              longitude: longitude,
              city: '',
              province: '',
            };

            // 保存位置信息并返回
            commit('SAVE_LOCATION_INFO', locationInfo);
            commit('SET_LAST_LOCATION', locationInfo);
            resole(locationInfo);
          },
          function (error) {
            // 处理错误
            let errorMsg = '';

            switch (error.code) {
              case error.PERMISSION_DENIED:
                errorMsg = '位置权限被拒绝';

                // 只在应该显示位置请求时显示提示
                if (shouldShowLocationRequest()) {
                  uni.showModal({
                    title: '需要位置权限',
                    content: isInEmbeddedApp
                      ? '应用需要获取您的位置信息才能提供准确的服务。请尝试刷新页面并允许位置访问，或手动输入您的位置。'
                      : '您拒绝了位置权限请求。需要位置信息才能提供准确的服务。请刷新页面并允许位置访问。',
                    confirmText: '我知道了',
                    showCancel: false,
                  });
                }
                break;
              case error.POSITION_UNAVAILABLE:
                errorMsg = '位置信息不可用';
                break;
              case error.TIMEOUT:
                errorMsg = '获取位置超时';
                break;
              case error.UNKNOWN_ERROR:
                errorMsg = '发生未知错误';
                break;
            }

            console.error('获取位置失败:', errorMsg);

            // 如果有缓存的位置信息，在错误时使用缓存的位置
            if (state.lastLocation.latitude && state.lastLocation.longitude) {
              console.log('使用缓存的位置信息');
              resole(state.lastLocation);
              return;
            }

            reject({
              msg: errorMsg || '获取位置失败',
            });
          },
          options
        );
      } else {
        console.error('抱歉，您的浏览器不支持地理位置服务');

        // 如果有缓存的位置信息，在不支持geolocation时使用缓存的位置
        if (state.lastLocation.latitude && state.lastLocation.longitude) {
          console.log('使用缓存的位置信息');
          resole(state.lastLocation);
          return;
        }

        reject({
          msg: '您的浏览器不支持地理位置服务',
        });
      }
      // ls.location((res) => {
      //   if (res.code == 200) {
      //     console.log(66666, res.data)
      //     const { data } = res

      //     let locationInfo = {
      //       latitude: data.latitude,
      //       longitude: data.longitude,
      //       city: data.city,
      //       province: data.city,
      //     }
      //     commit('SET_LAST_LOCATION', locationInfo)
      //     commit('SAVE_LOCATION_INFO', locationInfo)

      //     resole(locationInfo)
      //   } else {
      //     reject({
      //       msg: '获取定位失败',
      //     })
      //   }
      // })

      // #endif

      // #ifdef  MP-ALIPAY

      my.getLocation({
        type: 1,
        success(res) {
          console.log('这里是定位：', res);
          commit('SET_LAST_LOCATION', res);
          commit('SAVE_LOCATION_INFO', res);
          resole(res);
        },
        fail(err) {
          console.log(err, '????');
          if (!my.canIUse('getLocation')) {
            my.showAuthGuide({
              bizType: 'AppletPG',
              authType: 'LBS',
              success: (res) => {
                //shown为true时表示会显示权限引导弹窗，为false时表示用户已经授权
              },
              fail: (error) => {},
            });
            return false;
          }
          if (err.error == 11) {
            my.showAuthGuide({
              bizType: 'AppletPG',
              authType: 'LBS',
              success: (res) => {
                //shown为true时表示会显示权限引导弹窗，为false时表示用户已经授权
              },
              fail: (error) => {},
            });
            return false;
          }
          if (err.error == 2001) {
            uni.showModal({
              title: '提示',
              showCancel: false,
              content: '用户拒绝定位授权',
              success: function (res) {
                if (res.confirm) {
                  resole(dispatch('common/getLocation', { city }, { root: true }));
                }
              },
            });
          } else {
            uni.showModal({
              title: '提示',
              showCancel: false,
              content: err.errorMessage,
              success: function (res) {
                // uni.navigateTo({
                //   url: '/pages/carTips/index',
                // })
                uni.hideLoading();
              },
            });
          }
          if (!my.canIUse('getLocation')) {
            console.log(11111111, '定位GPS未开启2！');
            my.showAuthGuide({
              bizType: 'AppletPG',
              authType: 'LBS',
              success: (res) => {
                //shown为true时表示会显示权限引导弹窗，为false时表示用户已经授权
              },
              fail: (error) => {},
            });
            reject({
              msg: '获取定位失败',
            });
          }
        },
      });
      // #endif

      // #ifdef  MP-WEIXIN
      uni.authorize({
        scope: 'scope.userLocation',
        success() {
          wx.getLocation({
            type: 'gcj02',
            success: (res) => {
              console.log('这里是定位：', res);
              let locationInfo = {
                latitude: res.latitude,
                longitude: res.longitude,
              };
              if (city) {
                amapPlugin.getRegeo({
                  success: (address) => {
                    let addressDetails = address[0].regeocodeData.addressComponent;
                    if (addressDetails.city instanceof Array) {
                      locationInfo.city = addressDetails.province;
                      locationInfo.province = addressDetails.province;
                    } else {
                      locationInfo.city = addressDetails.city;
                      locationInfo.province = addressDetails.province;
                    }
                    commit('SAVE_LOCATION_INFO', locationInfo);
                    commit('SET_LAST_LOCATION', locationInfo);
                    resole(locationInfo);
                  },
                  fail: (errdata) => {
                    commit('SAVE_LOCATION_INFO', locationInfo);
                    commit('SET_LAST_LOCATION', locationInfo);
                    resole(locationInfo);
                  },
                });
              } else {
                commit('SAVE_LOCATION_INFO', locationInfo);
                commit('SET_LAST_LOCATION', locationInfo);
                resole(locationInfo);
              }
            },
            fail: (err) => {
              reject({
                msg: '获取定位失败',
              });
            },
          });
        },
        fail() {
          reject({
            msg: '请先打开定位功能',
          });
        },
      });
      // #endif
    });
  },
  changeNetWork({ commit }, hasNetWork) {
    commit('SET_NETWORK', hasNetWork);
  },
  checkNetWork({ commit }) {
    // uni.onNetworkStatusChange(function (res) {
    //     console.log(333333, res.isConnected)
    //     commit('SET_NETWORK', res.isConnected);
    // });
    return new Promise((resole, reject) => {
      // #ifdef  H5
      resole(true);
      return;
      // #endif
      uni.getNetworkType({
        success: (res) => {
          if (res.networkType == 'none') {
            commit('SET_NETWORK', false);
            reject(false);
            return;
          }
          commit('SET_NETWORK', true);
          resole(true);
        },
        fail: (err) => {
          commit('SET_NETWORK', false);
          reject();
        },
      });
    });
  },
  clear({ commit }) {
    commit('SET_WAITTIME', 0);
  },
  setUserPurpose({ commit }, purpose) {
    commit('SET_USER_PURPOSE', purpose);
  },
  setUserPurposeValue({ commit }, purposeValue) {
    commit('SET_USER_PURPOSE_VALUE', purposeValue);
  },
  setLicensePlate({ commit }, list) {
    commit('SET_LICENSE_PLATE', list);
  },
  setLicensePlate8({ commit }, list) {
    commit('SET_LICENSE_PLATE8', list);
  },
  setLicensePlate0({ commit }, num) {
    commit('SET_LICENSE_PLATE0', num);
  },
  setLicensePlate1({ commit }, num) {
    commit('SET_LICENSE_PLATE1', num);
  },
  setLicensePlate2({ commit }, num) {
    commit('SET_LICENSE_PLATE2', num);
  },
  setLicensePlate3({ commit }, num) {
    commit('SET_LICENSE_PLATE3', num);
  },
  setLicensePlate4({ commit }, num) {
    commit('SET_LICENSE_PLATE4', num);
  },
  setLicensePlate5({ commit }, num) {
    commit('SET_LICENSE_PLATE5', num);
  },
  setLicensePlate6({ commit }, num) {
    commit('SET_LICENSE_PLATE6', num);
  },
  setLicensePlate7({ commit }, num) {
    commit('SET_LICENSE_PLATE7', num);
  },
  setLicensePlateChange({ commit }, isOk) {
    commit('SET_LICENSE_PLATE_CHANGE', isOk);
  },
};
const mutations = {
  ['SET_HASGG'](state, has) {
    state.hasGG = has;
  },
  ['SET_WAITTIME'](state, time) {
    state.waitTime = time;
  },
  ['SET_NETWORK'](state, network) {
    state.network = network;
  },
  ['SAVE_LOCATION_INFO'](state, positionInfo) {
    state.saveLocationInfo = positionInfo;
  },
  ['SET_LAST_LOCATION'](state, positionInfo) {
    let time = new Date();
    const nowTime = time.getTime();
    state.lastLocation = positionInfo;
    //存5分钟
    state.lastLocation.updateTime = nowTime + 1000 * 60 * 5;
  },
  ['RESET_LAST_LOCATION'](state) {
    state.lastLocation = {
      latitude: null,
      longitude: null,
      city: '',
      province: '',
      updateTime: '',
    };
  },
  ['SET_USER_PURPOSE'](state, purpose) {
    state.userPurpose = purpose;
  },
  ['SET_USER_PURPOSE_VALUE'](state, purposeValue) {
    state.userPurposeValue = purposeValue;
  },
  ['SET_LICENSE_PLATE'](state, list) {
    state.licensePlate = list;
  },
  ['SET_LICENSE_PLATE8'](state, list) {
    state.licensePlate8 = list;
  },
  ['SET_LICENSE_PLATE0'](state, num) {
    state.licensePlate0 = num;
  },
  ['SET_LICENSE_PLATE1'](state, num) {
    state.licensePlate1 = num;
  },
  ['SET_LICENSE_PLATE2'](state, num) {
    state.licensePlate2 = num;
  },
  ['SET_LICENSE_PLATE3'](state, num) {
    state.licensePlate4 = num;
  },
  ['SET_LICENSE_PLATE4'](state, num) {
    state.licensePlate4 = num;
  },
  ['SET_LICENSE_PLATE5'](state, num) {
    state.licensePlate5 = num;
  },
  ['SET_LICENSE_PLATE6'](state, num) {
    state.licensePlate6 = num;
  },
  ['SET_LICENSE_PLATE7'](state, num) {
    state.licensePlate7 = num;
  },
  ['SET_LICENSE_PLATE_CHANGE'](state, isOk) {
    state.licensePlateChange = isOk;
  },
};
export const common = {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
};
