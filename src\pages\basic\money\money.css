.money {
  width: 100%;
  /* height: 482rpx; */
  background: #2196f3;
  box-sizing: border-box;
  padding: 30rpx;
  position: relative;
}

.money .money_top {
  font-size: 30rpx;
  color: #fff;
  line-height: 80rpx;
}

.money .money_middle {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.money .money_middle .money_middle_lf {
  font-size: 88rpx;
  color: #fff;
  flex: 1;
}

.money .money_middle .money_middle_lf text {
  font-size: 30rpx;
  margin: 10rpx;
}

.money .money_middle .money_middle_lf .lf_msg {
  color: #fff;
  font-size: 26rpx;
  padding-left: 42rpx;
}

.money .money_middle button {
  background: #fff;
  color: #2196f3;
  border-radius: 4rpx;
  margin: 0;
  padding: 3rpx 15rpx;
  margin-right: 10rpx;
}

.money .money_flex {
  display: flex;
  justify-content: space-around;
  align-items: center;
  /* position: absolute;
  left: 0;
  right: 0;
  bottom: 50rpx; */
}

.money .money_flex .money_flex_list {
  position: relative;
  display: flex;
  justify-content: center;
  align-content: space-around;
  flex-wrap: wrap;
}

.money .money_flex .money_flex_list view {
  width: 100%;
  text-align: center;
  color: #fff;
}

.money .money_flex .money_flex_list .net1 {
  font-size: 40rpx;
}

.money .money_flex .money_flex_list .net2 {
  font-size: 24rpx;
}

/* .money .money_flex .count::before {
  position: absolute;
  top: 25%;
  content: '';
  width: 1rpx;
  left: 0;
  height: 50rpx;
  background: #fff;
}

.money .money_flex .counts::before {
  position: absolute;
  top: 25%;
  content: '';
  width: 1rpx;
  left: -8rpx;
  height: 50rpx;
  display: block;
  background: #fff;
} */

.money_item {
  background: #fff;
}

.money_item view {
  display: flex;
  /* width: calc(100% - 30rpx); */
  width: 100%;
  box-sizing: border-box;
  /* margin-left: 30rpx; */
  padding-left: 30rpx;
  padding-right: 30rpx;
  justify-content: space-between;
  align-items: center;
  font-size: 30rpx;
  color: #333;
  height: 88rpx;
}

.money_item .item_info {
  border-bottom: 1rpx solid #ccc;
}