<template>
  <view>
    <view v-for="item in cpnList" :key="item.couponId">
      <CouponCard :cardInfo="item" :pageName="pageName" :redeem-button-flag="false" :un-use-flag="false" />
    </view>
  </view>
</template>

<script>
import CouponCard from '@/components/CouponCard/index';
import { getMemberInfo } from '@/services/index.js';

export default {
  name: 'index',
  components: {
    CouponCard,
  },
  data() {
    return {
      cpnList: [],
      pageName: '',
      map: {
        isExclusiveOffer: 'exclusiveOffer',
        isWelcomeGift: 'welcomeGift',
      },
    };
  },
  onLoad(options) {
    this.pageName = options.pageName;
    this.getMemberInfo();
  },
  methods: {
    // 获取会员信息
    async getMemberInfo() {
      const [err, res] = await getMemberInfo();
      if (res) {
        let obj = res.benefitList.find((item) => {
          return item.benefitCode === this.pageName;
        });
        const str = obj[this.map[this.pageName]];
        if (str) {
          const arr = JSON.parse(str);
          if (arr.length !== 0) {
            this.cpnList = arr.map((item) => {
              const { couponName, effectTime, remark, couponNum } = item;
              let obj = {
                cpnName: couponName,
                timeLimit: effectTime,
                cpnMarks: remark,
                cpnAmt: couponNum,
              };
              return obj;
            });
          }
        }
        console.log('this.cpnList', this.cpnList);
      }
    },
  },
};
</script>

<style scoped></style>
