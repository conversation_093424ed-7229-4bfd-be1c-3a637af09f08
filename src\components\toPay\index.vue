<template>
  <!--        选择支付方式弹框-->
  <uni-popup ref="channelPopup" background-color="#F6F7F9">
    <view class="popup-content">
      <view class="title">
        <text class="title-text">选择支付方式</text>
        <img class="close" :src="`${IMG_PATH}close.png`" @click="closeChannelPopup" />
      </view>
      <!--                支付渠道列表-->
      <view class="channel">
        <view class="channel-list" v-for="item in channelList" :key="item.type">
          <view class="channel-name">
            <img class="channel-icon" :src="`${IMG_PATH}member/${item.url}.png`" />
            <text>{{ item.name }}</text>
          </view>
          <img
            @click="getChannel(item)"
            class="check-icon"
            :src="`${IMG_PATH}${channel === item.type ? 'check' : 'circle'}.png`"
          />
        </view>
      </view>
    </view>
    <view class="footer-button">
      <AppButton type="primary" @click="payWay"> 立即支付 </AppButton>
    </view>
  </uni-popup>
</template>

<script>
import uniPopup from '@/components/uni-ui/uni-popup/uni-popup';
import uniPopupDialog from '@/components/uni-ui/uni-popup-dialog/uni-popup-dialog';
import { subscribeMessage } from '@/utils/messageCode.js';
import AppButton from '@/components/AppButton/index';
import { getMemberInfo, recharge, getIndData } from '@/services/index.js';
import { ToPay } from '@/utils/bridge/index.js';
import moment from 'moment';
import { ENVR } from '@/config/global';
export default {
  name: 'toPay',
  props: {
    selectVipPage: {
      type: Object,
      default: () => {},
    },
    memberInfo: {
      type: Object,
      default: () => {},
    },
  },
  components: { uniPopup, uniPopupDialog, AppButton },
  data() {
    return {
      channel: 2,
      channelList: [
        {
          type: 2,
          name: '微信支付',
          url: 'wx',
        },
        {
          type: 1,
          name: '支付宝支付',
          url: 'zfb',
        },
        {
          type: 3,
          name: '银联支付',
          url: 'yl',
        },
      ],
      channelMap: {
        1: '1302',
        2: '1303',
        3: '1301',
      },
      nowMemberInfo: {},
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 获取会员信息
    async getMemberInfo() {
      const params = {
        plusEquity: '1',
      };
      const [err, res] = await getMemberInfo(params);

      if (res) {
        this.nowMemberInfo = res;
      }
    },
    async open() {
      this.$refs.channelPopup && this.$refs.channelPopup.open('bottom');
    },

    // 立即开通 底部按钮
    chooseChannel() {
      if (!this.selectVipPage.vipId) {
        return;
      }
      // #ifdef H5
      this.open();
      // #endif
      // #ifdef MP
      this.recharge();
      // #endif
    }, // 选择支付方式后：微信支付直接调桥接器，其他支付方式先查询支付参数后调用桥接器
    payWay() {
      // i宁德直接跳转
      const appName = uni.getStorageSync('app') || '';
      if (appName == 'i宁德') {
        this.rechargeIND();
        return;
      }
      if (this.channel === 2) {
        this.payMoney({});
      } else {
        this.recharge();
      }
    },
    // 关闭选择渠道弹框
    closeChannelPopup() {
      uni.showModal({
        title: '提示',
        content: '是否已支付',
        cancelText: '否',
        confirmText: '是',
        success: async (res) => {
          console.log(res, 'res');
          if (res.confirm) {
            await this.getMemberInfo();
            console.log(this.memberInfo, this.nowMemberInfo);
            if (
              (this.memberInfo.vipFlag == '0' && this.nowMemberInfo.vipFlag == '1') ||
              moment(this.memberInfo.vipExpireTime).isBefore(this.nowMemberInfo.vipExpireTime)
            ) {
              let content = '';
              if (this.memberInfo.vipFlag == '0' && this.nowMemberInfo.vipFlag == '1') {
                content = '您已成功开通会员';
              } else if (moment(this.memberInfo.vipExpireTime).isBefore(this.nowMemberInfo.vipExpireTime)) {
                content = '您已成功续费会员';
              } else {
                content = '支付失败';
              }
              uni.showModal({
                title: '提示',
                content: content,
              });
              this.$emit('reset');
            } else {
              let content =
                this.memberInfo.vipFlag == '0'
                  ? '开通会员失败'
                  : this.memberInfo.vipFlag == '1'
                  ? '续费失败'
                  : '支付失败';
              uni.showModal({
                title: '提示',
                content: content,
              });
            }
            this.$refs.channelPopup.close();
          } else {
            this.$refs.channelPopup.close();
          }
        },
        fail: () => {
          this.$refs.channelPopup.close();
        },
      });
      // this.$refs.channelPopup.close();
    },
    // 选择支付渠道
    getChannel(item) {
      this.channel = item.type;
    },
    // 查询支付参数
    async recharge() {
      const params = {
        reqType: '01',
        appType: '04',
        payAmount: this.selectVipPage.price,
        vipType: this.selectVipPage.vipType,
      };

      // #ifdef MP
      let extend = '';
      uni.login({
        success: async (info) => {
          extend = info.code;
        },
      });
      params.extend = extend;
      params.transactionChannel = '1303';
      // #endif

      // #ifdef H5
      params.transactionChannel = this.channelMap[this.channel];
      // #endif

      const [err, res] = await recharge(params);
      if (res) {
        let data = '';
        // #ifdef MP
        data = res.orderResultChinaUms.miniPayRequest;
        // #endif
        // #ifdef H5
        data = res;
        // #endif
        this.payMoney(data);
      }
    },
    // 支付
    payMoney(params) {
      // #ifdef MP
      this.wxPay(params);
      // #endif
      // #ifdef H5
      this.appPay(params);
      // #endif
    },
    // 微信支付
    wxPay(params) {
      const that = this;
      ENVR !== 'wx' &&
        uni.requestPayment({
          timeStamp: params.timeStamp,
          nonceStr: params.nonceStr,
          package: params.package,
          signType: 'MD5',
          paySign: params.paySign,
          success: function () {
            that.paySuccess();
          },
          fail: function (err) {
            that.payFail(err.msg);
          },
        });
      subscribeMessage(['会员购买成功通知'], () => {
        uni.requestPayment({
          timeStamp: params.timeStamp,
          nonceStr: params.nonceStr,
          package: params.package,
          signType: 'MD5',
          paySign: params.paySign,
          success: function () {
            that.paySuccess();
          },
          fail: function (err) {
            that.payFail(err.msg);
          },
        });
      });
    },
    // app支付
    async appPay(params) {
      // payChannel 1 支付宝 ，2微信 ，3 银联
      console.log(params, 'params');
      const getPayResult = ToPay({
        isNeedLogin: true,
        payChannel: this.channel + '',
        serviceType: 'member',
        payAmount: this.selectVipPage.price,
        vipType: this.selectVipPage.vipType,
        transactionChannel: this.channelMap[this.channel],
        ...params,
      });

      const that = this;
      getPayResult.then((result) => {
        console.log(result, 'result:支付后');
        if (result.code === '9999') {
          that.paySuccess();
        } else {
          that.payFail(result.msg);
        }
      });
    },
    // 付款成功后操作
    paySuccess() {
      uni.showModal({
        title: '开通成功',
        showCancel: false,
        content: '欢迎您成为尊贵的VIP会员',
        confirmText: '我知道了',
      });
      this.closeChannelPopup();
      this.$store.dispatch('login/getUserInfoCallback');
      this.$emit('reset');
    },
    // 支付失败操作
    payFail(msg) {
      uni.showModal({
        title: '开通失败',
        showCancel: false,
        content: msg,
        confirmText: '确定',
      });
    }, // 查询支付参数
    async rechargeIND() {
      const params = {
        reqType: '01',
        appType: '04',
        payAmount: this.selectVipPage.price,
        vipType: this.selectVipPage.vipType,
        transactionChannel: '12',
      };
      const [err, res] = await recharge(params);
      if (res && res?.orderResultINingDe) {
        window.location.href = res.orderResultINingDe;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.popup-content {
  .title {
    display: flex;
    justify-content: center;
    margin: 16rpx 0 24rpx 0;
    align-items: center;
    padding: 24rpx;
    position: relative;
    .title-text {
      font-size: 32rpx;
      font-weight: bold;
      line-height: 38rpx;
      color: #333333;
    }
    .close {
      position: absolute;
      top: 24rpx;
      right: 24rpx;
      height: 40rpx;
      width: 40rpx;
    }
  }
  .popup-footer-button {
    padding: 0 24rpx 92rpx 24rpx;
  }
  .content-wrap {
    padding: 35rpx 24rpx 42rpx 24rpx;
    font-size: 26rpx;
    line-height: 40rpx;
    color: #3d3d3d;
    overflow: scroll;
  }
  .line {
    background: #eeeeee;
    width: 100%;
    margin-bottom: 23rpx;
    height: 1rpx;
  }
  .channel {
    display: flex;
    flex-direction: column;
    padding: 24rpx;
    .channel-list {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1rpx solid #eeeeee;
      padding: 24rpx 0;
      .channel-name {
        display: flex;
        align-items: center;
        .channel-text {
          font-size: 36rpx;
          color: #333333;
        }
        .channel-icon {
          width: 40rpx;
          height: 40rpx;
          margin-right: 16rpx;
        }
      }
      .check-icon {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
}
</style>
