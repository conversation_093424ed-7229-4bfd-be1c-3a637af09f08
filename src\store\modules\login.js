import { initCodeApi, loginApi, checkRefreshTokenApi } from '../../services/loginService';
import TripleDES from '../../utils/TripleDES/index';
import { ORG_CODE, LOGIN_CHANNEL } from '../../config/global';
import network from '@/utils/network.js';
import { baseDate, baseDateNo } from '@/utils/base.js';
import { ENVR } from '../../config/global';
const baseurl = ENVR === 'wx' ? 'scan/wx' : 'def/api';
import { loginInterface, logOff, getUserInfo, logout } from '@/services/index.js';

const state = {
  bannerType: true,
  userInfo: null,
  code: null, //小程序code
  mobile: '',
  custId: null, //用户标识id
  fromCustInfo: {
    //邀请人信息
    custId: null, //用户标识id
    name: '',
    avatar: '', //头像
    mobile: '',
  },
  token: '',
  tokenNd: '',
  refreshTokenNd: '',
  Intervals: null,
  refreshToken: '',
  h5AppId: null, //h5Appid
  openId: null, //小程序openid
  qrcodes: null, //外部第三方启动 传入qrcode枪信息
  exchangeCode: null, //支付宝扫码进入 传入优惠券兑换码
  startPath: null, //存储启动页面地址
  startActivityPageCode: null, //活动页面启动参数
  billServiceTel: null,
  faultServiceTel: null,
};
const getters = {
  getUserInfo: (state) => {
    return state.userInfo;
  },
  getMobile: (state) => {
    console.log('getMobile方法：', state.mobile);
    return state.mobile;
  },
  getPhones: (state) => {
    return {
      billServiceTel: state.billServiceTel,
      faultServiceTel: state.faultServiceTel,
    };
  },
  getToken: (state) => {
    console.log('此时的state：', state);
    return {
      token: state.token,
      refreshToken: state.refreshToken,
      tokenNd: state.tokenNd,
      refreshTokenNd: state.refreshTokenNd,
    };
  },
  getTokenZXC: (state) => {
    console.log('state.tokenstate.token', state.token);
    return {
      token: state.token,
    };
  },
  getBannerType: (state) => {
    return state.bannerType;
  },
  getCustId: (state) => {
    return state.custId;
  },
  getIntervals: (state) => {
    return state.Intervals;
  },
  getFromCustInfo: (state) => {
    return state.fromCustInfo;
  },
  // #ifdef  MP-WEIXIN
  isLogin: (state) => {
    if (state.openId) {
      return true;
    }
    return false;
  },
  // #endif

  // #ifdef  MP-ALIPAY
  isLogin: (state) => {
    if (state.token) {
      return true;
    }
    return false;
  },
  // #endif
  // #ifdef  H5
  isLogin: (state) => {
    if (LOGIN_CHANNEL == '0602') {
      if (state.token && state.h5AppId) {
        return true;
      }
    } else {
      if (state.token) {
        return true;
      }
    }
    return false;
  },
  // #endif
  getCode: (state) => {
    return state.code;
  },
  getH5AppId: (state) => {
    return state.h5AppId;
  },
  getOpenId: (state) => {
    return state.openId;
  },
  getQrcodes: (state) => {
    return state.qrcodes;
  },
  getExchangeCode: (state) => {
    return state.exchangeCode;
  },

  getStartPath: (state) => {
    return state.startPath;
  },
  // getStartActivityPageCode: state => {
  //     return state.startActivityPageCode
  // },
};
let refreshTokenEvent = null;
const actions = {
  setUserInfo({ commit }, option) {
    commit('SET_USERINFO', option);
  },
  setFromCustInfo({ commit }, option) {
    commit('SET_FROM_CUSTINFO', option);
  },
  setMobile({ commit }, option) {
    commit('SET_MOBILE', option);
  },
  setQurcode({ commit }, option) {
    commit('SET_qrcode', option);
  },
  setExchangeCode({ commit }, option) {
    commit('SET_EXCHANGECODE', option);
  },
  setStartPath({ commit }, option) {
    commit('SET_START_PATH', option);
  },
  setChargingPhone({ commit }, option) {
    //计费电话
    commit('SET_CHARG_PHONE', option);
  },
  setBanner({ commit }, option) {
    //广告显示状态
    commit('SET_BANNER', option);
  },

  setFaultPhone({ commit }, option) {
    //故障电话
    commit('SET_Fault_PHONE', option);
  },
  // setStartActivityPageCode ({ commit }, option) {
  //     commit('SET_START_ACTIVITY_PAGE_CODE', option);
  // },
  setToken({ commit }, option) {
    // console.log('传进来的option', option);
    commit('SET_TOKEN_ZXC', {
      token: option.token,
      refreshToken: option.refreshToken,
    });
  },
  setIntervals({ commit }, option) {
    // console.log('传进来的setIntervals', option);
    commit('SET_Intervals', {
      Intervals: option,
    });
  },
  tokensWX({ commit }, option) {
    // console.log('此时的tokensWX33333，：', option);
    commit('SET_TOKEN', {
      token: option.token,
      refreshToken: option.refreshToken,
    });
    commit('SET_OPENID', option.openId);
  },
  async login({ commit, state, dispatch }, option) {
    try {
      // const newCode = await dispatch('login/refreshWxCode', {}, { root: true });
      // console.log('login:', option);

      let params = {
        orgCode: ORG_CODE,
        loginChannel: LOGIN_CHANNEL,
        ...option,
      };
      // #ifdef  H5
      //   params.authCode = newCode
      //   // #endif

      //   // #ifdef MP-WEIXIN
      //   params.wxCode = newCode
      //   // #endif

      //   // #ifdef MP-ALIPAY
      //   params.authCode = newCode
      //   // #endif

      const [, info] = await loginInterface(params);
      console.log(info, '这是一键登录！！！');
      const mobileInfo = TripleDES.decrypt(info.data);

      dispatch('login/setMobile', mobileInfo.mobile, { root: true });

      dispatch(
        'login/loginCode',
        {
          custId: info.custId,
          token: info.token,
          refreshToken: info.refreshToken,
          openId: info.openId,
        },
        { root: true }
      );
    } catch (error) {
      return Promise.reject(error);
    }
  },

  loginCode({ commit, state, dispatch }, option) {
    try {
      console.log('loginCode:', option);

      commit('SET_TOKEN', {
        token: option.token,
        refreshToken: option.refreshToken,
      });
      commit('SET_CUSTID', option.custId);
      commit('SET_OPENID', option.openId);

      //     return state.startPath
      // },
      // getStartActivityPageCode: state => {
      //     return state.startActivityPageCode

      // #ifdef MP-ALIPAY
      uni.reLaunch({
        url: '/pages/mapHt/index',
      });
      // #endif
      // #ifndef H5
      if (state.qrcodes) {
        uni.redirectTo({
          url: '/pages/transaction/index?qrCode=' + encodeURIComponent(state.qrcodes),
        });
      } else if (state.startPath) {
        let path = '/' + state.startPath;
        // if (state.startPath.indexOf('pages/team/index') >= 0 && state.startActivityPageCode) {
        //     path += '?shareTeamCode=' + state.startActivityPageCode;
        //     console.log(777, '当前的url：', '/' + state.startPath + '?shareTeamCode=' + state.startActivityPageCode)
        //     commit('SET_START_PATH', null)
        //     commit('SET_START_ACTIVITY_PAGE_CODE', null)
        // }

        uni.redirectTo({
          url: path,
          success: () => {
            commit('SET_START_PATH', null);
          },
        });
      } else {
        uni.reLaunch({
          url: '/pages/mapHt/index',
        });
      }
      // #endif
    } catch (error) {
      return Promise.reject(error);
    }
  },

  getxxx({ commit, state }) {
    return 333;
  },
  getRefreshToken({ commit, state }) {
    console.log(555, state.token);
    console.log(555666, state.refreshToken);
    if (!refreshTokenEvent) {
      refreshTokenEvent = new Promise((resolve, reject) => {
        let option = {
          token: state.token,
          refreshToken: state.refreshToken,
        };
        checkRefreshTokenApi(option)
          .then((info) => {
            commit('SET_TOKEN', {
              token: info.token,
              refreshToken: info.refreshToken,
            });
            // commit('SET_TOKEN_ZXC', {
            //   token: info.token,
            //   refreshToken: info.refreshToken,
            // })
            commit('SET_CUSTID', info.custId);
            resolve(info);
            refreshTokenEvent = null;
          })
          .catch((err) => {
            reject(err);
            refreshTokenEvent = null;
          });
      });
    }
    return refreshTokenEvent;
    // try {

    //     const info = await checkRefreshTokenApi({
    //         token: state.token,
    //         refreshToken: state.refreshToken,
    //     })
    //     commit('SET_TOKEN', {
    //         token: info.token,
    //         refreshToken: info.refreshToken
    //     });
    //     return info
    // } catch (error) {
    //     return Promise.reject(error)
    // }
  },
  refreshWxCode({ commit, dispatch }) {
    return new Promise(async (reslove, reject) => {
      try {
        await dispatch('common/checkNetWork', {}, { root: true });
      } catch (error) {
        reject({
          msg: '网络不可用,请稍后重试',
        });
      }
      // #ifdef  MP-WEIXIN
      uni.login({
        provider: 'weixin',
        success: (loginRes) => {
          commit('SET_WX_CODE', loginRes.code);
          reslove(loginRes.code);
        },
        fail: (err) => {
          reject(err);
        },
      });
      // #endif

      // #ifdef  MP-ALIPAY
      my.getAuthCode({
        scopes: 'auth_base', // 主动授权：auth_user，静默授权：auth_base。或者其它scope
        success: (res) => {
          if (res.authCode) {
            // 认证成功
            // 调用自己的服务端接口，让服务端进行后端的授权认证，并且种session，需要解决跨域问题
            console.log('SET_WX_authCode:', res.authCode);
            commit('SET_WX_CODE', res.authCode);
            reslove(res.authCode);
          }
        },
        fail: (err) => {
          reject(err);
        },
      });

      // #endif

      // #ifdef  H5
      try {
        if (LOGIN_CHANNEL == '0602') {
          //超级app
          const info = await initCodeApi();
          commit('SET_WX_CODE', info.initCode);
          commit('SET_H5_APPID', info.appId);

          ls.config({
            appId: info.appId,
            initCode: info.initCode,
          });

          ls.ready(function () {
            ls.userAuth({ appId: info.appId }, function (res) {
              if (res.code == 200) {
                reslove(res.data.requestCode);
              } else {
                reject({ msg: res.msg || res.data.msg });
              }
            });
          });

          ls.error(function (e) {
            reject(e);
          });
        } else {
          ls.userAuth({}, function (res) {
            if (res.code == 200) {
              reslove(res.data.requestCode);
            } else {
              reject({ msg: res.msg || res.data.msg });
            }
          });
        }
      } catch (error) {
        reject(error);
      }

      // #endif
    });
  },
  clear({ commit }) {
    commit('SET_MOBILE', '');
    commit('SET_TOKEN', {
      token: null,
      refreshToken: null,
    });
    commit('SET_OPENID', null);
  },
  // 获取用户信息
  getUserInfoCallback({ commit, dispatch }) {
    console.log('获取用户信息');
    return new Promise(async (resolve, reject) => {
      const [err, res] = await getUserInfo();
      console.log(res, '用户信息');
      if (res) {
        if (res && res.ret == 200) {
          dispatch('setUserInfo', res);
        } else {
          dispatch('setUserInfo', null);
        }
        resolve(res);
      } else {
        console.log('失败');
        dispatch('setUserInfo', null);

        reject(err);
      }
    });
  },
  // 退出登录
  async logout({ commit, dispatch }) {
    console.log('logout方法');
    try {
      const [err, res] = await logout();
      console.log(res, 'res');
      if (res && res.ret == 200) {
        dispatch('setUserInfo', null);
        uni.removeStorageSync('token');
        uni.navigateTo({ url: '/pages/login/login' });
      } else {
        console.log('失败');
      }
    } catch (error) {
      return Promise.reject(error);
    }
  },
  // 注销
  async logOff({ commit, dispatch }) {
    console.log('logOff方法');
    try {
      const [err, res] = await logOff();
      if (res && res.ret == 200) {
        dispatch('setUserInfo', null);
        uni.removeStorageSync('token');
        uni.navigateTo({ url: '/pages/login/login' });
        if (res.msg) {
          uni.showModal({
            icon: 'warning',
            title: res.data.msg,
            showCancel: false,
          });
        }
      } else {
        console.log('失败');
        dispatch('setUserInfo', null);
        uni.removeStorageSync('token');
      }
    } catch (error) {
      return Promise.reject(error);
    }
  },
};
const mutations = {
  ['SET_USERINFO'](state, info) {
    uni.setStorageSync('userInfo', info);
    state.userInfo = info;
  },
  ['SET_FROM_CUSTINFO'](state, info) {
    state.fromCustInfo = {
      ...info,
    };
  },
  ['SET_MOBILE'](state, mobile) {
    state.mobile = mobile;
  },
  ['SET_qrcode'](state, qrcode) {
    state.qrcodes = qrcode;
  },
  ['SET_EXCHANGECODE'](state, code) {
    state.exchangeCode = code;
  },
  ['SET_START_PATH'](state, startPath) {
    state.startPath = startPath;
  },
  ['SET_CHARG_PHONE'](state, startPath) {
    state.faultServiceTel = startPath.mobile;
  },
  ['SET_BANNER'](state, startPath) {
    state.bannerType = false;
  },
  ['SET_Fault_PHONE'](state, startPath) {
    state.billServiceTel = startPath.mobile;
  },
  // ['SET_START_ACTIVITY_PAGE_CODE'] (state, startActivityPageCode) {
  //     console.log('4545', startActivityPageCode)
  //     state.startActivityPageCode = startActivityPageCode;
  // },
  ['SET_H5_APPID'](state, appid) {
    state.h5AppId = appid;
  },
  ['SET_WX_CODE'](state, code) {
    state.code = code;
  },
  ['SET_TOKEN'](state, option) {
    state.token = option.token;
    state.refreshToken = option.refreshToken;
    state.tokenNd = option.token;
    state.refreshTokenNd = option.refreshToken;
  },
  ['SET_Intervals'](state, code) {
    state.Intervals = code;
  },
  ['SET_TOKEN_ZXC'](state, option) {
    state.token = option.token;
    state.refreshToken = option.refreshToken;
  },

  ['SET_CUSTID'](state, custId) {
    state.custId = custId;
  },
  ['SET_OPENID'](state, openId) {
    state.openId = openId;
  },
};
export const login = {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
};
